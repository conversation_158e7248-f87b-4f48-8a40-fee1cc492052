{"folders": [{"name": "Root", "path": "."}, {"name": "Web App", "path": "./apps/web"}, {"name": "Mobile App", "path": "./apps/mobile"}, {"name": "Shared Types", "path": "./packages/shared-types"}, {"name": "Shared Utils", "path": "./packages/shared-utils"}, {"name": "API Client", "path": "./packages/api-client"}, {"name": "UI Components", "path": "./packages/ui-components"}, {"name": "Database Models", "path": "./packages/database-models"}, {"name": "Tools", "path": "./tools"}, {"name": "Documentation", "path": "./docs"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.workspaceSymbols.scope": "allOpenProjects", "editor.formatOnSave": true, "eslint.workingDirectories": ["./apps/web", "./apps/mobile", "./packages/shared-types", "./packages/shared-utils", "./packages/api-client", "./packages/ui-components", "./packages/database-models"]}, "extensions": {"recommendations": ["ms-vscode.typescript-hero", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-jest", "ms-vscode.vscode-eslint"]}}