# Production Preparation Roadmap

This document outlines the steps needed to prepare the codebase for production. The issues are categorized by priority to allow for incremental improvement without blocking deployment.

## Immediate Fixes (Required for Production) ✅

- [x] Fix syntax errors in storekeeper pages
- [x] Fix test file extension (rename `.test.ts` to `.test.tsx` for files containing JSX)
- [x] Add TypeScript checking script to package.json
- [x] Update ESLint configuration to warn instead of error on less critical issues
- [x] Verify the code can build for production with `npm run build`

## High Priority (Fix Soon After Deployment)

1. **Fix React hooks dependency warnings**
   - Update useEffect dependency arrays in components to include all dependencies
   - Use useCallback for functions passed to useEffect dependencies

2. **Fix unused variables/imports**
   - Remove or rename unused variables with underscore prefix (e.g., `_unused`)
   - Clean up unused imports

## Medium Priority (Fix in Next Release)

1. **Replace `any` types with proper interfaces**
   - Focus on models first (src/models/*.ts)
   - Then API routes (src/app/api/*)
   - Finally components (src/components/*)

2. **Fix React hooks issues**
   - Resolve missing dependencies
   - Fix useState type declarations

## Low Priority (Fix Later)

1. **Improve test files**
   - Fix test utilities
   - Update mock data to match current interfaces

2. **Code style improvements**
   - Add strong TypeScript types throughout the codebase
   - Add JSDoc comments for functions

## Automated Testing Setup

1. **Setup pre-commit hooks with husky**
   - Run linting and TypeScript checks on commit
   - Prevent committing code with errors

2. **CI/CD Integration**
   - Setup GitHub Actions for automated testing
   - Add TypeScript checks to CI pipeline

## Maintenance Strategy

Going forward, follow these practices to maintain code quality:

1. **No new `any` types**: Always define proper interfaces for new code
2. **Complete dependency arrays**: Ensure all useEffect dependencies are included
3. **Run linting before committing**: Make `npm run lint` and `npm run typecheck` part of your workflow
4. **Document complex logic**: Add comments explaining non-obvious code