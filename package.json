{"name": "foodprepai-monorepo", "version": "0.1.0", "private": true, "description": "FoodPrepAI Monorepo - Unified development environment for FoodPrepAI web app and IonicPOS mobile app", "workspaces": ["apps/*", "packages/*", "tools/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev --parallel", "lint": "turbo run lint", "test": "turbo run test", "typecheck": "turbo run typecheck", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "dev:web": "turbo run dev --filter=web", "dev:mobile": "turbo run dev --filter=mobile", "build:web": "turbo run build --filter=web", "build:mobile": "turbo run build --filter=mobile", "test:user-sync": "node scripts/create-test-user.js", "prepare": "husky install", "setup": "bash scripts/dev-setup.sh", "dev-tools": "node scripts/dev-tools.js", "clean:deps": "rm -rf node_modules apps/*/node_modules packages/*/node_modules tools/*/node_modules && rm -f package-lock.json apps/*/package-lock.json packages/*/package-lock.json tools/*/package-lock.json", "health-check": "node scripts/dev-tools.js", "generate:component": "node scripts/generators/component-generator.js", "generate:api": "node scripts/generators/api-generator.js", "workspace:info": "npm ls --depth=0 && npm run --workspaces --if-present"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menu": "^2.1.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-popper": "^1.2.0", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@tanstack/react-query": "^5.62.11", "@tanstack/react-query-devtools": "^5.62.11", "@tanstack/react-table": "^8.20.6", "@types/dotenv": "^6.1.1", "@upstash/redis": "^1.34.6", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "firebase-admin": "^13.0.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "mongodb": "^6.12.0", "mongoose": "^8.9.2", "next": "15.1.0", "next-auth": "^4.24.11", "node-polyfill-webpack-plugin": "^4.1.0", "react": "^18.3.1", "react-day-picker": "^9.5.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "recharts": "^3.0.2", "shadcn-ui": "^0.9.4", "sharp": "^0.33.5", "sonner": "^1.7.1", "swr": "^2.3.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.0", "zod": "^3.24.1"}, "devDependencies": {"turbo": "^2.3.0", "prettier": "^3.4.2", "husky": "^9.1.7", "lint-staged": "^15.2.11", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@babel/core": "^7.26.0", "@babel/plugin-syntax-import-attributes": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@jest/globals": "^29.7.0", "@jest/types": "^29.6.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/text-encoding": "^0.0.40", "@types/webpack": "^5.28.5", "assert": "^2.1.0", "babel-jest": "^29.7.0", "browserify-zlib": "^0.2.0", "crypto-browserify": "^3.12.1", "eslint": "^8", "eslint-config-next": "15.1.0", "https-browserify": "^1.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mongodb-memory-server": "^10.1.4", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8", "process": "^0.11.10", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^3.4.1", "text-encoding": "^0.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5", "url": "^0.11.4"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "packageManager": "npm@10.9.0"}