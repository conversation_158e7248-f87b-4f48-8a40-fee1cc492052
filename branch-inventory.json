[{"branchInventoryId": "6784d906b6183517c88f3c1e", "itemId": "676c5175da75210c99b129c8", "itemType": "RECIPE", "name": "Apple Tartlet-Batch46EA", "components": []}, {"branchInventoryId": "6784d906b6183517c88f3c1f", "itemId": "676c5175da75210c99b129c8", "itemType": "RECIPE", "name": "Apple Tartlet-Batch46EA", "components": []}, {"branchInventoryId": "6784d906b6183517c88f3c20", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784d906b6183517c88f3c21", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784d906b6183517c88f3c22", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784d906b6183517c88f3c23", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4380", "itemId": "676c5175da75210c99b129c8", "itemType": "RECIPE", "name": "Apple Tartlet-Batch46EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f437f", "itemId": "676c5175da75210c99b129c8", "itemType": "RECIPE", "name": "Apple Tartlet-Batch46EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f437e", "itemId": "676c5175da75210c99b129c8", "itemType": "RECIPE", "name": "Apple Tartlet-Batch46EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4381", "itemId": "676c5175da75210c99b129d3", "itemType": "RECIPE", "name": "Biscuit for coffee-Pack1Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4382", "itemId": "676c5175da75210c99b129d3", "itemType": "RECIPE", "name": "Biscuit for coffee-Pack1Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4395", "itemId": "676c5175da75210c99b12a55", "itemType": "RECIPE", "name": "Veggie Roll-Batch54EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4384", "itemId": "676c5175da75210c99b129d5", "itemType": "RECIPE", "name": "Burger Bun Sesame 70gr-Batch240EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4394", "itemId": "676c5175da75210c99b12a55", "itemType": "RECIPE", "name": "Veggie Roll-Batch54EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4397", "itemId": "676c5175da75210c99b12a5c", "itemType": "RECIPE", "name": "VP Sauce BBQ-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4386", "itemId": "676c5175da75210c99b129d5", "itemType": "RECIPE", "name": "Burger Bun Sesame 70gr-Batch240EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4388", "itemId": "676c5175da75210c99b129dd", "itemType": "RECIPE", "name": "Cake Pudding-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4389", "itemId": "676c5175da75210c99b129dd", "itemType": "RECIPE", "name": "Cake Pudding-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f438c", "itemId": "676c5175da75210c99b129e2", "itemType": "RECIPE", "name": "Campagne Bread 460gr-Box17", "components": []}, {"branchInventoryId": "6784f168b6183517c88f438f", "itemId": "676c5175da75210c99b12a0c", "itemType": "RECIPE", "name": "Frozen Chicken Wings-Pack12EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f438b", "itemId": "676c5175da75210c99b129e2", "itemType": "RECIPE", "name": "Campagne Bread 460gr-Box17", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4392", "itemId": "676c5175da75210c99b12a47", "itemType": "RECIPE", "name": "Sliced Milk Bread 600gr-Pack1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4391", "itemId": "676c5175da75210c99b12a47", "itemType": "RECIPE", "name": "Sliced Milk Bread 600gr-Pack1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f438d", "itemId": "676c5175da75210c99b12a0c", "itemType": "RECIPE", "name": "Frozen Chicken Wings-Pack12EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4390", "itemId": "676c5175da75210c99b12a47", "itemType": "RECIPE", "name": "Sliced Milk Bread 600gr-Pack1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4398", "itemId": "676c5175da75210c99b12a5c", "itemType": "RECIPE", "name": "VP Sauce BBQ-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4385", "itemId": "676c5175da75210c99b129d5", "itemType": "RECIPE", "name": "Burger Bun Sesame 70gr-Batch240EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f438a", "itemId": "676c5175da75210c99b129e2", "itemType": "RECIPE", "name": "Campagne Bread 460gr-Box17", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4387", "itemId": "676c5175da75210c99b129dd", "itemType": "RECIPE", "name": "Cake Pudding-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4393", "itemId": "676c5175da75210c99b12a55", "itemType": "RECIPE", "name": "Veggie Roll-Batch54EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4399", "itemId": "676c5175da75210c99b12a5d", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f438e", "itemId": "676c5175da75210c99b12a0c", "itemType": "RECIPE", "name": "Frozen Chicken Wings-Pack12EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4396", "itemId": "676c5175da75210c99b12a5c", "itemType": "RECIPE", "name": "VP Sauce BBQ-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4383", "itemId": "676c5175da75210c99b129d3", "itemType": "RECIPE", "name": "Biscuit for coffee-Pack1Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f439a", "itemId": "676c5175da75210c99b12a5d", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f439b", "itemId": "676c5175da75210c99b12a5d", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f439d", "itemId": "676c5175da75210c99b12a63", "itemType": "RECIPE", "name": "VP <PERSON>ato Sauce Pizza-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f439e", "itemId": "676c5175da75210c99b12a63", "itemType": "RECIPE", "name": "VP <PERSON>ato Sauce Pizza-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f439c", "itemId": "676c5175da75210c99b12a63", "itemType": "RECIPE", "name": "VP <PERSON>ato Sauce Pizza-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f439f", "itemId": "676c5175da75210c99b12a6b", "itemType": "RECIPE", "name": "Wholewheat Bread 400Gr-Box21", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a1", "itemId": "676c5175da75210c99b12a6b", "itemType": "RECIPE", "name": "Wholewheat Bread 400Gr-Box21", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a3", "itemId": "676c5175da75210c99b129d1", "itemType": "RECIPE", "name": "Bavarois Two Choco 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a0", "itemId": "676c5175da75210c99b12a6b", "itemType": "RECIPE", "name": "Wholewheat Bread 400Gr-Box21", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a2", "itemId": "676c5175da75210c99b129d1", "itemType": "RECIPE", "name": "Bavarois Two Choco 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a5", "itemId": "676c5175da75210c99b129e4", "itemType": "RECIPE", "name": "Cheese Pie-Batch76EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a6", "itemId": "676c5175da75210c99b129e4", "itemType": "RECIPE", "name": "Cheese Pie-Batch76EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a4", "itemId": "676c5175da75210c99b129d1", "itemType": "RECIPE", "name": "Bavarois Two Choco 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ac", "itemId": "676c5175da75210c99b129fb", "itemType": "RECIPE", "name": "Eclair Moka Caramel-Batch15 NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ad", "itemId": "676c5175da75210c99b129fb", "itemType": "RECIPE", "name": "Eclair Moka Caramel-Batch15 NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a8", "itemId": "676c5175da75210c99b129e9", "itemType": "RECIPE", "name": "Compound <PERSON> (cake writing)-Pack100gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ab", "itemId": "676c5175da75210c99b129fb", "itemType": "RECIPE", "name": "Eclair Moka Caramel-Batch15 NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a7", "itemId": "676c5175da75210c99b129e4", "itemType": "RECIPE", "name": "Cheese Pie-Batch76EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43aa", "itemId": "676c5175da75210c99b129e9", "itemType": "RECIPE", "name": "Compound <PERSON> (cake writing)-Pack100gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ae", "itemId": "676c5175da75210c99b129ff", "itemType": "RECIPE", "name": "Forest Cake Choco 10P-Batch3.5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43a9", "itemId": "676c5175da75210c99b129e9", "itemType": "RECIPE", "name": "Compound <PERSON> (cake writing)-Pack100gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43af", "itemId": "676c5175da75210c99b129ff", "itemType": "RECIPE", "name": "Forest Cake Choco 10P-Batch3.5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b0", "itemId": "676c5175da75210c99b129ff", "itemType": "RECIPE", "name": "Forest Cake Choco 10P-Batch3.5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b1", "itemId": "676c5175da75210c99b12a02", "itemType": "RECIPE", "name": "Fresh Rasped Carrots-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b2", "itemId": "676c5175da75210c99b12a02", "itemType": "RECIPE", "name": "Fresh Rasped Carrots-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b3", "itemId": "676c5175da75210c99b12a02", "itemType": "RECIPE", "name": "Fresh Rasped Carrots-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b4", "itemId": "676c5175da75210c99b12a23", "itemType": "RECIPE", "name": "Meat Roll 180gr-Batch62", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b5", "itemId": "676c5175da75210c99b12a23", "itemType": "RECIPE", "name": "Meat Roll 180gr-Batch62", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b6", "itemId": "676c5175da75210c99b12a23", "itemType": "RECIPE", "name": "Meat Roll 180gr-Batch62", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b7", "itemId": "676c5175da75210c99b12a24", "itemType": "RECIPE", "name": "Meat Roll 180gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b8", "itemId": "676c5175da75210c99b12a24", "itemType": "RECIPE", "name": "Meat Roll 180gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43b9", "itemId": "676c5175da75210c99b12a24", "itemType": "RECIPE", "name": "Meat Roll 180gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ba", "itemId": "676c5175da75210c99b12a25", "itemType": "RECIPE", "name": "MINI Cream Filled Donut 20gr-Batch25EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43bb", "itemId": "676c5175da75210c99b12a25", "itemType": "RECIPE", "name": "MINI Cream Filled Donut 20gr-Batch25EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43bc", "itemId": "676c5175da75210c99b12a25", "itemType": "RECIPE", "name": "MINI Cream Filled Donut 20gr-Batch25EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43bd", "itemId": "676c5175da75210c99b12ac4", "itemType": "RECIPE", "name": "WIP Ring Donut Plain-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c4", "itemId": "676c5175da75210c99b129fe", "itemType": "RECIPE", "name": "Energy Juice-Batch16.6L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c1", "itemId": "676c5175da75210c99b129d9", "itemType": "RECIPE", "name": "Cake Banana-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c3", "itemId": "676c5175da75210c99b129fe", "itemType": "RECIPE", "name": "Energy Juice-Batch16.6L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43bf", "itemId": "676c5175da75210c99b12ac4", "itemType": "RECIPE", "name": "WIP Ring Donut Plain-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c2", "itemId": "676c5175da75210c99b129d9", "itemType": "RECIPE", "name": "Cake Banana-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43be", "itemId": "676c5175da75210c99b12ac4", "itemType": "RECIPE", "name": "WIP Ring Donut Plain-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c0", "itemId": "676c5175da75210c99b129d9", "itemType": "RECIPE", "name": "Cake Banana-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c5", "itemId": "676c5175da75210c99b129fe", "itemType": "RECIPE", "name": "Energy Juice-Batch16.6L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c6", "itemId": "676c5175da75210c99b12a08", "itemType": "RECIPE", "name": "Frozen Beef Patty-Pack5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c7", "itemId": "676c5175da75210c99b12a08", "itemType": "RECIPE", "name": "Frozen Beef Patty-Pack5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c8", "itemId": "676c5175da75210c99b12a08", "itemType": "RECIPE", "name": "Frozen Beef Patty-Pack5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43c9", "itemId": "676c5175da75210c99b12a10", "itemType": "RECIPE", "name": "Frozen Ham Sliced-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ca", "itemId": "676c5175da75210c99b12a10", "itemType": "RECIPE", "name": "Frozen Ham Sliced-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43cb", "itemId": "676c5175da75210c99b12a10", "itemType": "RECIPE", "name": "Frozen Ham Sliced-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43cc", "itemId": "676c5175da75210c99b12a18", "itemType": "RECIPE", "name": "<PERSON>ice-Batch16.8L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43cd", "itemId": "676c5175da75210c99b12a18", "itemType": "RECIPE", "name": "<PERSON>ice-Batch16.8L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ce", "itemId": "676c5175da75210c99b12a18", "itemType": "RECIPE", "name": "<PERSON>ice-Batch16.8L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43cf", "itemId": "676c5175da75210c99b12a2a", "itemType": "RECIPE", "name": "MINI Donut White-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d3", "itemId": "676c5175da75210c99b12a31", "itemType": "RECIPE", "name": "Panini Bread 150gr-Batch320EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d4", "itemId": "676c5175da75210c99b12a31", "itemType": "RECIPE", "name": "Panini Bread 150gr-Batch320EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d2", "itemId": "676c5175da75210c99b12a31", "itemType": "RECIPE", "name": "Panini Bread 150gr-Batch320EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d0", "itemId": "676c5175da75210c99b12a2a", "itemType": "RECIPE", "name": "MINI Donut White-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d1", "itemId": "676c5175da75210c99b12a2a", "itemType": "RECIPE", "name": "MINI Donut White-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d6", "itemId": "676c5175da75210c99b12a43", "itemType": "RECIPE", "name": "Raisin Whirl Classic 100gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d9", "itemId": "676c5175da75210c99b12a53", "itemType": "RECIPE", "name": "Tortilla-Box200", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d5", "itemId": "676c5175da75210c99b12a43", "itemType": "RECIPE", "name": "Raisin Whirl Classic 100gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d8", "itemId": "676c5175da75210c99b12a53", "itemType": "RECIPE", "name": "Tortilla-Box200", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43d7", "itemId": "676c5175da75210c99b12a43", "itemType": "RECIPE", "name": "Raisin Whirl Classic 100gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43da", "itemId": "676c5175da75210c99b12a53", "itemType": "RECIPE", "name": "Tortilla-Box200", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43db", "itemId": "676c5175da75210c99b12a56", "itemType": "RECIPE", "name": "VP Caramelized Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43dc", "itemId": "676c5175da75210c99b12a56", "itemType": "RECIPE", "name": "VP Caramelized Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43dd", "itemId": "676c5175da75210c99b12a56", "itemType": "RECIPE", "name": "VP Caramelized Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43de", "itemId": "676c5175da75210c99b12a60", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43df", "itemId": "676c5175da75210c99b12a60", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e0", "itemId": "676c5175da75210c99b12a60", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e3", "itemId": "676c5175da75210c99b129cd", "itemType": "RECIPE", "name": "<PERSON><PERSON><PERSON> 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e4", "itemId": "676c5175da75210c99b129df", "itemType": "RECIPE", "name": "Cake Zebra-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e1", "itemId": "676c5175da75210c99b129cd", "itemType": "RECIPE", "name": "<PERSON><PERSON><PERSON> 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e2", "itemId": "676c5175da75210c99b129cd", "itemType": "RECIPE", "name": "<PERSON><PERSON><PERSON> 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e9", "itemId": "676c5175da75210c99b129e8", "itemType": "RECIPE", "name": "Christmas Cake 17cm-Batch204cm (4x51cm)-Batch12EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e7", "itemId": "676c5175da75210c99b129e8", "itemType": "RECIPE", "name": "Christmas Cake 17cm-Batch204cm (4x51cm)-Batch12EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e5", "itemId": "676c5175da75210c99b129df", "itemType": "RECIPE", "name": "Cake Zebra-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e8", "itemId": "676c5175da75210c99b129e8", "itemType": "RECIPE", "name": "Christmas Cake 17cm-Batch204cm (4x51cm)-Batch12EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43e6", "itemId": "676c5175da75210c99b129df", "itemType": "RECIPE", "name": "Cake Zebra-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ea", "itemId": "676c5175da75210c99b12a00", "itemType": "RECIPE", "name": "Forest Cake Strawberry 10P-Batch3.5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43eb", "itemId": "676c5175da75210c99b12a00", "itemType": "RECIPE", "name": "Forest Cake Strawberry 10P-Batch3.5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ec", "itemId": "676c5175da75210c99b12a00", "itemType": "RECIPE", "name": "Forest Cake Strawberry 10P-Batch3.5EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ed", "itemId": "676c5175da75210c99b12a07", "itemType": "RECIPE", "name": "Frozen Basmati Rice-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ee", "itemId": "676c5175da75210c99b12a07", "itemType": "RECIPE", "name": "Frozen Basmati Rice-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ef", "itemId": "676c5175da75210c99b12a07", "itemType": "RECIPE", "name": "Frozen Basmati Rice-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f0", "itemId": "676c5175da75210c99b12a0a", "itemType": "RECIPE", "name": "Frozen Chicken Breast Cubes-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f1", "itemId": "676c5175da75210c99b12a0a", "itemType": "RECIPE", "name": "Frozen Chicken Breast Cubes-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f2", "itemId": "676c5175da75210c99b12a0a", "itemType": "RECIPE", "name": "Frozen Chicken Breast Cubes-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f3", "itemId": "676c5175da75210c99b12a0f", "itemType": "RECIPE", "name": "Frozen Half Quiche Spinach Feta-Batch16EA (Half)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f4", "itemId": "676c5175da75210c99b12a0f", "itemType": "RECIPE", "name": "Frozen Half Quiche Spinach Feta-Batch16EA (Half)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f5", "itemId": "676c5175da75210c99b12a0f", "itemType": "RECIPE", "name": "Frozen Half Quiche Spinach Feta-Batch16EA (Half)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f6", "itemId": "676c5175da75210c99b12a14", "itemType": "RECIPE", "name": "Frozen <PERSON>-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f8", "itemId": "676c5175da75210c99b12a14", "itemType": "RECIPE", "name": "Frozen <PERSON>-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f7", "itemId": "676c5175da75210c99b12a14", "itemType": "RECIPE", "name": "Frozen <PERSON>-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43f9", "itemId": "676c5175da75210c99b12a1f", "itemType": "RECIPE", "name": "Ice Cube-Pack1.2Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43fa", "itemId": "676c5175da75210c99b12a1f", "itemType": "RECIPE", "name": "Ice Cube-Pack1.2Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43fb", "itemId": "676c5175da75210c99b12a1f", "itemType": "RECIPE", "name": "Ice Cube-Pack1.2Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43fc", "itemId": "676c5175da75210c99b12a30", "itemType": "RECIPE", "name": "Pain Chocolat Classic 90gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43fd", "itemId": "676c5175da75210c99b12a30", "itemType": "RECIPE", "name": "Pain Chocolat Classic 90gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4400", "itemId": "676c5175da75210c99b12a5e", "itemType": "RECIPE", "name": "VP Sauce Chicken Wings-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43ff", "itemId": "676c5175da75210c99b12a5e", "itemType": "RECIPE", "name": "VP Sauce Chicken Wings-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f43fe", "itemId": "676c5175da75210c99b12a30", "itemType": "RECIPE", "name": "Pain Chocolat Classic 90gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4402", "itemId": "676c5175da75210c99b12a98", "itemType": "RECIPE", "name": "WIP Fresh Natural Yoghurt-Batch6L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4401", "itemId": "676c5175da75210c99b12a5e", "itemType": "RECIPE", "name": "VP Sauce Chicken Wings-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4403", "itemId": "676c5175da75210c99b12a98", "itemType": "RECIPE", "name": "WIP Fresh Natural Yoghurt-Batch6L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4404", "itemId": "676c5175da75210c99b12a98", "itemType": "RECIPE", "name": "WIP Fresh Natural Yoghurt-Batch6L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4406", "itemId": "676c5175da75210c99b129db", "itemType": "RECIPE", "name": "Cake Lemon-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4405", "itemId": "676c5175da75210c99b129db", "itemType": "RECIPE", "name": "Cake Lemon-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4407", "itemId": "676c5175da75210c99b129db", "itemType": "RECIPE", "name": "Cake Lemon-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f440c", "itemId": "676c5175da75210c99b12a1b", "itemType": "RECIPE", "name": "Glaze White Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4409", "itemId": "676c5175da75210c99b129f0", "itemType": "RECIPE", "name": "Croissant Premium 80gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f440a", "itemId": "676c5175da75210c99b129f0", "itemType": "RECIPE", "name": "Croissant Premium 80gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f440b", "itemId": "676c5175da75210c99b12a1b", "itemType": "RECIPE", "name": "Glaze White Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4408", "itemId": "676c5175da75210c99b129f0", "itemType": "RECIPE", "name": "Croissant Premium 80gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4412", "itemId": "676c5175da75210c99b12a50", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Pack10", "components": []}, {"branchInventoryId": "6784f168b6183517c88f440e", "itemId": "676c5175da75210c99b12a22", "itemType": "RECIPE", "name": "Madeleine-Pack8", "components": []}, {"branchInventoryId": "6784f168b6183517c88f440d", "itemId": "676c5175da75210c99b12a1b", "itemType": "RECIPE", "name": "Glaze White Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f440f", "itemId": "676c5175da75210c99b12a22", "itemType": "RECIPE", "name": "Madeleine-Pack8", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4410", "itemId": "676c5175da75210c99b12a22", "itemType": "RECIPE", "name": "Madeleine-Pack8", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4411", "itemId": "676c5175da75210c99b12a50", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Pack10", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4413", "itemId": "676c5175da75210c99b12a50", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Pack10", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4414", "itemId": "676c5175da75210c99b12a54", "itemType": "RECIPE", "name": "Veggie Roll 120Gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4415", "itemId": "676c5175da75210c99b12a54", "itemType": "RECIPE", "name": "Veggie Roll 120Gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4416", "itemId": "676c5175da75210c99b12a54", "itemType": "RECIPE", "name": "Veggie Roll 120Gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4418", "itemId": "676c5175da75210c99b12aa2", "itemType": "RECIPE", "name": "WIP Ice Cream Chocolate-Batch17.9Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4417", "itemId": "676c5175da75210c99b12aa2", "itemType": "RECIPE", "name": "WIP Ice Cream Chocolate-Batch17.9Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f441c", "itemId": "676c5175da75210c99b129c9", "itemType": "RECIPE", "name": "Apple Tartlet-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4419", "itemId": "676c5175da75210c99b12aa2", "itemType": "RECIPE", "name": "WIP Ice Cream Chocolate-Batch17.9Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f441b", "itemId": "676c5175da75210c99b129c9", "itemType": "RECIPE", "name": "Apple Tartlet-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f441a", "itemId": "676c5175da75210c99b129c9", "itemType": "RECIPE", "name": "Apple Tartlet-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f441d", "itemId": "676c5175da75210c99b129d2", "itemType": "RECIPE", "name": "Bavarois Two Choco 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f441e", "itemId": "676c5175da75210c99b129d2", "itemType": "RECIPE", "name": "Bavarois Two Choco 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f441f", "itemId": "676c5175da75210c99b129d2", "itemType": "RECIPE", "name": "Bavarois Two Choco 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4438", "itemId": "676c5175da75210c99b129e5", "itemType": "RECIPE", "name": "Cheese Pie-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f444e", "itemId": "676c5175da75210c99b12a26", "itemType": "RECIPE", "name": "MINI Donut Chocolate Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4469", "itemId": "676c5175da75210c99b12a1d", "itemType": "RECIPE", "name": "Ice Cream Tub Choco 16oz", "components": []}, {"branchInventoryId": "6784f168b6183517c88f446a", "itemId": "676c5175da75210c99b12a1d", "itemType": "RECIPE", "name": "Ice Cream Tub Choco 16oz", "components": []}, {"branchInventoryId": "6784f168b6183517c88f443d", "itemId": "676c5175da75210c99b129e7", "itemType": "RECIPE", "name": "Chicken Roll-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4441", "itemId": "676c5175da75210c99b129f2", "itemType": "RECIPE", "name": "Dark MultiCereals Bread 500gr-Box15", "components": []}, {"branchInventoryId": "6784f168b6183517c88f444b", "itemId": "676c5175da75210c99b12a21", "itemType": "RECIPE", "name": "Madeleine-Box200", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4462", "itemId": "676c5175da75210c99b12a12", "itemType": "RECIPE", "name": "Frozen Meatballs Pizza-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f442d", "itemId": "676c5175da75210c99b12a32", "itemType": "RECIPE", "name": "Panini Bread 150gr-Box50", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4463", "itemId": "676c5175da75210c99b12a12", "itemType": "RECIPE", "name": "Frozen Meatballs Pizza-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4426", "itemId": "676c5175da75210c99b12a27", "itemType": "RECIPE", "name": "MINI Donut Chocolate-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4427", "itemId": "676c5175da75210c99b12a27", "itemType": "RECIPE", "name": "MINI Donut Chocolate-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f442c", "itemId": "676c5175da75210c99b12a32", "itemType": "RECIPE", "name": "Panini Bread 150gr-Box50", "components": []}, {"branchInventoryId": "6784f168b6183517c88f442f", "itemId": "676c5175da75210c99b12a4d", "itemType": "RECIPE", "name": "Strawberry Tart 8P-Batch8EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4436", "itemId": "676c5175da75210c99b129d4", "itemType": "RECIPE", "name": "Bonbon Juice-Batch20L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4429", "itemId": "676c5175da75210c99b12a28", "itemType": "RECIPE", "name": "MINI Donut Pink Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4442", "itemId": "676c5175da75210c99b129f2", "itemType": "RECIPE", "name": "Dark MultiCereals Bread 500gr-Box15", "components": []}, {"branchInventoryId": "6784f168b6183517c88f444d", "itemId": "676c5175da75210c99b12a26", "itemType": "RECIPE", "name": "MINI Donut Chocolate Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4461", "itemId": "676c5175da75210c99b12a05", "itemType": "RECIPE", "name": "Fresh Sliced White Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f442b", "itemId": "676c5175da75210c99b12a28", "itemType": "RECIPE", "name": "MINI Donut Pink Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4437", "itemId": "676c5175da75210c99b129d4", "itemType": "RECIPE", "name": "Bonbon Juice-Batch20L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f443a", "itemId": "676c5175da75210c99b129e5", "itemType": "RECIPE", "name": "Cheese Pie-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f444a", "itemId": "676c5175da75210c99b12a21", "itemType": "RECIPE", "name": "Madeleine-Box200", "components": []}, {"branchInventoryId": "6784f168b6183517c88f445c", "itemId": "676c5175da75210c99b129f7", "itemType": "RECIPE", "name": "Donut Plain Sugar-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4467", "itemId": "676c5175da75210c99b12a16", "itemType": "RECIPE", "name": "Frozen Pulled Chicken-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f442a", "itemId": "676c5175da75210c99b12a28", "itemType": "RECIPE", "name": "MINI Donut Pink Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4466", "itemId": "676c5175da75210c99b12a16", "itemType": "RECIPE", "name": "Frozen Pulled Chicken-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f446d", "itemId": "676c5175da75210c99b12a2d", "itemType": "RECIPE", "name": "Muffin Plain 90gr-Batch150EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4424", "itemId": "676c5175da75210c99b12a20", "itemType": "RECIPE", "name": "Madeleine-Batch560EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f443b", "itemId": "676c5175da75210c99b129e7", "itemType": "RECIPE", "name": "Chicken Roll-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f443c", "itemId": "676c5175da75210c99b129e7", "itemType": "RECIPE", "name": "Chicken Roll-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f443e", "itemId": "676c5175da75210c99b129ed", "itemType": "RECIPE", "name": "Croissant Classic 90gr-Batch84EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f443f", "itemId": "676c5175da75210c99b129ed", "itemType": "RECIPE", "name": "Croissant Classic 90gr-Batch84EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4445", "itemId": "676c5175da75210c99b12a15", "itemType": "RECIPE", "name": "Frozen Pulled Beef-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4446", "itemId": "676c5175da75210c99b12a15", "itemType": "RECIPE", "name": "Frozen Pulled Beef-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4453", "itemId": "676c5175da75210c99b12a52", "itemType": "RECIPE", "name": "Tortilla 110gr-Batch170EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4459", "itemId": "676c5175da75210c99b129f5", "itemType": "RECIPE", "name": "Donut Chocolate-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f445d", "itemId": "676c5175da75210c99b129f7", "itemType": "RECIPE", "name": "Donut Plain Sugar-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f446b", "itemId": "676c5175da75210c99b12a2d", "itemType": "RECIPE", "name": "Muffin Plain 90gr-Batch150EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4430", "itemId": "676c5175da75210c99b12a4d", "itemType": "RECIPE", "name": "Strawberry Tart 8P-Batch8EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4455", "itemId": "676c5175da75210c99b12a52", "itemType": "RECIPE", "name": "Tortilla 110gr-Batch170EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f445a", "itemId": "676c5175da75210c99b129f5", "itemType": "RECIPE", "name": "Donut Chocolate-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f445b", "itemId": "676c5175da75210c99b129f5", "itemType": "RECIPE", "name": "Donut Chocolate-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4464", "itemId": "676c5175da75210c99b12a12", "itemType": "RECIPE", "name": "Frozen Meatballs Pizza-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4431", "itemId": "676c5175da75210c99b12a4d", "itemType": "RECIPE", "name": "Strawberry Tart 8P-Batch8EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4440", "itemId": "676c5175da75210c99b129ed", "itemType": "RECIPE", "name": "Croissant Classic 90gr-Batch84EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4451", "itemId": "676c5175da75210c99b12a48", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Batch72EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4460", "itemId": "676c5175da75210c99b12a05", "itemType": "RECIPE", "name": "Fresh Sliced White Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4465", "itemId": "676c5175da75210c99b12a16", "itemType": "RECIPE", "name": "Frozen Pulled Chicken-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4421", "itemId": "676c5175da75210c99b12a0e", "itemType": "RECIPE", "name": "Frozen Half Quiche Chicken Mushroom-Batch16EA (Half)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f442e", "itemId": "676c5175da75210c99b12a32", "itemType": "RECIPE", "name": "Panini Bread 150gr-Box50", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4439", "itemId": "676c5175da75210c99b129e5", "itemType": "RECIPE", "name": "Cheese Pie-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4447", "itemId": "676c5175da75210c99b12a17", "itemType": "RECIPE", "name": "Frozen Vegan Patty 100gr-Pack5EA NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4454", "itemId": "676c5175da75210c99b12a52", "itemType": "RECIPE", "name": "Tortilla 110gr-Batch170EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4456", "itemId": "676c5175da75210c99b12a5a", "itemType": "RECIPE", "name": "VP Pizza Dough 2750gr-Batch10EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4457", "itemId": "676c5175da75210c99b12a5a", "itemType": "RECIPE", "name": "VP Pizza Dough 2750gr-Batch10EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f445f", "itemId": "676c5175da75210c99b12a05", "itemType": "RECIPE", "name": "Fresh Sliced White Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4428", "itemId": "676c5175da75210c99b12a27", "itemType": "RECIPE", "name": "MINI Donut Chocolate-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4433", "itemId": "676c5175da75210c99b129cc", "itemType": "RECIPE", "name": "Bavarois <PERSON> 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f444c", "itemId": "676c5175da75210c99b12a21", "itemType": "RECIPE", "name": "Madeleine-Box200", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4425", "itemId": "676c5175da75210c99b12a20", "itemType": "RECIPE", "name": "Madeleine-Batch560EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4450", "itemId": "676c5175da75210c99b12a48", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Batch72EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4458", "itemId": "676c5175da75210c99b12a5a", "itemType": "RECIPE", "name": "VP Pizza Dough 2750gr-Batch10EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4422", "itemId": "676c5175da75210c99b12a0e", "itemType": "RECIPE", "name": "Frozen Half Quiche Chicken Mushroom-Batch16EA (Half)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4423", "itemId": "676c5175da75210c99b12a20", "itemType": "RECIPE", "name": "Madeleine-Batch560EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4443", "itemId": "676c5175da75210c99b129f2", "itemType": "RECIPE", "name": "Dark MultiCereals Bread 500gr-Box15", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4452", "itemId": "676c5175da75210c99b12a48", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Batch72EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f445e", "itemId": "676c5175da75210c99b129f7", "itemType": "RECIPE", "name": "Donut Plain Sugar-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f446e", "itemId": "676c5175da75210c99b12a2e", "itemType": "RECIPE", "name": "Muffin Plain Dough-Bucket5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4420", "itemId": "676c5175da75210c99b12a0e", "itemType": "RECIPE", "name": "Frozen Half Quiche Chicken Mushroom-Batch16EA (Half)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4432", "itemId": "676c5175da75210c99b129cc", "itemType": "RECIPE", "name": "Bavarois <PERSON> 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4434", "itemId": "676c5175da75210c99b129cc", "itemType": "RECIPE", "name": "Bavarois <PERSON> 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4435", "itemId": "676c5175da75210c99b129d4", "itemType": "RECIPE", "name": "Bonbon Juice-Batch20L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4444", "itemId": "676c5175da75210c99b12a15", "itemType": "RECIPE", "name": "Frozen Pulled Beef-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4448", "itemId": "676c5175da75210c99b12a17", "itemType": "RECIPE", "name": "Frozen Vegan Patty 100gr-Pack5EA NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4449", "itemId": "676c5175da75210c99b12a17", "itemType": "RECIPE", "name": "Frozen Vegan Patty 100gr-Pack5EA NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f444f", "itemId": "676c5175da75210c99b12a26", "itemType": "RECIPE", "name": "MINI Donut Chocolate Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4468", "itemId": "676c5175da75210c99b12a1d", "itemType": "RECIPE", "name": "Ice Cream Tub Choco 16oz", "components": []}, {"branchInventoryId": "6784f168b6183517c88f446c", "itemId": "676c5175da75210c99b12a2d", "itemType": "RECIPE", "name": "Muffin Plain 90gr-Batch150EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4470", "itemId": "676c5175da75210c99b12a2e", "itemType": "RECIPE", "name": "Muffin Plain Dough-Bucket5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f446f", "itemId": "676c5175da75210c99b12a2e", "itemType": "RECIPE", "name": "Muffin Plain Dough-Bucket5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4471", "itemId": "676c5175da75210c99b12a4b", "itemType": "RECIPE", "name": "St Valentine Cake 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4473", "itemId": "676c5175da75210c99b12a4b", "itemType": "RECIPE", "name": "St Valentine Cake 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4472", "itemId": "676c5175da75210c99b12a4b", "itemType": "RECIPE", "name": "St Valentine Cake 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4475", "itemId": "676c5175da75210c99b12a65", "itemType": "RECIPE", "name": "Wheat Tourte Bread 800gr-Box7", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4474", "itemId": "676c5175da75210c99b12a65", "itemType": "RECIPE", "name": "Wheat Tourte Bread 800gr-Box7", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4476", "itemId": "676c5175da75210c99b12a65", "itemType": "RECIPE", "name": "Wheat Tourte Bread 800gr-Box7", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4477", "itemId": "676c5175da75210c99b12a66", "itemType": "RECIPE", "name": "White Baguette 250gr-Batch160EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4478", "itemId": "676c5175da75210c99b12a66", "itemType": "RECIPE", "name": "White Baguette 250gr-Batch160EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4479", "itemId": "676c5175da75210c99b12a66", "itemType": "RECIPE", "name": "White Baguette 250gr-Batch160EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f447b", "itemId": "676c5175da75210c99b129cb", "itemType": "RECIPE", "name": "Bavarois Mango Passion 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f447c", "itemId": "676c5175da75210c99b129cb", "itemType": "RECIPE", "name": "Bavarois Mango Passion 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f447a", "itemId": "676c5175da75210c99b129cb", "itemType": "RECIPE", "name": "Bavarois Mango Passion 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f447f", "itemId": "676c5175da75210c99b12a1a", "itemType": "RECIPE", "name": "Glaze Pink Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f447e", "itemId": "676c5175da75210c99b12a1a", "itemType": "RECIPE", "name": "Glaze Pink Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4480", "itemId": "676c5175da75210c99b12a35", "itemType": "RECIPE", "name": "Parisian Baguette-Box50", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4481", "itemId": "676c5175da75210c99b12a35", "itemType": "RECIPE", "name": "Parisian Baguette-Box50", "components": []}, {"branchInventoryId": "6784f168b6183517c88f447d", "itemId": "676c5175da75210c99b12a1a", "itemType": "RECIPE", "name": "Glaze Pink Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4482", "itemId": "676c5175da75210c99b12a35", "itemType": "RECIPE", "name": "Parisian Baguette-Box50", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4483", "itemId": "676c5175da75210c99b12a42", "itemType": "RECIPE", "name": "Raisin Whirl Classic 100gr-Batch94EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4484", "itemId": "676c5175da75210c99b12a42", "itemType": "RECIPE", "name": "Raisin Whirl Classic 100gr-Batch94EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4485", "itemId": "676c5175da75210c99b12a42", "itemType": "RECIPE", "name": "Raisin Whirl Classic 100gr-Batch94EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4486", "itemId": "676c5175da75210c99b12a5b", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4487", "itemId": "676c5175da75210c99b12a5b", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4488", "itemId": "676c5175da75210c99b12a5b", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4489", "itemId": "676c5175da75210c99b129e0", "itemType": "RECIPE", "name": "Cake Zebra-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f448a", "itemId": "676c5175da75210c99b129e0", "itemType": "RECIPE", "name": "Cake Zebra-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f448b", "itemId": "676c5175da75210c99b129e0", "itemType": "RECIPE", "name": "Cake Zebra-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f448c", "itemId": "676c5175da75210c99b129f1", "itemType": "RECIPE", "name": "Dark MultiCereals Bread 500gr-Batch92EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f448d", "itemId": "676c5175da75210c99b129f1", "itemType": "RECIPE", "name": "Dark MultiCereals Bread 500gr-Batch92EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f448e", "itemId": "676c5175da75210c99b129f1", "itemType": "RECIPE", "name": "Dark MultiCereals Bread 500gr-Batch92EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4490", "itemId": "676c5175da75210c99b12a01", "itemType": "RECIPE", "name": "Fresh Cabbage Salad-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4491", "itemId": "676c5175da75210c99b12a01", "itemType": "RECIPE", "name": "Fresh Cabbage Salad-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4492", "itemId": "676c5175da75210c99b12a06", "itemType": "RECIPE", "name": "Fresh Washed Lettuce-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f448f", "itemId": "676c5175da75210c99b12a01", "itemType": "RECIPE", "name": "Fresh Cabbage Salad-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4493", "itemId": "676c5175da75210c99b12a06", "itemType": "RECIPE", "name": "Fresh Washed Lettuce-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4494", "itemId": "676c5175da75210c99b12a06", "itemType": "RECIPE", "name": "Fresh Washed Lettuce-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4495", "itemId": "676c5175da75210c99b12a2f", "itemType": "RECIPE", "name": "Pain Chocolat Classic 90gr-Batch110", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4498", "itemId": "676c5175da75210c99b129ca", "itemType": "RECIPE", "name": "Bavarois Mango Passion 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4496", "itemId": "676c5175da75210c99b12a2f", "itemType": "RECIPE", "name": "Pain Chocolat Classic 90gr-Batch110", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4497", "itemId": "676c5175da75210c99b12a2f", "itemType": "RECIPE", "name": "Pain Chocolat Classic 90gr-Batch110", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4499", "itemId": "676c5175da75210c99b129ca", "itemType": "RECIPE", "name": "Bavarois Mango Passion 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f449a", "itemId": "676c5175da75210c99b129ca", "itemType": "RECIPE", "name": "Bavarois Mango Passion 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f449b", "itemId": "676c5175da75210c99b129e6", "itemType": "RECIPE", "name": "Chicken Roll-Batch47EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f449c", "itemId": "676c5175da75210c99b129e6", "itemType": "RECIPE", "name": "Chicken Roll-Batch47EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f449f", "itemId": "676c5175da75210c99b129ea", "itemType": "RECIPE", "name": "Compound <PERSON> Choco (cake writing)-Pack100gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f449e", "itemId": "676c5175da75210c99b129ea", "itemType": "RECIPE", "name": "Compound <PERSON> Choco (cake writing)-Pack100gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a0", "itemId": "676c5175da75210c99b129ea", "itemType": "RECIPE", "name": "Compound <PERSON> Choco (cake writing)-Pack100gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f449d", "itemId": "676c5175da75210c99b129e6", "itemType": "RECIPE", "name": "Chicken Roll-Batch47EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a2", "itemId": "676c5175da75210c99b129eb", "itemType": "RECIPE", "name": "Copy of Cream Filled Donut-Batch25EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a1", "itemId": "676c5175da75210c99b129eb", "itemType": "RECIPE", "name": "Copy of Cream Filled Donut-Batch25EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a5", "itemId": "676c5175da75210c99b12a09", "itemType": "RECIPE", "name": "Frozen Bolognese Sauce-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a3", "itemId": "676c5175da75210c99b129eb", "itemType": "RECIPE", "name": "Copy of Cream Filled Donut-Batch25EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a4", "itemId": "676c5175da75210c99b12a09", "itemType": "RECIPE", "name": "Frozen Bolognese Sauce-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a6", "itemId": "676c5175da75210c99b12a09", "itemType": "RECIPE", "name": "Frozen Bolognese Sauce-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a7", "itemId": "676c5175da75210c99b12a1e", "itemType": "RECIPE", "name": "Ice Cream Tub Vanilla 16oz", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a8", "itemId": "676c5175da75210c99b12a1e", "itemType": "RECIPE", "name": "Ice Cream Tub Vanilla 16oz", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44aa", "itemId": "676c5175da75210c99b12a29", "itemType": "RECIPE", "name": "MINI Donut Plain Sugar-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44a9", "itemId": "676c5175da75210c99b12a1e", "itemType": "RECIPE", "name": "Ice Cream Tub Vanilla 16oz", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ac", "itemId": "676c5175da75210c99b12a29", "itemType": "RECIPE", "name": "MINI Donut Plain Sugar-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ad", "itemId": "676c5175da75210c99b12a46", "itemType": "RECIPE", "name": "Sliced Milk Bread 600gr-Box19EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ab", "itemId": "676c5175da75210c99b12a29", "itemType": "RECIPE", "name": "MINI Donut Plain Sugar-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ae", "itemId": "676c5175da75210c99b12a46", "itemType": "RECIPE", "name": "Sliced Milk Bread 600gr-Box19EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44af", "itemId": "676c5175da75210c99b12a46", "itemType": "RECIPE", "name": "Sliced Milk Bread 600gr-Box19EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b2", "itemId": "676c5175da75210c99b12a4a", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b1", "itemId": "676c5175da75210c99b12a4a", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b3", "itemId": "676c5175da75210c99b12a4e", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Batch480EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b0", "itemId": "676c5175da75210c99b12a4a", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b4", "itemId": "676c5175da75210c99b12a4e", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Batch480EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b5", "itemId": "676c5175da75210c99b12a4e", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Batch480EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b6", "itemId": "676c5175da75210c99b12a5f", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b7", "itemId": "676c5175da75210c99b12a5f", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b8", "itemId": "676c5175da75210c99b12a5f", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44b9", "itemId": "676c5175da75210c99b12a61", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ba", "itemId": "676c5175da75210c99b12a61", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44bb", "itemId": "676c5175da75210c99b12a61", "itemType": "RECIPE", "name": "VP <PERSON><PERSON>-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44bc", "itemId": "676c5175da75210c99b12a62", "itemType": "RECIPE", "name": "VP Sliced Pepperoni-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44bd", "itemId": "676c5175da75210c99b12a62", "itemType": "RECIPE", "name": "VP Sliced Pepperoni-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44be", "itemId": "676c5175da75210c99b12a62", "itemType": "RECIPE", "name": "VP Sliced Pepperoni-Pack250gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44bf", "itemId": "676c5175da75210c99b12aa3", "itemType": "RECIPE", "name": "WIP Ice Cream Vanilla-Batch17.5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c1", "itemId": "676c5175da75210c99b12aa3", "itemType": "RECIPE", "name": "WIP Ice Cream Vanilla-Batch17.5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c0", "itemId": "676c5175da75210c99b12aa3", "itemType": "RECIPE", "name": "WIP Ice Cream Vanilla-Batch17.5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c2", "itemId": "676c5175da75210c99b129cf", "itemType": "RECIPE", "name": "<PERSON><PERSON><PERSON> 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c3", "itemId": "676c5175da75210c99b129cf", "itemType": "RECIPE", "name": "<PERSON><PERSON><PERSON> 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c4", "itemId": "676c5175da75210c99b129cf", "itemType": "RECIPE", "name": "<PERSON><PERSON><PERSON> 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c5", "itemId": "676c5175da75210c99b129fa", "itemType": "RECIPE", "name": "Eclair Chocolate-Batch25", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c6", "itemId": "676c5175da75210c99b129fa", "itemType": "RECIPE", "name": "Eclair Chocolate-Batch25", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c7", "itemId": "676c5175da75210c99b129fa", "itemType": "RECIPE", "name": "Eclair Chocolate-Batch25", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c8", "itemId": "676c5175da75210c99b129fc", "itemType": "RECIPE", "name": "Eclair Moka <PERSON>mel-Batch25", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44c9", "itemId": "676c5175da75210c99b129fc", "itemType": "RECIPE", "name": "Eclair Moka <PERSON>mel-Batch25", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ca", "itemId": "676c5175da75210c99b129fc", "itemType": "RECIPE", "name": "Eclair Moka <PERSON>mel-Batch25", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44cb", "itemId": "676c5175da75210c99b12a0b", "itemType": "RECIPE", "name": "Frozen Chicken Maffe-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44cc", "itemId": "676c5175da75210c99b12a0b", "itemType": "RECIPE", "name": "Frozen Chicken Maffe-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ce", "itemId": "676c5175da75210c99b12a0d", "itemType": "RECIPE", "name": "Frozen Falafel-Pack20EA (New)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44cd", "itemId": "676c5175da75210c99b12a0b", "itemType": "RECIPE", "name": "Frozen Chicken Maffe-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44cf", "itemId": "676c5175da75210c99b12a0d", "itemType": "RECIPE", "name": "Frozen Falafel-Pack20EA (New)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d0", "itemId": "676c5175da75210c99b12a0d", "itemType": "RECIPE", "name": "Frozen Falafel-Pack20EA (New)", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d1", "itemId": "676c5175da75210c99b12a2b", "itemType": "RECIPE", "name": "Muffin Choco 90gr-Batch162EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d2", "itemId": "676c5175da75210c99b12a2b", "itemType": "RECIPE", "name": "Muffin Choco 90gr-Batch162EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d3", "itemId": "676c5175da75210c99b12a2b", "itemType": "RECIPE", "name": "Muffin Choco 90gr-Batch162EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d4", "itemId": "676c5175da75210c99b12a2c", "itemType": "RECIPE", "name": "Muffin Chocolate Dough-Bucket5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d5", "itemId": "676c5175da75210c99b12a2c", "itemType": "RECIPE", "name": "Muffin Chocolate Dough-Bucket5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d6", "itemId": "676c5175da75210c99b12a2c", "itemType": "RECIPE", "name": "Muffin Chocolate Dough-Bucket5Kg", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d7", "itemId": "676c5175da75210c99b12a4f", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d8", "itemId": "676c5175da75210c99b12a4f", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44d9", "itemId": "676c5175da75210c99b12a4f", "itemType": "RECIPE", "name": "Superbread Sandwich 60gr-Box80", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44da", "itemId": "676c5175da75210c99b12a59", "itemType": "RECIPE", "name": "VP Green Pesto-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44db", "itemId": "676c5175da75210c99b12a59", "itemType": "RECIPE", "name": "VP Green Pesto-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44de", "itemId": "676c5175da75210c99b129d7", "itemType": "RECIPE", "name": "<PERSON><PERSON>-Batch64EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44dc", "itemId": "676c5175da75210c99b12a59", "itemType": "RECIPE", "name": "VP Green Pesto-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44dd", "itemId": "676c5175da75210c99b129d7", "itemType": "RECIPE", "name": "<PERSON><PERSON>-Batch64EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44df", "itemId": "676c5175da75210c99b129d7", "itemType": "RECIPE", "name": "<PERSON><PERSON>-Batch64EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e0", "itemId": "676c5175da75210c99b129dc", "itemType": "RECIPE", "name": "Cake Lemon-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e1", "itemId": "676c5175da75210c99b129dc", "itemType": "RECIPE", "name": "Cake Lemon-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e3", "itemId": "676c5175da75210c99b129e1", "itemType": "RECIPE", "name": "Campagne Bread 460gr-Batch96EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e2", "itemId": "676c5175da75210c99b129dc", "itemType": "RECIPE", "name": "Cake Lemon-Pack1", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e4", "itemId": "676c5175da75210c99b129e1", "itemType": "RECIPE", "name": "Campagne Bread 460gr-Batch96EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e5", "itemId": "676c5175da75210c99b129e1", "itemType": "RECIPE", "name": "Campagne Bread 460gr-Batch96EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e9", "itemId": "676c5175da75210c99b12a13", "itemType": "RECIPE", "name": "Frozen Mix Shakshuka-Pack400gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ea", "itemId": "676c5175da75210c99b12a13", "itemType": "RECIPE", "name": "Frozen Mix Shakshuka-Pack400gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e6", "itemId": "676c5175da75210c99b129ee", "itemType": "RECIPE", "name": "Croissant Classic 90gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e8", "itemId": "676c5175da75210c99b129ee", "itemType": "RECIPE", "name": "Croissant Classic 90gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44e7", "itemId": "676c5175da75210c99b129ee", "itemType": "RECIPE", "name": "Croissant Classic 90gr-Box100", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44eb", "itemId": "676c5175da75210c99b12a13", "itemType": "RECIPE", "name": "Frozen Mix Shakshuka-Pack400gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ec", "itemId": "676c5175da75210c99b12a58", "itemType": "RECIPE", "name": "VP Gouda Cheese Sliced-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ed", "itemId": "676c5175da75210c99b12a58", "itemType": "RECIPE", "name": "VP Gouda Cheese Sliced-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ee", "itemId": "676c5175da75210c99b12a58", "itemType": "RECIPE", "name": "VP Gouda Cheese Sliced-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ef", "itemId": "676c5175da75210c99b12a67", "itemType": "RECIPE", "name": "White Baguette 250gr-Box30", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f0", "itemId": "676c5175da75210c99b12a67", "itemType": "RECIPE", "name": "White Baguette 250gr-Box30", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f1", "itemId": "676c5175da75210c99b12a67", "itemType": "RECIPE", "name": "White Baguette 250gr-Box30", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f2", "itemId": "676c5175da75210c99b12a6a", "itemType": "RECIPE", "name": "Wholewheat Bread 400gr-Batch80EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f3", "itemId": "676c5175da75210c99b12a6a", "itemType": "RECIPE", "name": "Wholewheat Bread 400gr-Batch80EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f4", "itemId": "676c5175da75210c99b12a6a", "itemType": "RECIPE", "name": "Wholewheat Bread 400gr-Batch80EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f5", "itemId": "676c5175da75210c99b129ce", "itemType": "RECIPE", "name": "Bavarois Strawberry 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f6", "itemId": "676c5175da75210c99b129ce", "itemType": "RECIPE", "name": "Bavarois Strawberry 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f8", "itemId": "676c5175da75210c99b129d0", "itemType": "RECIPE", "name": "Bavarois Two Choco 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f9", "itemId": "676c5175da75210c99b129d0", "itemType": "RECIPE", "name": "Bavarois Two Choco 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44fa", "itemId": "676c5175da75210c99b129d0", "itemType": "RECIPE", "name": "Bavarois Two Choco 10P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44f7", "itemId": "676c5175da75210c99b129ce", "itemType": "RECIPE", "name": "Bavarois Strawberry 1P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44fb", "itemId": "676c5175da75210c99b129d8", "itemType": "RECIPE", "name": "<PERSON>ake <PERSON>-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44ff", "itemId": "676c5175da75210c99b129f4", "itemType": "RECIPE", "name": "Donut Chocolate Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44fe", "itemId": "676c5175da75210c99b129f4", "itemType": "RECIPE", "name": "Donut Chocolate Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44fc", "itemId": "676c5175da75210c99b129d8", "itemType": "RECIPE", "name": "<PERSON>ake <PERSON>-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f44fd", "itemId": "676c5175da75210c99b129d8", "itemType": "RECIPE", "name": "<PERSON>ake <PERSON>-Box34", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4500", "itemId": "676c5175da75210c99b129f4", "itemType": "RECIPE", "name": "Donut Chocolate Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4501", "itemId": "676c5175da75210c99b12a19", "itemType": "RECIPE", "name": "<PERSON><PERSON>ze Dark Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4502", "itemId": "676c5175da75210c99b12a19", "itemType": "RECIPE", "name": "<PERSON><PERSON>ze Dark Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4503", "itemId": "676c5175da75210c99b12a19", "itemType": "RECIPE", "name": "<PERSON><PERSON>ze Dark Choco-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4504", "itemId": "676c5175da75210c99b12a57", "itemType": "RECIPE", "name": "VP Dry Oats with Honey-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4507", "itemId": "676c5175da75210c99b129ec", "itemType": "RECIPE", "name": "Cream Filled Donut-Box75", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4505", "itemId": "676c5175da75210c99b12a57", "itemType": "RECIPE", "name": "VP Dry Oats with Honey-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4506", "itemId": "676c5175da75210c99b12a57", "itemType": "RECIPE", "name": "VP Dry Oats with Honey-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f450b", "itemId": "676c5175da75210c99b129f6", "itemType": "RECIPE", "name": "Donut Pink Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4508", "itemId": "676c5175da75210c99b129ec", "itemType": "RECIPE", "name": "Cream Filled Donut-Box75", "components": []}, {"branchInventoryId": "6784f168b6183517c88f450a", "itemId": "676c5175da75210c99b129f6", "itemType": "RECIPE", "name": "Donut Pink Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4509", "itemId": "676c5175da75210c99b129ec", "itemType": "RECIPE", "name": "Cream Filled Donut-Box75", "components": []}, {"branchInventoryId": "6784f168b6183517c88f450c", "itemId": "676c5175da75210c99b129f6", "itemType": "RECIPE", "name": "Donut Pink Vermicelli-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f450d", "itemId": "676c5175da75210c99b129f8", "itemType": "RECIPE", "name": "Donut White-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f450e", "itemId": "676c5175da75210c99b129f8", "itemType": "RECIPE", "name": "Donut White-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f450f", "itemId": "676c5175da75210c99b129f8", "itemType": "RECIPE", "name": "Donut White-1EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4511", "itemId": "676c5175da75210c99b129f9", "itemType": "RECIPE", "name": "Eclair Chocolate-Batch15 NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4513", "itemId": "676c5175da75210c99b12a03", "itemType": "RECIPE", "name": "Fresh Sliced Green and Red Peppers-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4510", "itemId": "676c5175da75210c99b129f9", "itemType": "RECIPE", "name": "Eclair Chocolate-Batch15 NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4512", "itemId": "676c5175da75210c99b129f9", "itemType": "RECIPE", "name": "Eclair Chocolate-Batch15 NEW", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4514", "itemId": "676c5175da75210c99b12a03", "itemType": "RECIPE", "name": "Fresh Sliced Green and Red Peppers-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4515", "itemId": "676c5175da75210c99b12a03", "itemType": "RECIPE", "name": "Fresh Sliced Green and Red Peppers-Pack300gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4516", "itemId": "676c5175da75210c99b12a04", "itemType": "RECIPE", "name": "Fresh Sliced Red Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4517", "itemId": "676c5175da75210c99b12a04", "itemType": "RECIPE", "name": "Fresh Sliced Red Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4519", "itemId": "676c5175da75210c99b12a33", "itemType": "RECIPE", "name": "Parisian Baguette-Batch144EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4518", "itemId": "676c5175da75210c99b12a04", "itemType": "RECIPE", "name": "Fresh Sliced Red Onions-Pack500gr", "components": []}, {"branchInventoryId": "6784f168b6183517c88f451a", "itemId": "676c5175da75210c99b12a33", "itemType": "RECIPE", "name": "Parisian Baguette-Batch144EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f451d", "itemId": "676c5175da75210c99b12a36", "itemType": "RECIPE", "name": "Passion Juice-Batch20.5L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f451b", "itemId": "676c5175da75210c99b12a33", "itemType": "RECIPE", "name": "Parisian Baguette-Batch144EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f451c", "itemId": "676c5175da75210c99b12a36", "itemType": "RECIPE", "name": "Passion Juice-Batch20.5L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f451e", "itemId": "676c5175da75210c99b12a36", "itemType": "RECIPE", "name": "Passion Juice-Batch20.5L", "components": []}, {"branchInventoryId": "6784f168b6183517c88f451f", "itemId": "676c5175da75210c99b12a49", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Box19", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4520", "itemId": "676c5175da75210c99b12a49", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Box19", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4521", "itemId": "676c5175da75210c99b12a49", "itemType": "RECIPE", "name": "Sliced Soft MultiCereals Bread 600gr-Box19", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4522", "itemId": "676c5175da75210c99b12a51", "itemType": "RECIPE", "name": "Tartlet Choco Caramel", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4523", "itemId": "676c5175da75210c99b12a51", "itemType": "RECIPE", "name": "Tartlet Choco Caramel", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4524", "itemId": "676c5175da75210c99b12a51", "itemType": "RECIPE", "name": "Tartlet Choco Caramel", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4525", "itemId": "676c5175da75210c99b12a64", "itemType": "RECIPE", "name": "Wheat Tourte Bread 800gr-Batch20", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4526", "itemId": "676c5175da75210c99b12a64", "itemType": "RECIPE", "name": "Wheat Tourte Bread 800gr-Batch20", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4527", "itemId": "676c5175da75210c99b12a64", "itemType": "RECIPE", "name": "Wheat Tourte Bread 800gr-Batch20", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4528", "itemId": "676c5175da75210c99b129ef", "itemType": "RECIPE", "name": "Croissant Premium 80gr-Batch82EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4529", "itemId": "676c5175da75210c99b129ef", "itemType": "RECIPE", "name": "Croissant Premium 80gr-Batch82EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f452a", "itemId": "676c5175da75210c99b129ef", "itemType": "RECIPE", "name": "Croissant Premium 80gr-Batch82EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f452b", "itemId": "676c5175da75210c99b12a1c", "itemType": "RECIPE", "name": "Half Soft Baguette 150gr-Batch320EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f452c", "itemId": "676c5175da75210c99b12a1c", "itemType": "RECIPE", "name": "Half Soft Baguette 150gr-Batch320EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f452f", "itemId": "676c5175da75210c99b12a4c", "itemType": "RECIPE", "name": "St Valentine Cake 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4532", "itemId": "676aca53d253bdfa34d2052c", "itemType": "INGREDIENT", "name": "Aluminium Foil", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4534", "itemId": "676aca53d253bdfa34d20534", "itemType": "INGREDIENT", "name": "Baking Paper-Pack1000 sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4531", "itemId": "676aca53d253bdfa34d2052c", "itemType": "INGREDIENT", "name": "Aluminium Foil", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f452e", "itemId": "676c5175da75210c99b12a4c", "itemType": "RECIPE", "name": "St Valentine Cake 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4533", "itemId": "676aca53d253bdfa34d2052c", "itemType": "INGREDIENT", "name": "Aluminium Foil", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f452d", "itemId": "676c5175da75210c99b12a1c", "itemType": "RECIPE", "name": "Half Soft Baguette 150gr-Batch320EA", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4530", "itemId": "676c5175da75210c99b12a4c", "itemType": "RECIPE", "name": "St Valentine Cake 6P", "components": []}, {"branchInventoryId": "6784f168b6183517c88f4535", "itemId": "676aca53d253bdfa34d20534", "itemType": "INGREDIENT", "name": "Baking Paper-Pack1000 sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4536", "itemId": "676aca53d253bdfa34d20534", "itemType": "INGREDIENT", "name": "Baking Paper-Pack1000 sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4537", "itemId": "676aca53d253bdfa34d20539", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (bunch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4538", "itemId": "676aca53d253bdfa34d20539", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (bunch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f453c", "itemId": "676aca53d253bdfa34d20556", "itemType": "INGREDIENT", "name": "Cake Board Round Silver 6inch 15cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4539", "itemId": "676aca53d253bdfa34d20539", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (bunch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f453b", "itemId": "676aca53d253bdfa34d20556", "itemType": "INGREDIENT", "name": "Cake Board Round Silver 6inch 15cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f453a", "itemId": "676aca53d253bdfa34d20556", "itemType": "INGREDIENT", "name": "Cake Board Round Silver 6inch 15cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f453d", "itemId": "676aca53d253bdfa34d2056d", "itemType": "INGREDIENT", "name": "Clear Tiles Cleaner 5L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4543", "itemId": "676aca53d253bdfa34d205a1", "itemType": "INGREDIENT", "name": "Fanta Citron Plastic 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4542", "itemId": "676aca53d253bdfa34d20596", "itemType": "INGREDIENT", "name": "Dinner Fork", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f453e", "itemId": "676aca53d253bdfa34d2056d", "itemType": "INGREDIENT", "name": "Clear Tiles Cleaner 5L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4540", "itemId": "676aca53d253bdfa34d20596", "itemType": "INGREDIENT", "name": "Dinner Fork", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f453f", "itemId": "676aca53d253bdfa34d2056d", "itemType": "INGREDIENT", "name": "Clear Tiles Cleaner 5L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4541", "itemId": "676aca53d253bdfa34d20596", "itemType": "INGREDIENT", "name": "Dinner Fork", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4544", "itemId": "676aca53d253bdfa34d205a1", "itemType": "INGREDIENT", "name": "Fanta Citron Plastic 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4545", "itemId": "676aca53d253bdfa34d205a1", "itemType": "INGREDIENT", "name": "Fanta Citron Plastic 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4546", "itemId": "676aca53d253bdfa34d205f5", "itemType": "INGREDIENT", "name": "Onions red (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4547", "itemId": "676aca53d253bdfa34d205f5", "itemType": "INGREDIENT", "name": "Onions red (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4548", "itemId": "676aca53d253bdfa34d205f5", "itemType": "INGREDIENT", "name": "Onions red (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4549", "itemId": "676aca53d253bdfa34d2061d", "itemType": "INGREDIENT", "name": "Plate Large (29cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f454a", "itemId": "676aca53d253bdfa34d2061d", "itemType": "INGREDIENT", "name": "Plate Large (29cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f454b", "itemId": "676aca53d253bdfa34d2061d", "itemType": "INGREDIENT", "name": "Plate Large (29cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f454c", "itemId": "676aca53d253bdfa34d20635", "itemType": "INGREDIENT", "name": "Salt & Pepper Holder Stainless Steel", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f454d", "itemId": "676aca53d253bdfa34d20635", "itemType": "INGREDIENT", "name": "Salt & Pepper Holder Stainless Steel", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f454e", "itemId": "676aca53d253bdfa34d20635", "itemType": "INGREDIENT", "name": "Salt & Pepper Holder Stainless Steel", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f454f", "itemId": "676aca53d253bdfa34d20655", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #6", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4550", "itemId": "676aca53d253bdfa34d20655", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #6", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4551", "itemId": "676aca53d253bdfa34d20655", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #6", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4552", "itemId": "676aca53d253bdfa34d2065f", "itemType": "INGREDIENT", "name": "Sweet Corn Tin 400gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4553", "itemId": "676aca53d253bdfa34d2065f", "itemType": "INGREDIENT", "name": "Sweet Corn Tin 400gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4554", "itemId": "676aca53d253bdfa34d2065f", "itemType": "INGREDIENT", "name": "Sweet Corn Tin 400gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4555", "itemId": "676aca53d253bdfa34d2066b", "itemType": "INGREDIENT", "name": "Topping <PERSON><PERSON> (ml)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4556", "itemId": "676aca53d253bdfa34d2066b", "itemType": "INGREDIENT", "name": "Topping <PERSON><PERSON> (ml)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4557", "itemId": "676aca53d253bdfa34d2066b", "itemType": "INGREDIENT", "name": "Topping <PERSON><PERSON> (ml)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4558", "itemId": "676aca53d253bdfa34d20672", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (White)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4559", "itemId": "676aca53d253bdfa34d20672", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (White)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f455a", "itemId": "676aca53d253bdfa34d20672", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (White)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f455b", "itemId": "676aca53d253bdfa34d2068d", "itemType": "INGREDIENT", "name": "Winnaz Salt Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f455c", "itemId": "676aca53d253bdfa34d2068d", "itemType": "INGREDIENT", "name": "Winnaz Salt Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f455d", "itemId": "676aca53d253bdfa34d2068d", "itemType": "INGREDIENT", "name": "Winnaz Salt Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f455e", "itemId": "676aca53d253bdfa34d2054f", "itemType": "INGREDIENT", "name": "Brown Bag Size16 30Kg (1140EA)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f455f", "itemId": "676aca53d253bdfa34d2054f", "itemType": "INGREDIENT", "name": "Brown Bag Size16 30Kg (1140EA)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4560", "itemId": "676aca53d253bdfa34d2054f", "itemType": "INGREDIENT", "name": "Brown Bag Size16 30Kg (1140EA)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4561", "itemId": "676aca53d253bdfa34d205cf", "itemType": "INGREDIENT", "name": "Kitchen Towel Roll", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4562", "itemId": "676aca53d253bdfa34d205cf", "itemType": "INGREDIENT", "name": "Kitchen Towel Roll", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4563", "itemId": "676aca53d253bdfa34d205cf", "itemType": "INGREDIENT", "name": "Kitchen Towel Roll", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4564", "itemId": "676aca53d253bdfa34d205e2", "itemType": "INGREDIENT", "name": "Mint (bunch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4565", "itemId": "676aca53d253bdfa34d205e2", "itemType": "INGREDIENT", "name": "Mint (bunch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4566", "itemId": "676aca53d253bdfa34d205e2", "itemType": "INGREDIENT", "name": "Mint (bunch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f456a", "itemId": "676aca53d253bdfa34d205f2", "itemType": "INGREDIENT", "name": "Olive Oil 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4568", "itemId": "676aca53d253bdfa34d205f0", "itemType": "INGREDIENT", "name": "<PERSON>ard <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4567", "itemId": "676aca53d253bdfa34d205f0", "itemType": "INGREDIENT", "name": "<PERSON>ard <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f4569", "itemId": "676aca53d253bdfa34d205f0", "itemType": "INGREDIENT", "name": "<PERSON>ard <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f456b", "itemId": "676aca53d253bdfa34d205f2", "itemType": "INGREDIENT", "name": "Olive Oil 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f456c", "itemId": "676aca53d253bdfa34d205f2", "itemType": "INGREDIENT", "name": "Olive Oil 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f456d", "itemId": "676aca53d253bdfa34d20609", "itemType": "INGREDIENT", "name": "Paper Straws Large 10mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f456e", "itemId": "676aca53d253bdfa34d20609", "itemType": "INGREDIENT", "name": "Paper Straws Large 10mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f168b6183517c88f456f", "itemId": "676aca53d253bdfa34d20609", "itemType": "INGREDIENT", "name": "Paper Straws Large 10mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4570", "itemId": "676aca53d253bdfa34d2061b", "itemType": "INGREDIENT", "name": "Plastic Box Pizza Dough 32x25X6cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4571", "itemId": "676aca53d253bdfa34d2061b", "itemType": "INGREDIENT", "name": "Plastic Box Pizza Dough 32x25X6cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4572", "itemId": "676aca53d253bdfa34d2061b", "itemType": "INGREDIENT", "name": "Plastic Box Pizza Dough 32x25X6cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4573", "itemId": "676aca53d253bdfa34d2061c", "itemType": "INGREDIENT", "name": "Plastic Cling Film", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4574", "itemId": "676aca53d253bdfa34d2061c", "itemType": "INGREDIENT", "name": "Plastic Cling Film", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4575", "itemId": "676aca53d253bdfa34d2061c", "itemType": "INGREDIENT", "name": "Plastic Cling Film", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4576", "itemId": "676aca53d253bdfa34d2063a", "itemType": "INGREDIENT", "name": "Saucer Cappuccino Cup (17cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4577", "itemId": "676aca53d253bdfa34d2063a", "itemType": "INGREDIENT", "name": "Saucer Cappuccino Cup (17cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4578", "itemId": "676aca53d253bdfa34d2063a", "itemType": "INGREDIENT", "name": "Saucer Cappuccino Cup (17cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f457a", "itemId": "676aca53d253bdfa34d20665", "itemType": "INGREDIENT", "name": "Tissue Roll", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4579", "itemId": "676aca53d253bdfa34d20665", "itemType": "INGREDIENT", "name": "Tissue Roll", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f457b", "itemId": "676aca53d253bdfa34d20665", "itemType": "INGREDIENT", "name": "Tissue Roll", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f457c", "itemId": "676aca53d253bdfa34d2066d", "itemType": "INGREDIENT", "name": "T-Shirt Black with <PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f457d", "itemId": "676aca53d253bdfa34d2066d", "itemType": "INGREDIENT", "name": "T-Shirt Black with <PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f457e", "itemId": "676aca53d253bdfa34d2066d", "itemType": "INGREDIENT", "name": "T-Shirt Black with <PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f457f", "itemId": "676aca53d253bdfa34d2066f", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Green)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4582", "itemId": "676aca53d253bdfa34d2067d", "itemType": "INGREDIENT", "name": "Vitalo Mineral Water Glass 30cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4580", "itemId": "676aca53d253bdfa34d2066f", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Green)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4581", "itemId": "676aca53d253bdfa34d2066f", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Green)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4583", "itemId": "676aca53d253bdfa34d2067d", "itemType": "INGREDIENT", "name": "Vitalo Mineral Water Glass 30cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4584", "itemId": "676aca53d253bdfa34d2067d", "itemType": "INGREDIENT", "name": "Vitalo Mineral Water Glass 30cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4585", "itemId": "676aca53d253bdfa34d20681", "itemType": "INGREDIENT", "name": "Watermelon (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4586", "itemId": "676aca53d253bdfa34d20681", "itemType": "INGREDIENT", "name": "Watermelon (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4587", "itemId": "676aca53d253bdfa34d20681", "itemType": "INGREDIENT", "name": "Watermelon (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4589", "itemId": "676aca53d253bdfa34d2054e", "itemType": "INGREDIENT", "name": "Broom Wooden Stick", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4588", "itemId": "676aca53d253bdfa34d2054e", "itemType": "INGREDIENT", "name": "Broom Wooden Stick", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f458a", "itemId": "676aca53d253bdfa34d2054e", "itemType": "INGREDIENT", "name": "Broom Wooden Stick", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f458b", "itemId": "676aca53d253bdfa34d2055a", "itemType": "INGREDIENT", "name": "Cake Box WHITE 8inch 20x20cm H8cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f458c", "itemId": "676aca53d253bdfa34d2055a", "itemType": "INGREDIENT", "name": "Cake Box WHITE 8inch 20x20cm H8cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f458f", "itemId": "676aca53d253bdfa34d20583", "itemType": "INGREDIENT", "name": "Cucumber (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4591", "itemId": "676aca53d253bdfa34d205a5", "itemType": "INGREDIENT", "name": "Fire Blanket", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f458e", "itemId": "676aca53d253bdfa34d20583", "itemType": "INGREDIENT", "name": "Cucumber (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f458d", "itemId": "676aca53d253bdfa34d2055a", "itemType": "INGREDIENT", "name": "Cake Box WHITE 8inch 20x20cm H8cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4590", "itemId": "676aca53d253bdfa34d20583", "itemType": "INGREDIENT", "name": "Cucumber (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4592", "itemId": "676aca53d253bdfa34d205a5", "itemType": "INGREDIENT", "name": "Fire Blanket", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4593", "itemId": "676aca53d253bdfa34d205a5", "itemType": "INGREDIENT", "name": "Fire Blanket", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4598", "itemId": "676aca53d253bdfa34d2060f", "itemType": "INGREDIENT", "name": "Pasta Bowl Regular (24cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4597", "itemId": "676aca53d253bdfa34d2060f", "itemType": "INGREDIENT", "name": "Pasta Bowl Regular (24cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4595", "itemId": "676aca53d253bdfa34d2060d", "itemType": "INGREDIENT", "name": "Passion Fruit (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4596", "itemId": "676aca53d253bdfa34d2060d", "itemType": "INGREDIENT", "name": "Passion Fruit (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4594", "itemId": "676aca53d253bdfa34d2060d", "itemType": "INGREDIENT", "name": "Passion Fruit (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4599", "itemId": "676aca53d253bdfa34d2060f", "itemType": "INGREDIENT", "name": "Pasta Bowl Regular (24cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f459a", "itemId": "676aca53d253bdfa34d2064c", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Pudding", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f459b", "itemId": "676aca53d253bdfa34d2064c", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Pudding", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f459c", "itemId": "676aca53d253bdfa34d2064c", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Pudding", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f459f", "itemId": "676aca53d253bdfa34d20653", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #4", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a0", "itemId": "676aca53d253bdfa34d20662", "itemType": "INGREDIENT", "name": "Tea Green 50 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a1", "itemId": "676aca53d253bdfa34d20662", "itemType": "INGREDIENT", "name": "Tea Green 50 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f459e", "itemId": "676aca53d253bdfa34d20653", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #4", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f459d", "itemId": "676aca53d253bdfa34d20653", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #4", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a2", "itemId": "676aca53d253bdfa34d20662", "itemType": "INGREDIENT", "name": "Tea Green 50 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a3", "itemId": "676aca53d253bdfa34d2066a", "itemType": "INGREDIENT", "name": "Toothpicks Individually Wrapped", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a4", "itemId": "676aca53d253bdfa34d2066a", "itemType": "INGREDIENT", "name": "Toothpicks Individually Wrapped", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a7", "itemId": "676aca53d253bdfa34d2067c", "itemType": "INGREDIENT", "name": "Virunga Sparkling Water Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45aa", "itemId": "676aca53d253bdfa34d20686", "itemType": "INGREDIENT", "name": "White Paper Bag MEDIUM", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a9", "itemId": "676aca53d253bdfa34d20686", "itemType": "INGREDIENT", "name": "White Paper Bag MEDIUM", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a5", "itemId": "676aca53d253bdfa34d2066a", "itemType": "INGREDIENT", "name": "Toothpicks Individually Wrapped", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a6", "itemId": "676aca53d253bdfa34d2067c", "itemType": "INGREDIENT", "name": "Virunga Sparkling Water Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45a8", "itemId": "676aca53d253bdfa34d2067c", "itemType": "INGREDIENT", "name": "Virunga Sparkling Water Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ab", "itemId": "676aca53d253bdfa34d20686", "itemType": "INGREDIENT", "name": "White Paper Bag MEDIUM", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ac", "itemId": "676aca53d253bdfa34d2057d", "itemType": "INGREDIENT", "name": "Cooking Oil - Amavuta", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ae", "itemId": "676aca53d253bdfa34d2057d", "itemType": "INGREDIENT", "name": "Cooking Oil - Amavuta", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ad", "itemId": "676aca53d253bdfa34d2057d", "itemType": "INGREDIENT", "name": "Cooking Oil - Amavuta", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45af", "itemId": "676aca53d253bdfa34d205be", "itemType": "INGREDIENT", "name": "Gorilla Instant Coffee-Pack150gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b0", "itemId": "676aca53d253bdfa34d205be", "itemType": "INGREDIENT", "name": "Gorilla Instant Coffee-Pack150gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b2", "itemId": "676aca53d253bdfa34d205ec", "itemType": "INGREDIENT", "name": "Monin Syrup Lemon Tea 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b4", "itemId": "676aca53d253bdfa34d205ec", "itemType": "INGREDIENT", "name": "Monin Syrup Lemon Tea 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b3", "itemId": "676aca53d253bdfa34d205ec", "itemType": "INGREDIENT", "name": "Monin Syrup Lemon Tea 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b1", "itemId": "676aca53d253bdfa34d205be", "itemType": "INGREDIENT", "name": "Gorilla Instant Coffee-Pack150gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b5", "itemId": "676aca53d253bdfa34d205f6", "itemType": "INGREDIENT", "name": "Onions white (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b6", "itemId": "676aca53d253bdfa34d205f6", "itemType": "INGREDIENT", "name": "Onions white (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b7", "itemId": "676aca53d253bdfa34d205f6", "itemType": "INGREDIENT", "name": "Onions white (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b8", "itemId": "676aca53d253bdfa34d2060a", "itemType": "INGREDIENT", "name": "Paper Straws Regular 6mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45b9", "itemId": "676aca53d253bdfa34d2060a", "itemType": "INGREDIENT", "name": "Paper Straws Regular 6mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ba", "itemId": "676aca53d253bdfa34d2060a", "itemType": "INGREDIENT", "name": "Paper Straws Regular 6mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45bb", "itemId": "676aca53d253bdfa34d2062c", "itemType": "INGREDIENT", "name": "Register Book (blue book)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45bc", "itemId": "676aca53d253bdfa34d2062c", "itemType": "INGREDIENT", "name": "Register Book (blue book)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45be", "itemId": "676aca53d253bdfa34d20634", "itemType": "INGREDIENT", "name": "Salt - Umunyu", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45bf", "itemId": "676aca53d253bdfa34d20634", "itemType": "INGREDIENT", "name": "Salt - Umunyu", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45bd", "itemId": "676aca53d253bdfa34d2062c", "itemType": "INGREDIENT", "name": "Register Book (blue book)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c0", "itemId": "676aca53d253bdfa34d20634", "itemType": "INGREDIENT", "name": "Salt - Umunyu", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c1", "itemId": "676aca53d253bdfa34d20669", "itemType": "INGREDIENT", "name": "Tomatoes (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c2", "itemId": "676aca53d253bdfa34d20669", "itemType": "INGREDIENT", "name": "Tomatoes (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c3", "itemId": "676aca53d253bdfa34d20669", "itemType": "INGREDIENT", "name": "Tomatoes (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c4", "itemId": "676aca53d253bdfa34d2067f", "itemType": "INGREDIENT", "name": "Vitalo Sparkling Water Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c5", "itemId": "676aca53d253bdfa34d2067f", "itemType": "INGREDIENT", "name": "Vitalo Sparkling Water Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c6", "itemId": "676aca53d253bdfa34d2067f", "itemType": "INGREDIENT", "name": "Vitalo Sparkling Water Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c7", "itemId": "676aca53d253bdfa34d20538", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c8", "itemId": "676aca53d253bdfa34d20538", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45c9", "itemId": "676aca53d253bdfa34d20538", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ca", "itemId": "676aca53d253bdfa34d2053c", "itemType": "INGREDIENT", "name": "Beer Amstel Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45cb", "itemId": "676aca53d253bdfa34d2053c", "itemType": "INGREDIENT", "name": "Beer Amstel Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45cc", "itemId": "676aca53d253bdfa34d2053c", "itemType": "INGREDIENT", "name": "Beer Amstel Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45cd", "itemId": "676aca53d253bdfa34d20588", "itemType": "INGREDIENT", "name": "Cup Cappuccino (28cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ce", "itemId": "676aca53d253bdfa34d20588", "itemType": "INGREDIENT", "name": "Cup Cappuccino (28cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45cf", "itemId": "676aca53d253bdfa34d20588", "itemType": "INGREDIENT", "name": "Cup Cappuccino (28cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d0", "itemId": "676aca53d253bdfa34d20597", "itemType": "INGREDIENT", "name": "Dinner Knife", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d1", "itemId": "676aca53d253bdfa34d20597", "itemType": "INGREDIENT", "name": "Dinner Knife", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d2", "itemId": "676aca53d253bdfa34d20597", "itemType": "INGREDIENT", "name": "Dinner Knife", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d3", "itemId": "676aca53d253bdfa34d205a2", "itemType": "INGREDIENT", "name": "Fanta Orange Glass 30cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d4", "itemId": "676aca53d253bdfa34d205a2", "itemType": "INGREDIENT", "name": "Fanta Orange Glass 30cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d5", "itemId": "676aca53d253bdfa34d205a2", "itemType": "INGREDIENT", "name": "Fanta Orange Glass 30cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d6", "itemId": "676aca53d253bdfa34d205b2", "itemType": "INGREDIENT", "name": "Garlic (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d7", "itemId": "676aca53d253bdfa34d205b2", "itemType": "INGREDIENT", "name": "Garlic (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d8", "itemId": "676aca53d253bdfa34d205b2", "itemType": "INGREDIENT", "name": "Garlic (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45d9", "itemId": "676aca53d253bdfa34d205fb", "itemType": "INGREDIENT", "name": "Organic Black Tea 25 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45da", "itemId": "676aca53d253bdfa34d205fb", "itemType": "INGREDIENT", "name": "Organic Black Tea 25 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45df", "itemId": "676aca53d253bdfa34d20615", "itemType": "INGREDIENT", "name": "Pineapple (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45de", "itemId": "676aca53d253bdfa34d20606", "itemType": "INGREDIENT", "name": "Paper Bag Superbread BRANDED Coated 190+70x370mm (REF55)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45db", "itemId": "676aca53d253bdfa34d205fb", "itemType": "INGREDIENT", "name": "Organic Black Tea 25 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45dd", "itemId": "676aca53d253bdfa34d20606", "itemType": "INGREDIENT", "name": "Paper Bag Superbread BRANDED Coated 190+70x370mm (REF55)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45dc", "itemId": "676aca53d253bdfa34d20606", "itemType": "INGREDIENT", "name": "Paper Bag Superbread BRANDED Coated 190+70x370mm (REF55)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e2", "itemId": "676aca53d253bdfa34d2063f", "itemType": "INGREDIENT", "name": "Sign Holder Plastic A6 10.5x14.8cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e4", "itemId": "676aca53d253bdfa34d2063f", "itemType": "INGREDIENT", "name": "Sign Holder Plastic A6 10.5x14.8cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e0", "itemId": "676aca53d253bdfa34d20615", "itemType": "INGREDIENT", "name": "Pineapple (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e5", "itemId": "676aca53d253bdfa34d20661", "itemType": "INGREDIENT", "name": "Tea Black GOLD blend 50 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e1", "itemId": "676aca53d253bdfa34d20615", "itemType": "INGREDIENT", "name": "Pineapple (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e3", "itemId": "676aca53d253bdfa34d2063f", "itemType": "INGREDIENT", "name": "Sign Holder Plastic A6 10.5x14.8cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e6", "itemId": "676aca53d253bdfa34d20661", "itemType": "INGREDIENT", "name": "Tea Black GOLD blend 50 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e7", "itemId": "676aca53d253bdfa34d20661", "itemType": "INGREDIENT", "name": "Tea Black GOLD blend 50 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e8", "itemId": "676aca53d253bdfa34d20673", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Yellow)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45e9", "itemId": "676aca53d253bdfa34d20673", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Yellow)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ea", "itemId": "676aca53d253bdfa34d20673", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Yellow)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45eb", "itemId": "676aca53d253bdfa34d20684", "itemType": "INGREDIENT", "name": "White Button Mushrooms (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ec", "itemId": "676aca53d253bdfa34d20684", "itemType": "INGREDIENT", "name": "White Button Mushrooms (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ed", "itemId": "676aca53d253bdfa34d20684", "itemType": "INGREDIENT", "name": "White Button Mushrooms (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ee", "itemId": "676aca53d253bdfa34d20688", "itemType": "INGREDIENT", "name": "White Paper Ice Cream Cup with Lid 16oz", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f0", "itemId": "676aca53d253bdfa34d20688", "itemType": "INGREDIENT", "name": "White Paper Ice Cream Cup with Lid 16oz", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ef", "itemId": "676aca53d253bdfa34d20688", "itemType": "INGREDIENT", "name": "White Paper Ice Cream Cup with Lid 16oz", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f2", "itemId": "676aca53d253bdfa34d20542", "itemType": "INGREDIENT", "name": "Beer Virunga Gold Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f1", "itemId": "676aca53d253bdfa34d20542", "itemType": "INGREDIENT", "name": "Beer Virunga Gold Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f3", "itemId": "676aca53d253bdfa34d20542", "itemType": "INGREDIENT", "name": "Beer Virunga Gold Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f4", "itemId": "676aca53d253bdfa34d20546", "itemType": "INGREDIENT", "name": "Black beans (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f5", "itemId": "676aca53d253bdfa34d20546", "itemType": "INGREDIENT", "name": "Black beans (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f6", "itemId": "676aca53d253bdfa34d20546", "itemType": "INGREDIENT", "name": "Black beans (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f7", "itemId": "676aca53d253bdfa34d2055f", "itemType": "INGREDIENT", "name": "Canderelle Sugar", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f8", "itemId": "676aca53d253bdfa34d2055f", "itemType": "INGREDIENT", "name": "Canderelle Sugar", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45f9", "itemId": "676aca53d253bdfa34d2055f", "itemType": "INGREDIENT", "name": "Canderelle Sugar", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45fa", "itemId": "676aca53d253bdfa34d20576", "itemType": "INGREDIENT", "name": "Colour Red AC0051", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45fb", "itemId": "676aca53d253bdfa34d20576", "itemType": "INGREDIENT", "name": "Colour Red AC0051", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45fd", "itemId": "676aca53d253bdfa34d205d0", "itemType": "INGREDIENT", "name": "Knife Bread", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45fc", "itemId": "676aca53d253bdfa34d20576", "itemType": "INGREDIENT", "name": "Colour Red AC0051", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45ff", "itemId": "676aca53d253bdfa34d205d0", "itemType": "INGREDIENT", "name": "Knife Bread", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4601", "itemId": "676aca53d253bdfa34d205f9", "itemType": "INGREDIENT", "name": "Orea Biscuit NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f45fe", "itemId": "676aca53d253bdfa34d205d0", "itemType": "INGREDIENT", "name": "Knife Bread", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4600", "itemId": "676aca53d253bdfa34d205f9", "itemType": "INGREDIENT", "name": "Orea Biscuit NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4603", "itemId": "676aca53d253bdfa34d20674", "itemType": "INGREDIENT", "name": "Tuna Oil Net 150gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4602", "itemId": "676aca53d253bdfa34d205f9", "itemType": "INGREDIENT", "name": "Orea Biscuit NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4605", "itemId": "676aca53d253bdfa34d20674", "itemType": "INGREDIENT", "name": "Tuna Oil Net 150gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4604", "itemId": "676aca53d253bdfa34d20674", "itemType": "INGREDIENT", "name": "Tuna Oil Net 150gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4606", "itemId": "676aca53d253bdfa34d20682", "itemType": "INGREDIENT", "name": "Waxed Bag Bread MEDIUM", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4607", "itemId": "676aca53d253bdfa34d20682", "itemType": "INGREDIENT", "name": "Waxed Bag Bread MEDIUM", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4608", "itemId": "676aca53d253bdfa34d20682", "itemType": "INGREDIENT", "name": "Waxed Bag Bread MEDIUM", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4609", "itemId": "676aca53d253bdfa34d20533", "itemType": "INGREDIENT", "name": "Baking Paper Roll Falcon 75mx45cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f460a", "itemId": "676aca53d253bdfa34d20533", "itemType": "INGREDIENT", "name": "Baking Paper Roll Falcon 75mx45cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f460b", "itemId": "676aca53d253bdfa34d20533", "itemType": "INGREDIENT", "name": "Baking Paper Roll Falcon 75mx45cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f460c", "itemId": "676aca53d253bdfa34d20541", "itemType": "INGREDIENT", "name": "Beer Skol Malt Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f460d", "itemId": "676aca53d253bdfa34d20541", "itemType": "INGREDIENT", "name": "Beer Skol Malt Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f460e", "itemId": "676aca53d253bdfa34d20541", "itemType": "INGREDIENT", "name": "Beer Skol Malt Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f460f", "itemId": "676aca53d253bdfa34d20559", "itemType": "INGREDIENT", "name": "Cake Box WHITE 10inch 25x25cm H8cm (10P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4610", "itemId": "676aca53d253bdfa34d20559", "itemType": "INGREDIENT", "name": "Cake Box WHITE 10inch 25x25cm H8cm (10P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4611", "itemId": "676aca53d253bdfa34d20559", "itemType": "INGREDIENT", "name": "Cake Box WHITE 10inch 25x25cm H8cm (10P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4612", "itemId": "676aca53d253bdfa34d20572", "itemType": "INGREDIENT", "name": "Coca Zero Plastic 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4613", "itemId": "676aca53d253bdfa34d20572", "itemType": "INGREDIENT", "name": "Coca Zero Plastic 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4614", "itemId": "676aca53d253bdfa34d20572", "itemType": "INGREDIENT", "name": "Coca Zero Plastic 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4615", "itemId": "676aca53d253bdfa34d20580", "itemType": "INGREDIENT", "name": "Corrugated Box 5ply LARGE 545x330x330mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4617", "itemId": "676aca53d253bdfa34d20580", "itemType": "INGREDIENT", "name": "Corrugated Box 5ply LARGE 545x330x330mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4616", "itemId": "676aca53d253bdfa34d20580", "itemType": "INGREDIENT", "name": "Corrugated Box 5ply LARGE 545x330x330mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4618", "itemId": "676aca53d253bdfa34d2058e", "itemType": "INGREDIENT", "name": "Cupcake Case WHITE Falcon Greaseproof 12.5cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4619", "itemId": "676aca53d253bdfa34d2058e", "itemType": "INGREDIENT", "name": "Cupcake Case WHITE Falcon Greaseproof 12.5cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f461e", "itemId": "676aca53d253bdfa34d205a6", "itemType": "INGREDIENT", "name": "Flavour Pineapple", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f461c", "itemId": "676aca53d253bdfa34d20598", "itemType": "INGREDIENT", "name": "Dinner Spoon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f461d", "itemId": "676aca53d253bdfa34d20598", "itemType": "INGREDIENT", "name": "Dinner Spoon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4620", "itemId": "676aca53d253bdfa34d205a6", "itemType": "INGREDIENT", "name": "Flavour Pineapple", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4621", "itemId": "676aca53d253bdfa34d205ba", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> Latex <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4622", "itemId": "676aca53d253bdfa34d205ba", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> Latex <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4626", "itemId": "676aca53d253bdfa34d205c6", "itemType": "INGREDIENT", "name": "Ice Cube Bio Bag 25x45cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4624", "itemId": "676aca53d253bdfa34d205c6", "itemType": "INGREDIENT", "name": "Ice Cube Bio Bag 25x45cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4625", "itemId": "676aca53d253bdfa34d205c6", "itemType": "INGREDIENT", "name": "Ice Cube Bio Bag 25x45cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f461f", "itemId": "676aca53d253bdfa34d205a6", "itemType": "INGREDIENT", "name": "Flavour Pineapple", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f461a", "itemId": "676aca53d253bdfa34d2058e", "itemType": "INGREDIENT", "name": "Cupcake Case WHITE Falcon Greaseproof 12.5cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f461b", "itemId": "676aca53d253bdfa34d20598", "itemType": "INGREDIENT", "name": "Dinner Spoon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4623", "itemId": "676aca53d253bdfa34d205ba", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> Latex <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4627", "itemId": "676aca53d253bdfa34d205c8", "itemType": "INGREDIENT", "name": "Inyange Juice Apple 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4628", "itemId": "676aca53d253bdfa34d205c8", "itemType": "INGREDIENT", "name": "Inyange Juice Apple 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4629", "itemId": "676aca53d253bdfa34d205c8", "itemType": "INGREDIENT", "name": "Inyange Juice Apple 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4630", "itemId": "676aca53d253bdfa34d205e4", "itemType": "INGREDIENT", "name": "Monin Fruit Puree Coconut 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4632", "itemId": "676aca53d253bdfa34d205e4", "itemType": "INGREDIENT", "name": "Monin Fruit Puree Coconut 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4631", "itemId": "676aca53d253bdfa34d205e4", "itemType": "INGREDIENT", "name": "Monin Fruit Puree Coconut 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f462b", "itemId": "676aca53d253bdfa34d205c9", "itemType": "INGREDIENT", "name": "Inyange Juice Cocktail 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f462e", "itemId": "676aca53d253bdfa34d205ca", "itemType": "INGREDIENT", "name": "Inyange Juice Mango 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4633", "itemId": "676aca53d253bdfa34d205ea", "itemType": "INGREDIENT", "name": "<PERSON>in <PERSON>up Cloudy Lemonade 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f462a", "itemId": "676aca53d253bdfa34d205c9", "itemType": "INGREDIENT", "name": "Inyange Juice Cocktail 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f462c", "itemId": "676aca53d253bdfa34d205c9", "itemType": "INGREDIENT", "name": "Inyange Juice Cocktail 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f462f", "itemId": "676aca53d253bdfa34d205ca", "itemType": "INGREDIENT", "name": "Inyange Juice Mango 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f462d", "itemId": "676aca53d253bdfa34d205ca", "itemType": "INGREDIENT", "name": "Inyange Juice Mango 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4634", "itemId": "676aca53d253bdfa34d205ea", "itemType": "INGREDIENT", "name": "<PERSON>in <PERSON>up Cloudy Lemonade 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4635", "itemId": "676aca53d253bdfa34d205ea", "itemType": "INGREDIENT", "name": "<PERSON>in <PERSON>up Cloudy Lemonade 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4636", "itemId": "676aca53d253bdfa34d205fc", "itemType": "INGREDIENT", "name": "Organic Green Tea 25 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4637", "itemId": "676aca53d253bdfa34d205fc", "itemType": "INGREDIENT", "name": "Organic Green Tea 25 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f463b", "itemId": "676aca53d253bdfa34d205fe", "itemType": "INGREDIENT", "name": "Panache Lemon Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4639", "itemId": "676aca53d253bdfa34d205fe", "itemType": "INGREDIENT", "name": "Panache Lemon Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4638", "itemId": "676aca53d253bdfa34d205fc", "itemType": "INGREDIENT", "name": "Organic Green Tea 25 Bags-Pack1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f463a", "itemId": "676aca53d253bdfa34d205fe", "itemType": "INGREDIENT", "name": "Panache Lemon Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f463f", "itemId": "676aca53d253bdfa34d20612", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f463d", "itemId": "676aca53d253bdfa34d20608", "itemType": "INGREDIENT", "name": "Paper Saucer Cups-Box2000", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f463e", "itemId": "676aca53d253bdfa34d20608", "itemType": "INGREDIENT", "name": "Paper Saucer Cups-Box2000", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4640", "itemId": "676aca53d253bdfa34d20612", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f463c", "itemId": "676aca53d253bdfa34d20608", "itemType": "INGREDIENT", "name": "Paper Saucer Cups-Box2000", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4643", "itemId": "676aca53d253bdfa34d2063d", "itemType": "INGREDIENT", "name": "Shinex Window Cleaner 650ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4644", "itemId": "676aca53d253bdfa34d2063d", "itemType": "INGREDIENT", "name": "Shinex Window Cleaner 650ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4642", "itemId": "676aca53d253bdfa34d2063d", "itemType": "INGREDIENT", "name": "Shinex Window Cleaner 650ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4645", "itemId": "676aca53d253bdfa34d20647", "itemType": "INGREDIENT", "name": "Sponge Foam", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4641", "itemId": "676aca53d253bdfa34d20612", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4646", "itemId": "676aca53d253bdfa34d20647", "itemType": "INGREDIENT", "name": "Sponge Foam", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4649", "itemId": "676aca53d253bdfa34d20656", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #7", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4648", "itemId": "676aca53d253bdfa34d20656", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #7", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4647", "itemId": "676aca53d253bdfa34d20647", "itemType": "INGREDIENT", "name": "Sponge Foam", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f464b", "itemId": "676aca53d253bdfa34d2065b", "itemType": "INGREDIENT", "name": "Sugar Pot Stainless Steel 10cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f464a", "itemId": "676aca53d253bdfa34d20656", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #7", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f464c", "itemId": "676aca53d253bdfa34d2065b", "itemType": "INGREDIENT", "name": "Sugar Pot Stainless Steel 10cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f464d", "itemId": "676aca53d253bdfa34d2065b", "itemType": "INGREDIENT", "name": "Sugar Pot Stainless Steel 10cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f464e", "itemId": "676aca53d253bdfa34d2067e", "itemType": "INGREDIENT", "name": "Vitalo Mineral Water Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4650", "itemId": "676aca53d253bdfa34d2067e", "itemType": "INGREDIENT", "name": "Vitalo Mineral Water Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f464f", "itemId": "676aca53d253bdfa34d2067e", "itemType": "INGREDIENT", "name": "Vitalo Mineral Water Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4651", "itemId": "676aca53d253bdfa34d20683", "itemType": "INGREDIENT", "name": "Waxed Bag Bread SMALL", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4652", "itemId": "676aca53d253bdfa34d20683", "itemType": "INGREDIENT", "name": "Waxed Bag Bread SMALL", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4653", "itemId": "676aca53d253bdfa34d20683", "itemType": "INGREDIENT", "name": "Waxed Bag Bread SMALL", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4654", "itemId": "676aca53d253bdfa34d2068c", "itemType": "INGREDIENT", "name": "Winnaz Paprika Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4656", "itemId": "676aca53d253bdfa34d2068c", "itemType": "INGREDIENT", "name": "Winnaz Paprika Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4655", "itemId": "676aca53d253bdfa34d2068c", "itemType": "INGREDIENT", "name": "Winnaz Paprika Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4657", "itemId": "676aca53d253bdfa34d20530", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> Bora Flour", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4658", "itemId": "676aca53d253bdfa34d20530", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> Bora Flour", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4659", "itemId": "676aca53d253bdfa34d20530", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> Bora Flour", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f465a", "itemId": "676aca53d253bdfa34d20549", "itemType": "INGREDIENT", "name": "Bouffant Caps", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f465b", "itemId": "676aca53d253bdfa34d20549", "itemType": "INGREDIENT", "name": "Bouffant Caps", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f465c", "itemId": "676aca53d253bdfa34d20549", "itemType": "INGREDIENT", "name": "Bouffant Caps", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f465e", "itemId": "676aca53d253bdfa34d2057b", "itemType": "INGREDIENT", "name": "Container Plastic White 4L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4661", "itemId": "676aca53d253bdfa34d20581", "itemType": "INGREDIENT", "name": "Corrugated Box 5ply MEDIUM 545x330x240mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f465d", "itemId": "676aca53d253bdfa34d2057b", "itemType": "INGREDIENT", "name": "Container Plastic White 4L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f465f", "itemId": "676aca53d253bdfa34d2057b", "itemType": "INGREDIENT", "name": "Container Plastic White 4L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4660", "itemId": "676aca53d253bdfa34d20581", "itemType": "INGREDIENT", "name": "Corrugated Box 5ply MEDIUM 545x330x240mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4662", "itemId": "676aca53d253bdfa34d20581", "itemType": "INGREDIENT", "name": "Corrugated Box 5ply MEDIUM 545x330x240mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4665", "itemId": "676aca53d253bdfa34d20585", "itemType": "INGREDIENT", "name": "Culture <PERSON><PERSON><PERSON> (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4664", "itemId": "676aca53d253bdfa34d20585", "itemType": "INGREDIENT", "name": "Culture <PERSON><PERSON><PERSON> (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4666", "itemId": "676aca53d253bdfa34d205bf", "itemType": "INGREDIENT", "name": "Green Capsicum Pepper (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4667", "itemId": "676aca53d253bdfa34d205bf", "itemType": "INGREDIENT", "name": "Green Capsicum Pepper (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4663", "itemId": "676aca53d253bdfa34d20585", "itemType": "INGREDIENT", "name": "Culture <PERSON><PERSON><PERSON> (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4668", "itemId": "676aca53d253bdfa34d205bf", "itemType": "INGREDIENT", "name": "Green Capsicum Pepper (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4669", "itemId": "676aca53d253bdfa34d205d1", "itemType": "INGREDIENT", "name": "Kraft Paper Ice Cream Cup with Lid 16oz", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f466a", "itemId": "676aca53d253bdfa34d205d1", "itemType": "INGREDIENT", "name": "Kraft Paper Ice Cream Cup with Lid 16oz", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f466b", "itemId": "676aca53d253bdfa34d205d1", "itemType": "INGREDIENT", "name": "Kraft Paper Ice Cream Cup with Lid 16oz", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f466c", "itemId": "676aca53d253bdfa34d205d2", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (29cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f466d", "itemId": "676aca53d253bdfa34d205d2", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (29cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f466e", "itemId": "676aca53d253bdfa34d205d2", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (29cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4673", "itemId": "676aca53d253bdfa34d20605", "itemType": "INGREDIENT", "name": "Paper Bag Small Pastry BRANDED Coated 140+55x270mm (REF47)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f466f", "itemId": "676aca53d253bdfa34d205f3", "itemType": "INGREDIENT", "name": "Omo", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4670", "itemId": "676aca53d253bdfa34d205f3", "itemType": "INGREDIENT", "name": "Omo", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4672", "itemId": "676aca53d253bdfa34d20605", "itemType": "INGREDIENT", "name": "Paper Bag Small Pastry BRANDED Coated 140+55x270mm (REF47)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4671", "itemId": "676aca53d253bdfa34d205f3", "itemType": "INGREDIENT", "name": "Omo", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4674", "itemId": "676aca53d253bdfa34d20605", "itemType": "INGREDIENT", "name": "Paper Bag Small Pastry BRANDED Coated 140+55x270mm (REF47)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4675", "itemId": "676aca53d253bdfa34d20617", "itemType": "INGREDIENT", "name": "Plastic Bag Bread Large 100x80cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4676", "itemId": "676aca53d253bdfa34d20617", "itemType": "INGREDIENT", "name": "Plastic Bag Bread Large 100x80cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4677", "itemId": "676aca53d253bdfa34d20617", "itemType": "INGREDIENT", "name": "Plastic Bag Bread Large 100x80cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4678", "itemId": "676aca53d253bdfa34d2061a", "itemType": "INGREDIENT", "name": "Plastic Bag Sous Vide 80-90um 25x40cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4679", "itemId": "676aca53d253bdfa34d2061a", "itemType": "INGREDIENT", "name": "Plastic Bag Sous Vide 80-90um 25x40cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f467a", "itemId": "676aca53d253bdfa34d2061a", "itemType": "INGREDIENT", "name": "Plastic Bag Sous Vide 80-90um 25x40cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f467b", "itemId": "676aca53d253bdfa34d20640", "itemType": "INGREDIENT", "name": "SKOL Crate", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f467c", "itemId": "676aca53d253bdfa34d20640", "itemType": "INGREDIENT", "name": "SKOL Crate", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f467d", "itemId": "676aca53d253bdfa34d20640", "itemType": "INGREDIENT", "name": "SKOL Crate", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f467e", "itemId": "676aca53d253bdfa34d20645", "itemType": "INGREDIENT", "name": "Spaghetti (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f467f", "itemId": "676aca53d253bdfa34d20645", "itemType": "INGREDIENT", "name": "Spaghetti (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4680", "itemId": "676aca53d253bdfa34d20645", "itemType": "INGREDIENT", "name": "Spaghetti (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4681", "itemId": "676aca53d253bdfa34d20649", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Banana", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4683", "itemId": "676aca53d253bdfa34d20649", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Banana", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4682", "itemId": "676aca53d253bdfa34d20649", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Banana", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4685", "itemId": "676aca53d253bdfa34d2065e", "itemType": "INGREDIENT", "name": "Sunflower Oil", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4686", "itemId": "676aca53d253bdfa34d2065e", "itemType": "INGREDIENT", "name": "Sunflower Oil", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4687", "itemId": "676aca53d253bdfa34d20660", "itemType": "INGREDIENT", "name": "Tape Scotch Large (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4684", "itemId": "676aca53d253bdfa34d2065e", "itemType": "INGREDIENT", "name": "Sunflower Oil", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4688", "itemId": "676aca53d253bdfa34d20660", "itemType": "INGREDIENT", "name": "Tape Scotch Large (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f468a", "itemId": "676aca53d253bdfa34d2068f", "itemType": "INGREDIENT", "name": "Winnaz Vinegar Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4689", "itemId": "676aca53d253bdfa34d20660", "itemType": "INGREDIENT", "name": "Tape Scotch Large (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f468c", "itemId": "676aca53d253bdfa34d2068f", "itemType": "INGREDIENT", "name": "Winnaz Vinegar Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f468b", "itemId": "676aca53d253bdfa34d2068f", "itemType": "INGREDIENT", "name": "Winnaz Vinegar Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f468d", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f468e", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f468f", "itemId": "676aca53d253bdfa34d20529", "itemType": "INGREDIENT", "name": "Air Freshener 300ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4691", "itemId": "676aca53d253bdfa34d2052a", "itemType": "INGREDIENT", "name": "Akabanga Chili Oil 20ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4690", "itemId": "676aca53d253bdfa34d2052a", "itemType": "INGREDIENT", "name": "Akabanga Chili Oil 20ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4692", "itemId": "676aca53d253bdfa34d2052a", "itemType": "INGREDIENT", "name": "Akabanga Chili Oil 20ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4693", "itemId": "676aca53d253bdfa34d20540", "itemType": "INGREDIENT", "name": "Beer Skol Lager Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4694", "itemId": "676aca53d253bdfa34d20540", "itemType": "INGREDIENT", "name": "Beer Skol Lager Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4695", "itemId": "676aca53d253bdfa34d20540", "itemType": "INGREDIENT", "name": "Beer Skol Lager Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4696", "itemId": "676aca53d253bdfa34d20558", "itemType": "INGREDIENT", "name": "Cake Box BRANDED 9inch 23x23cm H8cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4697", "itemId": "676aca53d253bdfa34d20558", "itemType": "INGREDIENT", "name": "Cake Box BRANDED 9inch 23x23cm H8cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4698", "itemId": "676aca53d253bdfa34d20558", "itemType": "INGREDIENT", "name": "Cake Box BRANDED 9inch 23x23cm H8cm (6P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4699", "itemId": "676aca53d253bdfa34d20584", "itemType": "INGREDIENT", "name": "Culture Feta CHN-22 (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f469a", "itemId": "676aca53d253bdfa34d20584", "itemType": "INGREDIENT", "name": "Culture Feta CHN-22 (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f469c", "itemId": "676aca53d253bdfa34d2058a", "itemType": "INGREDIENT", "name": "Cupcake Case PINK 10cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f469b", "itemId": "676aca53d253bdfa34d20584", "itemType": "INGREDIENT", "name": "Culture Feta CHN-22 (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f469d", "itemId": "676aca53d253bdfa34d2058a", "itemType": "INGREDIENT", "name": "Cupcake Case PINK 10cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f469e", "itemId": "676aca53d253bdfa34d2058a", "itemType": "INGREDIENT", "name": "Cupcake Case PINK 10cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f469f", "itemId": "676aca53d253bdfa34d205a7", "itemType": "INGREDIENT", "name": "Flavour Strawberry", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a0", "itemId": "676aca53d253bdfa34d205a7", "itemType": "INGREDIENT", "name": "Flavour Strawberry", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a2", "itemId": "676aca53d253bdfa34d205ae", "itemType": "INGREDIENT", "name": "Fries Basket Stainless Steel (10.5x9x6cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a1", "itemId": "676aca53d253bdfa34d205a7", "itemType": "INGREDIENT", "name": "Flavour Strawberry", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a3", "itemId": "676aca53d253bdfa34d205ae", "itemType": "INGREDIENT", "name": "Fries Basket Stainless Steel (10.5x9x6cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a4", "itemId": "676aca53d253bdfa34d205ae", "itemType": "INGREDIENT", "name": "Fries Basket Stainless Steel (10.5x9x6cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a5", "itemId": "676aca53d253bdfa34d205cb", "itemType": "INGREDIENT", "name": "Inyange Milk Whole UHT 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a6", "itemId": "676aca53d253bdfa34d205cb", "itemType": "INGREDIENT", "name": "Inyange Milk Whole UHT 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a8", "itemId": "676aca53d253bdfa34d205ff", "itemType": "INGREDIENT", "name": "Panflex Tin Grease Oil 20L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a9", "itemId": "676aca53d253bdfa34d205ff", "itemType": "INGREDIENT", "name": "Panflex Tin Grease Oil 20L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46aa", "itemId": "676aca53d253bdfa34d205ff", "itemType": "INGREDIENT", "name": "Panflex Tin Grease Oil 20L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46a7", "itemId": "676aca53d253bdfa34d205cb", "itemType": "INGREDIENT", "name": "Inyange Milk Whole UHT 1L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ab", "itemId": "676aca53d253bdfa34d20654", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #5", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ac", "itemId": "676aca53d253bdfa34d20654", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #5", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ae", "itemId": "676aca53d253bdfa34d2067a", "itemType": "INGREDIENT", "name": "Vim <PERSON> 500gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ad", "itemId": "676aca53d253bdfa34d20654", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #5", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46af", "itemId": "676aca53d253bdfa34d2067a", "itemType": "INGREDIENT", "name": "Vim <PERSON> 500gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b0", "itemId": "676aca53d253bdfa34d2067a", "itemType": "INGREDIENT", "name": "Vim <PERSON> 500gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b1", "itemId": "676aca53d253bdfa34d20689", "itemType": "INGREDIENT", "name": "White Pepper Powder-Pack250gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b5", "itemId": "676aca53d253bdfa34d20693", "itemType": "INGREDIENT", "name": "Wipe ground (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ba", "itemId": "676aca53d253bdfa34d20569", "itemType": "INGREDIENT", "name": "Christmas Cake Board Gold 19x13cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b4", "itemId": "676aca53d253bdfa34d20693", "itemType": "INGREDIENT", "name": "Wipe ground (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b3", "itemId": "676aca53d253bdfa34d20689", "itemType": "INGREDIENT", "name": "White Pepper Powder-Pack250gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b7", "itemId": "676aca53d253bdfa34d20552", "itemType": "INGREDIENT", "name": "Cabbage (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b9", "itemId": "676aca53d253bdfa34d20552", "itemType": "INGREDIENT", "name": "Cabbage (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b8", "itemId": "676aca53d253bdfa34d20552", "itemType": "INGREDIENT", "name": "Cabbage (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b2", "itemId": "676aca53d253bdfa34d20689", "itemType": "INGREDIENT", "name": "White Pepper Powder-Pack250gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46b6", "itemId": "676aca53d253bdfa34d20693", "itemType": "INGREDIENT", "name": "Wipe ground (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c0", "itemId": "676aca53d253bdfa34d20616", "itemType": "INGREDIENT", "name": "Pizza Box 10inch (25cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c3", "itemId": "676aca53d253bdfa34d20619", "itemType": "INGREDIENT", "name": "Plastic Bag Sous Vide 30um 15x20cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46bc", "itemId": "676aca53d253bdfa34d20569", "itemType": "INGREDIENT", "name": "Christmas Cake Board Gold 19x13cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c2", "itemId": "676aca53d253bdfa34d20616", "itemType": "INGREDIENT", "name": "Pizza Box 10inch (25cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46bd", "itemId": "676aca53d253bdfa34d20594", "itemType": "INGREDIENT", "name": "Decorgel (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46bf", "itemId": "676aca53d253bdfa34d20594", "itemType": "INGREDIENT", "name": "Decorgel (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46be", "itemId": "676aca53d253bdfa34d20594", "itemType": "INGREDIENT", "name": "Decorgel (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46bb", "itemId": "676aca53d253bdfa34d20569", "itemType": "INGREDIENT", "name": "Christmas Cake Board Gold 19x13cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c1", "itemId": "676aca53d253bdfa34d20616", "itemType": "INGREDIENT", "name": "Pizza Box 10inch (25cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c4", "itemId": "676aca53d253bdfa34d20619", "itemType": "INGREDIENT", "name": "Plastic Bag Sous Vide 30um 15x20cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c5", "itemId": "676aca53d253bdfa34d20619", "itemType": "INGREDIENT", "name": "Plastic Bag Sous Vide 30um 15x20cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c6", "itemId": "676aca53d253bdfa34d2061f", "itemType": "INGREDIENT", "name": "Plate Small (21cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c7", "itemId": "676aca53d253bdfa34d2061f", "itemType": "INGREDIENT", "name": "Plate Small (21cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c8", "itemId": "676aca53d253bdfa34d2061f", "itemType": "INGREDIENT", "name": "Plate Small (21cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46c9", "itemId": "676aca53d253bdfa34d20629", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ca", "itemId": "676aca53d253bdfa34d20629", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46cb", "itemId": "676aca53d253bdfa34d20629", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46cc", "itemId": "676aca53d253bdfa34d20658", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Prod Date", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46cd", "itemId": "676aca53d253bdfa34d20658", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Prod Date", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ce", "itemId": "676aca53d253bdfa34d20658", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Prod Date", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46cf", "itemId": "676aca53d253bdfa34d20692", "itemType": "INGREDIENT", "name": "Wipe dishes (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d0", "itemId": "676aca53d253bdfa34d20692", "itemType": "INGREDIENT", "name": "Wipe dishes (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d1", "itemId": "676aca53d253bdfa34d20692", "itemType": "INGREDIENT", "name": "Wipe dishes (ea)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d2", "itemId": "676aca53d253bdfa34d2053d", "itemType": "INGREDIENT", "name": "Beer Heineken 0% Can 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d3", "itemId": "676aca53d253bdfa34d2053d", "itemType": "INGREDIENT", "name": "Beer Heineken 0% Can 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d4", "itemId": "676aca53d253bdfa34d2053d", "itemType": "INGREDIENT", "name": "Beer Heineken 0% Can 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d5", "itemId": "676aca53d253bdfa34d20547", "itemType": "INGREDIENT", "name": "Black Olives-Can 400gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d6", "itemId": "676aca53d253bdfa34d20547", "itemType": "INGREDIENT", "name": "Black Olives-Can 400gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d7", "itemId": "676aca53d253bdfa34d20547", "itemType": "INGREDIENT", "name": "Black Olives-Can 400gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d8", "itemId": "676aca53d253bdfa34d20560", "itemType": "INGREDIENT", "name": "Carrots (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46da", "itemId": "676aca53d253bdfa34d20560", "itemType": "INGREDIENT", "name": "Carrots (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46db", "itemId": "676aca53d253bdfa34d20589", "itemType": "INGREDIENT", "name": "Cup Espresso (9cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46dd", "itemId": "676aca53d253bdfa34d20589", "itemType": "INGREDIENT", "name": "Cup Espresso (9cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46d9", "itemId": "676aca53d253bdfa34d20560", "itemType": "INGREDIENT", "name": "Carrots (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46dc", "itemId": "676aca53d253bdfa34d20589", "itemType": "INGREDIENT", "name": "Cup Espresso (9cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46de", "itemId": "676aca53d253bdfa34d20600", "itemType": "INGREDIENT", "name": "Paper Bag BAGUETTE 100+50x600mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46df", "itemId": "676aca53d253bdfa34d20600", "itemType": "INGREDIENT", "name": "Paper Bag BAGUETTE 100+50x600mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e0", "itemId": "676aca53d253bdfa34d20600", "itemType": "INGREDIENT", "name": "Paper Bag BAGUETTE 100+50x600mm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e2", "itemId": "676aca53d253bdfa34d2061e", "itemType": "INGREDIENT", "name": "Plate Medium (24cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e1", "itemId": "676aca53d253bdfa34d2061e", "itemType": "INGREDIENT", "name": "Plate Medium (24cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e4", "itemId": "676aca53d253bdfa34d20638", "itemType": "INGREDIENT", "name": "Sauce Cup Porcelain 45ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e3", "itemId": "676aca53d253bdfa34d2061e", "itemType": "INGREDIENT", "name": "Plate Medium (24cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e5", "itemId": "676aca53d253bdfa34d20638", "itemType": "INGREDIENT", "name": "Sauce Cup Porcelain 45ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e7", "itemId": "676aca53d253bdfa34d2064a", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Lemon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e6", "itemId": "676aca53d253bdfa34d20638", "itemType": "INGREDIENT", "name": "Sauce Cup Porcelain 45ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e8", "itemId": "676aca53d253bdfa34d2064a", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Lemon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46e9", "itemId": "676aca53d253bdfa34d2064a", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Lemon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ea", "itemId": "676aca53d253bdfa34d2064f", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Ice Cream Vanilla", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46eb", "itemId": "676aca53d253bdfa34d2064f", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Ice Cream Vanilla", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ec", "itemId": "676aca53d253bdfa34d2064f", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Ice Cream Vanilla", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ed", "itemId": "676aca53d253bdfa34d20651", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #2", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ee", "itemId": "676aca53d253bdfa34d20651", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #2", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f3", "itemId": "676aca53d253bdfa34d20663", "itemType": "INGREDIENT", "name": "Thermometer Fridge Freezer Hygiplas", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f1", "itemId": "676aca53d253bdfa34d20652", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #3", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f2", "itemId": "676aca53d253bdfa34d20652", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #3", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ef", "itemId": "676aca53d253bdfa34d20651", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #2", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f0", "itemId": "676aca53d253bdfa34d20652", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #3", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f4", "itemId": "676aca53d253bdfa34d20663", "itemType": "INGREDIENT", "name": "Thermometer Fridge Freezer Hygiplas", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f5", "itemId": "676aca53d253bdfa34d20663", "itemType": "INGREDIENT", "name": "Thermometer Fridge Freezer Hygiplas", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f6", "itemId": "676aca53d253bdfa34d20671", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Red)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f7", "itemId": "676aca53d253bdfa34d20671", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Red)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f8", "itemId": "676aca53d253bdfa34d20671", "itemType": "INGREDIENT", "name": "Tulip Cup Muffin Large (Red)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46fa", "itemId": "676aca53d253bdfa34d2068b", "itemType": "INGREDIENT", "name": "Winnaz Paprika Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46f9", "itemId": "676aca53d253bdfa34d2068b", "itemType": "INGREDIENT", "name": "Winnaz Paprika Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46fb", "itemId": "676aca53d253bdfa34d2068b", "itemType": "INGREDIENT", "name": "Winnaz Paprika Large 125gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46fc", "itemId": "676aca53d253bdfa34d20554", "itemType": "INGREDIENT", "name": "Cadbury Drinking Chocolate Powder", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46fd", "itemId": "676aca53d253bdfa34d20554", "itemType": "INGREDIENT", "name": "Cadbury Drinking Chocolate Powder", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46fe", "itemId": "676aca53d253bdfa34d20554", "itemType": "INGREDIENT", "name": "Cadbury Drinking Chocolate Powder", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f46ff", "itemId": "676aca53d253bdfa34d20571", "itemType": "INGREDIENT", "name": "Coca Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4700", "itemId": "676aca53d253bdfa34d20571", "itemType": "INGREDIENT", "name": "Coca Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4701", "itemId": "676aca53d253bdfa34d20571", "itemType": "INGREDIENT", "name": "Coca Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4702", "itemId": "676aca53d253bdfa34d20574", "itemType": "INGREDIENT", "name": "Coffee Cup 12oz New", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4703", "itemId": "676aca53d253bdfa34d20574", "itemType": "INGREDIENT", "name": "Coffee Cup 12oz New", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4704", "itemId": "676aca53d253bdfa34d20574", "itemType": "INGREDIENT", "name": "Coffee Cup 12oz New", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4705", "itemId": "676aca53d253bdfa34d2057a", "itemType": "INGREDIENT", "name": "Container Plastic White 2L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4706", "itemId": "676aca53d253bdfa34d2057a", "itemType": "INGREDIENT", "name": "Container Plastic White 2L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4707", "itemId": "676aca53d253bdfa34d2057a", "itemType": "INGREDIENT", "name": "Container Plastic White 2L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4708", "itemId": "676aca53d253bdfa34d205b7", "itemType": "INGREDIENT", "name": "Ginger (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4709", "itemId": "676aca53d253bdfa34d205b7", "itemType": "INGREDIENT", "name": "Ginger (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f470b", "itemId": "676aca53d253bdfa34d205b9", "itemType": "INGREDIENT", "name": "Gloves Latex Blue", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f470d", "itemId": "676aca53d253bdfa34d205b9", "itemType": "INGREDIENT", "name": "Gloves Latex Blue", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f470e", "itemId": "676aca53d253bdfa34d205c1", "itemType": "INGREDIENT", "name": "Heavy Drinking Glass (36cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f470a", "itemId": "676aca53d253bdfa34d205b7", "itemType": "INGREDIENT", "name": "Ginger (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f470c", "itemId": "676aca53d253bdfa34d205b9", "itemType": "INGREDIENT", "name": "Gloves Latex Blue", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f470f", "itemId": "676aca53d253bdfa34d205c1", "itemType": "INGREDIENT", "name": "Heavy Drinking Glass (36cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4710", "itemId": "676aca53d253bdfa34d205c1", "itemType": "INGREDIENT", "name": "Heavy Drinking Glass (36cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4711", "itemId": "676aca53d253bdfa34d205c3", "itemType": "INGREDIENT", "name": "Honey 550Gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4714", "itemId": "676aca53d253bdfa34d205d8", "itemType": "INGREDIENT", "name": "Local Rice - Umuceli", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4715", "itemId": "676aca53d253bdfa34d205d8", "itemType": "INGREDIENT", "name": "Local Rice - Umuceli", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4712", "itemId": "676aca53d253bdfa34d205c3", "itemType": "INGREDIENT", "name": "Honey 550Gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4713", "itemId": "676aca53d253bdfa34d205c3", "itemType": "INGREDIENT", "name": "Honey 550Gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4719", "itemId": "676aca53d253bdfa34d205e3", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 1.36Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4717", "itemId": "676aca53d253bdfa34d205e3", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 1.36Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4716", "itemId": "676aca53d253bdfa34d205d8", "itemType": "INGREDIENT", "name": "Local Rice - Umuceli", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f471a", "itemId": "676aca53d253bdfa34d205ed", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4718", "itemId": "676aca53d253bdfa34d205e3", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 1.36Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f471b", "itemId": "676aca53d253bdfa34d205ed", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f471c", "itemId": "676aca53d253bdfa34d205ed", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f471d", "itemId": "676aca53d253bdfa34d20641", "itemType": "INGREDIENT", "name": "SKOL Empty Glass", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f471f", "itemId": "676aca53d253bdfa34d20641", "itemType": "INGREDIENT", "name": "SKOL Empty Glass", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f471e", "itemId": "676aca53d253bdfa34d20641", "itemType": "INGREDIENT", "name": "SKOL Empty Glass", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4720", "itemId": "676aca53d253bdfa34d20676", "itemType": "INGREDIENT", "name": "V Neck Shirt Black with Logo", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4721", "itemId": "676aca53d253bdfa34d20676", "itemType": "INGREDIENT", "name": "V Neck Shirt Black with Logo", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4722", "itemId": "676aca53d253bdfa34d20676", "itemType": "INGREDIENT", "name": "V Neck Shirt Black with Logo", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4724", "itemId": "676aca53d253bdfa34d2052b", "itemType": "INGREDIENT", "name": "Aluminium Box SMALL 1P", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4723", "itemId": "676aca53d253bdfa34d2052b", "itemType": "INGREDIENT", "name": "Aluminium Box SMALL 1P", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4725", "itemId": "676aca53d253bdfa34d2052b", "itemType": "INGREDIENT", "name": "Aluminium Box SMALL 1P", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4726", "itemId": "676aca53d253bdfa34d20543", "itemType": "INGREDIENT", "name": "Beer Virunga Mist Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4728", "itemId": "676aca53d253bdfa34d20543", "itemType": "INGREDIENT", "name": "Beer Virunga Mist Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4729", "itemId": "676aca53d253bdfa34d2054a", "itemType": "INGREDIENT", "name": "BRALIRWA Crate", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4727", "itemId": "676aca53d253bdfa34d20543", "itemType": "INGREDIENT", "name": "Beer Virunga Mist Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f472a", "itemId": "676aca53d253bdfa34d2054a", "itemType": "INGREDIENT", "name": "BRALIRWA Crate", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f472b", "itemId": "676aca53d253bdfa34d2054a", "itemType": "INGREDIENT", "name": "BRALIRWA Crate", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f472c", "itemId": "676aca53d253bdfa34d2054c", "itemType": "INGREDIENT", "name": "Branded Greaseproof Sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f472d", "itemId": "676aca53d253bdfa34d2054c", "itemType": "INGREDIENT", "name": "Branded Greaseproof Sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f472e", "itemId": "676aca53d253bdfa34d2054c", "itemType": "INGREDIENT", "name": "Branded Greaseproof Sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f472f", "itemId": "676aca53d253bdfa34d2056e", "itemType": "INGREDIENT", "name": "Clear Virgin Napkins 75sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4730", "itemId": "676aca53d253bdfa34d2056e", "itemType": "INGREDIENT", "name": "Clear Virgin Napkins 75sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4731", "itemId": "676aca53d253bdfa34d2056e", "itemType": "INGREDIENT", "name": "Clear Virgin Napkins 75sheets", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4732", "itemId": "676aca53d253bdfa34d20593", "itemType": "INGREDIENT", "name": "Deco Christmas Cake-Pack100", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4733", "itemId": "676aca53d253bdfa34d20593", "itemType": "INGREDIENT", "name": "Deco Christmas Cake-Pack100", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4734", "itemId": "676aca53d253bdfa34d20593", "itemType": "INGREDIENT", "name": "Deco Christmas Cake-Pack100", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4735", "itemId": "676aca53d253bdfa34d205d6", "itemType": "INGREDIENT", "name": "Lemon (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f473b", "itemId": "676aca53d253bdfa34d205e1", "itemType": "INGREDIENT", "name": "Mini Cake Board Round Gold 8cm (3inch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4739", "itemId": "676aca53d253bdfa34d205dd", "itemType": "INGREDIENT", "name": "Microfiber Cloth 30x30cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4736", "itemId": "676aca53d253bdfa34d205d6", "itemType": "INGREDIENT", "name": "Lemon (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4738", "itemId": "676aca53d253bdfa34d205dd", "itemType": "INGREDIENT", "name": "Microfiber Cloth 30x30cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f473c", "itemId": "676aca53d253bdfa34d205e1", "itemType": "INGREDIENT", "name": "Mini Cake Board Round Gold 8cm (3inch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4737", "itemId": "676aca53d253bdfa34d205d6", "itemType": "INGREDIENT", "name": "Lemon (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f473a", "itemId": "676aca53d253bdfa34d205dd", "itemType": "INGREDIENT", "name": "Microfiber Cloth 30x30cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4741", "itemId": "676aca53d253bdfa34d2062b", "itemType": "INGREDIENT", "name": "Red Capsicum Pepper (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4740", "itemId": "676aca53d253bdfa34d205e7", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 1.89L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f473d", "itemId": "676aca53d253bdfa34d205e1", "itemType": "INGREDIENT", "name": "Mini Cake Board Round Gold 8cm (3inch)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f473f", "itemId": "676aca53d253bdfa34d205e7", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 1.89L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f473e", "itemId": "676aca53d253bdfa34d205e7", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 1.89L", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4742", "itemId": "676aca53d253bdfa34d2062b", "itemType": "INGREDIENT", "name": "Red Capsicum Pepper (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4743", "itemId": "676aca53d253bdfa34d2062b", "itemType": "INGREDIENT", "name": "Red Capsicum Pepper (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4744", "itemId": "676aca53d253bdfa34d2064b", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Madeleine", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4746", "itemId": "676aca53d253bdfa34d2064b", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Madeleine", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4745", "itemId": "676aca53d253bdfa34d2064b", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Madeleine", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4747", "itemId": "676aca53d253bdfa34d2064e", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Ice Cream Choco", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4748", "itemId": "676aca53d253bdfa34d2064e", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Ice Cream Choco", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4749", "itemId": "676aca53d253bdfa34d2064e", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Ice Cream Choco", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f474a", "itemId": "676aca53d253bdfa34d20650", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f474b", "itemId": "676aca53d253bdfa34d20650", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f474c", "itemId": "676aca53d253bdfa34d20650", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Nber #1", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f474d", "itemId": "676aca53d253bdfa34d20687", "itemType": "INGREDIENT", "name": "White Paper Bag SMALL", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f474e", "itemId": "676aca53d253bdfa34d20687", "itemType": "INGREDIENT", "name": "White Paper Bag SMALL", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f474f", "itemId": "676aca53d253bdfa34d20687", "itemType": "INGREDIENT", "name": "White Paper Bag SMALL", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4751", "itemId": "676aca53d253bdfa34d2068e", "itemType": "INGREDIENT", "name": "Winnaz Salt Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4750", "itemId": "676aca53d253bdfa34d2068e", "itemType": "INGREDIENT", "name": "Winnaz Salt Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4752", "itemId": "676aca53d253bdfa34d2068e", "itemType": "INGREDIENT", "name": "Winnaz Salt Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4753", "itemId": "676aca53d253bdfa34d20694", "itemType": "INGREDIENT", "name": "<PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4754", "itemId": "676aca53d253bdfa34d20694", "itemType": "INGREDIENT", "name": "<PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4755", "itemId": "676aca53d253bdfa34d20694", "itemType": "INGREDIENT", "name": "<PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4756", "itemId": "676aca53d253bdfa34d2052e", "itemType": "INGREDIENT", "name": "Apples Green (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4757", "itemId": "676aca53d253bdfa34d2052e", "itemType": "INGREDIENT", "name": "Apples Green (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4758", "itemId": "676aca53d253bdfa34d2052e", "itemType": "INGREDIENT", "name": "Apples Green (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f475a", "itemId": "676aca53d253bdfa34d2056a", "itemType": "INGREDIENT", "name": "Christmas Cake Box 20x14cm H12cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4759", "itemId": "676aca53d253bdfa34d2056a", "itemType": "INGREDIENT", "name": "Christmas Cake Box 20x14cm H12cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f475b", "itemId": "676aca53d253bdfa34d2056a", "itemType": "INGREDIENT", "name": "Christmas Cake Box 20x14cm H12cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f475c", "itemId": "676aca53d253bdfa34d20575", "itemType": "INGREDIENT", "name": "Coffee Spoon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f475d", "itemId": "676aca53d253bdfa34d20575", "itemType": "INGREDIENT", "name": "Coffee Spoon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f475e", "itemId": "676aca53d253bdfa34d20575", "itemType": "INGREDIENT", "name": "Coffee Spoon", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f475f", "itemId": "676aca53d253bdfa34d2059e", "itemType": "INGREDIENT", "name": "Face Mask", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4760", "itemId": "676aca53d253bdfa34d2059e", "itemType": "INGREDIENT", "name": "Face Mask", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4761", "itemId": "676aca53d253bdfa34d2059e", "itemType": "INGREDIENT", "name": "Face Mask", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4762", "itemId": "676aca53d253bdfa34d205ee", "itemType": "INGREDIENT", "name": "Multipurpose Liquid Soap (L)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4763", "itemId": "676aca53d253bdfa34d205ee", "itemType": "INGREDIENT", "name": "Multipurpose Liquid Soap (L)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4764", "itemId": "676aca53d253bdfa34d205ee", "itemType": "INGREDIENT", "name": "Multipurpose Liquid Soap (L)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4765", "itemId": "676aca53d253bdfa34d2060e", "itemType": "INGREDIENT", "name": "Pasta Bowl Oval Shape (22cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4766", "itemId": "676aca53d253bdfa34d2060e", "itemType": "INGREDIENT", "name": "Pasta Bowl Oval Shape (22cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4767", "itemId": "676aca53d253bdfa34d2060e", "itemType": "INGREDIENT", "name": "Pasta Bowl Oval Shape (22cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4768", "itemId": "676aca53d253bdfa34d20618", "itemType": "INGREDIENT", "name": "Plastic Bag Bread Small 45x60cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4769", "itemId": "676aca53d253bdfa34d20618", "itemType": "INGREDIENT", "name": "Plastic Bag Bread Small 45x60cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f476c", "itemId": "676aca53d253bdfa34d20626", "itemType": "INGREDIENT", "name": "Printer Roll POS (80mm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f476a", "itemId": "676aca53d253bdfa34d20618", "itemType": "INGREDIENT", "name": "Plastic Bag Bread Small 45x60cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f476b", "itemId": "676aca53d253bdfa34d20626", "itemType": "INGREDIENT", "name": "Printer Roll POS (80mm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f476d", "itemId": "676aca53d253bdfa34d20626", "itemType": "INGREDIENT", "name": "Printer Roll POS (80mm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f476e", "itemId": "676aca53d253bdfa34d20637", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (L)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4774", "itemId": "676aca53d253bdfa34d20680", "itemType": "INGREDIENT", "name": "Water", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4770", "itemId": "676aca53d253bdfa34d20637", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (L)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4772", "itemId": "676aca53d253bdfa34d20657", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Pizza", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4773", "itemId": "676aca53d253bdfa34d20657", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Pizza", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f476f", "itemId": "676aca53d253bdfa34d20637", "itemType": "INGREDIENT", "name": "<PERSON><PERSON><PERSON> (L)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4771", "itemId": "676aca53d253bdfa34d20657", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Pizza", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4775", "itemId": "676aca53d253bdfa34d20680", "itemType": "INGREDIENT", "name": "Water", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4776", "itemId": "676aca53d253bdfa34d20680", "itemType": "INGREDIENT", "name": "Water", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4777", "itemId": "676aca53d253bdfa34d20591", "itemType": "INGREDIENT", "name": "Cutlery Holder Stainless Steel", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f477c", "itemId": "676aca53d253bdfa34d2059d", "itemType": "INGREDIENT", "name": "Eggs Laying", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f477b", "itemId": "676aca53d253bdfa34d2059d", "itemType": "INGREDIENT", "name": "Eggs Laying", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4779", "itemId": "676aca53d253bdfa34d20591", "itemType": "INGREDIENT", "name": "Cutlery Holder Stainless Steel", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f477a", "itemId": "676aca53d253bdfa34d2059d", "itemType": "INGREDIENT", "name": "Eggs Laying", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4778", "itemId": "676aca53d253bdfa34d20591", "itemType": "INGREDIENT", "name": "Cutlery Holder Stainless Steel", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f477d", "itemId": "676aca53d253bdfa34d205a8", "itemType": "INGREDIENT", "name": "F<PERSON>our <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f477e", "itemId": "676aca53d253bdfa34d205a8", "itemType": "INGREDIENT", "name": "F<PERSON>our <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f477f", "itemId": "676aca53d253bdfa34d205a8", "itemType": "INGREDIENT", "name": "F<PERSON>our <PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4782", "itemId": "676aca53d253bdfa34d205d3", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (26cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4780", "itemId": "676aca53d253bdfa34d205d3", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (26cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4781", "itemId": "676aca53d253bdfa34d205d3", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (26cl)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4787", "itemId": "676aca53d253bdfa34d20614", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> Cornichon Aigre Doux NET360gr NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4788", "itemId": "676aca53d253bdfa34d20614", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> Cornichon Aigre Doux NET360gr NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4783", "itemId": "676aca53d253bdfa34d205e9", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4785", "itemId": "676aca53d253bdfa34d205e9", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4786", "itemId": "676aca53d253bdfa34d20614", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> Cornichon Aigre Doux NET360gr NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4784", "itemId": "676aca53d253bdfa34d205e9", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> 700ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4789", "itemId": "676aca53d253bdfa34d20624", "itemType": "INGREDIENT", "name": "Potatoes Red Skin (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f478a", "itemId": "676aca53d253bdfa34d20624", "itemType": "INGREDIENT", "name": "Potatoes Red Skin (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f478c", "itemId": "676aca53d253bdfa34d20625", "itemType": "INGREDIENT", "name": "Printer <PERSON> E<PERSON> (58mm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4792", "itemId": "676aca53d253bdfa34d20639", "itemType": "INGREDIENT", "name": "Sauce Cup Stainless Steel 45ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4793", "itemId": "676aca53d253bdfa34d20639", "itemType": "INGREDIENT", "name": "Sauce Cup Stainless Steel 45ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f478b", "itemId": "676aca53d253bdfa34d20624", "itemType": "INGREDIENT", "name": "Potatoes Red Skin (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f478f", "itemId": "676aca53d253bdfa34d2062d", "itemType": "INGREDIENT", "name": "Rennet (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f478e", "itemId": "676aca53d253bdfa34d20625", "itemType": "INGREDIENT", "name": "Printer <PERSON> E<PERSON> (58mm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4791", "itemId": "676aca53d253bdfa34d2062d", "itemType": "INGREDIENT", "name": "Rennet (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f478d", "itemId": "676aca53d253bdfa34d20625", "itemType": "INGREDIENT", "name": "Printer <PERSON> E<PERSON> (58mm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4790", "itemId": "676aca53d253bdfa34d2062d", "itemType": "INGREDIENT", "name": "Rennet (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4794", "itemId": "676aca53d253bdfa34d20639", "itemType": "INGREDIENT", "name": "Sauce Cup Stainless Steel 45ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4795", "itemId": "676aca53d253bdfa34d20666", "itemType": "INGREDIENT", "name": "Toilet Cleaner 750ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4796", "itemId": "676aca53d253bdfa34d20666", "itemType": "INGREDIENT", "name": "Toilet Cleaner 750ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f479b", "itemId": "676aca53d253bdfa34d20532", "itemType": "INGREDIENT", "name": "Azam SBF Flour", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f479a", "itemId": "676aca53d253bdfa34d2066e", "itemType": "INGREDIENT", "name": "Tulip <PERSON> Muffin <PERSON> (Brown)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4799", "itemId": "676aca53d253bdfa34d2066e", "itemType": "INGREDIENT", "name": "Tulip <PERSON> Muffin <PERSON> (Brown)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f479c", "itemId": "676aca53d253bdfa34d20532", "itemType": "INGREDIENT", "name": "Azam SBF Flour", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4797", "itemId": "676aca53d253bdfa34d20666", "itemType": "INGREDIENT", "name": "Toilet Cleaner 750ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f4798", "itemId": "676aca53d253bdfa34d2066e", "itemType": "INGREDIENT", "name": "Tulip <PERSON> Muffin <PERSON> (Brown)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a4", "itemId": "676aca53d253bdfa34d20573", "itemType": "INGREDIENT", "name": "Coffee Beans Plain-Pack1Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a2", "itemId": "676aca53d253bdfa34d2056c", "itemType": "INGREDIENT", "name": "Clear Recycled Toilet Paper", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a6", "itemId": "676aca53d253bdfa34d20573", "itemType": "INGREDIENT", "name": "Coffee Beans Plain-Pack1Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f479e", "itemId": "676aca53d253bdfa34d20557", "itemType": "INGREDIENT", "name": "Cake Board Round Silver 8inch 20cm (10P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a1", "itemId": "676aca53d253bdfa34d2056c", "itemType": "INGREDIENT", "name": "Clear Recycled Toilet Paper", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a3", "itemId": "676aca53d253bdfa34d2056c", "itemType": "INGREDIENT", "name": "Clear Recycled Toilet Paper", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a8", "itemId": "676aca53d253bdfa34d20577", "itemType": "INGREDIENT", "name": "Colour Yellow AC0068", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f479d", "itemId": "676aca53d253bdfa34d20532", "itemType": "INGREDIENT", "name": "Azam SBF Flour", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a5", "itemId": "676aca53d253bdfa34d20573", "itemType": "INGREDIENT", "name": "Coffee Beans Plain-Pack1Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a7", "itemId": "676aca53d253bdfa34d20577", "itemType": "INGREDIENT", "name": "Colour Yellow AC0068", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f479f", "itemId": "676aca53d253bdfa34d20557", "itemType": "INGREDIENT", "name": "Cake Board Round Silver 8inch 20cm (10P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a0", "itemId": "676aca53d253bdfa34d20557", "itemType": "INGREDIENT", "name": "Cake Board Round Silver 8inch 20cm (10P)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47a9", "itemId": "676aca53d253bdfa34d20577", "itemType": "INGREDIENT", "name": "Colour Yellow AC0068", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47aa", "itemId": "676aca53d253bdfa34d2057c", "itemType": "INGREDIENT", "name": "Cooking Gas-Bottle12Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ab", "itemId": "676aca53d253bdfa34d2057c", "itemType": "INGREDIENT", "name": "Cooking Gas-Bottle12Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ac", "itemId": "676aca53d253bdfa34d2057c", "itemType": "INGREDIENT", "name": "Cooking Gas-Bottle12Kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ad", "itemId": "676aca53d253bdfa34d20586", "itemType": "INGREDIENT", "name": "Culture Yoghurt (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ae", "itemId": "676aca53d253bdfa34d20586", "itemType": "INGREDIENT", "name": "Culture Yoghurt (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47af", "itemId": "676aca53d253bdfa34d20586", "itemType": "INGREDIENT", "name": "Culture Yoghurt (gr)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b0", "itemId": "676aca53d253bdfa34d205a4", "itemType": "INGREDIENT", "name": "Fanta Orange Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b1", "itemId": "676aca53d253bdfa34d205a4", "itemType": "INGREDIENT", "name": "Fanta Orange Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b2", "itemId": "676aca53d253bdfa34d205a4", "itemType": "INGREDIENT", "name": "Fanta Orange Plastic 50cl", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b6", "itemId": "676aca53d253bdfa34d205bd", "itemType": "INGREDIENT", "name": "Gorilla Ground Coffee-Pack500gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b4", "itemId": "676aca53d253bdfa34d205b1", "itemType": "INGREDIENT", "name": "Garbage Bag Large 90x120cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b5", "itemId": "676aca53d253bdfa34d205b1", "itemType": "INGREDIENT", "name": "Garbage Bag Large 90x120cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b7", "itemId": "676aca53d253bdfa34d205bd", "itemType": "INGREDIENT", "name": "Gorilla Ground Coffee-Pack500gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b3", "itemId": "676aca53d253bdfa34d205b1", "itemType": "INGREDIENT", "name": "Garbage Bag Large 90x120cm", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b8", "itemId": "676aca53d253bdfa34d205bd", "itemType": "INGREDIENT", "name": "Gorilla Ground Coffee-Pack500gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47b9", "itemId": "676aca53d253bdfa34d205c4", "itemType": "INGREDIENT", "name": "Honey Can 1kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ba", "itemId": "676aca53d253bdfa34d205c4", "itemType": "INGREDIENT", "name": "Honey Can 1kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47bb", "itemId": "676aca53d253bdfa34d205c4", "itemType": "INGREDIENT", "name": "Honey Can 1kg", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47be", "itemId": "676aca53d253bdfa34d205cc", "itemType": "INGREDIENT", "name": "Inyange Mineral Water 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47bc", "itemId": "676aca53d253bdfa34d205cc", "itemType": "INGREDIENT", "name": "Inyange Mineral Water 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47bd", "itemId": "676aca53d253bdfa34d205cc", "itemType": "INGREDIENT", "name": "Inyange Mineral Water 50cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47bf", "itemId": "676aca53d253bdfa34d205dc", "itemType": "INGREDIENT", "name": "Mango (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c0", "itemId": "676aca53d253bdfa34d205dc", "itemType": "INGREDIENT", "name": "Mango (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c1", "itemId": "676aca53d253bdfa34d205dc", "itemType": "INGREDIENT", "name": "Mango (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c2", "itemId": "676aca53d253bdfa34d20620", "itemType": "INGREDIENT", "name": "Polo Shirt Black with <PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c3", "itemId": "676aca53d253bdfa34d20620", "itemType": "INGREDIENT", "name": "Polo Shirt Black with <PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c4", "itemId": "676aca53d253bdfa34d20620", "itemType": "INGREDIENT", "name": "Polo Shirt Black with <PERSON><PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c5", "itemId": "676aca53d253bdfa34d20636", "itemType": "INGREDIENT", "name": "Sanitizer Bottle 500ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c6", "itemId": "676aca53d253bdfa34d20636", "itemType": "INGREDIENT", "name": "Sanitizer Bottle 500ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c7", "itemId": "676aca53d253bdfa34d20636", "itemType": "INGREDIENT", "name": "Sanitizer Bottle 500ml", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c8", "itemId": "676aca53d253bdfa34d2063b", "itemType": "INGREDIENT", "name": "Saucer Espresso Cup (14cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47c9", "itemId": "676aca53d253bdfa34d2063b", "itemType": "INGREDIENT", "name": "Saucer Espresso Cup (14cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ca", "itemId": "676aca53d253bdfa34d2063b", "itemType": "INGREDIENT", "name": "Saucer Espresso Cup (14cm)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47cb", "itemId": "676aca53d253bdfa34d2064d", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Zebra", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47cc", "itemId": "676aca53d253bdfa34d2064d", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Zebra", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47cd", "itemId": "676aca53d253bdfa34d2064d", "itemType": "INGREDIENT", "name": "Sticker A3 Sheet Cake Zebra", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47ce", "itemId": "676aca53d253bdfa34d2065a", "itemType": "INGREDIENT", "name": "<PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47cf", "itemId": "676aca53d253bdfa34d2065a", "itemType": "INGREDIENT", "name": "<PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d0", "itemId": "676aca53d253bdfa34d2065a", "itemType": "INGREDIENT", "name": "<PERSON>", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d1", "itemId": "676aca53d253bdfa34d2065d", "itemType": "INGREDIENT", "name": "Sugar White (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d2", "itemId": "676aca53d253bdfa34d2065d", "itemType": "INGREDIENT", "name": "Sugar White (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d3", "itemId": "676aca53d253bdfa34d2065d", "itemType": "INGREDIENT", "name": "Sugar White (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d4", "itemId": "676aca53d253bdfa34d20667", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (Kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d5", "itemId": "676aca53d253bdfa34d20667", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (Kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d6", "itemId": "676aca53d253bdfa34d20667", "itemType": "INGREDIENT", "name": "<PERSON><PERSON> (Kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d7", "itemId": "676aca53d253bdfa34d20679", "itemType": "INGREDIENT", "name": "Ver<PERSON>elli Rainbow (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d8", "itemId": "676aca53d253bdfa34d20679", "itemType": "INGREDIENT", "name": "Ver<PERSON>elli Rainbow (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47d9", "itemId": "676aca53d253bdfa34d20679", "itemType": "INGREDIENT", "name": "Ver<PERSON>elli Rainbow (kg)", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47da", "itemId": "676aca53d253bdfa34d2067b", "itemType": "INGREDIENT", "name": "Virunga Mineral Water Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47db", "itemId": "676aca53d253bdfa34d2067b", "itemType": "INGREDIENT", "name": "Virunga Mineral Water Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47dc", "itemId": "676aca53d253bdfa34d2067b", "itemType": "INGREDIENT", "name": "Virunga Mineral Water Glass 33cl NEW", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47dd", "itemId": "676aca53d253bdfa34d20690", "itemType": "INGREDIENT", "name": "<PERSON><PERSON>z <PERSON>egar Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47de", "itemId": "676aca53d253bdfa34d20690", "itemType": "INGREDIENT", "name": "<PERSON><PERSON>z <PERSON>egar Small 30gr", "unit": "Unknown", "status": "Unknown"}, {"branchInventoryId": "6784f169b6183517c88f47df", "itemId": "676aca53d253bdfa34d20690", "itemType": "INGREDIENT", "name": "<PERSON><PERSON>z <PERSON>egar Small 30gr", "unit": "Unknown", "status": "Unknown"}]