import { NextRequest, NextResponse } from "next/server";

// Mock companies data that would typically come from MongoDB
// This simulates what we expect to find in the "test" database
const MOCK_COMPANIES = [
  {
    _id: "60f7b3b3b3b3b3b3b3b3b3b1",
    name: "Example Company",
    subdomain: "example-company",
    companyCode: "EX001",
    isActive: true,
  },
  {
    _id: "60f7b3b3b3b3b3b3b3b3b3b2", 
    name: "Demo Restaurant",
    subdomain: "demo-restaurant",
    companyCode: "DEMO01",
    isActive: true,
  },
  {
    _id: "60f7b3b3b3b3b3b3b3b3b3b3",
    name: "Test Kitchen",
    subdomain: "test-kitchen", 
    companyCode: "TEST01",
    isActive: true,
  }
];

export async function GET(req: NextRequest) {
  try {
    // Check API key
    const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
    if (apiKey !== "test_api_key_1") {
      const response = NextResponse.json(
        {
          success: false,
          message: "Invalid API key",
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      );

      // Add CORS headers
      response.headers.set('Access-Control-Allow-Origin', req.headers.get('origin') || '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');

      return response;
    }

    // Use mock data for now (simulating database response)
    console.log("Using mock companies data...");
    
    // Transform companies for the mobile app
    const companiesData = MOCK_COMPANIES
      .filter(company => company.isActive !== false) // Include active companies
      .map((company) => ({
        id: company._id,
        name: company.name,
        code: company.companyCode || company.subdomain,
      }));

    console.log(`Returning ${companiesData.length} companies:`, companiesData);

    const response = NextResponse.json({
      success: true,
      data: companiesData,
      message: "Using mock data (database credentials need to be configured)",
      timestamp: new Date().toISOString()
    });

    // Add CORS headers
    response.headers.set('Access-Control-Allow-Origin', req.headers.get('origin') || '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');

    return response;
  } catch (error: unknown) {
    console.error("Error in companies API:", error);
    const response = NextResponse.json(
      {
        success: false,
        message: "Internal server error: " + (error as Error).message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );

    // Add CORS headers even for errors
    response.headers.set('Access-Control-Allow-Origin', req.headers.get('origin') || '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');

    return response;
  }
}

export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
    },
  });
}