{"name": "web", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "clean": "rm -rf .next dist"}, "dependencies": {"@foodprepai/shared-types": "*", "@foodprepai/shared-utils": "*", "@foodprepai/api-client": "*", "@foodprepai/ui-components": "*", "@foodprepai/database-models": "*", "next": "15.1.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@foodprepai/eslint-config": "*", "@foodprepai/tsconfig": "*", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.57.0", "typescript": "^5.0.0"}}