# Camera Plugin Setup for Wastage Photo Capture

## Required Plugin

To enable photo capture functionality in the wastage recording feature, you need to install the Capacitor Camera plugin:

```bash
npm install @capacitor/camera
npx cap sync
```

## Permissions

### Android
Add these permissions to `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### iOS
Add these to `ios/App/App/Info.plist`:

```xml
<key>NSCameraUsageDescription</key>
<string>This app needs access to camera to take photos of wastage items for documentation.</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>This app needs access to photo library to save wastage documentation photos.</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs access to photo library to save wastage documentation photos.</string>
```

## Implementation

Once the plugin is installed, update the `takePhoto` function in the Wastage component:

```typescript
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';

const takePhoto = async () => {
  try {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.Uri,
      source: CameraSource.Camera,
    });

    if (image.webPath) {
      setFormData(prev => ({ ...prev, photoUrl: image.webPath }));
    }
  } catch (error) {
    console.error('Error taking photo:', error);
    setToast({
      show: true,
      message: 'Failed to take photo',
      color: 'danger',
    });
  }
};
```

## Current Status

The camera functionality is currently implemented as a placeholder. The actual photo capture will work once the Camera plugin is installed and configured as described above.

The barcode scanner functionality is already fully implemented using `@capacitor-community/barcode-scanner` which is already included in the project dependencies.