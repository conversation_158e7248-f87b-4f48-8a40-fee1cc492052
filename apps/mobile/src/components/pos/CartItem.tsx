import React from 'react';
import {
  IonItem,
  IonLabel,
  IonButton,
  IonIcon,
  IonChip,
  IonText,
  IonGrid,
  IonRow,
  IonCol,
} from '@ionic/react';
import {
  add,
  remove,
  trash,
  warning,
} from 'ionicons/icons';
import { POSCartItem } from '@/types/order';

interface CartItemProps {
  item: POSCartItem;
  onUpdateQuantity: (itemId: string, quantity: number, timestamp?: number) => void;
  onRemove: (itemId: string, timestamp?: number) => void;
  readonly?: boolean;
  showImage?: boolean;
  compact?: boolean;
}

export const CartItemComponent: React.FC<CartItemProps> = ({
  item,
  onUpdateQuantity,
  onRemove,
  readonly = false,
  showImage = false,
  compact = false,
}) => {
  const itemTotal = item.price * item.quantity;
  const modifierTotal = (item.modifiers || []).reduce((total, mod) => total + mod.price, 0) * item.quantity;
  const total = itemTotal + modifierTotal;

  const isLowStock = item.stockLevel !== undefined && item.stockLevel < 5;
  const isOutOfStock = item.stockLevel !== undefined && item.stockLevel === 0;

  const handleQuantityChange = (newQuantity: number) => {
    if (readonly) return;
    onUpdateQuantity(item.id, newQuantity, item.timestamp);
  };

  const handleRemove = () => {
    if (readonly) return;
    onRemove(item.id, item.timestamp);
  };

  if (compact) {
    return (
      <IonItem className="cart-item-compact">
        <IonLabel>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="font-medium text-sm">{item.name}</h3>
              <p className="text-xs text-gray-600">
                ${item.price.toFixed(2)} × {item.quantity}
              </p>
              {item.modifiers && item.modifiers.length > 0 && (
                <div className="text-xs text-gray-500 mt-1">
                  {item.modifiers.map(mod => mod.name).join(', ')}
                </div>
              )}
            </div>
            <div className="text-right">
              <div className="font-semibold text-sm">${total.toFixed(2)}</div>
              {(isLowStock || isOutOfStock) && (
                <IonChip color={isOutOfStock ? 'danger' : 'warning'} size="small">
                  <IonIcon icon={warning} />
                  <span className="ml-1 text-xs">
                    {isOutOfStock ? 'Out' : 'Low'}
                  </span>
                </IonChip>
              )}
            </div>
          </div>
        </IonLabel>
      </IonItem>
    );
  }

  return (
    <IonItem className="cart-item">
      <IonGrid className="px-0">
        <IonRow className="ion-align-items-center">
          {showImage && item.imageUrl && (
            <IonCol size="2" className="ion-text-center">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={item.imageUrl}
                alt={item.name}
                className="w-12 h-12 object-cover rounded"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </IonCol>
          )}
          
          <IonCol size={showImage && item.imageUrl ? "6" : "8"}>
            <IonLabel>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-base">{item.name}</h3>
                  <p className="text-sm text-gray-600">
                    ${item.price.toFixed(2)} each
                  </p>
                  
                  {item.modifiers && item.modifiers.length > 0 && (
                    <div className="mt-1">
                      {item.modifiers.map((modifier, index) => (
                        <IonChip key={index} color="light" size="small">
                          {modifier.name}
                          {modifier.price > 0 && ` (+$${modifier.price.toFixed(2)})`}
                        </IonChip>
                      ))}
                    </div>
                  )}
                  
                  {item.specialInstructions && (
                    <p className="text-xs text-gray-500 mt-1 italic">
                      Note: {item.specialInstructions}
                    </p>
                  )}
                  
                  {item.stockLevel !== undefined && (
                    <div className="flex items-center mt-1">
                      <span className="text-xs text-gray-500 mr-2">
                        Stock: {item.stockLevel}
                      </span>
                      {(isLowStock || isOutOfStock) && (
                        <IonChip color={isOutOfStock ? 'danger' : 'warning'} size="small">
                          <IonIcon icon={warning} />
                          <span className="ml-1">
                            {isOutOfStock ? 'Out of Stock' : 'Low Stock'}
                          </span>
                        </IonChip>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </IonLabel>
          </IonCol>
          
          <IonCol size="4" className="ion-text-right">
            {!readonly && (
              <div className="flex items-center justify-end mb-2">
                <IonButton
                  fill="clear"
                  size="small"
                  onClick={() => handleQuantityChange(item.quantity - 1)}
                  disabled={item.quantity <= 1}
                >
                  <IonIcon icon={remove} />
                </IonButton>
                
                <span className="mx-2 font-medium min-w-[30px] text-center">
                  {item.quantity}
                </span>
                
                <IonButton
                  fill="clear"
                  size="small"
                  onClick={() => handleQuantityChange(item.quantity + 1)}
                  disabled={isOutOfStock || (item.stockLevel !== undefined && item.quantity >= item.stockLevel)}
                >
                  <IonIcon icon={add} />
                </IonButton>
                
                <IonButton
                  fill="clear"
                  size="small"
                  color="danger"
                  onClick={handleRemove}
                  className="ml-2"
                >
                  <IonIcon icon={trash} />
                </IonButton>
              </div>
            )}
            
            <div className="text-right">
              <IonText color="primary">
                <strong>${total.toFixed(2)}</strong>
              </IonText>
              {modifierTotal > 0 && (
                <div className="text-xs text-gray-500">
                  Base: ${itemTotal.toFixed(2)}
                  <br />
                  Add-ons: ${modifierTotal.toFixed(2)}
                </div>
              )}
            </div>
          </IonCol>
        </IonRow>
      </IonGrid>
    </IonItem>
  );
};