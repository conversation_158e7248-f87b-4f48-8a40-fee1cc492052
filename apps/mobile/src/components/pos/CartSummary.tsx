import React from 'react';
import {
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon,
  IonText,
  IonChip,
  IonBadge,
} from '@ionic/react';
import {
  cart,
  card,
  cash,
  warning,
  checkmarkCircle,
} from 'ionicons/icons';
import { POSCartItem } from '@/types/order';

interface CartSummaryProps {
  items: POSCartItem[];
  subtotal: number;
  tax: number;
  total: number;
  taxRate?: number;
  onCheckout?: () => void;
  onClearCart?: () => void;
  checkoutDisabled?: boolean;
  errors?: string[];
  className?: string;
  showDetailedBreakdown?: boolean;
}

export const CartSummary: React.FC<CartSummaryProps> = ({
  items,
  subtotal,
  tax,
  total,
  taxRate = 8,
  onCheckout,
  onClearCart,
  checkoutDisabled = false,
  errors = [],
  className = '',
  showDetailedBreakdown = true,
}) => {
  const itemCount = items.reduce((count, item) => count + item.quantity, 0);
  const hasErrors = errors.length > 0;
  const hasStockIssues = items.some(item => 
    item.stockLevel !== undefined && (item.stockLevel === 0 || item.stockLevel < item.quantity)
  );

  return (
    <IonCard className={`cart-summary ${className}`}>
      <IonCardContent>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <IonIcon icon={cart} className="text-xl mr-2" />
            <h3 className="text-lg font-semibold">Cart Summary</h3>
          </div>
          {itemCount > 0 && (
            <IonBadge color="primary">{itemCount} items</IonBadge>
          )}
        </div>

        {/* Empty cart state */}
        {items.length === 0 && (
          <div className="text-center py-8">
            <IonIcon icon={cart} className="text-4xl text-gray-400 mb-2" />
            <p className="text-gray-500">Your cart is empty</p>
            <p className="text-sm text-gray-400">Add items to get started</p>
          </div>
        )}

        {/* Cart contents */}
        {items.length > 0 && (
          <>
            {/* Quick item list */}
            {!showDetailedBreakdown && (
              <div className="mb-4">
                {items.map((item, _index) => (
                  <div key={`${item.id}-${item.timestamp}`} className="flex justify-between items-center py-1">
                    <span className="text-sm">
                      {item.quantity}× {item.name}
                    </span>
                    <span className="text-sm font-medium">
                      ${(item.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {/* Price breakdown */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">${subtotal.toFixed(2)}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Tax ({taxRate}%):</span>
                <span className="font-medium">${tax.toFixed(2)}</span>
              </div>
              
              <hr className="my-2" />
              
              <div className="flex justify-between items-center text-lg">
                <span className="font-semibold">Total:</span>
                <IonText color="primary">
                  <strong>${total.toFixed(2)}</strong>
                </IonText>
              </div>
            </div>

            {/* Error messages */}
            {hasErrors && (
              <div className="mb-4">
                {errors.map((error, index) => (
                  <IonChip key={index} color="danger" className="mb-1 mr-1">
                    <IonIcon icon={warning} />
                    <span className="ml-1 text-xs">{error}</span>
                  </IonChip>
                ))}
              </div>
            )}

            {/* Stock warnings */}
            {hasStockIssues && (
              <div className="mb-4">
                <IonChip color="warning">
                  <IonIcon icon={warning} />
                  <span className="ml-1 text-xs">Some items have stock issues</span>
                </IonChip>
              </div>
            )}

            {/* Action buttons */}
            <div className="space-y-2">
              {onCheckout && (
                <IonButton
                  expand="block"
                  onClick={onCheckout}
                  disabled={checkoutDisabled || hasErrors || hasStockIssues}
                  className="font-medium"
                >
                  <IonIcon icon={card} slot="start" />
                  Proceed to Payment
                </IonButton>
              )}
              
              {onClearCart && (
                <IonButton
                  expand="block"
                  fill="outline"
                  color="medium"
                  onClick={onClearCart}
                  size="small"
                >
                  Clear Cart
                </IonButton>
              )}
            </div>

            {/* Payment method hints */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500 text-center mb-2">Accepted Payment Methods</p>
              <div className="flex justify-center space-x-4">
                <div className="flex items-center text-xs text-gray-400">
                  <IonIcon icon={cash} className="mr-1" />
                  Cash
                </div>
                <div className="flex items-center text-xs text-gray-400">
                  <IonIcon icon={card} className="mr-1" />
                  Card
                </div>
                <div className="flex items-center text-xs text-gray-400">
                  <IonIcon icon={checkmarkCircle} className="mr-1" />
                  Digital
                </div>
              </div>
            </div>
          </>
        )}
      </IonCardContent>
    </IonCard>
  );
};