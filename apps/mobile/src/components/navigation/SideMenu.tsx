import React from 'react';
import {
  IonMenu,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonIcon,
  IonLabel,
  IonMenuToggle
} from '@ionic/react';
import {
  home,
  storefront,
  cube,
  receipt,
  settings,
  people,
  logOut,
  cart,
  car,
  person
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { useEnhancedAuth } from '../../contexts/EnhancedAuthContext';

const SideMenu: React.FC = () => {
  const history = useHistory();
  const { logout, currentUser } = useEnhancedAuth();

  const menuItems = [
    { title: 'Dashboard', path: '/pos/dashboard', icon: home },
    { title: 'New Order', path: '/pos/order-entry', icon: storefront },
    { title: 'Shop Portal', path: '/shop-portal', icon: cart },
    { title: 'Order History', path: '/ordering', icon: receipt },
    { title: 'Inventory', path: '/cube', icon: cube },
    { title: '<PERSON><PERSON><PERSON>', path: '/settings', icon: settings },
  ];

  // Add delivery functions for dispatch/driver roles
  const deliveryMenuItems = [
    { title: 'Dispatch Handover', path: '/delivery/dispatch', icon: person, roles: ['dispatcher', 'manager', 'admin'] },
    { title: 'Driver Dashboard', path: '/delivery/driver', icon: car, roles: ['driver', 'manager', 'admin'] },
  ];

  // Add staff management for managers and admins
  if (currentUser?.role === 'manager' || currentUser?.role === 'admin') {
    menuItems.splice(-1, 0, { title: 'Staff', path: '/staff', icon: people });
  }

  // Filter delivery items based on user role
  const visibleDeliveryItems = deliveryMenuItems.filter(item => 
    !item.roles || item.roles.includes(currentUser?.role || '')
  );

  const personleLogout = async () => {
    await logout();
    history.push('/login');
  };

  return (
    <IonMenu contentId="main-content">
      <IonHeader>
        <IonToolbar color="primary">
          <IonTitle>FoodPrepAI POS</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent>
        <IonList>
          {/* Main Menu Items */}
          {menuItems.map((item) => (
            <IonMenuToggle key={item.path} autoHide={false}>
              <IonItem
                button
                onClick={() => history.push(item.path)}
                routerLink={item.path}
                routerDirection="root"
              >
                <IonIcon slot="start" icon={item.icon} />
                <IonLabel>{item.title}</IonLabel>
              </IonItem>
            </IonMenuToggle>
          ))}
          
          {/* Delivery Functions (role-based) */}
          {visibleDeliveryItems.length > 0 && (
            <>
              <IonItem>
                <IonLabel>
                  <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                    Delivery Operations
                  </h3>
                </IonLabel>
              </IonItem>
              {visibleDeliveryItems.map((item) => (
                <IonMenuToggle key={item.path} autoHide={false}>
                  <IonItem
                    button
                    onClick={() => history.push(item.path)}
                    routerLink={item.path}
                    routerDirection="root"
                  >
                    <IonIcon slot="start" icon={item.icon} />
                    <IonLabel>{item.title}</IonLabel>
                  </IonItem>
                </IonMenuToggle>
              ))}
            </>
          )}
          
          <IonMenuToggle autoHide={false}>
            <IonItem button onClick={personleLogout}>
              <IonIcon slot="start" icon={logOut} />
              <IonLabel>Logout</IonLabel>
            </IonItem>
          </IonMenuToggle>
        </IonList>
      </IonContent>
    </IonMenu>
  );
};

export default SideMenu;