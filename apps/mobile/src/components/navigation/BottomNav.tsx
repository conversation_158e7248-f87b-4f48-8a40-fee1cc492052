import React from 'react';
import { IonTabBar, IonTabButton, IonIcon, IonLabel } from '@ionic/react';
import { storefront, cube, receipt, settings } from 'ionicons/icons';
import { useLocation, useHistory } from 'react-router-dom';

const BottomNav: React.FC = () => {
  const location = useLocation();
  const history = useHistory();

  const isActive = (path: string) => location.pathname.startsWith(path);

  return (
    <IonTabBar slot="bottom">
      <IonTabButton
        tab="pos"
        href="/pos/dashboard"
        selected={isActive('/pos')}
        onClick={(e) => {
          e.preventDefault();
          history.push('/pos/dashboard');
        }}
      >
        <IonIcon icon={storefront} />
        <IonLabel>POS</IonLabel>
      </IonTabButton>

      <IonTabButton
        tab="cube"
        href="/cube"
        selected={isActive('/cube')}
        onClick={(e) => {
          e.preventDefault();
          history.push('/cube');
        }}
      >
        <IonIcon icon={cube} />
        <IonLabel>Inventory</IonLabel>
      </IonTabButton>

      <IonTabButton
        tab="orders"
        href="/ordering"
        selected={isActive('/ordering')}
        onClick={(e) => {
          e.preventDefault();
          history.push('/ordering');
        }}
      >
        <IonIcon icon={receipt} />
        <IonLabel>Orders</IonLabel>
      </IonTabButton>

      <IonTabButton
        tab="settings"
        href="/settings"
        selected={isActive('/settings')}
        onClick={(e) => {
          e.preventDefault();
          history.push('/settings');
        }}
      >
        <IonIcon icon={settings} />
        <IonLabel>Settings</IonLabel>
      </IonTabButton>
    </IonTabBar>
  );
};

export default BottomNav;