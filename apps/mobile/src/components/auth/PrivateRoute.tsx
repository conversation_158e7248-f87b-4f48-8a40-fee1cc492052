import React from 'react';
import { Redirect, Route, RouteProps } from 'react-router-dom';
import { useEnhancedAuth } from '../../contexts/EnhancedAuthContext';
import { LoadingSpinner } from '../layout/LoadingSpinner';

interface PrivateRouteProps extends Omit<RouteProps, 'component' | 'render'> {
  component: React.ComponentType<Record<string, unknown>>;
  requireRole?: 'cashier' | 'manager' | 'admin';
  requireManagerPrivileges?: boolean;
  requireAdminPrivileges?: boolean;
}

export const PrivateRoute: React.FC<PrivateRouteProps> = ({
  component: Component,
  requireRole,
  requireManagerPrivileges,
  requireAdminPrivileges,
  ...rest
}) => {
  const { 
    isAuthenticated, 
    currentUser, 
    company, 
    location, 
    isSyncing 
  } = useEnhancedAuth();

  return (
    <Route
      {...rest}
      render={(props) => {
        // Show loading spinner during initial sync
        if (isSyncing && !isAuthenticated) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <LoadingSpinner />
              <span className="ml-2 text-gray-600">Syncing data...</span>
            </div>
          );
        }

        // Check if user is authenticated
        if (!isAuthenticated || !currentUser) {
          return <Redirect to="/login" />;
        }

        // Check if company and location are selected
        if (!company || !location) {
          return <Redirect to="/login" />;
        }

        // Check role-based access
        if (requireRole && currentUser.role !== requireRole) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Access Denied
                </h2>
                <p className="text-gray-600">
                  You don&apos;t have permission to access this page.
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Required role: {requireRole}
                </p>
              </div>
            </div>
          );
        }

        // Check manager privileges
        if (requireManagerPrivileges && !hasManagerPrivileges(currentUser)) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Access Denied
                </h2>
                <p className="text-gray-600">
                  You need manager or admin privileges to access this page.
                </p>
              </div>
            </div>
          );
        }

        // Check admin privileges
        if (requireAdminPrivileges && !hasAdminPrivileges(currentUser)) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Access Denied
                </h2>
                <p className="text-gray-600">
                  You need admin privileges to access this page.
                </p>
              </div>
            </div>
          );
        }

        // Render the component if all checks pass
        return <Component {...props} />;
      }}
    />
  );
};

// Helper functions
const hasManagerPrivileges = (user: { role?: string }): boolean => {
  return user.role === 'manager' || user.role === 'admin';
};

const hasAdminPrivileges = (user: { role?: string }): boolean => {
  return user.role === 'admin';
};

export default PrivateRoute;