import React from 'react';
import { IonButton, IonIcon, IonSpinner } from '@ionic/react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  onPress?: () => void; // Alternative prop name for consistency
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'ghost' | 'outline';
  size?: 'small' | 'default' | 'large';
  icon?: string;
  iconPosition?: 'start' | 'end';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  fullWidth?: boolean;
  strong?: boolean;
  color?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  onPress,
  type = 'button',
  variant = 'primary',
  size = 'default',
  icon,
  iconPosition = 'start',
  disabled = false,
  loading = false,
  className = '',
  fullWidth = false,
  strong = false,
  color,
}) => {
  const handleClick = onClick || onPress;

  const getVariantProps = () => {
    switch (variant) {
      case 'secondary':
        return { fill: 'outline' as const, color: 'secondary' };
      case 'danger':
        return { fill: 'solid' as const, color: 'danger' };
      case 'success':
        return { fill: 'solid' as const, color: 'success' };
      case 'warning':
        return { fill: 'solid' as const, color: 'warning' };
      case 'ghost':
        return { fill: 'clear' as const };
      case 'outline':
        return { fill: 'outline' as const };
      default:
        return { fill: 'solid' as const, color: color || 'primary' };
    }
  };

  const getSizeProps = () => {
    switch (size) {
      case 'small':
        return 'small';
      case 'large':
        return 'large';
      default:
        return 'default';
    }
  };

  const variantProps = getVariantProps();

  return (
    <IonButton
      type={type}
      onClick={handleClick}
      className={`button-${variant} button-${size} ${className}`}
      disabled={disabled || loading}
      expand={fullWidth ? 'block' : undefined}
      strong={strong}
      size={getSizeProps()}
      fill={variantProps.fill}
      color={variantProps.color}
      aria-disabled={disabled || loading}
    >
      {loading ? (
        <IonSpinner name="crescent" />
      ) : (
        <>
          {icon && iconPosition === 'start' && <IonIcon icon={icon} slot="start" />}
          {children}
          {icon && iconPosition === 'end' && <IonIcon icon={icon} slot="end" />}
        </>
      )}
    </IonButton>
  );
};

export default Button;