import React from 'react';
import { IonSpinner } from '@ionic/react';

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  color?: string;
  message?: string;
  fullscreen?: boolean;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  color = 'primary',
  message,
  fullscreen = false,
  className = '',
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'w-4 h-4';
      case 'large':
        return 'w-12 h-12';
      default:
        return 'w-8 h-8';
    }
  };

  const content = (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <IonSpinner 
        name="crescent" 
        color={color}
        className={getSizeClass()}
      />
      {message && (
        <p className={`mt-3 text-gray-600 ${size === 'small' ? 'text-sm' : 'text-base'}`}>
          {message}
        </p>
      )}
    </div>
  );

  if (fullscreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
};

export default LoadingSpinner;