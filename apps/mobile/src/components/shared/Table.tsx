import React, { useState, useMemo } from 'react';
import {
  IonList,
  IonItem,
  IonLabel,
  IonSkeletonText,
  IonSearchbar,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonIcon,
  IonChip,
  IonToolbar,
  IonButtons,
  IonGrid,
  IonRow,
  IonCol,
} from '@ionic/react';
import { funnel, swapVertical, chevronUp, chevronDown } from 'ionicons/icons';

export interface Column<T> {
  key: keyof T;
  header: string;
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: T[keyof T], item: T) => React.ReactNode;
}

export interface FilterOption {
  key: string;
  label: string;
  values: { value: string; label: string }[];
}

interface TableProps<T> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  onRowClick?: (item: T) => void;
  className?: string;
  emptyMessage?: string;
  loadingRows?: number;
  searchable?: boolean;
  searchPlaceholder?: string;
  searchFields?: (keyof T)[];
  filterOptions?: FilterOption[];
  enableSorting?: boolean;
  onExport?: () => void;
  onRefresh?: () => void;
}

interface SortState {
  field: string | null;
  direction: 'asc' | 'desc';
}

function Table<T extends { id?: string | number }>({
  columns,
  data,
  loading = false,
  onRowClick,
  className = '',
  emptyMessage = 'No data available',
  loadingRows = 3,
  searchable = false,
  searchPlaceholder = 'Search...',
  searchFields,
  filterOptions = [],
  enableSorting = false,
  onExport,
  onRefresh,
}: TableProps<T>) {
  const [searchText, setSearchText] = useState('');
  const [activeFilters, setActiveFilters] = useState<Record<string, string>>({});
  const [sortState, setSortState] = useState<SortState>({ field: null, direction: 'asc' });
  const [showFilters, setShowFilters] = useState(false);

  // Filter and search data
  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply text search
    if (searchText && searchFields) {
      const searchLower = searchText.toLowerCase();
      result = result.filter((item) =>
        searchFields.some((field) => {
          const value = item[field];
          return value && String(value).toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply filters
    Object.entries(activeFilters).forEach(([filterKey, filterValue]) => {
      if (filterValue && filterValue !== 'all') {
        result = result.filter((item) => {
          const value = item[filterKey as keyof T];
          return String(value) === filterValue;
        });
      }
    });

    // Apply sorting
    if (sortState.field && enableSorting) {
      result.sort((a, b) => {
        const aValue = a[sortState.field as keyof T];
        const bValue = b[sortState.field as keyof T];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
        
        return sortState.direction === 'desc' ? comparison * -1 : comparison;
      });
    }

    return result;
  }, [data, searchText, searchFields, activeFilters, sortState, enableSorting]);

  const handleSort = (field: string) => {
    if (!enableSorting) return;
    
    setSortState((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handleFilterChange = (filterKey: string, value: string) => {
    setActiveFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  const clearFilters = () => {
    setActiveFilters({});
    setSearchText('');
  };

  const getSortIcon = (columnKey: string) => {
    if (sortState.field !== columnKey) return swapVertical;
    return sortState.direction === 'asc' ? chevronUp : chevronDown;
  };

  if (loading) {
    return (
      <div className={`table-container ${className}`}>
        {(searchable || filterOptions.length > 0) && (
          <div className="table-controls p-4 border-b">
            {searchable && (
              <IonSkeletonText animated style={{ width: '100%', height: '40px' }} />
            )}
          </div>
        )}
        <IonList>
          {Array.from({ length: loadingRows }).map((_, index) => (
            <IonItem key={`loading-${index}`}>
              {columns.map((column, colIndex) => (
                <IonLabel key={`loading-col-${colIndex}`} style={{ width: column.width }}>
                  <IonSkeletonText animated style={{ width: '70%' }} />
                </IonLabel>
              ))}
            </IonItem>
          ))}
        </IonList>
      </div>
    );
  }

  return (
    <div className={`table-container ${className}`}>
      {/* Search and Filter Controls */}
      {(searchable || filterOptions.length > 0) && (
        <div className="table-controls p-4 border-b">
          <IonGrid>
            <IonRow>
              {searchable && (
                <IonCol size="12" sizeMd="6">
                  <IonSearchbar
                    value={searchText}
                    onIonInput={(e) => setSearchText(e.detail.value!)}
                    placeholder={searchPlaceholder}
                    showClearButton="focus"
                  />
                </IonCol>
              )}
              {filterOptions.length > 0 && (
                <IonCol size="12" sizeMd="6">
                  <IonToolbar>
                    <IonButtons slot="start">
                      <IonButton
                        fill="outline"
                        onClick={() => setShowFilters(!showFilters)}
                      >
                        <IonIcon icon={funnel} slot="start" />
                        Filters
                      </IonButton>
                    </IonButtons>
                    <IonButtons slot="end">
                      {Object.keys(activeFilters).length > 0 && (
                        <IonButton fill="clear" onClick={clearFilters}>
                          Clear
                        </IonButton>
                      )}
                      {onRefresh && (
                        <IonButton fill="clear" onClick={onRefresh}>
                          Refresh
                        </IonButton>
                      )}
                      {onExport && (
                        <IonButton fill="outline" onClick={onExport}>
                          Export
                        </IonButton>
                      )}
                    </IonButtons>
                  </IonToolbar>
                </IonCol>
              )}
            </IonRow>
            
            {/* Filter Options */}
            {showFilters && filterOptions.length > 0 && (
              <IonRow className="mt-2">
                {filterOptions.map((filter) => (
                  <IonCol key={filter.key} size="12" sizeMd="4">
                    <IonSelect
                      placeholder={filter.label}
                      value={activeFilters[filter.key] || 'all'}
                      onSelectionChange={(e) =>
                        handleFilterChange(filter.key, e.detail.value)
                      }
                    >
                      <IonSelectOption value="all">All {filter.label}</IonSelectOption>
                      {filter.values.map((option) => (
                        <IonSelectOption key={option.value} value={option.value}>
                          {option.label}
                        </IonSelectOption>
                      ))}
                    </IonSelect>
                  </IonCol>
                ))}
              </IonRow>
            )}
            
            {/* Active Filters Display */}
            {Object.entries(activeFilters).some(([_, value]) => value && value !== 'all') && (
              <IonRow className="mt-2">
                <IonCol>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(activeFilters).map(([key, value]) => {
                      if (!value || value === 'all') return null;
                      const filter = filterOptions.find(f => f.key === key);
                      const option = filter?.values.find(v => v.value === value);
                      return (
                        <IonChip
                          key={key}
                          color="primary"
                          outline
                          onClick={() => handleFilterChange(key, 'all')}
                        >
                          {filter?.label}: {option?.label || value}
                        </IonChip>
                      );
                    })}
                  </div>
                </IonCol>
              </IonRow>
            )}
          </IonGrid>
        </div>
      )}

      {/* Table Content */}
      {filteredData.length === 0 ? (
        <IonList>
          <IonItem>
            <IonLabel className="ion-text-center">{emptyMessage}</IonLabel>
          </IonItem>
        </IonList>
      ) : (
        <IonList>
          {/* Header Row */}
          <IonItem className="header-row">
            {columns.map((column) => (
              <IonLabel
                key={String(column.key)}
                style={{ width: column.width }}
                className="font-medium text-sm"
                onClick={() => column.sortable && handleSort(String(column.key))}
              >
                <div className={`flex items-center ${column.sortable ? 'cursor-pointer hover:text-primary' : ''}`}>
                  {column.header}
                  {column.sortable && enableSorting && (
                    <IonIcon
                      icon={getSortIcon(String(column.key))}
                      className="ml-1"
                      size="small"
                    />
                  )}
                </div>
              </IonLabel>
            ))}
          </IonItem>
          
          {/* Data Rows */}
          {filteredData.map((item, index) => (
            <IonItem
              key={item.id || index}
              onClick={() => onRowClick?.(item)}
              button={!!onRowClick}
              className="data-row"
            >
              {columns.map((column) => (
                <IonLabel
                  key={`${String(column.key)}-${item.id || index}`}
                  style={{ width: column.width }}
                >
                  {column.render
                    ? column.render(item[column.key], item)
                    : String(item[column.key] || '')}
                </IonLabel>
              ))}
            </IonItem>
          ))}
        </IonList>
      )}
      
      {/* Results Summary */}
      {filteredData.length > 0 && (
        <div className="table-footer p-4 text-sm text-gray-600 border-t">
          Showing {filteredData.length} of {data.length} items
          {searchText && ` for "${searchText}"`}
        </div>
      )}
    </div>
  );
}

export default Table;