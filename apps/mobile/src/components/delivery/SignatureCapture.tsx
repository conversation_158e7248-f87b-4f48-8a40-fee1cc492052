import React, { useRef, useEffect, useState } from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonIcon,
  IonText,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonToast,
} from '@ionic/react';
import { 
  refresh, 
  checkmarkCircle, 
  createOutline,
  personOutline,
  timeOutline
} from 'ionicons/icons';

interface SignatureCaptureProps {
  title: string;
  onSignatureCapture: (signatureData: SignatureData) => void;
  disabled?: boolean;
  existingSignature?: SignatureData;
  roleLabel?: string; // e.g., "Dispatcher", "Driver", "Shop Manager"
}

export interface SignatureData {
  signature: string; // Base64 encoded image
  signerName: string;
  signerTitle?: string;
  timestamp: Date;
  notes?: string;
}

const SignatureCapture: React.FC<SignatureCaptureProps> = ({
  title,
  onSignatureCapture,
  disabled = false,
  existingSignature,
  roleLabel = "Signer"
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [signerName, setSignerName] = useState('');
  const [signerTitle, setSignerTitle] = useState('');
  const [notes, setNotes] = useState('');
  const [hasSignature, setHasSignature] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  useEffect(() => {
    if (existingSignature) {
      setSignerName(existingSignature.signerName);
      setSignerTitle(existingSignature.signerTitle || '');
      setNotes(existingSignature.notes || '');
      setHasSignature(true);
      // Load existing signature on canvas
      loadSignatureOnCanvas(existingSignature.signature);
    }
  }, [existingSignature]);

  const loadSignatureOnCanvas = (base64Signature: string) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
    };
    img.src = base64Signature;
  };

  const setupCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Set drawing styles
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Clear canvas with white background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  useEffect(() => {
    setupCanvas();
  }, []);

  const getMousePos = (e: MouseEvent | TouchEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    return {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  };

  const startDrawing = (e: MouseEvent | TouchEvent) => {
    if (disabled) return;
    
    setIsDrawing(true);
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!ctx) return;

    const pos = getMousePos(e);
    ctx.beginPath();
    ctx.moveTo(pos.x, pos.y);
    e.preventDefault();
  };

  const draw = (e: MouseEvent | TouchEvent) => {
    if (!isDrawing || disabled) return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!ctx) return;

    const pos = getMousePos(e);
    ctx.lineTo(pos.x, pos.y);
    ctx.stroke();
    setHasSignature(true);
    e.preventDefault();
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
  };

  const captureSignature = () => {
    if (!hasSignature) {
      setToastMessage('Please provide a signature');
      setShowToast(true);
      return;
    }

    if (!signerName.trim()) {
      setToastMessage('Please enter signer name');
      setShowToast(true);
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    const signatureData: SignatureData = {
      signature: canvas.toDataURL('image/png'),
      signerName: signerName.trim(),
      signerTitle: signerTitle.trim() || undefined,
      timestamp: new Date(),
      notes: notes.trim() || undefined,
    };

    onSignatureCapture(signatureData);
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleString();
  };

  // Touch event handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    startDrawing(e.nativeEvent);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    draw(e.nativeEvent);
  };

  const handleTouchEnd = () => {
    stopDrawing();
  };

  // Mouse event handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    startDrawing(e.nativeEvent);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    draw(e.nativeEvent);
  };

  const handleMouseUp = () => {
    stopDrawing();
  };

  const handleMouseLeave = () => {
    stopDrawing();
  };

  return (
    <IonCard>
      <IonCardHeader>
        <IonCardTitle className="flex items-center">
          <IonIcon icon={createOutline} className="mr-2" />
          {title}
        </IonCardTitle>
      </IonCardHeader>
      
      <IonCardContent>
        {/* Signer Information */}
        <div className="mb-4 space-y-3">
          <IonItem>
            <IonIcon icon={personOutline} slot="start" />
            <IonLabel position="stacked">{roleLabel} Name *</IonLabel>
            <IonInput
              value={signerName}
              onIonInput={(e) => setSignerName(e.detail.value!)}
              placeholder="Enter full name"
              disabled={disabled}
              required
            />
          </IonItem>

          <IonItem>
            <IonLabel position="stacked">Title/Position</IonLabel>
            <IonInput
              value={signerTitle}
              onIonInput={(e) => setSignerTitle(e.detail.value!)}
              placeholder="e.g., Store Manager, Driver"
              disabled={disabled}
            />
          </IonItem>

          <IonItem>
            <IonLabel position="stacked">Notes (Optional)</IonLabel>
            <IonTextarea
              value={notes}
              onIonInput={(e) => setNotes(e.detail.value!)}
              placeholder="Additional notes or comments"
              rows={2}
              disabled={disabled}
            />
          </IonItem>
        </div>

        {/* Signature Canvas */}
        <div className="mb-4">
          <IonText>
            <p className="text-sm font-medium mb-2">Digital Signature *</p>
          </IonText>
          <div className="border-2 border-gray-300 rounded-lg relative">
            <canvas
              ref={canvasRef}
              className={`w-full h-48 ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-crosshair'}`}
              style={{ touchAction: 'none' }}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseLeave}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            />
            {!hasSignature && !disabled && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <IonText color="medium">
                  <p className="text-sm">Sign here</p>
                </IonText>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <IonButton
            fill="outline"
            onClick={clearSignature}
            disabled={disabled || !hasSignature}
            className="flex-1"
          >
            <IonIcon icon={refresh} slot="start" />
            Clear
          </IonButton>
          <IonButton
            onClick={captureSignature}
            disabled={disabled || !hasSignature || !signerName.trim()}
            className="flex-1"
          >
            <IonIcon icon={checkmarkCircle} slot="start" />
            Confirm Signature
          </IonButton>
        </div>

        {/* Existing Signature Display */}
        {existingSignature && (
          <div className="mt-4 p-3 bg-green-50 rounded-lg">
            <div className="flex items-center mb-2">
              <IonIcon icon={checkmarkCircle} color="success" className="mr-2" />
              <IonText color="success">
                <span className="font-medium">Signature Captured</span>
              </IonText>
            </div>
            <div className="text-sm space-y-1">
              <p><strong>Signed by:</strong> {existingSignature.signerName}</p>
              {existingSignature.signerTitle && (
                <p><strong>Title:</strong> {existingSignature.signerTitle}</p>
              )}
              <p className="flex items-center">
                <IonIcon icon={timeOutline} className="mr-1" />
                <strong>Time:</strong> {formatTimestamp(existingSignature.timestamp)}
              </p>
              {existingSignature.notes && (
                <p><strong>Notes:</strong> {existingSignature.notes}</p>
              )}
            </div>
          </div>
        )}

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color="warning"
          duration={3000}
          position="top"
        />
      </IonCardContent>
    </IonCard>
  );
};

export default SignatureCapture;