import React from 'react';
import { 
  IonButton, 
  IonContent, 
  IonHeader, 
  IonModal, 
  IonTitle, 
  IonToolbar,
  IonIcon
} from '@ionic/react';
import { checkmark, close } from 'ionicons/icons';
import Card from '../layout/Card';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger' | 'warning' | 'success';
  loading?: boolean;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  loading = false,
}) => {
  const getConfirmColor = () => {
    switch (variant) {
      case 'danger':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return 'primary';
    }
  };

  const getIcon = () => {
    switch (variant) {
      case 'danger':
        return close;
      case 'success':
        return checkmark;
      default:
        return undefined;
    }
  };

  const handleConfirm = () => {
    if (!loading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <IonModal
      isOpen={isOpen}
      onDidDismiss={handleCancel}
      className="confirmation-dialog"
      aria-labelledby="confirmation-title"
      aria-describedby="confirmation-message"
      backdropDismiss={!loading}
    >
      <IonHeader>
        <IonToolbar>
          <IonTitle id="confirmation-title">{title}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <Card>
          <div className="text-center mb-6">
            {getIcon() && (
              <div className="mb-4">
                <IonIcon 
                  icon={getIcon()} 
                  className={`text-4xl text-${getConfirmColor()}`}
                />
              </div>
            )}
            <p id="confirmation-message" className="text-base text-gray-700">
              {message}
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-end gap-3">
            <IonButton 
              fill="outline" 
              onClick={handleCancel}
              disabled={loading}
              className="order-2 sm:order-1"
            >
              {cancelText}
            </IonButton>
            <IonButton 
              onClick={handleConfirm}
              color={getConfirmColor()}
              strong={true}
              disabled={loading}
              className="order-1 sm:order-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                confirmText
              )}
            </IonButton>
          </div>
        </Card>
      </IonContent>
    </IonModal>
  );
};

export default ConfirmationDialog;