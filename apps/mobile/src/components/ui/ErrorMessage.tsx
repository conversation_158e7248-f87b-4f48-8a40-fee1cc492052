import React from 'react';
import { IonCard, IonCardContent, IonIcon, IonButton } from '@ionic/react';
import { alertCircle, refresh } from 'ionicons/icons';

interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Error',
  message,
  onRetry,
  showRetryButton = true,
}) => {
  return (
    <IonCard className="mx-4 my-4">
      <IonCardContent>
        <div className="flex flex-col items-center text-center py-4">
          <IonIcon 
            icon={alertCircle} 
            className="text-red-500 mb-4" 
            style={{ fontSize: '3rem' }}
          />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 mb-4">{message}</p>
          {showRetryButton && onRetry && (
            <IonButton 
              fill="outline" 
              color="primary" 
              onClick={onRetry}
            >
              <IonIcon icon={refresh} slot="start" />
              Try Again
            </IonButton>
          )}
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default ErrorMessage;