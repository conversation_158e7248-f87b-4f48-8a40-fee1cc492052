import React from 'react';
import { <PERSON><PERSON><PERSON>, IonCard<PERSON>ontent, IonCard<PERSON>eader, IonCardTitle } from '@ionic/react';

interface CardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({ 
  children, 
  title, 
  subtitle, 
  className = '', 
  onClick,
  disabled = false 
}) => {
  return (
    <IonCard 
      className={`shadow-md transition-all duration-200 ${onClick ? 'cursor-pointer hover:shadow-lg' : ''} ${disabled ? 'opacity-50' : ''} ${className}`}
      onClick={!disabled ? onClick : undefined}
    >
      {(title || subtitle) && (
        <IonCardHeader>
          {title && <IonCardTitle>{title}</IonCardTitle>}
          {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
        </IonCardHeader>
      )}
      <IonCardContent>{children}</IonCardContent>
    </IonCard>
  );
};

export default Card;