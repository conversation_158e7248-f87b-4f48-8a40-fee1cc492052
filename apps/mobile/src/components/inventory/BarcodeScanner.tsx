import React, { useState, useEffect } from 'react';
import {
  IonButton,
  IonIcon,
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonAlert,
  IonProgressBar,
  IonCard,
  IonCardContent,
  IonInput,
  IonItem,
  IonLabel,
} from '@ionic/react';
import { scan, close, flashlight, cameraReverse, keypad } from 'ionicons/icons';

interface BarcodeScannerProps {
  isOpen: boolean;
  onDidDismiss: () => void;
  onBarcodeScanned: (barcode: string) => void;
  title?: string;
  allowManualEntry?: boolean;
  placeholder?: string;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  isOpen,
  onDidDismiss,
  onBarcodeScanned,
  title = 'Scan Barcode',
  allowManualEntry = true,
  placeholder = 'Enter barcode manually...',
}) => {
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [manualEntry, setManualEntry] = useState('');
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [cameraDirection, setCameraDirection] = useState<'front' | 'back'>('back');

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setError(null);
      setManualEntry('');
      setShowManualEntry(false);
      setFlashEnabled(false);
      setCameraDirection('back');
    } else {
      setIsScanning(false);
    }
  }, [isOpen]);

  // Start camera scanning
  const startScanning = async () => {
    try {
      setIsScanning(true);
      setError(null);

      // Import barcode service dynamically to avoid issues in web environment
      const { barcodeService } = await import('@/services/barcodeService');
      
      const result = await barcodeService.startScan({
        cameraDirection,
        prompt: 'Position barcode within the frame to scan',
      });

      if (result.success && result.data) {
        handleBarcodeDetected(result.data);
      } else {
        setError(result.error || 'Failed to scan barcode');
        setIsScanning(false);
      }

    } catch (err) {
      console.error('Error starting camera:', err);
      setError('Failed to start camera. Please check permissions.');
      setIsScanning(false);
    }
  };

  // Stop scanning
  const stopScanning = async () => {
    setIsScanning(false);
    try {
      const { barcodeService } = await import('@/services/barcodeService');
      await barcodeService.stopScan();
    } catch (err) {
      console.error('Error stopping scanner:', err);
    }
  };

  // Handle barcode detection
  const handleBarcodeDetected = (barcode: string) => {
    stopScanning();
    onBarcodeScanned(barcode);
    onDidDismiss();
  };

  // Handle manual entry
  const handleManualSubmit = () => {
    if (manualEntry.trim()) {
      onBarcodeScanned(manualEntry.trim());
      onDidDismiss();
    }
  };

  // Toggle flashlight
  const toggleFlash = async () => {
    try {
      const { barcodeService } = await import('@/services/barcodeService');
      const success = await barcodeService.toggleTorch();
      if (success) {
        setFlashEnabled(!flashEnabled);
      }
    } catch (err) {
      console.error('Error toggling flash:', err);
    }
  };

  // Switch camera
  const switchCamera = () => {
    setCameraDirection(prev => prev === 'front' ? 'back' : 'front');
    // Note: Camera switching requires restarting the scan with new direction
    if (isScanning) {
      stopScanning();
      // Auto-restart with new direction would happen when user clicks start again
    }
  };

  return (
    <>
      <IonModal isOpen={isOpen} onDidDismiss={onDidDismiss}>
        <IonHeader>
          <IonToolbar color="primary">
            <IonTitle>{title}</IonTitle>
            <IonButtons slot="end">
              <IonButton onClick={onDidDismiss}>
                <IonIcon icon={close} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>

        <IonContent>
          <div className="h-full flex flex-col">
            {/* Camera View Area */}
            <div className="flex-1 relative bg-black">
              {!isScanning && !showManualEntry ? (
                // Initial state - show start button
                <div className="h-full flex items-center justify-center">
                  <div className="text-center text-white">
                    <IonIcon icon={scan} className="text-6xl mb-4 opacity-60" />
                    <p className="text-lg mb-6">Position barcode within the frame</p>
                    <IonButton
                      size="large"
                      onClick={startScanning}
                      className="mb-4"
                    >
                      <IonIcon icon={scan} slot="start" />
                      Start Scanning
                    </IonButton>
                    {allowManualEntry && (
                      <IonButton
                        fill="outline"
                        onClick={() => setShowManualEntry(true)}
                      >
                        <IonIcon icon={keypad} slot="start" />
                        Enter Manually
                      </IonButton>
                    )}
                  </div>
                </div>
              ) : isScanning ? (
                // Scanning state
                <div className="h-full relative">
                  {/* Simulated camera view */}
                  <div className="h-full bg-gray-900 flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="w-64 h-64 border-2 border-white border-dashed rounded-lg flex items-center justify-center mb-4">
                        <div className="animate-pulse">
                          <IonIcon icon={scan} className="text-4xl" />
                        </div>
                      </div>
                      <p className="text-lg">Scanning for barcode...</p>
                      <IonProgressBar type="indeterminate" className="mt-4" />
                    </div>
                  </div>

                  {/* Camera controls overlay */}
                  <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-4">
                    <IonButton
                      fill="clear"
                      onClick={toggleFlash}
                      className={flashEnabled ? 'text-yellow-400' : 'text-white'}
                    >
                      <IonIcon icon={flashlight} />
                    </IonButton>
                    <IonButton fill="solid" color="danger" onClick={stopScanning}>
                      Stop
                    </IonButton>
                    <IonButton fill="clear" onClick={switchCamera} className="text-white">
                      <IonIcon icon={cameraReverse} />
                    </IonButton>
                  </div>
                </div>
              ) : showManualEntry ? (
                // Manual entry state
                <div className="h-full flex items-center justify-center p-6">
                  <IonCard className="w-full max-w-md">
                    <IonCardContent>
                      <div className="text-center mb-6">
                        <IonIcon icon={keypad} className="text-4xl text-primary mb-2" />
                        <h2 className="text-lg font-semibold">Manual Entry</h2>
                        <p className="text-gray-600">Enter the barcode manually</p>
                      </div>

                      <IonItem>
                        <IonLabel position="stacked">Barcode</IonLabel>
                        <IonInput
                          value={manualEntry}
                          onIonChange={e => setManualEntry(e.detail.value!)}
                          placeholder={placeholder}
                          clearInput
                          onKeyDown={e => {
                            if (e.key === 'Enter') {
                              handleManualSubmit();
                            }
                          }}
                        />
                      </IonItem>

                      <div className="flex gap-2 mt-6">
                        <IonButton
                          expand="block"
                          fill="outline"
                          onClick={() => setShowManualEntry(false)}
                        >
                          Back to Scanner
                        </IonButton>
                        <IonButton
                          expand="block"
                          onClick={handleManualSubmit}
                          disabled={!manualEntry.trim()}
                        >
                          Submit
                        </IonButton>
                      </div>
                    </IonCardContent>
                  </IonCard>
                </div>
              ) : null}
            </div>
          </div>
        </IonContent>
      </IonModal>

      {/* Error Alert */}
      <IonAlert
        isOpen={!!error}
        onDidDismiss={() => setError(null)}
        header="Scanner Error"
        message={error || ''}
        buttons={['OK']}
      />
    </>
  );
};

export default BarcodeScanner;