// Global type declarations for Ionic JSX elements
interface IonicComponentProps {
  [key: string]: unknown;
}

declare namespace JSX {
  interface IntrinsicElements {
    'ion-app': IonicComponentProps;
    'ion-page': IonicComponentProps;
    'ion-header': IonicComponentProps;
    'ion-toolbar': IonicComponentProps;
    'ion-title': IonicComponentProps;
    'ion-content': IonicComponentProps;
    'ion-button': IonicComponentProps;
    'ion-buttons': IonicComponentProps;
    'ion-icon': IonicComponentProps;
    'ion-item': IonicComponentProps;
    'ion-label': IonicComponentProps;
    'ion-list': IonicComponentProps;
    'ion-card': IonicComponentProps;
    'ion-card-header': IonicComponentProps;
    'ion-card-title': IonicComponentProps;
    'ion-card-content': IonicComponentProps;
    'ion-input': IonicComponentProps;
    'ion-textarea': IonicComponentProps;
    'ion-select': IonicComponentProps;
    'ion-select-option': IonicComponentProps;
    'ion-checkbox': IonicComponentProps;
    'ion-radio': IonicComponentProps;
    'ion-radio-group': IonicComponentProps;
    'ion-toggle': IonicComponentProps;
    'ion-range': IonicComponentProps;
    'ion-segment': IonicComponentProps;
    'ion-segment-button': IonicComponentProps;
    'ion-tab-bar': IonicComponentProps;
    'ion-tab-button': IonicComponentProps;
    'ion-tabs': IonicComponentProps;
    'ion-modal': IonicComponentProps;
    'ion-popover': IonicComponentProps;
    'ion-alert': IonicComponentProps;
    'ion-toast': IonicComponentProps;
    'ion-loading': IonicComponentProps;
    'ion-progress-bar': IonicComponentProps;
    'ion-spinner': IonicComponentProps;
    'ion-skeleton-text': IonicComponentProps;
    'ion-infinite-scroll': IonicComponentProps;
    'ion-infinite-scroll-content': IonicComponentProps;
    'ion-refresher': IonicComponentProps;
    'ion-refresher-content': IonicComponentProps;
    'ion-fab': IonicComponentProps;
    'ion-fab-button': IonicComponentProps;
    'ion-fab-list': IonicComponentProps;
    'ion-menu': IonicComponentProps;
    'ion-menu-button': IonicComponentProps;
    'ion-nav': IonicComponentProps;
    'ion-router': IonicComponentProps;
    'ion-router-outlet': IonicComponentProps;
    'ion-route': IonicComponentProps;
    'ion-route-redirect': IonicComponentProps;
    'ion-footer': IonicComponentProps;
    'ion-grid': IonicComponentProps;
    'ion-row': IonicComponentProps;
    'ion-col': IonicComponentProps;
    'ion-slide': IonicComponentProps;
    'ion-slides': IonicComponentProps;
    'ion-chip': IonicComponentProps;
    'ion-avatar': IonicComponentProps;
    'ion-thumbnail': IonicComponentProps;
    'ion-badge': IonicComponentProps;
    'ion-note': IonicComponentProps;
    'ion-text': IonicComponentProps;
    'ion-datetime': IonicComponentProps;
    'ion-picker': IonicComponentProps;
    'ion-searchbar': IonicComponentProps;
    'ion-reorder': IonicComponentProps;
    'ion-reorder-group': IonicComponentProps;
  }
}