import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonBadge,
  IonButton,
  IonCheckbox,
  IonSelect,
  IonSelectOption,
  IonSearchbar,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  IonText,
  IonGrid,
  IonRow,
  IonCol,
  IonToast,
  IonAlert,
  IonModal,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  qrCodeOutline,
  carOutline,
  checkmarkCircle,
  personOutline,
  timeOutline,
  documentTextOutline,
  cubeOutline,
  alertCircle,
  refreshOutline,
  scanOutline,
  handRightOutline
} from 'ionicons/icons';
import { useAuth } from '@/contexts/EnhancedAuthContext';
import { shopPortalService, DeliveryNote } from '@/services/shopPortalService';
import SignatureCapture, { SignatureData } from '@/components/delivery/SignatureCapture';
import { format } from 'date-fns';

interface Driver {
  id: string;
  name: string;
  phone: string;
  vehicleNumber: string;
  isActive: boolean;
}

const DispatchHandover: React.FC = () => {
  const { user, companyId, locationId } = useAuth();
  
  const [deliveryNotes, setDeliveryNotes] = useState<DeliveryNote[]>([]);
  const [selectedDeliveryNote, setSelectedDeliveryNote] = useState<DeliveryNote | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<string>('');
  const [drivers] = useState<Driver[]>([
    { id: '1', name: 'John Smith', phone: '+1234567890', vehicleNumber: 'VH-001', isActive: true },
    { id: '2', name: 'Mike Johnson', phone: '+1234567891', vehicleNumber: 'VH-002', isActive: true },
    { id: '3', name: 'Sarah Wilson', phone: '+1234567892', vehicleNumber: 'VH-003', isActive: true },
  ]);
  const [checkedItems, setCheckedItems] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [showHandoverModal, setShowHandoverModal] = useState(false);
  const [showConfirmAlert, setShowConfirmAlert] = useState(false);
  const [dispatcherSignature, setDispatcherSignature] = useState<SignatureData | null>(null);
  const [driverSignature, setDriverSignature] = useState<SignatureData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'warning' | 'danger'>('success');

  useEffect(() => {
    if (companyId && locationId) {
      shopPortalService.updateCredentials(companyId, locationId);
      fetchPendingDeliveryNotes();
    }
  }, [companyId, locationId]);

  const fetchPendingDeliveryNotes = async () => {
    try {
      setLoading(true);
      // Fetch delivery notes that are ready for dispatch
      const response = await shopPortalService.getDeliveryNotes({
        status: 'pending' // Ready for dispatch
      });
      
      if (response.success) {
        setDeliveryNotes(response.data || []);
      } else {
        showNotification('Failed to load delivery notes', 'danger');
      }
    } catch (error) {
      console.error('Error fetching delivery notes:', error);
      showNotification('Error loading delivery notes', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await fetchPendingDeliveryNotes();
    event.detail.complete();
  };

  const selectDeliveryNote = (deliveryNote: DeliveryNote) => {
    setSelectedDeliveryNote(deliveryNote);
    setCheckedItems(new Set());
    setSelectedDriver('');
    setDispatcherSignature(null);
    setDriverSignature(null);
    setShowHandoverModal(true);
  };

  const toggleItemCheck = (itemId: string, checked: boolean) => {
    const newCheckedItems = new Set(checkedItems);
    if (checked) {
      newCheckedItems.add(itemId);
    } else {
      newCheckedItems.delete(itemId);
    }
    setCheckedItems(newCheckedItems);
  };

  const handleDispatcherSignature = (signature: SignatureData) => {
    setDispatcherSignature(signature);
    showNotification('Dispatcher signature captured', 'success');
  };

  const handleDriverSignature = (signature: SignatureData) => {
    setDriverSignature(signature);
    showNotification('Driver signature captured', 'success');
  };

  const validateHandover = () => {
    if (!selectedDeliveryNote) return false;
    
    if (checkedItems.size !== selectedDeliveryNote.items.length) {
      showNotification('Please verify all items before handover', 'warning');
      return false;
    }
    
    if (!selectedDriver) {
      showNotification('Please select a driver', 'warning');
      return false;
    }
    
    if (!dispatcherSignature) {
      showNotification('Dispatcher signature required', 'warning');
      return false;
    }
    
    if (!driverSignature) {
      showNotification('Driver signature required', 'warning');
      return false;
    }
    
    return true;
  };

  const processHandover = async () => {
    if (!validateHandover() || !selectedDeliveryNote) return;

    try {
      setIsProcessing(true);
      
      const driver = drivers.find(d => d.id === selectedDriver);
      if (!driver) return;

      // In a real implementation, this would call the API to update delivery note status
      // and record the handover details including signatures
      const handoverData = {
        deliveryNoteId: selectedDeliveryNote.id,
        driverId: selectedDriver,
        driverName: driver.name,
        vehicleNumber: driver.vehicleNumber,
        dispatcherSignature,
        driverSignature,
        handoverTime: new Date().toISOString(),
        verifiedItems: Array.from(checkedItems),
        status: 'dispatched'
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showNotification('Delivery successfully handed over to driver', 'success');
      setShowHandoverModal(false);
      setShowConfirmAlert(false);
      
      // Refresh the delivery notes list
      await fetchPendingDeliveryNotes();
      
    } catch (error) {
      console.error('Error processing handover:', error);
      showNotification('Error processing handover', 'danger');
    } finally {
      setIsProcessing(false);
    }
  };

  const showNotification = (message: string, color: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatDateTime = (date: Date | string) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  const filteredDeliveryNotes = deliveryNotes.filter(note =>
    note.deliveryNoteNumber.toLowerCase().includes(searchText.toLowerCase()) ||
    note.orderId.toLowerCase().includes(searchText.toLowerCase())
  );

  const getSelectedDriver = () => drivers.find(d => d.id === selectedDriver);

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/delivery" />
          </IonButtons>
          <IonTitle>Dispatch Handover</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={fetchPendingDeliveryNotes}>
              <IonIcon icon={refreshOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={refreshOutline}
            pullingText="Pull to refresh"
            refreshingSpinner="circles"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        <div className="p-4">
          {/* Header Info */}
          <IonCard>
            <IonCardContent>
              <div className="text-center">
                <IonIcon icon={handRightOutline} style={{ fontSize: '48px' }} color="primary" />
                <h2 className="text-xl font-bold mt-2">Dispatch Handover</h2>
                <p className="text-gray-600">Hand over delivery packages to drivers</p>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Search */}
          <IonSearchbar
            value={searchText}
            onIonInput={(e) => setSearchText(e.detail.value!)}
            placeholder="Search delivery notes..."
            showClearButton="focus"
            className="mb-4"
          />

          {/* Pending Delivery Notes */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <IonSpinner name="crescent" />
            </div>
          ) : filteredDeliveryNotes.length === 0 ? (
            <div className="text-center py-12">
              <IonIcon icon={documentTextOutline} style={{ fontSize: '64px' }} className="text-gray-400 mb-4" />
              <h3 className="text-lg font-medium">No delivery notes ready</h3>
              <p className="text-gray-500">
                {searchText ? 'No delivery notes match your search' : 'All deliveries have been dispatched'}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredDeliveryNotes.map((note) => (
                <IonCard key={note.id} button onClick={() => selectDeliveryNote(note)}>
                  <IonCardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <IonCardTitle className="text-lg">
                          Delivery #{note.deliveryNoteNumber}
                        </IonCardTitle>
                        <IonText color="medium">
                          <p className="text-sm">Order: {note.orderId}</p>
                        </IonText>
                      </div>
                      <IonBadge color="warning">Ready for Dispatch</IonBadge>
                    </div>
                  </IonCardHeader>

                  <IonCardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Items:</span>
                        <span className="font-medium">{note.items.length} items</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Delivery Date:</span>
                        <span className="font-medium">
                          {formatDateTime(note.deliveryDate)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Location:</span>
                        <span className="font-medium">{note.locationId}</span>
                      </div>
                    </div>

                    <div className="flex justify-end mt-4">
                      <IonButton fill="clear" size="small">
                        <IonIcon icon={handRightOutline} slot="start" />
                        Process Handover
                      </IonButton>
                    </div>
                  </IonCardContent>
                </IonCard>
              ))}
            </div>
          )}
        </div>

        {/* Handover Modal */}
        <IonModal isOpen={showHandoverModal} onDidDismiss={() => setShowHandoverModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Dispatch Handover</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowHandoverModal(false)}>Close</IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            {selectedDeliveryNote && (
              <div className="p-4">
                {/* Delivery Note Info */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Delivery #{selectedDeliveryNote.deliveryNoteNumber}</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Order ID:</span>
                        <span className="font-medium">{selectedDeliveryNote.orderId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Delivery Date:</span>
                        <span className="font-medium">{formatDateTime(selectedDeliveryNote.deliveryDate)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Destination:</span>
                        <span className="font-medium">{selectedDeliveryNote.locationId}</span>
                      </div>
                    </div>
                  </IonCardContent>
                </IonCard>

                {/* Driver Selection */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Assign Driver</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <IonItem>
                      <IonIcon icon={personOutline} slot="start" />
                      <IonLabel>Select Driver</IonLabel>
                      <IonSelect
                        value={selectedDriver}
                        onIonChange={(e) => setSelectedDriver(e.detail.value)}
                        placeholder="Choose driver"
                      >
                        {drivers.filter(d => d.isActive).map(driver => (
                          <IonSelectOption key={driver.id} value={driver.id}>
                            {driver.name} - {driver.vehicleNumber}
                          </IonSelectOption>
                        ))}
                      </IonSelect>
                    </IonItem>
                    
                    {getSelectedDriver() && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                        <div className="text-sm space-y-1">
                          <p><strong>Driver:</strong> {getSelectedDriver()!.name}</p>
                          <p><strong>Phone:</strong> {getSelectedDriver()!.phone}</p>
                          <p><strong>Vehicle:</strong> {getSelectedDriver()!.vehicleNumber}</p>
                        </div>
                      </div>
                    )}
                  </IonCardContent>
                </IonCard>

                {/* Item Verification */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Verify Items ({checkedItems.size}/{selectedDeliveryNote.items.length})</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent className="p-0">
                    <IonList>
                      {selectedDeliveryNote.items.map((item, index) => (
                        <IonItem key={index}>
                          <IonCheckbox
                            checked={checkedItems.has(item.itemId)}
                            onIonChange={(e) => toggleItemCheck(item.itemId, e.detail.checked)}
                            slot="start"
                          />
                          <IonLabel>
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-sm text-gray-600">
                              Quantity: {item.deliveredQuantity} {item.unit}
                            </p>
                          </IonLabel>
                          {checkedItems.has(item.itemId) && (
                            <IonIcon icon={checkmarkCircle} color="success" slot="end" />
                          )}
                        </IonItem>
                      ))}
                    </IonList>
                  </IonCardContent>
                </IonCard>

                {/* Dispatcher Signature */}
                <SignatureCapture
                  title="Dispatcher Signature"
                  roleLabel="Dispatcher"
                  onSignatureCapture={handleDispatcherSignature}
                  existingSignature={dispatcherSignature}
                />

                {/* Driver Signature */}
                {selectedDriver && (
                  <SignatureCapture
                    title="Driver Signature"
                    roleLabel="Driver"
                    onSignatureCapture={handleDriverSignature}
                    existingSignature={driverSignature}
                  />
                )}

                {/* Complete Handover Button */}
                <div className="mt-6">
                  <IonButton
                    expand="block"
                    size="large"
                    onClick={() => setShowConfirmAlert(true)}
                    disabled={!validateHandover()}
                  >
                    <IonIcon icon={carOutline} slot="start" />
                    Complete Handover
                  </IonButton>
                </div>
              </div>
            )}
          </IonContent>
        </IonModal>

        {/* Confirmation Alert */}
        <IonAlert
          isOpen={showConfirmAlert}
          onDidDismiss={() => setShowConfirmAlert(false)}
          header="Confirm Handover"
          message={`Hand over delivery ${selectedDeliveryNote?.deliveryNoteNumber} to ${getSelectedDriver()?.name}?`}
          buttons={[
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'Confirm',
              handler: processHandover,
            },
          ]}
        />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color={toastColor}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default DispatchHandover;