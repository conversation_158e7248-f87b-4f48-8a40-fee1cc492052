import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonBadge,
  IonButton,
  IonInput,
  IonTextarea,
  IonToggle,
  IonSearchbar,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  IonText,
  IonToast,
  IonAlert,
  IonModal,
  IonChip,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  qrCodeOutline,
  checkmarkCircle,
  cameraOutline,
  documentTextOutline,
  cubeOutline,
  refreshOutline,
  scanOutline,
  warningOutline,
  addCircleOutline,
  removeCircleOutline
} from 'ionicons/icons';
import { useAuth } from '@/contexts/EnhancedAuthContext';
import { shopPortalService, DeliveryNote, DeliveryNoteItem } from '@/services/shopPortalService';
import SignatureCapture, { SignatureData } from '@/components/delivery/SignatureCapture';
import { format } from 'date-fns';

interface ReceivedItem extends DeliveryNoteItem {
  receivedQuantity: number;
  hasDiscrepancy: boolean;
  discrepancyNotes?: string;
  damagePhotos?: string[];
}

const DeliveryReceiving: React.FC = () => {
  const { companyId, locationId } = useAuth();
  
  const [incomingDeliveries, setIncomingDeliveries] = useState<DeliveryNote[]>([]);
  const [selectedDelivery, setSelectedDelivery] = useState<DeliveryNote | null>(null);
  const [receivedItems, setReceivedItems] = useState<ReceivedItem[]>([]);
  const [receiverSignature, setReceiverSignature] = useState<SignatureData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [showReceivingModal, setShowReceivingModal] = useState(false);
  const [showConfirmAlert, setShowConfirmAlert] = useState(false);
  const [, setIsProcessing] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'warning' | 'danger'>('success');
  const [scanningMode, setScanningMode] = useState(false);

  useEffect(() => {
    if (companyId && locationId) {
      shopPortalService.updateCredentials(companyId, locationId);
      fetchIncomingDeliveries();
    }
  }, [companyId, locationId, fetchIncomingDeliveries]);

  const fetchIncomingDeliveries = async () => {
    try {
      setLoading(true);
      // Fetch delivery notes that are in-transit or delivered to this location
      const response = await shopPortalService.getDeliveryNotes({
        status: 'in-transit' // Or 'delivered' status
      });
      
      if (response.success) {
        setIncomingDeliveries(response.data || []);
      } else {
        showNotification('Failed to load deliveries', 'danger');
      }
    } catch (error) {
      console.error('Error fetching deliveries:', error);
      showNotification('Error loading deliveries', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await fetchIncomingDeliveries();
    event.detail.complete();
  };

  const startReceiving = (delivery: DeliveryNote) => {
    setSelectedDelivery(delivery);
    setReceivedItems(delivery.items.map(item => ({
      ...item,
      receivedQuantity: item.deliveredQuantity,
      hasDiscrepancy: false,
      discrepancyNotes: '',
      damagePhotos: []
    })));
    setReceiverSignature(null);
    setShowReceivingModal(true);
  };

  const updateReceivedQuantity = (itemId: string, quantity: number) => {
    setReceivedItems(prev => prev.map(item => {
      if (item.itemId === itemId) {
        const hasDiscrepancy = quantity !== item.deliveredQuantity;
        return {
          ...item,
          receivedQuantity: Math.max(0, quantity),
          hasDiscrepancy
        };
      }
      return item;
    }));
  };

  const updateDiscrepancyNotes = (itemId: string, notes: string) => {
    setReceivedItems(prev => prev.map(item => 
      item.itemId === itemId ? { ...item, discrepancyNotes: notes } : item
    ));
  };

  const toggleDiscrepancy = (itemId: string, hasDiscrepancy: boolean) => {
    setReceivedItems(prev => prev.map(item => {
      if (item.itemId === itemId) {
        return {
          ...item,
          hasDiscrepancy,
          discrepancyNotes: hasDiscrepancy ? item.discrepancyNotes : '',
          receivedQuantity: hasDiscrepancy ? item.receivedQuantity : item.deliveredQuantity
        };
      }
      return item;
    }));
  };

  const takeDamagePhoto = async (itemId: string) => {
    // In a real app, this would use Capacitor Camera plugin
    const mockPhotoUrl = `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=`;
    
    setReceivedItems(prev => prev.map(item => {
      if (item.itemId === itemId) {
        return {
          ...item,
          damagePhotos: [...(item.damagePhotos || []), mockPhotoUrl]
        };
      }
      return item;
    }));
    
    showNotification('Damage photo added', 'success');
  };

  const handleReceiverSignature = (signature: SignatureData) => {
    setReceiverSignature(signature);
    showNotification('Receiver signature captured', 'success');
  };

  const scanDeliveryNote = () => {
    // In a real app, this would use barcode scanner
    setScanningMode(true);
    setTimeout(() => {
      setScanningMode(false);
      showNotification('Delivery note scanned', 'success');
      // Would automatically select the corresponding delivery
    }, 2000);
  };

  const validateReceiving = () => {
    if (!selectedDelivery) return false;
    
    if (!receiverSignature) {
      showNotification('Receiver signature required', 'warning');
      return false;
    }
    
    // Check if discrepancies are properly documented
    const discrepancyItems = receivedItems.filter(item => item.hasDiscrepancy);
    const undocumentedDiscrepancies = discrepancyItems.filter(item => 
      !item.discrepancyNotes || item.discrepancyNotes.trim() === ''
    );
    
    if (undocumentedDiscrepancies.length > 0) {
      showNotification('Please document all discrepancies', 'warning');
      return false;
    }
    
    return true;
  };

  const completeReceiving = async () => {
    if (!validateReceiving() || !selectedDelivery) return;

    try {
      setIsProcessing(true);
      
      // Prepare data for API call
      const receivingData = receivedItems.map(item => ({
        itemId: item.itemId,
        receivedQuantity: item.receivedQuantity,
        hasDiscrepancy: item.hasDiscrepancy,
        discrepancyNotes: item.discrepancyNotes
      }));

      const response = await shopPortalService.receiveDelivery(
        selectedDelivery.id,
        receivingData
      );
      
      if (response.success) {
        showNotification('Delivery received successfully!', 'success');
        setShowReceivingModal(false);
        setShowConfirmAlert(false);
        
        // Refresh the deliveries list
        await fetchIncomingDeliveries();
      } else {
        showNotification('Failed to process delivery', 'danger');
      }
      
    } catch (error) {
      console.error('Error completing receiving:', error);
      showNotification('Error processing delivery', 'danger');
    } finally {
      setIsProcessing(false);
    }
  };

  const showNotification = (message: string, color: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatDateTime = (date: Date | string) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  const filteredDeliveries = incomingDeliveries.filter(delivery =>
    delivery.deliveryNoteNumber.toLowerCase().includes(searchText.toLowerCase()) ||
    delivery.id.toLowerCase().includes(searchText.toLowerCase())
  );

  const getTotalDiscrepancies = () => {
    return receivedItems.filter(item => item.hasDiscrepancy).length;
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/shop-portal" />
          </IonButtons>
          <IonTitle>Receive Deliveries</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={scanDeliveryNote}>
              <IonIcon icon={qrCodeOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={refreshOutline}
            pullingText="Pull to refresh"
            refreshingSpinner="circles"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        {scanningMode && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg text-center">
              <IonSpinner name="crescent" />
              <p className="mt-2">Scanning delivery note...</p>
            </div>
          </div>
        )}

        <div className="p-4">
          {/* Header Info */}
          <IonCard>
            <IonCardContent>
              <div className="text-center">
                <IonIcon icon={cubeOutline} style={{ fontSize: '48px' }} color="primary" />
                <h2 className="text-xl font-bold mt-2">Delivery Receiving</h2>
                <p className="text-gray-600">Receive and verify incoming deliveries</p>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Scan Button */}
          <IonCard>
            <IonCardContent>
              <IonButton expand="block" fill="outline" onClick={scanDeliveryNote}>
                <IonIcon icon={scanOutline} slot="start" />
                Scan Delivery Note
              </IonButton>
            </IonCardContent>
          </IonCard>

          {/* Search */}
          <IonSearchbar
            value={searchText}
            onIonInput={(e) => setSearchText(e.detail.value!)}
            placeholder="Search delivery notes..."
            showClearButton="focus"
            className="mb-4"
          />

          {/* Incoming Deliveries */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <IonSpinner name="crescent" />
            </div>
          ) : filteredDeliveries.length === 0 ? (
            <div className="text-center py-12">
              <IonIcon icon={documentTextOutline} style={{ fontSize: '64px' }} className="text-gray-400 mb-4" />
              <h3 className="text-lg font-medium">No incoming deliveries</h3>
              <p className="text-gray-500">
                {searchText ? 'No deliveries match your search' : 'All deliveries have been received'}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredDeliveries.map((delivery) => (
                <IonCard key={delivery.id} button onClick={() => startReceiving(delivery)}>
                  <IonCardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <IonCardTitle className="text-lg">
                          Delivery #{delivery.deliveryNoteNumber}
                        </IonCardTitle>
                        <IonText color="medium">
                          <p className="text-sm">Order: {delivery.orderId}</p>
                        </IonText>
                      </div>
                      <IonBadge color="primary">In Transit</IonBadge>
                    </div>
                  </IonCardHeader>

                  <IonCardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Items:</span>
                        <span className="font-medium">{delivery.items.length} items</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Expected:</span>
                        <span className="font-medium">
                          {formatDateTime(delivery.deliveryDate)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="font-medium capitalize">{delivery.status}</span>
                      </div>
                    </div>

                    <div className="flex justify-end mt-4">
                      <IonButton fill="clear" size="small">
                        <IonIcon icon={checkmarkCircle} slot="start" />
                        Start Receiving
                      </IonButton>
                    </div>
                  </IonCardContent>
                </IonCard>
              ))}
            </div>
          )}
        </div>

        {/* Receiving Modal */}
        <IonModal isOpen={showReceivingModal} onDidDismiss={() => setShowReceivingModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Receive Delivery</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowReceivingModal(false)}>Close</IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            {selectedDelivery && (
              <div className="p-4">
                {/* Delivery Info */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Delivery #{selectedDelivery.deliveryNoteNumber}</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Order ID:</span>
                        <span className="font-medium">{selectedDelivery.orderId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Expected:</span>
                        <span className="font-medium">{formatDateTime(selectedDelivery.deliveryDate)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <span className="font-medium capitalize">{selectedDelivery.status}</span>
                      </div>
                      {getTotalDiscrepancies() > 0 && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Discrepancies:</span>
                          <IonChip color="warning" size="small">
                            {getTotalDiscrepancies()} items
                          </IonChip>
                        </div>
                      )}
                    </div>
                  </IonCardContent>
                </IonCard>

                {/* Items Verification */}
                <IonCard>
                  <IonCardHeader>
                    <IonCardTitle>Verify Items</IonCardTitle>
                  </IonCardHeader>
                  <IonCardContent className="p-0">
                    <IonList>
                      {receivedItems.map((item, index) => (
                        <div key={index}>
                          <IonItem>
                            <IonLabel>
                              <h3 className="font-medium">{item.name}</h3>
                              <p className="text-sm text-gray-600">
                                Expected: {item.deliveredQuantity} {item.unit}
                              </p>
                            </IonLabel>
                            <div slot="end" className="flex items-center space-x-2">
                              <IonToggle
                                checked={item.hasDiscrepancy}
                                onIonChange={(e) => toggleDiscrepancy(item.itemId, e.detail.checked)}
                                color="warning"
                              />
                              <IonText color="medium">
                                <span className="text-xs">Issues?</span>
                              </IonText>
                            </div>
                          </IonItem>

                          {/* Quantity Input */}
                          <IonItem>
                            <IonLabel position="stacked">Received Quantity</IonLabel>
                            <div className="flex items-center space-x-2 w-full">
                              <IonButton
                                size="small"
                                fill="outline"
                                onClick={() => updateReceivedQuantity(item.itemId, item.receivedQuantity - 1)}
                              >
                                <IonIcon icon={removeCircleOutline} />
                              </IonButton>
                              <IonInput
                                type="number"
                                value={item.receivedQuantity}
                                onIonInput={(e) => updateReceivedQuantity(item.itemId, parseInt(e.detail.value!) || 0)}
                                className="text-center font-bold"
                              />
                              <span className="text-sm text-gray-600">{item.unit}</span>
                              <IonButton
                                size="small"
                                fill="outline"
                                onClick={() => updateReceivedQuantity(item.itemId, item.receivedQuantity + 1)}
                              >
                                <IonIcon icon={addCircleOutline} />
                              </IonButton>
                            </div>
                          </IonItem>

                          {/* Discrepancy Details */}
                          {item.hasDiscrepancy && (
                            <>
                              <IonItem>
                                <IonLabel position="stacked">Issue Description</IonLabel>
                                <IonTextarea
                                  value={item.discrepancyNotes}
                                  onIonInput={(e) => updateDiscrepancyNotes(item.itemId, e.detail.value!)}
                                  placeholder="Describe the issue (damage, shortage, wrong item, etc.)"
                                  rows={2}
                                />
                              </IonItem>
                              <IonItem>
                                <IonButton
                                  fill="outline"
                                  size="small"
                                  onClick={() => takeDamagePhoto(item.itemId)}
                                >
                                  <IonIcon icon={cameraOutline} slot="start" />
                                  Take Photo ({item.damagePhotos?.length || 0})
                                </IonButton>
                              </IonItem>
                            </>
                          )}

                          {/* Status Indicator */}
                          <IonItem>
                            <div className="w-full py-2">
                              {item.receivedQuantity === item.deliveredQuantity && !item.hasDiscrepancy ? (
                                <IonChip color="success" size="small">
                                  <IonIcon icon={checkmarkCircle} />
                                  <IonLabel>Verified</IonLabel>
                                </IonChip>
                              ) : (
                                <IonChip color="warning" size="small">
                                  <IonIcon icon={warningOutline} />
                                  <IonLabel>Discrepancy</IonLabel>
                                </IonChip>
                              )}
                            </div>
                          </IonItem>

                          {index < receivedItems.length - 1 && <hr className="my-2" />}
                        </div>
                      ))}
                    </IonList>
                  </IonCardContent>
                </IonCard>

                {/* Receiver Signature */}
                <SignatureCapture
                  title="Receiver Signature"
                  roleLabel="Shop Manager"
                  onSignatureCapture={handleReceiverSignature}
                  existingSignature={receiverSignature}
                />

                {/* Complete Receiving */}
                <div className="mt-6">
                  <IonButton
                    expand="block"
                    size="large"
                    onClick={() => setShowConfirmAlert(true)}
                    disabled={!validateReceiving()}
                  >
                    <IonIcon icon={checkmarkCircle} slot="start" />
                    Complete Receiving
                  </IonButton>
                </div>
              </div>
            )}
          </IonContent>
        </IonModal>

        {/* Confirmation Alert */}
        <IonAlert
          isOpen={showConfirmAlert}
          onDidDismiss={() => setShowConfirmAlert(false)}
          header="Confirm Delivery Receipt"
          message={`Complete receiving for delivery ${selectedDelivery?.deliveryNoteNumber}?${getTotalDiscrepancies() > 0 ? ` Note: ${getTotalDiscrepancies()} discrepancies reported.` : ''}`}
          buttons={[
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'Confirm',
              handler: completeReceiving,
            },
          ]}
        />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color={toastColor}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default DeliveryReceiving;