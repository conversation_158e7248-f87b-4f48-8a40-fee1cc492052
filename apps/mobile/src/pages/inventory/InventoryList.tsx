import React, { useState, useEffect, useMemo } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonButton,
  IonIcon,
  IonBadge,
  IonChip,
  IonFab,
  IonFabButton,
  IonAlert,
  IonToast,
  IonProgressBar,
  IonRefresher,
  IonRefresherContent,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  add, 
  scan, 
  warning, 
  checkmark, 
  time,
  cash,
  cube,
  business,
  download,
} from 'ionicons/icons';
import Table, { Column, FilterOption } from '@/components/shared/Table';
import { BranchInventoryItem } from '@/types/inventory';
import { inventoryService } from '@/services/inventoryService';

interface InventoryListItem extends BranchInventoryItem {
  stockStatus: 'low' | 'normal' | 'high' | 'out';
  stockPercentage: number;
  totalValue: number;
}

const InventoryList: React.FC = () => {
  const [inventoryItems, setInventoryItems] = useState<InventoryListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [toast, setToast] = useState({ show: false, message: '', color: 'success' as 'success' | 'danger' });

  // Load cubedata
  const loadInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await inventoryService.getBranchInventories();
      
      if (response.success && response.data) {
        const allItems: InventoryListItem[] = response.data.flatMap(category =>
          category.items.map(item => {
            const currentStock = item.stockLevel || 0;
            const minStock = item.minStock || 0;
            const maxStock = item.maxStock || 100;
            const stockPercentage = maxStock > 0 ? (currentStock / maxStock) * 100 : 0;
            
            let stockStatus: 'low' | 'normal' | 'high' | 'out' = 'normal';
            if (currentStock === 0) stockStatus = 'out';
            else if (currentStock <= minStock) stockStatus = 'low';
            else if (stockPercentage >= 80) stockStatus = 'high';
            
            return {
              ...item,
              stockStatus,
              stockPercentage,
              totalValue: currentStock * (item.cost || 0),
            };
          })
        );
        
        setInventoryItems(allItems);
      } else {
        setError(response.error || 'Failed to load inventory');
      }
    } catch (err) {
      console.error('Error loading inventory', err);
      setError('Failed to load inventory data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInventory();
  }, []);

  // Get unique categories for filtering
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(inventoryItems.map(item => item.category))];
    return uniqueCategories.sort();
  }, [inventoryItems]);

  // Filter options for the table
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: 'Category',
      values: categories.map(cat => ({ value: cat, label: cat })),
    },
    {
      key: 'stockStatus',
      label: 'Stock Status',
      values: [
        { value: 'out', label: 'Out of Stock' },
        { value: 'low', label: 'Low Stock' },
        { value: 'normal', label: 'Normal' },
        { value: 'high', label: 'Well Stocked' },
      ],
    },
    {
      key: 'isAvailable',
      label: 'Availability',
      values: [
        { value: 'true', label: 'Available' },
        { value: 'false', label: 'Unavailable' },
      ],
    },
  ];

  // Table columns
  const columns: Column<InventoryListItem>[] = [
    {
      key: 'name',
      header: 'Item Name',
      width: '25%',
      sortable: true,
      render: (value, item) => (
        <div>
          <div className="font-medium">{item.name}</div>
          <div className="text-sm text-gray-500">{item.category}</div>
          {item.sku && (
            <div className="text-xs text-gray-400">SKU: {item.sku}</div>
          )}
        </div>
      ),
    },
    {
      key: 'stockLevel',
      header: 'Stock Level',
      width: '20%',
      sortable: true,
      render: (value, item) => (
        <div>
          <div className="flex items-center gap-2">
            <span className="font-medium">
              {item.stockLevel || 0} {item.unit || 'units'}
            </span>
            <IonBadge
              color={
                item.stockStatus === 'out' ? 'danger' :
                item.stockStatus === 'low' ? 'warning' :
                item.stockStatus === 'high' ? 'success' : 'medium'
              }
            >
              {item.stockStatus === 'out' ? 'Out' :
               item.stockStatus === 'low' ? 'Low' :
               item.stockStatus === 'high' ? 'High' : 'Normal'}
            </IonBadge>
          </div>
          <div className="mt-1">
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div
                className={`h-1 rounded-full ${
                  item.stockStatus === 'out' ? 'bg-red-500' :
                  item.stockStatus === 'low' ? 'bg-yellow-500' :
                  item.stockStatus === 'high' ? 'bg-green-500' : 'bg-blue-500'
                }`}
                style={{ width: `${Math.max(item.stockPercentage, 2)}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Min: {item.minStock || 0}</span>
              <span>Max: {item.maxStock || 100}</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      header: 'Price/Cost',
      width: '15%',
      sortable: true,
      render: (value, item) => (
        <div>
          <div className="font-medium text-green-600">
            ${item.price?.toFixed(2) || '0.00'}
          </div>
          {item.cost && (
            <div className="text-sm text-gray-500">
              Cost: ${item.cost.toFixed(2)}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'totalValue',
      header: 'Total Value',
      width: '15%',
      sortable: true,
      render: (value, item) => (
        <div className="font-medium">
          ${item.totalValue.toFixed(2)}
        </div>
      ),
    },
    {
      key: 'lastRestocked',
      header: 'Last Activity',
      width: '15%',
      sortable: true,
      render: (value, item) => (
        <div>
          {item.lastRestocked ? (
            <div className="text-sm">
              {new Date(item.lastRestocked).toLocaleDateString()}
            </div>
          ) : (
            <span className="text-gray-400">No data</span>
          )}
          {item.expiryDate && (
            <div className="text-xs text-red-500">
              Expires: {new Date(item.expiryDate).toLocaleDateString()}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'isAvailable',
      header: 'Status',
      width: '10%',
      render: (value, item) => (
        <IonChip
          color={item.isAvailable ? 'success' : 'danger'}
          outline
        >
          <IonIcon icon={item.isAvailable ? checkmark : warning} />
          {item.isAvailable ? 'Active' : 'Inactive'}
        </IonChip>
      ),
    },
  ];

  // Handle item click
  const handleItemClick = (item: InventoryListItem) => {
    // Navigate to item details or show quick actions
    setAlertMessage(`Actions for ${item.name}`);
    setShowAlert(true);
  };

  // Handle refresh
  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await loadInventory();
    event.detail.complete();
  };

  // Export functionality
  const handleExport = () => {
    setToast({
      show: true,
      message: 'Export functionality coming soon!',
      color: 'success',
    });
  };

  // Summary stats
  const stats = useMemo(() => {
    const total = inventoryItems.length;
    const lowStock = inventoryItems.filter(item => item.stockStatus === 'low').length;
    const outOfStock = inventoryItems.filter(item => item.stockStatus === 'out').length;
    const totalValue = inventoryItems.reduce((sum, item) => sum + item.totalValue, 0);
    
    return { total, lowStock, outOfStock, totalValue };
  }, [inventoryItems]);

  if (error && !inventoryItems.length) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar color="primary">
            <IonMenuButton slot="start" />
            <IonTitle>Inventory</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent>
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <IonIcon icon={warning} className="text-4xl text-red-500 mb-4" />
              <h2 className="text-lg font-medium mb-2">Error Loading Inventory</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <IonButton onClick={loadInventory}>
                Try Again
              </IonButton>
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Inventory Management</IonTitle>
          <IonButton slot="end" fill="clear" onClick={handleExport}>
            <IonIcon icon={download} />
          </IonButton>
        </IonToolbar>
      </IonHeader>
      
      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        {loading && <IonProgressBar type="indeterminate" />}

        {/* Summary Cards */}
        <div className="p-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg p-4 shadow">
            <div className="flex items-center">
              <IonIcon icon={cube} className="text-2xl text-blue-500 mr-2" />
              <div>
                <div className="text-sm text-gray-600">Total Items</div>
                <div className="text-xl font-bold">{stats.total}</div>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-4 shadow">
            <div className="flex items-center">
              <IonIcon icon={warning} className="text-2xl text-yellow-500 mr-2" />
              <div>
                <div className="text-sm text-gray-600">Low Stock</div>
                <div className="text-xl font-bold text-yellow-600">{stats.lowStock}</div>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-4 shadow">
            <div className="flex items-center">
              <IonIcon icon={time} className="text-2xl text-red-500 mr-2" />
              <div>
                <div className="text-sm text-gray-600">Out of Stock</div>
                <div className="text-xl font-bold text-red-600">{stats.outOfStock}</div>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-4 shadow">
            <div className="flex items-center">
              <IonIcon icon={cash} className="text-2xl text-green-500 mr-2" />
              <div>
                <div className="text-sm text-gray-600">Total Value</div>
                <div className="text-xl font-bold text-green-600">
                  ${stats.totalValue.toFixed(2)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Inventory Table */}
        <div className="px-4 pb-4">
          <Table
            columns={columns}
            data={inventoryItems}
            loading={loading}
            onRowClick={handleItemClick}
            searchable={true}
            searchPlaceholder="Search by name, SKU, or category..."
            searchFields={['name', 'category', 'sku', 'description']}
            filterOptions={filterOptions}
            enableSorting={true}
            onRefresh={loadInventory}
            onExport={handleExport}
            emptyMessage="No cubeitems found"
          />
        </div>

        {/* Floating Action Buttons */}
        <IonFab vertical="bottom" horizontal="end" slot="fixed">
          <IonFabButton>
            <IonIcon icon={add} />
          </IonFabButton>
        </IonFab>

        <IonFab vertical="bottom" horizontal="start" slot="fixed">
          <IonFabButton color="secondary">
            <IonIcon icon={scan} />
          </IonFabButton>
        </IonFab>
      </IonContent>

      {/* Alerts */}
      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header="Item Actions"
        message={alertMessage}
        buttons={[
          'Cancel',
          {
            text: 'Stock Count',
            handler: () => {
              // Navigate to stock count
              setToast({
                show: true,
                message: 'Stock count feature coming soon!',
                color: 'success',
              });
            },
          },
          {
            text: 'Record Wastage',
            handler: () => {
              // Navigate to wastage
              setToast({
                show: true,
                message: 'Wastage recording feature coming soon!',
                color: 'success',
              });
            },
          },
        ]}
      />

      <IonToast
        isOpen={toast.show}
        onDidDismiss={() => setToast({ ...toast, show: false })}
        message={toast.message}
        duration={3000}
        color={toast.color}
      />
    </IonPage>
  );
};

export default InventoryList;