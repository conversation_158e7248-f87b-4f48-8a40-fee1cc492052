import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonAlert,
  IonToast,
  IonProgressBar,
  IonSearchbar,
  IonModal,
  IonButtons,
  IonCheckbox,
  IonNote,
  IonBadge,
  IonImg,
} from '@ionic/react';
import {
  scan,
  save,
  arrowBack,
  camera,
  close,
  search,
  warning,
  trash,
  add,
  remove,
} from 'ionicons/icons';
import { BranchInventoryItem, WastageRecord, WastageReason, WASTAGE_REASONS } from '@/types/inventory';
import { inventoryService } from '@/services/inventoryService';

interface WastageFormData {
  item: BranchInventoryItem | null;
  quantity: number;
  reason: WastageReason | null;
  notes: string;
  photoUrl?: string;
  cost?: number;
}

const Wastage: React.FC = () => {
  const [formData, setFormData] = useState<WastageFormData>({
    item: null,
    quantity: 0,
    reason: null,
    notes: '',
  });
  
  const [availableItems, setAvailableItems] = useState<BranchInventoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showItemModal, setShowItemModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', type: 'success' as 'success' | 'danger' });
  const [toast, setToast] = useState({ show: false, message: '', color: 'success' as 'success' | 'danger' });
  const [recentWastage, setRecentWastage] = useState<WastageRecord[]>([]);

  // Load available inventory items
  const loadInventoryItems = async () => {
    try {
      setLoading(true);
      const response = await inventoryService.getBranchInventories();
      
      if (response.success && response.data) {
        const allItems = response.data.flatMap(category => category.items);
        setAvailableItems(allItems.filter(item => item.isAvailable));
      }
    } catch (error) {
      console.error('Error loading inventory items:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load recent wastage records
  const loadRecentWastage = async () => {
    try {
      const credentials = inventoryService.getBranchInventories(); // This should get credentials
      // For now, we'll use a placeholder
      const companyId = 'current-company-id';
      const response = await inventoryService.getWastageRecords(companyId);
      
      if (response.success && response.data) {
        setRecentWastage(response.data.slice(0, 5)); // Show last 5 records
      }
    } catch (error) {
      console.error('Error loading recent wastage:', error);
    }
  };

  useEffect(() => {
    loadInventoryItems();
    loadRecentWastage();
  }, []);

  // Select item
  const selectItem = (item: BranchInventoryItem) => {
    setFormData(prev => ({
      ...prev,
      item,
      cost: item.cost ? item.cost * prev.quantity : undefined,
    }));
    setShowItemModal(false);
    setSearchQuery('');
  };

  // Update quantity
  const updateQuantity = (quantity: number) => {
    const newQuantity = Math.max(0, quantity);
    setFormData(prev => ({
      ...prev,
      quantity: newQuantity,
      cost: prev.item?.cost ? prev.item.cost * newQuantity : undefined,
    }));
  };

  // Update reason
  const updateReason = (reason: WastageReason) => {
    setFormData(prev => ({ ...prev, reason }));
  };

  // Update notes
  const updateNotes = (notes: string) => {
    setFormData(prev => ({ ...prev, notes }));
  };

  // Take photo (placeholder)
  const takePhoto = async () => {
    // TODO: Implement Capacitor Camera plugin
    setToast({
      show: true,
      message: 'Photo capture feature coming soon!',
      color: 'success',
    });
  };

  // Scan barcode (placeholder)
  const scanBarcode = async () => {
    setToast({
      show: true,
      message: 'Barcode scanning feature coming soon!',
      color: 'success',
    });
  };

  // Validate form
  const validateForm = (): string | null => {
    if (!formData.item) return 'Please select an item';
    if (formData.quantity <= 0) return 'Please enter a valid quantity';
    if (!formData.reason) return 'Please select a wastage reason';
    if (formData.reason.requiresNote && !formData.notes.trim()) {
      return `Notes are required for ${formData.reason.label}`;
    }
    if (formData.reason.requiresPhoto && !formData.photoUrl) {
      return `Photo is required for ${formData.reason.label}`;
    }
    return null;
  };

  // Submit wastage
  const submitWastage = async () => {
    const validationError = validateForm();
    if (validationError) {
      setAlert({
        show: true,
        message: validationError,
        type: 'danger',
      });
      return;
    }

    if (!formData.item || !formData.reason) return;

    try {
      setSubmitting(true);

      const credentials = inventoryService.getBranchInventories(); // Should get actual credentials
      const wastageData = {
        inventoryItemId: formData.item.id,
        quantity: formData.quantity,
        unit: formData.item.unit || 'units',
        reason: formData.reason,
        notes: formData.notes,
        photoUrl: formData.photoUrl,
        performedBy: 'current-user', // Should be actual user
        cost: formData.cost,
        companyId: 'current-company-id', // Should be actual company ID
        locationId: 'current-location-id', // Should be actual location ID
      };

      const response = await inventoryService.recordWastage(wastageData);

      if (response.success) {
        setToast({
          show: true,
          message: 'Wastage recorded successfully!',
          color: 'success',
        });

        // Reset form
        setFormData({
          item: null,
          quantity: 0,
          reason: null,
          notes: '',
        });

        // Reload recent wastage
        loadRecentWastage();
      } else {
        setAlert({
          show: true,
          message: response.error || 'Failed to record wastage',
          type: 'danger',
        });
      }
    } catch (error) {
      console.error('Error submitting wastage:', error);
      setAlert({
        show: true,
        message: 'Failed to record wastage',
        type: 'danger',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Filter available items
  const filteredItems = availableItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (item.sku && item.sku.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButton slot="start" fill="clear" routerLink="/inventory">
            <IonIcon icon={arrowBack} />
          </IonButton>
          <IonTitle>Record Wastage</IonTitle>
          <IonButton
            slot="end"
            fill="solid"
            onClick={submitWastage}
            disabled={!formData.item || formData.quantity <= 0 || !formData.reason || submitting}
          >
            <IonIcon icon={save} slot="start" />
            Submit
          </IonButton>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        {loading && <IonProgressBar type="indeterminate" />}
        {submitting && <IonProgressBar type="indeterminate" color="success" />}

        <div className="p-4">
          {/* Main Form */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle className="flex items-center">
                <IonIcon icon={trash} className="mr-2 text-red-500" />
                Wastage Details
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              {/* Item Selection */}
              <div className="mb-6">
                <IonLabel className="font-medium text-gray-700">Item *</IonLabel>
                <div className="mt-2">
                  {formData.item ? (
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
                      <div>
                        <div className="font-medium">{formData.item.name}</div>
                        <div className="text-sm text-gray-600">
                          {formData.item.category} • Stock: {formData.item.stockLevel || 0} {formData.item.unit || 'units'}
                        </div>
                        {formData.item.sku && (
                          <div className="text-xs text-gray-500">SKU: {formData.item.sku}</div>
                        )}
                      </div>
                      <IonButton
                        fill="clear"
                        color="danger"
                        onClick={() => setFormData(prev => ({ ...prev, item: null }))}
                      >
                        <IonIcon icon={close} />
                      </IonButton>
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <IonButton
                        fill="outline"
                        expand="block"
                        onClick={() => setShowItemModal(true)}
                      >
                        <IonIcon icon={search} slot="start" />
                        Select Item
                      </IonButton>
                      <IonButton fill="outline" onClick={scanBarcode}>
                        <IonIcon icon={scan} />
                      </IonButton>
                    </div>
                  )}
                </div>
              </div>

              {/* Quantity */}
              <div className="mb-6">
                <IonLabel className="font-medium text-gray-700">Quantity *</IonLabel>
                <div className="flex items-center gap-3 mt-2">
                  <IonButton
                    fill="outline"
                    onClick={() => updateQuantity(formData.quantity - 1)}
                    disabled={formData.quantity <= 0}
                  >
                    <IonIcon icon={remove} />
                  </IonButton>
                  <IonInput
                    type="number"
                    value={formData.quantity}
                    onIonChange={e => updateQuantity(parseFloat(e.detail.value!) || 0)}
                    placeholder="0"
                    className="text-center border rounded-lg"
                    min="0"
                    step="0.1"
                  />
                  <IonButton
                    fill="outline"
                    onClick={() => updateQuantity(formData.quantity + 1)}
                  >
                    <IonIcon icon={add} />
                  </IonButton>
                  <div className="text-sm text-gray-600 ml-2">
                    {formData.item?.unit || 'units'}
                  </div>
                </div>
                {formData.cost && (
                  <div className="text-sm text-gray-600 mt-1">
                    Estimated cost: ${formData.cost.toFixed(2)}
                  </div>
                )}
              </div>

              {/* Reason */}
              <div className="mb-6">
                <IonLabel className="font-medium text-gray-700">Reason *</IonLabel>
                <IonSelect
                  placeholder="Select wastage reason"
                  value={formData.reason?.code}
                  onSelectionChange={e => {
                    const reason = WASTAGE_REASONS.find(r => r.code === e.detail.value);
                    if (reason) updateReason(reason);
                  }}
                  className="mt-2"
                >
                  {WASTAGE_REASONS.map(reason => (
                    <IonSelectOption key={reason.code} value={reason.code}>
                      {reason.label}
                    </IonSelectOption>
                  ))}
                </IonSelect>
                
                {formData.reason && (
                  <div className="mt-2 flex gap-2">
                    {formData.reason.requiresPhoto && (
                      <IonBadge color="warning">Photo Required</IonBadge>
                    )}
                    {formData.reason.requiresNote && (
                      <IonBadge color="primary">Notes Required</IonBadge>
                    )}
                  </div>
                )}
              </div>

              {/* Photo */}
              {formData.reason?.requiresPhoto && (
                <div className="mb-6">
                  <IonLabel className="font-medium text-gray-700">Photo *</IonLabel>
                  <div className="mt-2">
                    {formData.photoUrl ? (
                      <div className="relative">
                        <IonImg src={formData.photoUrl} className="w-full h-48 object-cover rounded-lg" />
                        <IonButton
                          fill="clear"
                          color="danger"
                          className="absolute top-2 right-2"
                          onClick={() => setFormData(prev => ({ ...prev, photoUrl: undefined }))}
                        >
                          <IonIcon icon={close} />
                        </IonButton>
                      </div>
                    ) : (
                      <IonButton fill="outline" expand="block" onClick={takePhoto}>
                        <IonIcon icon={camera} slot="start" />
                        Take Photo
                      </IonButton>
                    )}
                  </div>
                </div>
              )}

              {/* Notes */}
              <div className="mb-6">
                <IonLabel className="font-medium text-gray-700">
                  Notes {formData.reason?.requiresNote && '*'}
                </IonLabel>
                <IonTextarea
                  placeholder="Add details about the wastage..."
                  value={formData.notes}
                  onIonChange={e => updateNotes(e.detail.value!)}
                  rows={3}
                  className="mt-2"
                />
              </div>
            </IonCardContent>
          </IonCard>

          {/* Recent Wastage */}
          {recentWastage.length > 0 && (
            <IonCard className="mt-4">
              <IonCardHeader>
                <IonCardTitle>Recent Wastage</IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonList>
                  {recentWastage.map(wastage => (
                    <IonItem key={wastage.id}>
                      <div className="w-full">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">Item ID: {wastage.inventoryItemId}</h3>
                            <p className="text-sm text-gray-600">
                              {wastage.quantity} {wastage.unit} • {wastage.reason.label}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(wastage.timestamp).toLocaleString()}
                            </p>
                          </div>
                          {wastage.cost && (
                            <div className="text-right">
                              <div className="text-sm font-medium text-red-600">
                                -${wastage.cost.toFixed(2)}
                              </div>
                            </div>
                          )}
                        </div>
                        {wastage.notes && (
                          <p className="text-sm text-gray-600 mt-1 italic">
                            {wastage.notes}
                          </p>
                        )}
                      </div>
                    </IonItem>
                  ))}
                </IonList>
              </IonCardContent>
            </IonCard>
          )}
        </div>

        {/* Item Selection Modal */}
        <IonModal isOpen={showItemModal} onDidDismiss={() => setShowItemModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Select Item</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowItemModal(false)}>
                  <IonIcon icon={close} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            <div className="p-4">
              <IonSearchbar
                placeholder="Search items by name, SKU, or category..."
                value={searchQuery}
                onIonInput={e => setSearchQuery(e.detail.value!)}
              />
              
              <IonList>
                {filteredItems.map(item => (
                  <IonItem
                    key={item.id}
                    onClick={() => selectItem(item)}
                    button
                  >
                    <div className="w-full">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-gray-600">
                            {item.category} • Stock: {item.stockLevel || 0} {item.unit || 'units'}
                          </p>
                          {item.sku && (
                            <p className="text-xs text-gray-500">SKU: {item.sku}</p>
                          )}
                        </div>
                        <div className="text-right">
                          {item.cost && (
                            <div className="text-sm font-medium">
                              ${item.cost.toFixed(2)}
                            </div>
                          )}
                          <IonBadge color="success">Available</IonBadge>
                        </div>
                      </div>
                    </div>
                  </IonItem>
                ))}
              </IonList>

              {filteredItems.length === 0 && (
                <div className="text-center py-8">
                  <IonIcon icon={search} className="text-4xl text-gray-400 mb-4" />
                  <p className="text-gray-600">No items found matching your search</p>
                </div>
              )}
            </div>
          </IonContent>
        </IonModal>
      </IonContent>

      {/* Alerts and Toasts */}
      <IonAlert
        isOpen={alert.show}
        onDidDismiss={() => setAlert({ ...alert, show: false })}
        header={alert.type === 'danger' ? 'Error' : 'Success'}
        message={alert.message}
        buttons={['OK']}
      />

      <IonToast
        isOpen={toast.show}
        onDidDismiss={() => setToast({ ...toast, show: false })}
        message={toast.message}
        duration={3000}
        color={toast.color}
      />
    </IonPage>
  );
};

export default Wastage;