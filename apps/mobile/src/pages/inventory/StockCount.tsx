import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonBadge,
  IonSearchbar,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonAlert,
  IonToast,
  IonProgressBar,
  IonCheckbox,
  IonNote,
  IonFab,
  IonFabButton,
  IonModal,
  IonButtons,
} from '@ionic/react';
import {
  scan,
  save,
  arrowBack,
  checkmark,
  warning,
  add,
  remove,
  close,
  search,
} from 'ionicons/icons';
import { BranchInventoryItem, StockCountSession, StockCountItem } from '@/types/inventory';
import { inventoryService } from '@/services/inventoryService';

interface StockCountData extends StockCountItem {
  item: BranchInventoryItem;
  checked: boolean;
}

const StockCount: React.FC = () => {
  const [session, setSession] = useState<StockCountSession | null>(null);
  const [countItems, setCountItems] = useState<StockCountData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [availableItems, setAvailableItems] = useState<BranchInventoryItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<BranchInventoryItem | null>(null);
  const [alert, setAlert] = useState({ show: false, message: '', type: 'success' as 'success' | 'danger' });
  const [toast, setToast] = useState({ show: false, message: '', color: 'success' as 'success' | 'danger' });

  // Initialize stock count session
  const initializeSession = async () => {
    try {
      setLoading(true);
      const response = await inventoryService.createStockCountSession();
      
      if (response.success && response.data) {
        setSession(response.data);
        await loadInventoryItems();
      } else {
        setAlert({
          show: true,
          message: response.error || 'Failed to create stock count session',
          type: 'danger',
        });
      }
    } catch (error) {
      console.error('Error initializing session:', error);
      setAlert({
        show: true,
        message: 'Failed to initialize stock count session',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load available inventory items
  const loadInventoryItems = async () => {
    try {
      const response = await inventoryService.getBranchInventories();
      
      if (response.success && response.data) {
        const allItems = response.data.flatMap(category => category.items);
        setAvailableItems(allItems);
      }
    } catch (error) {
      console.error('Error loading inventory items:', error);
    }
  };

  useEffect(() => {
    initializeSession();
  }, []);

  // Add item to count
  const addItemToCount = (item: BranchInventoryItem) => {
    const existingItem = countItems.find(ci => ci.inventoryItemId === item.id);
    
    if (existingItem) {
      setToast({
        show: true,
        message: 'Item already added to count',
        color: 'danger',
      });
      return;
    }

    const newCountItem: StockCountData = {
      inventoryItemId: item.id,
      systemQuantity: item.stockLevel || 0,
      countedQuantity: 0,
      variance: 0,
      unit: item.unit || 'units',
      item,
      checked: false,
    };

    setCountItems(prev => [...prev, newCountItem]);
    setShowItemModal(false);
    setSelectedItem(null);
  };

  // Remove item from count
  const removeItemFromCount = (itemId: string) => {
    setCountItems(prev => prev.filter(item => item.inventoryItemId !== itemId));
  };

  // Update counted quantity
  const updateCountedQuantity = (itemId: string, quantity: number) => {
    setCountItems(prev =>
      prev.map(item => {
        if (item.inventoryItemId === itemId) {
          const variance = quantity - item.systemQuantity;
          return {
            ...item,
            countedQuantity: quantity,
            variance,
          };
        }
        return item;
      })
    );
  };

  // Update notes
  const updateNotes = (itemId: string, notes: string) => {
    setCountItems(prev =>
      prev.map(item =>
        item.inventoryItemId === itemId ? { ...item, notes } : item
      )
    );
  };

  // Toggle item checked
  const toggleItemChecked = (itemId: string) => {
    setCountItems(prev =>
      prev.map(item =>
        item.inventoryItemId === itemId ? { ...item, checked: !item.checked } : item
      )
    );
  };

  // Submit stock count
  const submitStockCount = async () => {
    if (!session) {
      setAlert({
        show: true,
        message: 'No active stock count session',
        type: 'danger',
      });
      return;
    }

    if (countItems.length === 0) {
      setAlert({
        show: true,
        message: 'Please add items to count before submitting',
        type: 'danger',
      });
      return;
    }

    const uncheckedItems = countItems.filter(item => !item.checked);
    if (uncheckedItems.length > 0) {
      setAlert({
        show: true,
        message: `Please verify all items before submitting. ${uncheckedItems.length} items remaining.`,
        type: 'danger',
      });
      return;
    }

    try {
      setSaving(true);

      // Update session with count items
      const updatedSession: Partial<StockCountSession> = {
        items: countItems.map(({ item, checked, ...countItem }) => countItem),
        totalVariance: countItems.reduce((sum, item) => sum + Math.abs(item.variance), 0),
      };

      await inventoryService.updateStockCountSession(session.id, updatedSession);
      await inventoryService.submitStockCount(session.id);

      setToast({
        show: true,
        message: 'Stock count submitted successfully!',
        color: 'success',
      });

      // Reset state
      setCountItems([]);
      setSession(null);

    } catch (error) {
      console.error('Error submitting stock count:', error);
      setAlert({
        show: true,
        message: 'Failed to submit stock count',
        type: 'danger',
      });
    } finally {
      setSaving(false);
    }
  };

  // Scan barcode (placeholder)
  const scanBarcode = async () => {
    setToast({
      show: true,
      message: 'Barcode scanning feature coming soon!',
      color: 'success',
    });
  };

  // Filter available items for modal
  const filteredAvailableItems = availableItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (item.sku && item.sku.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Calculate summary stats
  const summary = {
    totalItems: countItems.length,
    checkedItems: countItems.filter(item => item.checked).length,
    totalVariance: countItems.reduce((sum, item) => sum + Math.abs(item.variance), 0),
    positiveVariance: countItems.filter(item => item.variance > 0).length,
    negativeVariance: countItems.filter(item => item.variance < 0).length,
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButton slot="start" fill="clear" routerLink="/inventory">
            <IonIcon icon={arrowBack} />
          </IonButton>
          <IonTitle>Stock Count</IonTitle>
          <IonButton
            slot="end"
            fill="solid"
            onClick={submitStockCount}
            disabled={countItems.length === 0 || saving}
          >
            <IonIcon icon={save} slot="start" />
            Submit
          </IonButton>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        {loading && <IonProgressBar type="indeterminate" />}
        {saving && <IonProgressBar type="indeterminate" color="success" />}

        {/* Summary Card */}
        <IonCard className="m-4">
          <IonCardHeader>
            <IonCardTitle>Count Summary</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{summary.totalItems}</div>
                <div className="text-sm text-gray-600">Total Items</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{summary.checkedItems}</div>
                <div className="text-sm text-gray-600">Verified</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">{summary.totalVariance.toFixed(0)}</div>
                <div className="text-sm text-gray-600">Total Variance</div>
              </div>
            </div>
            
            {summary.totalItems > 0 && (
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Progress</span>
                  <span>{summary.checkedItems} / {summary.totalItems}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all"
                    style={{ width: `${(summary.checkedItems / summary.totalItems) * 100}%` }}
                  />
                </div>
              </div>
            )}
          </IonCardContent>
        </IonCard>

        {/* Count Items List */}
        <div className="px-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Count Items ({countItems.length})</h2>
            <div className="flex gap-2">
              <IonButton fill="outline" onClick={scanBarcode}>
                <IonIcon icon={scan} slot="start" />
                Scan
              </IonButton>
              <IonButton onClick={() => setShowItemModal(true)}>
                <IonIcon icon={add} slot="start" />
                Add Item
              </IonButton>
            </div>
          </div>

          {countItems.length === 0 ? (
            <IonCard>
              <IonCardContent className="text-center py-8">
                <IonIcon icon={search} className="text-4xl text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">No Items Added</h3>
                <p className="text-gray-600 mb-4">
                  Add items to start your stock count
                </p>
                <IonButton onClick={() => setShowItemModal(true)}>
                  <IonIcon icon={add} slot="start" />
                  Add First Item
                </IonButton>
              </IonCardContent>
            </IonCard>
          ) : (
            <IonList>
              {countItems.map((countItem) => (
                <IonCard key={countItem.inventoryItemId}>
                  <IonCardContent>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <IonCheckbox
                            checked={countItem.checked}
                            onIonChange={() => toggleItemChecked(countItem.inventoryItemId)}
                          />
                          <h3 className="font-medium">{countItem.item.name}</h3>
                          {countItem.variance !== 0 && (
                            <IonBadge
                              color={countItem.variance > 0 ? 'success' : 'danger'}
                            >
                              {countItem.variance > 0 ? '+' : ''}{countItem.variance.toFixed(1)}
                            </IonBadge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {countItem.item.category} • SKU: {countItem.item.sku || 'N/A'}
                        </p>
                      </div>
                      <IonButton
                        fill="clear"
                        color="danger"
                        onClick={() => removeItemFromCount(countItem.inventoryItemId)}
                      >
                        <IonIcon icon={close} />
                      </IonButton>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div>
                        <IonLabel className="text-sm text-gray-600">System Qty</IonLabel>
                        <div className="text-lg font-medium">
                          {countItem.systemQuantity} {countItem.unit}
                        </div>
                      </div>
                      <div>
                        <IonLabel className="text-sm text-gray-600">Counted Qty</IonLabel>
                        <div className="flex items-center gap-2">
                          <IonButton
                            fill="clear"
                            size="small"
                            onClick={() =>
                              updateCountedQuantity(
                                countItem.inventoryItemId,
                                Math.max(0, countItem.countedQuantity - 1)
                              )
                            }
                          >
                            <IonIcon icon={remove} />
                          </IonButton>
                          <IonInput
                            type="number"
                            value={countItem.countedQuantity}
                            onIonChange={e =>
                              updateCountedQuantity(
                                countItem.inventoryItemId,
                                parseFloat(e.detail.value!) || 0
                              )
                            }
                            className="text-center font-medium"
                          />
                          <IonButton
                            fill="clear"
                            size="small"
                            onClick={() =>
                              updateCountedQuantity(
                                countItem.inventoryItemId,
                                countItem.countedQuantity + 1
                              )
                            }
                          >
                            <IonIcon icon={add} />
                          </IonButton>
                        </div>
                      </div>
                      <div>
                        <IonLabel className="text-sm text-gray-600">Variance</IonLabel>
                        <div className={`text-lg font-medium ${
                          countItem.variance > 0 ? 'text-green-600' :
                          countItem.variance < 0 ? 'text-red-600' : 'text-gray-900'
                        }`}>
                          {countItem.variance > 0 ? '+' : ''}{countItem.variance.toFixed(1)}
                        </div>
                      </div>
                    </div>

                    <IonTextarea
                      placeholder="Add notes about this count (optional)"
                      value={countItem.notes || ''}
                      onIonChange={e =>
                        updateNotes(countItem.inventoryItemId, e.detail.value!)
                      }
                      rows={2}
                      className="mt-2"
                    />
                  </IonCardContent>
                </IonCard>
              ))}
            </IonList>
          )}
        </div>

        {/* Add Item Modal */}
        <IonModal isOpen={showItemModal} onDidDismiss={() => setShowItemModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Add Item to Count</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowItemModal(false)}>
                  <IonIcon icon={close} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            <div className="p-4">
              <IonSearchbar
                placeholder="Search items by name, SKU, or category..."
                value={searchQuery}
                onIonInput={e => setSearchQuery(e.detail.value!)}
              />
              
              <IonList>
                {filteredAvailableItems.map(item => (
                  <IonItem
                    key={item.id}
                    onClick={() => addItemToCount(item)}
                    button
                  >
                    <div className="w-full">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-gray-600">
                            {item.category} • Current: {item.stockLevel || 0} {item.unit || 'units'}
                          </p>
                          {item.sku && (
                            <p className="text-xs text-gray-500">SKU: {item.sku}</p>
                          )}
                        </div>
                        <IonBadge color={item.isAvailable ? 'success' : 'medium'}>
                          {item.isAvailable ? 'Active' : 'Inactive'}
                        </IonBadge>
                      </div>
                    </div>
                  </IonItem>
                ))}
              </IonList>

              {filteredAvailableItems.length === 0 && (
                <div className="text-center py-8">
                  <IonIcon icon={search} className="text-4xl text-gray-400 mb-4" />
                  <p className="text-gray-600">No items found matching your search</p>
                </div>
              )}
            </div>
          </IonContent>
        </IonModal>
      </IonContent>

      {/* Alerts and Toasts */}
      <IonAlert
        isOpen={alert.show}
        onDidDismiss={() => setAlert({ ...alert, show: false })}
        header={alert.type === 'danger' ? 'Error' : 'Success'}
        message={alert.message}
        buttons={['OK']}
      />

      <IonToast
        isOpen={toast.show}
        onDidDismiss={() => setToast({ ...toast, show: false })}
        message={toast.message}
        duration={3000}
        color={toast.color}
      />
    </IonPage>
  );
};

export default StockCount;