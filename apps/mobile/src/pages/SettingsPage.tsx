import React from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonToggle,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
} from '@ionic/react';
import {
  person,
  storefront,
  notifications,
  moon,
  wifi,
  print,
  card,
  shield,
  help,
  information,
  logOut,
  chevronForward,
} from 'ionicons/icons';

const SettingsPage: React.FC = () => {
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Settings</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <div className="p-4">
          {/* Account Section */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle className="text-base">Account</IonCardTitle>
            </IonCardHeader>
            <IonCardContent className="p-0">
              <IonList>
                <IonItem button>
                  <IonIcon icon={person} slot="start" />
                  <IonLabel>
                    <h3>Profile</h3>
                    <p>Manage your account details</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
                
                <IonItem button>
                  <IonIcon icon={storefront} slot="start" />
                  <IonLabel>
                    <h3>Restaurant Settings</h3>
                    <p>Configure restaurant information</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* App Preferences */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle className="text-base">Preferences</IonCardTitle>
            </IonCardHeader>
            <IonCardContent className="p-0">
              <IonList>
                <IonItem>
                  <IonIcon icon={notifications} slot="start" />
                  <IonLabel>
                    <h3>Notifications</h3>
                    <p>Enable order alerts</p>
                  </IonLabel>
                  <IonToggle slot="end" checked />
                </IonItem>
                
                <IonItem>
                  <IonIcon icon={moon} slot="start" />
                  <IonLabel>
                    <h3>Dark Mode</h3>
                    <p>Use dark theme</p>
                  </IonLabel>
                  <IonToggle slot="end" />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Hardware & Integration */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle className="text-base">Hardware & Integration</IonCardTitle>
            </IonCardHeader>
            <IonCardContent className="p-0">
              <IonList>
                <IonItem button>
                  <IonIcon icon={print} slot="start" />
                  <IonLabel>
                    <h3>Receipt Printer</h3>
                    <p>Configure receipt printing</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
                
                <IonItem button>
                  <IonIcon icon={card} slot="start" />
                  <IonLabel>
                    <h3>Payment Gateway</h3>
                    <p>Setup payment processing</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
                
                <IonItem button>
                  <IonIcon icon={wifi} slot="start" />
                  <IonLabel>
                    <h3>Network Settings</h3>
                    <p>Configure connectivity</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Security */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle className="text-base">Security</IonCardTitle>
            </IonCardHeader>
            <IonCardContent className="p-0">
              <IonList>
                <IonItem button>
                  <IonIcon icon={shield} slot="start" />
                  <IonLabel>
                    <h3>Security Settings</h3>
                    <p>Manage passwords and access</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Support */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle className="text-base">Support</IonCardTitle>
            </IonCardHeader>
            <IonCardContent className="p-0">
              <IonList>
                <IonItem button>
                  <IonIcon icon={help} slot="start" />
                  <IonLabel>
                    <h3>Help Center</h3>
                    <p>Get help and tutorials</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
                
                <IonItem button>
                  <IonIcon icon={information} slot="start" />
                  <IonLabel>
                    <h3>About</h3>
                    <p>App version and information</p>
                  </IonLabel>
                  <IonIcon icon={chevronForward} slot="end" />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Sign Out */}
          <div className="mt-6">
            <IonButton 
              expand="block" 
              fill="outline" 
              color="danger"
              className="mb-4"
            >
              <IonIcon icon={logOut} slot="start" />
              Sign Out
            </IonButton>
          </div>

          {/* App Info */}
          <div className="text-center text-gray-500 text-sm mt-4">
            <p>FoodPrep AI POS System</p>
            <p>Version 1.0.0</p>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default SettingsPage;