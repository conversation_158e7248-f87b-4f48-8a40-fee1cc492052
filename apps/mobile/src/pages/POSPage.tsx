import React, { useState, useEffect, useCallback } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonGrid,
  IonRow,
  IonCol,
  IonButton,
  IonIcon,
  IonBadge,
  IonList,
  IonItem,
  IonLabel,
  IonChip,
  IonToast,
  IonRefresher,
  IonRefresherContent,
} from '@ionic/react';
import { 
  add, 
  remove, 
  cart, 
  wifi, 
  cloudOffline, 
  sync, 
  person, 
  business,
  chevronDown 
} from 'ionicons/icons';
import { useCart } from '@/hooks/useCart';
import { MenuItem } from '@/types/menu';
import { useEnhancedAuth } from '../contexts/EnhancedAuthContext';
import { LoadingSpinner, Button, Card, ConfirmationDialog } from '../components';
import { db } from '../services/db';

const POSPage: React.FC = () => {
  const { cartItems, addToCart, removeFromCart, getTotalPrice } = useCart();
  const {
    currentUser,
    company,
    isOnline,
    isSyncing,
    syncStatus,
    forceSync,
    logout
  } = useEnhancedAuth();

  // State management
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  // Load menu items from local database
  useEffect(() => {
    loadMenuItems();
  }, [company, loadMenuItems]);

  const loadMenuItems = useCallback(async () => {
    if (!company) return;

    try {
      setLoading(true);
      setError(null);

      // Load products from local database
      const products = await db.products
        .where('companyId')
        .equals(company.id)
        .and(product => product.isAvailable)
        .toArray();

      const items: MenuItem[] = products.map(product => ({
        id: product._id || product.id!.toString(),
        name: product.name,
        price: product.price,
        category: product.category,
        image: product.imageUrl || '',
        description: product.description || '',
        isAvailable: product.isAvailable,
        stockLevel: product.stockLevel
      }));

      setMenuItems(items);
    } catch (err) {
      console.error('Failed to load menu items:', err);
      setError('Failed to load menu items');
    } finally {
      setLoading(false);
    }
  }, [company]);

  const handleRefresh = async (event: CustomEvent) => {
    try {
      if (isOnline) {
        await forceSync();
      }
      await loadMenuItems();
      showToastMessage('Data refreshed successfully');
    } catch (error) {
      console.error('Refresh failed:', error);
      showToastMessage('Failed to refresh data');
    } finally {
      event.detail.complete();
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setShowLogoutConfirm(false);
    } catch (error) {
      console.error('Logout failed:', error);
      showToastMessage('Failed to logout');
    }
  };

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  if (loading) {
    return (
      <IonPage>
        <IonContent>
          <LoadingSpinner 
            fullscreen 
            message="Loading menu items..." 
            size="large"
          />
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Point of Sale</IonTitle>
          
          {/* Status indicators in toolbar */}
          <div slot="end" className="flex items-center space-x-2 mr-4">
            {/* Sync status */}
            {syncStatus.pendingCount > 0 && (
              <IonChip color="warning" size="small">
                <IonIcon icon={sync} />
                <span className="ml-1">{syncStatus.pendingCount}</span>
              </IonChip>
            )}
            
            {/* Connection status */}
            <IonChip color={isOnline ? 'success' : 'warning'} size="small">
              <IonIcon icon={isOnline ? wifi : cloudOffline} />
              <span className="ml-1">{isOnline ? 'Online' : 'Offline'}</span>
            </IonChip>
            
            {/* User info */}
            <IonButton 
              fill="clear" 
              size="small"
              onClick={() => setShowLogoutConfirm(true)}
            >
              <IonIcon icon={person} />
            </IonButton>
          </div>
        </IonToolbar>
        
        {/* User and location info bar */}
        <IonToolbar color="light" className="min-h-[40px]">
          <div className="flex items-center justify-between px-4 py-1">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <IonIcon icon={person} className="mr-1" />
                <span>{currentUser?.name} ({currentUser?.role})</span>
              </div>
              <div className="flex items-center">
                <IonIcon icon={business} className="mr-1" />
                <span>{company?.name}</span>
              </div>
              <div className="flex items-center">
                <IonIcon icon={location} className="mr-1" />
                <span>{location?.name}</span>
              </div>
            </div>
            {isSyncing && (
              <div className="flex items-center text-sm text-blue-600">
                <IonIcon icon={sync} className="animate-spin mr-1" />
                <span>Syncing...</span>
              </div>
            )}
          </div>
        </IonToolbar>
      </IonHeader>
      
      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={chevronDown}
            pullingText="Pull to refresh"
            refreshingSpinner="crescent"
            refreshingText="Refreshing..."
          />
        </IonRefresher>
        {error && (
          <div className="p-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <IonIcon icon={cloudOffline} className="text-red-500 mr-2" />
                <p className="text-red-700">{error}</p>
              </div>
              <Button
                variant="outline"
                size="small"
                onClick={loadMenuItems}
                className="mt-2"
              >
                Retry
              </Button>
            </div>
          </div>
        )}

        <IonGrid>
          <IonRow>
            <IonCol size="12" sizeMd="8">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold">Menu Items</h2>
                  <div className="flex items-center space-x-2">
                    {!isOnline && (
                      <IonChip color="warning" size="small">
                        <IonIcon icon={cloudOffline} />
                        <span className="ml-1">Offline Data</span>
                      </IonChip>
                    )}
                    <span className="text-sm text-gray-500">
                      {menuItems.length} items
                    </span>
                  </div>
                </div>

                {menuItems.length === 0 ? (
                  <div className="text-center py-12">
                    <IonIcon 
                      icon={cart} 
                      className="text-4xl text-gray-400 mb-4" 
                    />
                    <p className="text-gray-500 mb-4">No menu items available</p>
                    <Button
                      variant="outline"
                      onClick={loadMenuItems}
                    >
                      Reload Items
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {menuItems.map((item) => (
                      <Card 
                        key={item.id} 
                        className="menu-item-card hover:shadow-lg transition-shadow"
                      >
                        <div className="flex flex-col h-full">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="font-semibold text-lg">{item.name}</h3>
                            {item.stockLevel !== undefined && item.stockLevel < 5 && (
                              <IonChip color="warning" size="small">
                                Low Stock
                              </IonChip>
                            )}
                          </div>
                          
                          <p className="text-gray-600 text-sm mb-3 flex-grow">
                            {item.description}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-primary-600 font-bold text-lg">
                                ${item.price.toFixed(2)}
                              </span>
                              {item.stockLevel !== undefined && (
                                <div className="text-xs text-gray-500 mt-1">
                                  Stock: {item.stockLevel}
                                </div>
                              )}
                            </div>
                            
                            <Button
                              variant="primary"
                              size="small"
                              onClick={() => addToCart(item)}
                              disabled={!item.isAvailable || (item.stockLevel !== undefined && item.stockLevel === 0)}
                              icon={add}
                              iconPosition="start"
                            >
                              Add
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </IonCol>
            
            <IonCol size="12" sizeMd="4">
              <div className="p-4">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <IonIcon icon={cart} className="mr-2" />
                  Cart
                  {cartItems.length > 0 && (
                    <IonBadge color="primary" className="ml-2">
                      {cartItems.length}
                    </IonBadge>
                  )}
                </h2>
                
                {cartItems.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <p>No items in cart</p>
                  </div>
                ) : (
                  <>
                    <IonList>
                      {cartItems.map((item) => (
                        <IonItem key={`${item.id}-${item.timestamp}`} className="cart-item">
                          <IonLabel>
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-sm text-gray-600">
                              ${item.price.toFixed(2)} x {item.quantity}
                            </p>
                          </IonLabel>
                          <div className="flex items-center space-x-2">
                            <IonButton
                              fill="clear"
                              size="small"
                              onClick={() => removeFromCart(item.id)}
                            >
                              <IonIcon icon={remove} slot="icon-only" />
                            </IonButton>
                            <span className="font-medium">
                              ${(item.price * item.quantity).toFixed(2)}
                            </span>
                          </div>
                        </IonItem>
                      ))}
                    </IonList>
                    
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-lg font-semibold">Total:</span>
                        <span className="text-xl font-bold text-primary-600">
                          ${getTotalPrice().toFixed(2)}
                        </span>
                      </div>
                      <Button
                        fullWidth
                        variant="primary"
                        onClick={() => showToastMessage('Payment processing coming soon!')}
                      >
                        Process Payment
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </IonCol>
          </IonRow>
        </IonGrid>

        {/* Logout Confirmation Dialog */}
        <ConfirmationDialog
          isOpen={showLogoutConfirm}
          onClose={() => setShowLogoutConfirm(false)}
          onConfirm={handleLogout}
          title="Confirm Logout"
          message={`Are you sure you want to logout, ${currentUser?.name}?`}
          confirmText="Logout"
          cancelText="Cancel"
          variant="warning"
        />

        {/* Toast for messages */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default POSPage;