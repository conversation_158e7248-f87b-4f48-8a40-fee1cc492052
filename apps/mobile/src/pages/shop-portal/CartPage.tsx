import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonBadge,
  IonTextarea,
  IonDatetime,
  IonModal,
  IonSelect,
  IonSelectOption,
  IonCheckbox,
  IonToast,
  IonLoading,
  IonText,
  IonGrid,
  IonRow,
  IonCol,
  IonChip,
  IonAlert,
} from '@ionic/react';
import { 
  add, 
  remove, 
  trash, 
  calendar, 
  checkmarkCircle,
  alertCircle,
  timeOutline,
  documentTextOutline,
  cartOutline,
  chevronForward
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { useAuth } from '@/contexts/EnhancedAuthContext';
import { shopPortalService, ShopPortalOrderItem } from '@/services/shopPortalService';

interface CartItem {
  id: string;
  itemId: string;
  itemType: string;
  name: string;
  cartQuantity: number;
  minOrderQuantity: number;
  maxOrderQuantity: number;
  orderingUOM: string;
  estimatedCost: number;
  leadTimeDays: number;
}

const CartPage: React.FC = () => {
  const history = useHistory();
  const { user, companyId, locationId } = useAuth();
  
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [requestedDeliveryDate, setRequestedDeliveryDate] = useState<string>('');
  const [orderNotes, setOrderNotes] = useState<string>('');
  const [urgent, setUrgent] = useState<boolean>(false);
  const [showDateModal, setShowDateModal] = useState<boolean>(false);
  const [showSubmitAlert, setShowSubmitAlert] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showToast, setShowToast] = useState<boolean>(false);
  const [toastMessage, setToastMessage] = useState<string>('');
  const [toastColor, setToastColor] = useState<'success' | 'warning' | 'danger'>('success');

  useEffect(() => {
    loadCartFromStorage();
  }, []);

  const loadCartFromStorage = () => {
    const savedCart = localStorage.getItem('shopPortalCart');
    if (savedCart) {
      try {
        const cartData: Array<[string, CartItem]> = JSON.parse(savedCart);
        setCartItems(cartData.map(([_, item]) => item));
      } catch (error) {
        console.error('Error loading cart:', error);
        showNotification('Error loading cart', 'danger');
      }
    }
  };

  const saveCartToStorage = (items: CartItem[]) => {
    const cartMap = new Map(items.map(item => [item.id, item]));
    localStorage.setItem('shopPortalCart', JSON.stringify(Array.from(cartMap.entries())));
  };

  const updateCartQuantity = (itemId: string, delta: number) => {
    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        const newQuantity = item.cartQuantity + delta;
        if (newQuantity >= item.minOrderQuantity && newQuantity <= item.maxOrderQuantity) {
          return { ...item, cartQuantity: newQuantity };
        } else if (newQuantity < item.minOrderQuantity) {
          showNotification(`Minimum quantity is ${item.minOrderQuantity}`, 'warning');
        } else {
          showNotification(`Maximum quantity is ${item.maxOrderQuantity}`, 'warning');
        }
      }
      return item;
    });
    setCartItems(updatedItems);
    saveCartToStorage(updatedItems);
  };

  const removeFromCart = (itemId: string) => {
    const updatedItems = cartItems.filter(item => item.id !== itemId);
    setCartItems(updatedItems);
    saveCartToStorage(updatedItems);
    showNotification('Item removed from cart', 'success');
  };

  const clearCart = () => {
    setCartItems([]);
    localStorage.removeItem('shopPortalCart');
    showNotification('Cart cleared', 'success');
  };

  const calculateTotals = () => {
    const totalItems = cartItems.reduce((sum, item) => sum + item.cartQuantity, 0);
    const totalCost = cartItems.reduce((sum, item) => sum + (item.cartQuantity * item.estimatedCost), 0);
    const maxLeadTime = Math.max(...cartItems.map(item => item.leadTimeDays), 0);
    
    return { totalItems, totalCost, maxLeadTime };
  };

  const getMinDeliveryDate = () => {
    const { maxLeadTime } = calculateTotals();
    const minDate = new Date();
    minDate.setDate(minDate.getDate() + maxLeadTime + 1); // Add buffer day
    return minDate.toISOString();
  };

  const validateOrder = () => {
    if (cartItems.length === 0) {
      showNotification('Cart is empty', 'warning');
      return false;
    }

    if (!requestedDeliveryDate) {
      showNotification('Please select a delivery date', 'warning');
      return false;
    }

    const selectedDate = new Date(requestedDeliveryDate);
    const minDate = new Date(getMinDeliveryDate());
    
    if (selectedDate < minDate) {
      showNotification(`Minimum delivery date is ${minDate.toLocaleDateString()}`, 'warning');
      return false;
    }

    return true;
  };

  const submitOrder = async () => {
    if (!validateOrder()) return;

    if (!companyId || !locationId) {
      showNotification('Authentication error', 'danger');
      return;
    }

    try {
      setIsLoading(true);
      shopPortalService.updateCredentials(companyId, locationId);

      const orderItems: ShopPortalOrderItem[] = cartItems.map(item => ({
        itemId: item.itemId,
        itemType: item.itemType,
        name: item.name,
        quantity: item.cartQuantity,
        unit: item.orderingUOM,
        estimatedCost: item.estimatedCost
      }));

      const orderData = {
        items: orderItems,
        requestedDeliveryDate: new Date(requestedDeliveryDate),
        notes: orderNotes.trim() || undefined,
      };

      const response = await shopPortalService.placeOrder(orderData);

      if (response.success) {
        // Clear cart on successful order
        clearCart();
        setRequestedDeliveryDate('');
        setOrderNotes('');
        setUrgent(false);
        
        showNotification('Order submitted successfully!', 'success');
        
        // Navigate to order history after short delay
        setTimeout(() => {
          history.push('/shop-portal/orders');
        }, 2000);
      } else {
        showNotification(response.message || 'Failed to submit order', 'danger');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      showNotification('Error submitting order', 'danger');
    } finally {
      setIsLoading(false);
      setShowSubmitAlert(false);
    }
  };

  const showNotification = (message: string, color: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const { totalItems, totalCost, maxLeadTime } = calculateTotals();

  if (cartItems.length === 0) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar color="primary">
            <IonButtons slot="start">
              <IonBackButton defaultHref="/shop-portal/catalog" />
            </IonButtons>
            <IonTitle>Shopping Cart</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent>
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <IonIcon icon={cartOutline} style={{ fontSize: '64px' }} className="text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
            <p className="text-gray-500 mb-6">Add items from the catalog to get started</p>
            <IonButton 
              expand="block" 
              onClick={() => history.push('/shop-portal/catalog')}
            >
              Browse Catalog
            </IonButton>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/shop-portal/catalog" />
          </IonButtons>
          <IonTitle>Shopping Cart</IonTitle>
          <IonButtons slot="end">
            <IonBadge color="secondary">{totalItems} items</IonBadge>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <div className="p-4">
          {/* Cart Summary */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Order Summary</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonGrid>
                <IonRow>
                  <IonCol size="6">
                    <div className="text-center">
                      <IonText color="primary">
                        <h3 className="text-2xl font-bold">{totalItems}</h3>
                      </IonText>
                      <p className="text-sm text-gray-600">Items</p>
                    </div>
                  </IonCol>
                  <IonCol size="6">
                    <div className="text-center">
                      <IonText color="secondary">
                        <h3 className="text-2xl font-bold">${totalCost.toFixed(2)}</h3>
                      </IonText>
                      <p className="text-sm text-gray-600">Est. Total</p>
                    </div>
                  </IonCol>
                </IonRow>
              </IonGrid>
              {maxLeadTime > 0 && (
                <div className="mt-3 p-2 bg-yellow-50 rounded-lg flex items-center">
                  <IonIcon icon={timeOutline} className="text-yellow-600 mr-2" />
                  <IonText color="medium">
                    <p className="text-sm">Max lead time: {maxLeadTime} days</p>
                  </IonText>
                </div>
              )}
            </IonCardContent>
          </IonCard>

          {/* Cart Items */}
          <IonCard>
            <IonCardHeader>
              <div className="flex justify-between items-center">
                <IonCardTitle>Items in Cart</IonCardTitle>
                <IonButton fill="clear" color="danger" onClick={clearCart}>
                  <IonIcon icon={trash} slot="icon-only" />
                </IonButton>
              </div>
            </IonCardHeader>
            <IonCardContent className="p-0">
              <IonList>
                {cartItems.map((item, index) => (
                  <IonItem key={item.id} lines={index < cartItems.length - 1 ? 'full' : 'none'}>
                    <IonLabel>
                      <h3 className="font-medium">{item.name}</h3>
                      <p className="text-sm text-gray-600">
                        ${item.estimatedCost.toFixed(2)} per {item.orderingUOM}
                      </p>
                      <p className="text-sm text-gray-600">
                        Lead time: {item.leadTimeDays} days
                      </p>
                    </IonLabel>
                    <div className="flex items-center space-x-2" slot="end">
                      <IonButton
                        size="small"
                        fill="outline"
                        onClick={() => updateCartQuantity(item.id, -1)}
                      >
                        <IonIcon icon={remove} />
                      </IonButton>
                      <span className="font-bold text-lg min-w-[3rem] text-center">
                        {item.cartQuantity}
                      </span>
                      <IonButton
                        size="small"
                        fill="outline"
                        onClick={() => updateCartQuantity(item.id, 1)}
                      >
                        <IonIcon icon={add} />
                      </IonButton>
                      <IonButton
                        size="small"
                        fill="clear"
                        color="danger"
                        onClick={() => removeFromCart(item.id)}
                      >
                        <IonIcon icon={trash} />
                      </IonButton>
                    </div>
                  </IonItem>
                ))}
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Delivery Details */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Delivery Details</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              {/* Delivery Date */}
              <IonItem button onClick={() => setShowDateModal(true)}>
                <IonIcon icon={calendar} slot="start" />
                <IonLabel>
                  <h3>Delivery Date</h3>
                  <p>{requestedDeliveryDate ? 
                    new Date(requestedDeliveryDate).toLocaleDateString() : 
                    'Select delivery date'
                  }</p>
                </IonLabel>
                <IonIcon icon={chevronForward} slot="end" />
              </IonItem>

              {/* Urgent Flag */}
              <IonItem>
                <IonCheckbox
                  checked={urgent}
                  onIonChange={(e) => setUrgent(e.detail.checked)}
                  slot="start"
                />
                <IonLabel>
                  <h3>Urgent Order</h3>
                  <p>Mark as urgent for priority processing</p>
                </IonLabel>
              </IonItem>

              {/* Order Notes */}
              <div className="mt-4">
                <IonLabel>
                  <h3 className="mb-2">Order Notes (Optional)</h3>
                </IonLabel>
                <IonTextarea
                  value={orderNotes}
                  onIonInput={(e) => setOrderNotes(e.detail.value!)}
                  placeholder="Add special instructions or notes..."
                  rows={3}
                  className="border rounded-lg"
                />
              </div>
            </IonCardContent>
          </IonCard>

          {/* Submit Button */}
          <div className="mt-6 mb-8">
            <IonButton
              expand="block"
              size="large"
              onClick={() => setShowSubmitAlert(true)}
              disabled={cartItems.length === 0}
            >
              <IonIcon icon={checkmarkCircle} slot="start" />
              Submit Order (${totalCost.toFixed(2)})
            </IonButton>
          </div>
        </div>

        {/* Date Selection Modal */}
        <IonModal isOpen={showDateModal} onDidDismiss={() => setShowDateModal(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Select Delivery Date</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowDateModal(false)}>Close</IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            <IonDatetime
              value={requestedDeliveryDate}
              onIonChange={(e) => setRequestedDeliveryDate(e.detail.value as string)}
              min={getMinDeliveryDate()}
              presentation="date"
              showDefaultButtons={true}
              doneText="Select"
              cancelText="Cancel"
              onIonCancel={() => setShowDateModal(false)}
              onIonDone={() => setShowDateModal(false)}
            />
          </IonContent>
        </IonModal>

        {/* Submit Confirmation Alert */}
        <IonAlert
          isOpen={showSubmitAlert}
          onDidDismiss={() => setShowSubmitAlert(false)}
          header="Confirm Order Submission"
          message={`Submit order for ${totalItems} items totaling $${totalCost.toFixed(2)}?`}
          buttons={[
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'Submit',
              handler: submitOrder,
            },
          ]}
        />

        <IonLoading isOpen={isLoading} message="Submitting order..." />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color={toastColor}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default CartPage;