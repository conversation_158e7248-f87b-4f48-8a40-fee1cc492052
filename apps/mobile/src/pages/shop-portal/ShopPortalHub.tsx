import React from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonGrid,
  IonRow,
  IonCol,
  IonIcon,
  IonButton,
  IonButtons,
  IonBadge,
  IonText,
} from '@ionic/react';
import { 
  cartOutline, 
  cubeOutline, 
  timeOutline, 
  checkmarkCircleOutline,
  listOutline,
  arrowForward,
  alertCircleOutline,
  refreshOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { useAuth } from '@/contexts/EnhancedAuthContext';

const ShopPortalHub: React.FC = () => {
  const history = useHistory();
  const { user, companyId, locationId } = useAuth();

  const navigateTo = (path: string) => {
    history.push(path);
  };

  const cards = [
    {
      title: 'Order Items',
      description: 'Browse and order from central kitchen',
      icon: cubeOutline,
      color: 'primary',
      path: '/shop-portal/catalog',
      badge: null,
    },
    {
      title: 'Shopping Cart',
      description: 'Review and submit your order',
      icon: cartOutline,
      color: 'secondary',
      path: '/shop-portal/cart',
      badge: '0', // This would be dynamic
    },
    {
      title: 'Order History',
      description: 'Track your orders and status',
      icon: listOutline,
      color: 'tertiary',
      path: '/shop-portal/orders',
      badge: null,
    },
    {
      title: 'Receive Deliveries',
      description: 'Process incoming deliveries',
      icon: checkmarkCircleOutline,
      color: 'success',
      path: '/shop-portal/receive',
      badge: '2', // Pending deliveries
    },
    {
      title: 'Quick Reorder',
      description: 'Repeat previous orders',
      icon: refreshOutline,
      color: 'warning',
      path: '/shop-portal/quick-reorder',
      badge: null,
    },
    {
      title: 'Low Stock Alerts',
      description: 'Items needing reorder',
      icon: alertCircleOutline,
      color: 'danger',
      path: '/shop-portal/low-stock',
      badge: '5', // Low stock items count
    },
  ];

  const recentActivity = [
    {
      id: '1',
      type: 'delivery',
      title: 'Delivery #DN-001 Received',
      time: '2 hours ago',
      status: 'completed',
    },
    {
      id: '2',
      type: 'order',
      title: 'Order #ORD-123 Dispatched',
      time: '5 hours ago',
      status: 'in-transit',
    },
    {
      id: '3',
      type: 'order',
      title: 'Order #ORD-122 Approved',
      time: '1 day ago',
      status: 'approved',
    },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'delivery':
        return checkmarkCircleOutline;
      case 'order':
        return timeOutline;
      default:
        return listOutline;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-transit':
        return 'primary';
      case 'approved':
        return 'secondary';
      default:
        return 'medium';
    }
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Shop Portal</IonTitle>
          <IonButtons slot="end">
            <IonButton>
              <IonIcon icon={cartOutline} />
              <IonBadge color="danger" style={{ position: 'absolute', top: 5, right: 5 }}>
                0
              </IonBadge>
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        <div className="p-4">
          {/* Header Section */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Central Kitchen Ordering</h1>
            <IonText color="medium">
              <p className="text-sm mt-1">
                Order supplies and ingredients from the central kitchen
              </p>
            </IonText>
          </div>

          {/* Quick Actions Grid */}
          <IonGrid>
            <IonRow>
              {cards.map((card) => (
                <IonCol key={card.path} size="12" sizeMd="6" sizeLg="4">
                  <IonCard 
                    className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => navigateTo(card.path)}
                  >
                    <IonCardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg bg-${card.color}/10`}>
                            <IonIcon 
                              icon={card.icon} 
                              className={`text-2xl text-${card.color}`}
                              color={card.color}
                            />
                          </div>
                          <div>
                            <IonCardTitle className="text-lg">{card.title}</IonCardTitle>
                          </div>
                        </div>
                        {card.badge && (
                          <IonBadge color={card.color}>{card.badge}</IonBadge>
                        )}
                      </div>
                    </IonCardHeader>
                    <IonCardContent>
                      <IonText color="medium">
                        <p className="text-sm">{card.description}</p>
                      </IonText>
                      <div className="flex justify-end mt-3">
                        <IonIcon icon={arrowForward} className="text-gray-400" />
                      </div>
                    </IonCardContent>
                  </IonCard>
                </IonCol>
              ))}
            </IonRow>
          </IonGrid>

          {/* Recent Activity Section */}
          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Recent Activity</h2>
              <IonButton fill="clear" size="small">
                View All
              </IonButton>
            </div>

            <IonCard>
              <IonCardContent className="p-0">
                {recentActivity.map((activity, index) => (
                  <div 
                    key={activity.id}
                    className={`flex items-center justify-between p-4 ${
                      index < recentActivity.length - 1 ? 'border-b' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-full bg-gray-100`}>
                        <IonIcon 
                          icon={getActivityIcon(activity.type)} 
                          className="text-xl"
                        />
                      </div>
                      <div>
                        <p className="font-medium">{activity.title}</p>
                        <IonText color="medium">
                          <p className="text-sm">{activity.time}</p>
                        </IonText>
                      </div>
                    </div>
                    <IonBadge color={getStatusColor(activity.status)}>
                      {activity.status}
                    </IonBadge>
                  </div>
                ))}
              </IonCardContent>
            </IonCard>
          </div>

          {/* Stats Section */}
          <div className="mt-6">
            <IonGrid>
              <IonRow>
                <IonCol size="6">
                  <IonCard>
                    <IonCardContent className="text-center">
                      <IonText color="primary">
                        <h3 className="text-2xl font-bold">12</h3>
                      </IonText>
                      <IonText color="medium">
                        <p className="text-sm mt-1">Pending Orders</p>
                      </IonText>
                    </IonCardContent>
                  </IonCard>
                </IonCol>
                <IonCol size="6">
                  <IonCard>
                    <IonCardContent className="text-center">
                      <IonText color="success">
                        <h3 className="text-2xl font-bold">3</h3>
                      </IonText>
                      <IonText color="medium">
                        <p className="text-sm mt-1">Deliveries Today</p>
                      </IonText>
                    </IonCardContent>
                  </IonCard>
                </IonCol>
              </IonRow>
            </IonGrid>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default ShopPortalHub;