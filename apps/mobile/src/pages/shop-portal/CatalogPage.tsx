import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonSearchbar,
  IonLabel,
  IonBadge,
  IonIcon,
  IonGrid,
  IonRow,
  IonCol,
  IonChip,
  IonToast,
  IonFab,
  IonFabButton,
  IonSpinner,
  IonText,
  IonItem,
  IonSelect,
  IonSelectOption,
  IonRefresher,
  IonRefresherContent,
  RefresherEventDetail,
} from '@ionic/react';
import { 
  cart, 
  add, 
  remove, 
  filterOutline,
  timeOutline,
  cubeOutline,
  cartOutline,
  checkmarkCircle,
  alertCircle,
  refreshOutline
} from 'ionicons/icons';
import { useAuth } from '@/contexts/EnhancedAuthContext';
import { useHistory } from 'react-router-dom';
import { shopPortalService, OrderableItem } from '@/services/shopPortalService';

interface CartItem extends OrderableItem {
  cartQuantity: number;
}

const CatalogPage: React.FC = () => {
  const { user, companyId, locationId } = useAuth();
  const history = useHistory();
  
  const [items, setItems] = useState<OrderableItem[]>([]);
  const [cartItems, setCartItems] = useState<Map<string, CartItem>>(new Map());
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'warning' | 'danger'>('success');

  useEffect(() => {
    if (companyId && locationId) {
      shopPortalService.updateCredentials(companyId, locationId);
      fetchOrderableItems();
      loadCartFromStorage();
    }
  }, [companyId, locationId]);

  const loadCartFromStorage = () => {
    const savedCart = localStorage.getItem('shopPortalCart');
    if (savedCart) {
      try {
        const cartData = JSON.parse(savedCart);
        setCartItems(new Map(cartData));
      } catch (error) {
        console.error('Error loading cart from storage:', error);
      }
    }
  };

  const saveCartToStorage = (cart: Map<string, CartItem>) => {
    localStorage.setItem('shopPortalCart', JSON.stringify(Array.from(cart.entries())));
  };

  const fetchOrderableItems = async () => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (searchText) params.search = searchText;
      if (selectedCategory !== 'all') params.category = selectedCategory;
      if (selectedType !== 'all') params.itemType = selectedType;

      const response = await shopPortalService.getOrderableItems(params);

      if (response.success) {
        setItems(response.data || []);
      } else {
        showNotification('Failed to load items', 'danger');
      }
    } catch (error) {
      console.error('Error fetching orderable items:', error);
      showNotification('Error loading catalog', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async (event: CustomEvent<RefresherEventDetail>) => {
    await fetchOrderableItems();
    event.detail.complete();
  };

  const addToCart = (item: OrderableItem, quantity?: number) => {
    const newCart = new Map(cartItems);
    const existingItem = newCart.get(item.id);
    const qtyToAdd = quantity || item.minOrderQuantity;
    
    if (existingItem) {
      const newQuantity = existingItem.cartQuantity + qtyToAdd;
      if (newQuantity <= item.maxOrderQuantity) {
        existingItem.cartQuantity = newQuantity;
        newCart.set(item.id, existingItem);
        showNotification(`Updated ${item.name} quantity`, 'success');
      } else {
        showNotification(`Maximum order quantity is ${item.maxOrderQuantity} ${item.orderingUOM}`, 'warning');
        return;
      }
    } else {
      if (qtyToAdd >= item.minOrderQuantity) {
        newCart.set(item.id, { ...item, cartQuantity: qtyToAdd });
        showNotification(`Added ${item.name} to cart`, 'success');
      } else {
        showNotification(`Minimum order quantity is ${item.minOrderQuantity} ${item.orderingUOM}`, 'warning');
        return;
      }
    }
    
    setCartItems(newCart);
    saveCartToStorage(newCart);
  };

  const removeFromCart = (itemId: string) => {
    const newCart = new Map(cartItems);
    const item = newCart.get(itemId);
    if (item) {
      newCart.delete(itemId);
      setCartItems(newCart);
      saveCartToStorage(newCart);
      showNotification(`Removed ${item.name} from cart`, 'success');
    }
  };

  const updateCartQuantity = (itemId: string, delta: number) => {
    const newCart = new Map(cartItems);
    const item = newCart.get(itemId);
    if (item) {
      const newQuantity = item.cartQuantity + delta;
      if (newQuantity === 0) {
        removeFromCart(itemId);
      } else if (newQuantity >= item.minOrderQuantity && newQuantity <= item.maxOrderQuantity) {
        item.cartQuantity = newQuantity;
        newCart.set(itemId, item);
        setCartItems(newCart);
        saveCartToStorage(newCart);
      } else if (newQuantity < item.minOrderQuantity) {
        showNotification(`Minimum quantity is ${item.minOrderQuantity}`, 'warning');
      } else {
        showNotification(`Maximum quantity is ${item.maxOrderQuantity}`, 'warning');
      }
    }
  };

  const showNotification = (message: string, color: 'success' | 'warning' | 'danger') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const goToCart = () => {
    history.push('/shop-portal/cart');
  };

  const categories = [...new Set(items.map(item => item.category).filter(Boolean))];
  const cartCount = Array.from(cartItems.values()).reduce((sum, item) => sum + item.cartQuantity, 0);
  const cartTotal = Array.from(cartItems.values()).reduce((sum, item) => sum + (item.cartQuantity * item.estimatedCost), 0);

  const filteredItems = items.filter(item => {
    const matchesSearch = !searchText || 
      item.name.toLowerCase().includes(searchText.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesType = selectedType === 'all' || item.itemType === selectedType;
    return matchesSearch && matchesCategory && matchesType;
  });

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/shop-portal" />
          </IonButtons>
          <IonTitle>Order from Central Kitchen</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={goToCart}>
              <IonIcon icon={cartOutline} />
              {cartCount > 0 && (
                <IonBadge color="danger" style={{ position: 'absolute', top: 5, right: 5 }}>
                  {cartCount}
                </IonBadge>
              )}
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={refreshOutline}
            pullingText="Pull to refresh"
            refreshingSpinner="circles"
            refreshingText="Refreshing..."
          />
        </IonRefresher>

        <div className="p-4">
          {/* Search and Filters */}
          <div className="mb-4">
            <IonSearchbar
              value={searchText}
              onIonInput={(e) => setSearchText(e.detail.value!)}
              placeholder="Search items..."
              showClearButton="focus"
              onIonClear={() => {
                setSearchText('');
                fetchOrderableItems();
              }}
            />
            
            <div className="flex gap-2 mt-2">
              <IonItem className="flex-1">
                <IonLabel>Category</IonLabel>
                <IonSelect 
                  value={selectedCategory} 
                  onIonChange={(e) => setSelectedCategory(e.detail.value)}
                >
                  <IonSelectOption value="all">All Categories</IonSelectOption>
                  {categories.map(cat => (
                    <IonSelectOption key={cat} value={cat}>{cat}</IonSelectOption>
                  ))}
                </IonSelect>
              </IonItem>

              <IonItem className="flex-1">
                <IonLabel>Type</IonLabel>
                <IonSelect 
                  value={selectedType} 
                  onIonChange={(e) => setSelectedType(e.detail.value)}
                >
                  <IonSelectOption value="all">All Types</IonSelectOption>
                  <IonSelectOption value="ingredient">Ingredients</IonSelectOption>
                  <IonSelectOption value="menu-item">Menu Items</IonSelectOption>
                  <IonSelectOption value="recipe">Recipes</IonSelectOption>
                </IonSelect>
              </IonItem>
            </div>

            <IonButton 
              expand="block" 
              fill="outline" 
              onClick={fetchOrderableItems}
              className="mt-2"
            >
              <IonIcon icon={filterOutline} slot="start" />
              Apply Filters
            </IonButton>
          </div>

          {/* Items count */}
          {!loading && (
            <div className="mb-3 flex justify-between items-center">
              <IonText color="medium">
                <p className="text-sm">Showing {filteredItems.length} items</p>
              </IonText>
              {cartCount > 0 && (
                <IonChip color="primary">
                  <IonLabel>{cartCount} items • ${cartTotal.toFixed(2)}</IonLabel>
                </IonChip>
              )}
            </div>
          )}

          {/* Items Grid */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <IonSpinner name="crescent" />
            </div>
          ) : (
            <IonGrid>
              <IonRow>
                {filteredItems.map(item => {
                  const inCart = cartItems.has(item.id);
                  const cartItem = cartItems.get(item.id);
                  
                  return (
                    <IonCol key={item.id} size="12" sizeMd="6" sizeLg="4">
                      <IonCard>
                        <IonCardHeader>
                          <div className="flex justify-between items-start">
                            <IonCardTitle className="text-lg">{item.name}</IonCardTitle>
                            <IonBadge color={item.isAvailable ? 'success' : 'medium'}>
                              {item.isAvailable ? 'Available' : 'Low Stock'}
                            </IonBadge>
                          </div>
                          {item.description && (
                            <IonText color="medium">
                              <p className="text-sm mt-1">{item.description}</p>
                            </IonText>
                          )}
                          {item.category && (
                            <IonChip className="mt-2" color="tertiary" outline>
                              <IonLabel>{item.category}</IonLabel>
                            </IonChip>
                          )}
                        </IonCardHeader>

                        <IonCardContent>
                          <div className="space-y-2 mb-4">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Central Stock:</span>
                              <span className="font-medium">
                                {item.centralKitchenStock} {item.baseUom.shortCode}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Min Order:</span>
                              <span className="font-medium">
                                {item.minOrderQuantity} {item.orderingUOM}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Max Order:</span>
                              <span className="font-medium">
                                {item.maxOrderQuantity} {item.orderingUOM}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Lead Time:</span>
                              <span className="font-medium">
                                <IonIcon icon={timeOutline} className="text-xs mr-1" />
                                {item.leadTimeDays} days
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Est. Cost:</span>
                              <span className="font-medium text-primary">
                                ${item.estimatedCost.toFixed(2)} / {item.orderingUOM}
                              </span>
                            </div>
                          </div>

                          {inCart && cartItem ? (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <IonButton
                                  size="small"
                                  fill="outline"
                                  onClick={() => updateCartQuantity(item.id, -1)}
                                >
                                  <IonIcon icon={remove} />
                                </IonButton>
                                <span className="font-bold text-lg px-4">
                                  {cartItem.cartQuantity} {item.orderingUOM}
                                </span>
                                <IonButton
                                  size="small"
                                  fill="outline"
                                  onClick={() => updateCartQuantity(item.id, 1)}
                                >
                                  <IonIcon icon={add} />
                                </IonButton>
                              </div>
                              <IonButton
                                expand="block"
                                fill="clear"
                                color="danger"
                                size="small"
                                onClick={() => removeFromCart(item.id)}
                              >
                                Remove from Cart
                              </IonButton>
                            </div>
                          ) : (
                            <IonButton
                              expand="block"
                              onClick={() => addToCart(item)}
                              disabled={!item.isAvailable}
                            >
                              <IonIcon icon={cart} slot="start" />
                              Add to Cart ({item.minOrderQuantity} {item.orderingUOM})
                            </IonButton>
                          )}
                        </IonCardContent>
                      </IonCard>
                    </IonCol>
                  );
                })}
              </IonRow>
            </IonGrid>
          )}

          {filteredItems.length === 0 && !loading && (
            <div className="text-center py-12">
              <IonIcon icon={cubeOutline} style={{ fontSize: '64px' }} className="text-gray-400 mb-4" />
              <h3 className="text-lg font-medium">No items found</h3>
              <p className="text-gray-500">Try adjusting your search or filters</p>
            </div>
          )}
        </div>

        {/* Floating Cart Button */}
        {cartCount > 0 && (
          <IonFab vertical="bottom" horizontal="end" slot="fixed">
            <IonFabButton onClick={goToCart}>
              <IonBadge color="danger" style={{ position: 'absolute', top: -5, right: -5 }}>
                {cartCount}
              </IonBadge>
              <IonIcon icon={cartOutline} />
            </IonFabButton>
          </IonFab>
        )}

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          color={toastColor}
          duration={2000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default CatalogPage;