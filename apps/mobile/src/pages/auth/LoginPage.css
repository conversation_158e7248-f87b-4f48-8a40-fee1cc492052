/* Login Page Styles */
.login-content {
  --background: #f8fafc;
}

.login-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--ion-color-primary);
  margin: 0 0 0.5rem 0;
}

.login-header p {
  font-size: 1.1rem;
  color: var(--ion-color-medium);
  margin: 0;
}

/* Connection Status */
.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.connection-status.online {
  background: var(--ion-color-success-tint);
  color: var(--ion-color-success-shade);
}

.connection-status.offline {
  background: var(--ion-color-warning-tint);
  color: var(--ion-color-warning-shade);
}

.connection-status ion-icon {
  margin-right: 0.5rem;
}

.pending-sync {
  margin-left: 0.5rem;
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Company and Location Selection */
.company-grid,
.location-grid {
  display: grid;
  gap: 0.75rem;
  margin-top: 1rem;
}

.company-button,
.location-button {
  --border-radius: 0.75rem;
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  height: auto;
  min-height: 3rem;
}

.location-button {
  text-align: left;
}

.location-name {
  font-weight: 600;
  font-size: 1rem;
}

.location-address {
  font-size: 0.875rem;
  color: var(--ion-color-medium);
  margin-top: 0.25rem;
}

.selected-company,
.selected-info {
  font-size: 0.875rem;
  color: var(--ion-color-medium);
  margin-top: 0.5rem;
}

.selected-info div {
  margin: 0.25rem 0;
}

.change-company-btn,
.change-location-btn {
  margin-bottom: 1rem;
}

/* PIN Entry */
.pin-display {
  text-align: center;
  margin: 2rem 0;
}

.pin-dots {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
}

.pin-dot {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--ion-color-medium);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.pin-dot.filled {
  background: var(--ion-color-primary);
  border-color: var(--ion-color-primary);
}

/* Keypad */
.keypad {
  margin: 2rem 0;
}

.keypad ion-button {
  --border-radius: 0.75rem;
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.25rem 0;
  height: 3.5rem;
}

/* Login Button */
.login-button {
  --border-radius: 0.75rem;
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  margin-top: 2rem;
  height: 3.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--ion-color-medium);
}

.empty-state p {
  margin-bottom: 1rem;
}

/* Error Message */
.error-message {
  text-align: center;
  margin: 1rem 0;
  padding: 1rem;
  background: var(--ion-color-danger-tint);
  border-radius: 0.5rem;
}

.error-message p {
  margin: 0;
  font-weight: 500;
}

/* Offline Notice */
.offline-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: var(--ion-color-warning-tint);
  color: var(--ion-color-warning-shade);
  border-radius: 0.5rem;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.offline-notice ion-icon {
  margin-right: 0.5rem;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Responsive */
@media (max-width: 480px) {
  .login-container {
    padding: 1rem 0.5rem;
  }
  
  .login-header h1 {
    font-size: 1.75rem;
  }
  
  .company-grid,
  .location-grid {
    gap: 0.5rem;
  }
  
  .pin-dots {
    gap: 0.5rem;
  }
  
  .pin-dot {
    width: 0.875rem;
    height: 0.875rem;
  }
}