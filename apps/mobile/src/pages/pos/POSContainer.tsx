import React, { useState, useEffect } from 'react';
import { IonPage, IonRouterOutlet } from '@ionic/react';
import { Route, Redirect } from 'react-router-dom';
import { useEnhancedAuth } from '@/contexts/EnhancedAuthContext';
import { POSCartItem } from '@/types/order';
import { POSOrderResponse } from '@/services/posService';
import Dashboard from './Dashboard';
import OrderEntry from './OrderEntry';
import Payment from './Payment';
import Receipt from './Receipt';

type POSFlow = 'dashboard' | 'order-entry' | 'payment' | 'receipt';

interface POSContainerProps {
  basePath?: string;
}

const POSContainer: React.FC<POSContainerProps> = ({ basePath = '/pos' }) => {
  const { currentUser, company, location } = useEnhancedAuth();
  
  // POS flow state
  const [currentFlow, setCurrentFlow] = useState<POSFlow>('dashboard');
  const [cartItems, setCartItems] = useState<POSCartItem[]>([]);
  const [currentTransaction, setCurrentTransaction] = useState<POSOrderResponse | null>(null);
  const [paymentDetails, setPaymentDetails] = useState<{
    method: string;
    cashReceived?: number;
    customerName?: string;
    customerPhone?: string;
  } | null>(null);

  // Reset flow when user changes
  useEffect(() => {
    setCurrentFlow('dashboard');
    setCartItems([]);
    setCurrentTransaction(null);
    setPaymentDetails(null);
  }, [currentUser?.id, company?.id, location?.id]);

  const handleNewOrder = () => {
    setCurrentFlow('order-entry');
    setCartItems([]);
    setCurrentTransaction(null);
    setPaymentDetails(null);
  };

  const handleProceedToPayment = (items: POSCartItem[]) => {
    setCartItems(items);
    setCurrentFlow('payment');
  };

  const handlePaymentComplete = (
    transaction: POSOrderResponse,
    payment: {
      method: string;
      cashReceived?: number;
      customerName?: string;
      customerPhone?: string;
    }
  ) => {
    setCurrentTransaction(transaction);
    setPaymentDetails(payment);
    setCurrentFlow('receipt');
  };

  const handleBackToDashboard = () => {
    setCurrentFlow('dashboard');
    setCartItems([]);
    setCurrentTransaction(null);
    setPaymentDetails(null);
  };

  const handleBackToOrderEntry = () => {
    setCurrentFlow('order-entry');
  };

  const handleViewReports = () => {
    // Navigate to reports page or show reports modal
    console.log('Navigate to reports');
  };

  const handleViewInventory = () => {
    // Navigate to inventory page
    console.log('Navigate to inventory');
  };

  const handleSettings = () => {
    // Navigate to settings page
    console.log('Navigate to settings');
  };

  // Render current flow
  const renderCurrentFlow = () => {
    switch (currentFlow) {
      case 'dashboard':
        return (
          <Dashboard
            onNewOrder={handleNewOrder}
            onViewReports={handleViewReports}
            onViewInventory={handleViewInventory}
            onSettings={handleSettings}
          />
        );

      case 'order-entry':
        return (
          <OrderEntry
            onProceedToPayment={handleProceedToPayment}
            onBack={handleBackToDashboard}
          />
        );

      case 'payment':
        return (
          <Payment
            cartItems={cartItems}
            onPaymentComplete={handlePaymentComplete}
            onBack={handleBackToOrderEntry}
          />
        );

      case 'receipt':
        if (!currentTransaction || !paymentDetails) {
          // Fallback to dashboard if transaction data is missing
          setCurrentFlow('dashboard');
          return null;
        }
        return (
          <Receipt
            transaction={currentTransaction}
            paymentMethod={paymentDetails.method}
            cashReceived={paymentDetails.cashReceived}
            customerName={paymentDetails.customerName}
            customerPhone={paymentDetails.customerPhone}
            onNewOrder={handleNewOrder}
            onDashboard={handleBackToDashboard}
          />
        );

      default:
        return <Redirect to={`${basePath}/dashboard`} />;
    }
  };

  return (
    <IonPage>
      {renderCurrentFlow()}
    </IonPage>
  );
};

export default POSContainer;

// Alternative routing-based approach for more complex applications
export const POSRoutes: React.FC<{ basePath?: string }> = ({ basePath = '/pos' }) => {
  return (
    <IonRouterOutlet>
      <Route exact path={`${basePath}/dashboard`}>
        <Dashboard
          onNewOrder={() => window.location.href = `${basePath}/order-entry`}
          onViewReports={() => console.log('Navigate to reports')}
          onViewInventory={() => console.log('Navigate to inventory')}
          onSettings={() => console.log('Navigate to settings')}
        />
      </Route>
      
      <Route exact path={`${basePath}/order-entry`}>
        <OrderEntry
          onProceedToPayment={() => window.location.href = `${basePath}/payment`}
          onBack={() => window.location.href = `${basePath}/dashboard`}
        />
      </Route>
      
      <Route exact path={`${basePath}/payment`}>
        <Payment
          cartItems={[]} // You'd need to manage this state differently for routing
          onPaymentComplete={() => window.location.href = `${basePath}/receipt`}
          onBack={() => window.location.href = `${basePath}/order-entry`}
        />
      </Route>
      
      <Route exact path={`${basePath}/receipt`}>
        <Receipt
          transaction={{} as POSOrderResponse} // You'd need to pass this via state or props
          paymentMethod="cash"
          onNewOrder={() => window.location.href = `${basePath}/order-entry`}
          onDashboard={() => window.location.href = `${basePath}/dashboard`}
        />
      </Route>
      
      <Route exact path={basePath}>
        <Redirect to={`${basePath}/dashboard`} />
      </Route>
    </IonRouterOutlet>
  );
};