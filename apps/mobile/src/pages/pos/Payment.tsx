import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonMenuButton,
  IonButton,
  IonIcon,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonText,
  IonGrid,
  IonRow,
  IonCol,
  IonChip,
  IonToast,
  IonAlert,
  IonLoading,
  IonTextarea,
  IonCheckbox,
  IonNote,
} from '@ionic/react';
import {
  card,
  cash,
  call,
  checkmarkCircle,
  warning,
  calculator,
  receipt,
  person,
  business,
  arrowBack,
} from 'ionicons/icons';
import { useCart } from '@/hooks/useCart';
import { useEnhancedAuth } from '@/contexts/EnhancedAuthContext';
import { posService, POSOrder, POSOrderResponse } from '@/services/posService';
import { PaymentMethod } from '@/types/order';
import { POSCartItem } from '@/types/order';
import { CartItemComponent } from '@/components/pos';

interface PaymentProps {
  cartItems: POSCartItem[];
  onPaymentComplete: (transaction: POSOrderResponse) => void;
  onBack: () => void;
}

const Payment: React.FC<PaymentProps> = ({
  cartItems,
  onPaymentComplete,
  onBack,
}) => {
  const { currentUser, company, location } = useEnhancedAuth();
  const { getSubtotal, getTax, getTotalPrice, clearCart } = useCart();

  // Payment state
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [cashReceived, setCashReceived] = useState<number>(0);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [tableNumber, setTableNumber] = useState<number | undefined>();
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [requireCustomerInfo, setRequireCustomerInfo] = useState(false);
  
  // UI state
  const [isProcessing, setIsProcessing] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [showConfirmAlert, setShowConfirmAlert] = useState(false);
  const [showCalculator, setShowCalculator] = useState(false);
  const [calculatorValue, setCalculatorValue] = useState('');
  
  // Calculate totals
  const subtotal = getSubtotal();
  const tax = getTax();
  const total = getTotalPrice();
  const change = paymentMethod === 'cash' ? posService.calculateChange(total, cashReceived) : 0;
  const isValidCashPayment = paymentMethod !== 'cash' || cashReceived >= total;

  useEffect(() => {
    // Auto-set cash received to total for quick transactions
    if (paymentMethod === 'cash' && cashReceived === 0) {
      setCashReceived(Math.ceil(total));
    }
  }, [paymentMethod, total, cashReceived]);

  const handlePaymentMethodChange = (method: PaymentMethod) => {
    setPaymentMethod(method);
    if (method !== 'cash') {
      setCashReceived(0);
    } else {
      setCashReceived(Math.ceil(total));
    }
  };

  const handleQuickCashAmount = (amount: number) => {
    setCashReceived(amount);
  };

  const handleCalculatorInput = (value: string) => {
    if (value === 'clear') {
      setCalculatorValue('');
    } else if (value === 'backspace') {
      setCalculatorValue(prev => prev.slice(0, -1));
    } else if (value === 'enter') {
      const numValue = parseFloat(calculatorValue);
      if (!isNaN(numValue)) {
        setCashReceived(numValue);
      }
      setShowCalculator(false);
      setCalculatorValue('');
    } else {
      setCalculatorValue(prev => prev + value);
    }
  };

  const validatePayment = (): { isValid: boolean; error?: string } => {
    if (cartItems.length === 0) {
      return { isValid: false, error: 'No items in cart' };
    }

    if (paymentMethod === 'cash' && cashReceived < total) {
      return { isValid: false, error: 'Insufficient cash received' };
    }

    if (requireCustomerInfo && !customerName.trim()) {
      return { isValid: false, error: 'Customer name is required' };
    }

    return { isValid: true };
  };

  const handleProcessPayment = async () => {
    const validation = validatePayment();
    if (!validation.isValid) {
      showToastMessage(validation.error || 'Invalid payment details');
      return;
    }

    setShowConfirmAlert(true);
  };

  const confirmPayment = async () => {
    if (!company || !location) {
      showToastMessage('Company or location information missing');
      return;
    }

    setIsProcessing(true);
    setShowConfirmAlert(false);

    try {
      // Prepare order data
      const order: POSOrder = {
        items: cartItems.map(item => ({
          id: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          modifiers: item.modifiers,
          specialInstructions: item.specialInstructions,
        })),
        subtotal,
        tax,
        total,
        paymentMethod,
        customerName: customerName.trim() || undefined,
        customerPhone: customerPhone.trim() || undefined,
        tableNumber,
        specialInstructions: specialInstructions.trim() || undefined,
      };

      // Process the order
      const response = await posService.processOrder(order, company.id, location.id);

      // Clear cart and show success
      clearCart();
      showToastMessage('Payment processed successfully!');
      
      // Navigate to receipt
      onPaymentComplete(response);

    } catch (error) {
      console.error('Payment processing failed:', error);
      showToastMessage('Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const getQuickCashAmounts = () => {
    const amounts = [
      Math.ceil(total),
      Math.ceil(total / 5) * 5,
      Math.ceil(total / 10) * 10,
      Math.ceil(total / 20) * 20,
    ];
    return [...new Set(amounts)].sort((a, b) => a - b);
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonMenuButton slot="start" />
          <IonTitle>Payment</IonTitle>
          <IonButton fill="clear" slot="start" onClick={onBack}>
            <IonIcon icon={arrowBack} />
            Back
          </IonButton>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonGrid>
          <IonRow>
            {/* Order summary */}
            <IonCol size="12" sizeMd="6">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Order Summary</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  {/* Order items */}
                  <div className="space-y-2 mb-4">
                    {cartItems.map((item) => (
                      <CartItemComponent
                        key={`${item.id}-${item.timestamp}`}
                        item={item}
                        onUpdateQuantity={() => {}} // Read-only in payment
                        onRemove={() => {}} // Read-only in payment
                        readonly={true}
                        compact={true}
                      />
                    ))}
                  </div>

                  {/* Price breakdown */}
                  <div className="border-t pt-4 space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax (8%):</span>
                      <span>${tax.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold border-t pt-2">
                      <span>Total:</span>
                      <IonText color="primary">
                        <strong>${total.toFixed(2)}</strong>
                      </IonText>
                    </div>
                    {paymentMethod === 'cash' && cashReceived > 0 && (
                      <>
                        <div className="flex justify-between">
                          <span>Cash Received:</span>
                          <span>${cashReceived.toFixed(2)}</span>
                        </div>
                        {change > 0 && (
                          <div className="flex justify-between text-green-600 font-semibold">
                            <span>Change:</span>
                            <span>${change.toFixed(2)}</span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>

            {/* Payment details */}
            <IonCol size="12" sizeMd="6">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Payment Details</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  {/* Payment method selection */}
                  <div className="mb-6">
                    <IonLabel className="block mb-3 font-semibold">Payment Method</IonLabel>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                      {(['cash', 'card', 'digital_wallet'] as PaymentMethod[]).map((method) => (
                        <IonButton
                          key={method}
                          fill={paymentMethod === method ? 'solid' : 'outline'}
                          onClick={() => handlePaymentMethodChange(method)}
                          className="h-16"
                        >
                          <div className="flex flex-col items-center">
                            <IonIcon 
                              icon={method === 'cash' ? cash : method === 'card' ? card : call} 
                              className="text-xl mb-1"
                            />
                            <span className="text-sm capitalize">
                              {method.replace('_', ' ')}
                            </span>
                          </div>
                        </IonButton>
                      ))}
                    </div>
                  </div>

                  {/* Cash payment details */}
                  {paymentMethod === 'cash' && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-3">
                        <IonLabel className="font-semibold">Cash Received</IonLabel>
                        <IonButton
                          fill="outline"
                          size="small"
                          onClick={() => setShowCalculator(true)}
                        >
                          <IonIcon icon={calculator} slot="start" />
                          Calculator
                        </IonButton>
                      </div>
                      
                      <IonItem>
                        <IonLabel position="stacked">Amount Received</IonLabel>
                        <IonInput
                          type="number"
                          step="0.01"
                          min="0"
                          value={cashReceived}
                          onIonInput={(e) => setCashReceived(parseFloat(e.detail.value!) || 0)}
                          placeholder="0.00"
                        />
                      </IonItem>

                      {/* Quick cash amounts */}
                      <div className="mt-3">
                        <IonLabel className="block mb-2 text-sm text-gray-600">Quick Amounts</IonLabel>
                        <div className="flex flex-wrap gap-2">
                          {getQuickCashAmounts().map((amount) => (
                            <IonButton
                              key={amount}
                              size="small"
                              fill="outline"
                              onClick={() => handleQuickCashAmount(amount)}
                            >
                              ${amount}
                            </IonButton>
                          ))}
                        </div>
                      </div>

                      {/* Cash validation */}
                      {cashReceived > 0 && (
                        <div className="mt-3">
                          {cashReceived < total ? (
                            <IonChip color="danger">
                              <IonIcon icon={warning} />
                              <span className="ml-1">
                                Insufficient amount (${(total - cashReceived).toFixed(2)} short)
                              </span>
                            </IonChip>
                          ) : (
                            <IonChip color="success">
                              <IonIcon icon={checkmarkCircle} />
                              <span className="ml-1">
                                {change > 0 ? `Change: $${change.toFixed(2)}` : 'Exact amount'}
                              </span>
                            </IonChip>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Customer information */}
                  <div className="mb-6">
                    <div className="flex items-center mb-3">
                      <IonCheckbox
                        checked={requireCustomerInfo}
                        onIonChange={(e) => setRequireCustomerInfo(e.detail.checked)}
                      />
                      <IonLabel className="ml-2">Collect Customer Information</IonLabel>
                    </div>

                    {requireCustomerInfo && (
                      <div className="space-y-3">
                        <IonItem>
                          <IonLabel position="stacked">Customer Name *</IonLabel>
                          <IonInput
                            value={customerName}
                            onIonInput={(e) => setCustomerName(e.detail.value!)}
                            placeholder="Enter customer name"
                          />
                        </IonItem>

                        <IonItem>
                          <IonLabel position="stacked">Customer Phone</IonLabel>
                          <IonInput
                            type="tel"
                            value={customerPhone}
                            onIonInput={(e) => setCustomerPhone(e.detail.value!)}
                            placeholder="Enter call number"
                          />
                        </IonItem>

                        <IonItem>
                          <IonLabel position="stacked">Table Number</IonLabel>
                          <IonInput
                            type="number"
                            value={tableNumber}
                            onIonInput={(e) => setTableNumber(parseInt(e.detail.value!) || undefined)}
                            placeholder="Enter table number"
                          />
                        </IonItem>
                      </div>
                    )}
                  </div>

                  {/* Special instructions */}
                  <div className="mb-6">
                    <IonItem>
                      <IonLabel position="stacked">Special Instructions</IonLabel>
                      <IonTextarea
                        value={specialInstructions}
                        onIonInput={(e) => setSpecialInstructions(e.detail.value!)}
                        placeholder="Any special instructions for this order..."
                        rows={3}
                      />
                    </IonItem>
                  </div>

                  {/* Process payment button */}
                  <IonButton
                    expand="block"
                    onClick={handleProcessPayment}
                    disabled={!isValidCashPayment || isProcessing}
                    className="mt-4"
                    size="large"
                  >
                    <IonIcon icon={receipt} slot="start" />
                    Process Payment - ${total.toFixed(2)}
                  </IonButton>

                  {/* Payment info */}
                  <div className="mt-4 text-center">
                    <IonNote color="medium">
                      <div className="flex items-center justify-center text-sm">
                        <IonIcon icon={person} className="mr-1" />
                        Cashier: {currentUser?.name}
                      </div>
                      <div className="flex items-center justify-center text-sm mt-1">
                        <IonIcon icon={business} className="mr-1" />
                        {company?.name} - {location?.name}
                      </div>
                    </IonNote>
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
        </IonGrid>

        {/* Calculator Modal */}
        {showCalculator && (
          <IonCard className="fixed inset-x-4 top-1/4 z-50 max-w-sm mx-auto">
            <IonCardHeader>
              <IonCardTitle className="text-center">Calculator</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="text-center mb-4">
                <div className="text-2xl font-mono p-3 bg-gray-100 rounded">
                  ${calculatorValue || '0'}
                </div>
              </div>
              <div className="grid grid-cols-3 gap-2">
                {['7', '8', '9', '4', '5', '6', '1', '2', '3'].map((num) => (
                  <IonButton
                    key={num}
                    fill="outline"
                    onClick={() => handleCalculatorInput(num)}
                  >
                    {num}
                  </IonButton>
                ))}
                <IonButton fill="outline" onClick={() => handleCalculatorInput('0')}>
                  0
                </IonButton>
                <IonButton fill="outline" onClick={() => handleCalculatorInput('.')}>
                  .
                </IonButton>
                <IonButton color="danger" onClick={() => handleCalculatorInput('backspace')}>
                  ⌫
                </IonButton>
              </div>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <IonButton color="medium" onClick={() => handleCalculatorInput('clear')}>
                  Clear
                </IonButton>
                <IonButton color="success" onClick={() => handleCalculatorInput('enter')}>
                  Enter
                </IonButton>
              </div>
              <IonButton
                fill="clear"
                expand="block"
                onClick={() => setShowCalculator(false)}
                className="mt-2"
              >
                Cancel
              </IonButton>
            </IonCardContent>
          </IonCard>
        )}

        {/* Loading overlay */}
        <IonLoading
          isOpen={isProcessing}
          message="Processing payment..."
          spinner="crescent"
        />

        {/* Confirmation alert */}
        <IonAlert
          isOpen={showConfirmAlert}
          onDidDismiss={() => setShowConfirmAlert(false)}
          header="Confirm Payment"
          message={`Process ${paymentMethod} payment of $${total.toFixed(2)}?`}
          buttons={[
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'Confirm',
              handler: confirmPayment,
            },
          ]}
        />

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="top"
        />
      </IonContent>
    </IonPage>
  );
};

export default Payment;