import React from 'react';
import { Redirect, Route } from 'react-router-dom';
import {
  IonApp,
  IonRouterOutlet,
  setupIonicReact
} from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';

// Core CSS required for Ionic components to work properly
import '@ionic/react/css/core.css';

// Basic CSS for apps built with Ionic
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';

// Optional CSS utils that can be commented out
import '@ionic/react/css/padding.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/display.css';

// Theme variables
import './styles/variables.css';
import './styles/globals.css';

// Import pages
import InventoryPage from './pages/InventoryPage';
import OrderingPage from './pages/OrderingPage';
import SettingsPage from './pages/SettingsPage';
import LoginPage from './pages/auth/LoginPage';

// Import POS-specific pages
import { DashboardPage, OrderEntry, Payment, Receipt } from './pages/pos';

// Import Shop Portal pages
import ShopPortalHub from './pages/shop-portal/ShopPortalHub';
import CatalogPage from './pages/shop-portal/CatalogPage';
import CartPage from './pages/shop-portal/CartPage';
import OrdersPage from './pages/shop-portal/OrdersPage';

// Import Delivery pages
import DispatchHandover from './pages/delivery/DispatchHandover';
import DriverDashboard from './pages/delivery/DriverDashboard';
import DeliveryReceiving from './pages/delivery/DeliveryReceiving';

// Import contexts and components
import { AppContextProvider } from './contexts/AppContext';
import { EnhancedAuthProvider, useEnhancedAuth } from './contexts/EnhancedAuthContext';
import { PrivateRoute } from './components/auth/PrivateRoute';
import { ErrorBoundary, MainLayout } from './components';

setupIonicReact();

// Main app component with authentication
const AuthenticatedApp: React.FC = () => {
  const { isAuthenticated } = useEnhancedAuth();

  if (!isAuthenticated) {
    return (
      <IonReactRouter>
        <IonRouterOutlet>
          <Route path="/login" component={LoginPage} exact />
          <Route exact path="/">
            <Redirect to="/login" />
          </Route>
        </IonRouterOutlet>
      </IonReactRouter>
    );
  }

  return (
    <IonReactRouter>
      <MainLayout>
        <IonRouterOutlet>
          {/* POS Routes */}
          <PrivateRoute exact path="/pos" render={() => <Redirect to="/pos/dashboard" />} />
          <PrivateRoute exact path="/pos/dashboard" component={DashboardPage} />
          <PrivateRoute exact path="/pos/order-entry" component={OrderEntry} />
          <PrivateRoute exact path="/pos/payment" component={Payment} />
          <PrivateRoute exact path="/pos/receipt" component={Receipt} />
          
          {/* Shop Portal Routes */}
          <PrivateRoute exact path="/shop-portal" component={ShopPortalHub} />
          <PrivateRoute exact path="/shop-portal/catalog" component={CatalogPage} />
          <PrivateRoute exact path="/shop-portal/cart" component={CartPage} />
          <PrivateRoute exact path="/shop-portal/orders" component={OrdersPage} />
          <PrivateRoute exact path="/shop-portal/receive" component={DeliveryReceiving} />
          
          {/* Delivery Routes */}
          <PrivateRoute exact path="/delivery/dispatch" component={DispatchHandover} />
          <PrivateRoute exact path="/delivery/driver" component={DriverDashboard} />
          <PrivateRoute exact path="/delivery/receive" component={DeliveryReceiving} />
          
          {/* Other main routes */}
          <PrivateRoute exact path="/inventory" component={InventoryPage} />
          <PrivateRoute exact path="/ordering" component={OrderingPage} />
          <PrivateRoute exact path="/settings" component={SettingsPage} />
          
          {/* Auth routes */}
          <Route exact path="/login" component={LoginPage} />
          
          {/* Default redirect */}
          <Route exact path="/">
            <Redirect to="/pos/dashboard" />
          </Route>
        </IonRouterOutlet>
      </MainLayout>
    </IonReactRouter>
  );
};

const App: React.FC = () => (
  <IonApp>
    <ErrorBoundary>
      <EnhancedAuthProvider>
        <AppContextProvider>
          <AuthenticatedApp />
        </AppContextProvider>
      </EnhancedAuthProvider>
    </ErrorBoundary>
  </IonApp>
);

export default App;