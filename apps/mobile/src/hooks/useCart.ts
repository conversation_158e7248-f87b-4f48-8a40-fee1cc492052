import { useState, useCallback, useEffect } from 'react';
import { MenuItem } from '@/types';
import { POSCartItem } from '@/types/order';
import { BranchInventoryItem } from '@/types/api';
import { posService } from '@/services/posService';

interface UseCartReturn {
  cartItems: POSCartItem[];
  addToCart: (item: MenuItem | BranchInventoryItem, quantity?: number) => Promise<boolean>;
  removeFromCart: (itemId: string, timestamp?: number) => void;
  updateQuantity: (itemId: string, quantity: number, timestamp?: number) => void;
  clearCart: () => void;
  getTotalPrice: () => number;
  getTotalItems: () => number;
  getItemInCart: (itemId: string) => POSCartItem | undefined;
  getSubtotal: () => number;
  getTax: (taxRate?: number) => number;
  validateCartStock: () => Promise<{ isValid: boolean; issues: string[] }>;
  cartErrors: string[];
  clearErrors: () => void;
}

export const useCart = (): UseCartReturn => {
  const [cartItems, setCartItems] = useState<POSCartItem[]>([]);
  const [cartErrors, setCartErrors] = useState<string[]>([]);

  // Load cart from localStorage on mount
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem('pos_cart');
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        setCartItems(parsedCart);
      }
    } catch (error) {
      console.warn('Failed to load cart from localStorage:', error);
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('pos_cart', JSON.stringify(cartItems));
    } catch (error) {
      console.warn('Failed to save cart to localStorage:', error);
    }
  }, [cartItems]);

  const addToCart = useCallback(async (item: MenuItem | BranchInventoryItem, quantity: number = 1): Promise<boolean> => {
    try {
      // Convert MenuItem or BranchInventoryItem to POSCartItem
      const posItem: POSCartItem = {
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: 0, // Will be updated below
        category: item.category,
        imageUrl: 'imageUrl' in item ? item.imageUrl : item.image,
        description: item.description,
        isAvailable: item.isAvailable ?? true,
        stockLevel: 'stockLevel' in item ? item.stockLevel : undefined,
        timestamp: Date.now(),
      };

      // Check stock availability if stockLevel is available
      if (posItem.stockLevel !== undefined && posItem.stockLevel < quantity) {
        setCartErrors(prev => [...prev, `Not enough stock for ${item.name}. Available: ${posItem.stockLevel}`]);
        return false;
      }

      setCartItems(prevItems => {
        // Check if item already exists in cart (same id and modifiers)
        const existingItemIndex = prevItems.findIndex(cartItem => 
          cartItem.id === item.id && 
          JSON.stringify(cartItem.modifiers) === JSON.stringify(posItem.modifiers)
        );
        
        if (existingItemIndex > -1) {
          // Update existing item quantity
          const updatedItems = [...prevItems];
          const newQuantity = updatedItems[existingItemIndex].quantity + quantity;
          
          // Check stock for new quantity
          if (posItem.stockLevel !== undefined && posItem.stockLevel < newQuantity) {
            setCartErrors(prev => [...prev, `Not enough stock for ${item.name}. Available: ${posItem.stockLevel}`]);
            return prevItems; // Don't update if not enough stock
          }
          
          updatedItems[existingItemIndex] = {
            ...updatedItems[existingItemIndex],
            quantity: newQuantity,
          };
          return updatedItems;
        } else {
          // Add new item to cart
          const newCartItem: POSCartItem = {
            ...posItem,
            quantity,
          };
          return [...prevItems, newCartItem];
        }
      });

      return true;
    } catch (error) {
      console.error('Error adding item to cart:', error);
      setCartErrors(prev => [...prev, `Failed to add ${item.name} to cart`]);
      return false;
    }
  }, []);

  const removeFromCart = useCallback((itemId: string, timestamp?: number) => {
    setCartItems(prevItems => {
      if (timestamp) {
        // Remove specific item by timestamp (for items with same ID but different modifiers)
        return prevItems.filter(item => !(item.id === itemId && item.timestamp === timestamp));
      } else {
        // Find first matching item and either decrease quantity or remove
        const existingItemIndex = prevItems.findIndex(cartItem => cartItem.id === itemId);
        
        if (existingItemIndex > -1) {
          const updatedItems = [...prevItems];
          const currentItem = updatedItems[existingItemIndex];
          
          if (currentItem.quantity > 1) {
            // Decrease quantity
            updatedItems[existingItemIndex] = {
              ...currentItem,
              quantity: currentItem.quantity - 1,
            };
            return updatedItems;
          } else {
            // Remove item completely
            return updatedItems.filter((_, index) => index !== existingItemIndex);
          }
        }
        
        return prevItems;
      }
    });
  }, []);

  const updateQuantity = useCallback((itemId: string, quantity: number, timestamp?: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId, timestamp);
      return;
    }

    setCartItems(prevItems => {
      const existingItemIndex = timestamp 
        ? prevItems.findIndex(cartItem => cartItem.id === itemId && cartItem.timestamp === timestamp)
        : prevItems.findIndex(cartItem => cartItem.id === itemId);
      
      if (existingItemIndex > -1) {
        const updatedItems = [...prevItems];
        const currentItem = updatedItems[existingItemIndex];
        
        // Check stock if available
        if (currentItem.stockLevel !== undefined && currentItem.stockLevel < quantity) {
          setCartErrors(prev => [...prev, `Not enough stock for ${currentItem.name}. Available: ${currentItem.stockLevel}`]);
          return prevItems;
        }
        
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity,
        };
        return updatedItems;
      }
      
      return prevItems;
    });
  }, [removeFromCart]);

  const clearCart = useCallback(() => {
    setCartItems([]);
    setCartErrors([]);
    try {
      localStorage.removeItem('pos_cart');
    } catch (error) {
      console.warn('Failed to clear cart from localStorage:', error);
    }
  }, []);

  const getSubtotal = useCallback(() => {
    return cartItems.reduce((total, item) => {
      const itemTotal = item.price * item.quantity;
      const modifierTotal = (item.modifiers || []).reduce((modTotal, mod) => modTotal + mod.price, 0) * item.quantity;
      return total + itemTotal + modifierTotal;
    }, 0);
  }, [cartItems]);

  const getTax = useCallback((taxRate: number = 0.08) => {
    return posService.calculateTax(getSubtotal(), taxRate);
  }, [getSubtotal]);

  const getTotalPrice = useCallback(() => {
    const subtotal = getSubtotal();
    const tax = getTax();
    return subtotal + tax;
  }, [getSubtotal, getTax]);

  const getTotalItems = useCallback(() => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  }, [cartItems]);

  const getItemInCart = useCallback((itemId: string) => {
    return cartItems.find(item => item.id === itemId);
  }, [cartItems]);

  const validateCartStock = useCallback(async () => {
    try {
      const items = cartItems.map(item => ({
        productId: item.id,
        quantity: item.quantity
      }));

      if (items.length === 0) {
        return { isValid: true, issues: [] };
      }

      const validation = await posService.validateStock(items);
      
      if (!validation.isValid) {
        const issues = validation.unavailableItems.map(item => 
          `${cartItems.find(c => c.id === item.productId)?.name || 'Unknown item'}: requested ${item.requestedQuantity}, available ${item.availableStock}`
        );
        return { isValid: false, issues };
      }

      return { isValid: true, issues: [] };
    } catch (error) {
      console.error('Error validating cart stock:', error);
      return { isValid: false, issues: ['Failed to validate stock availability'] };
    }
  }, [cartItems]);

  const clearErrors = useCallback(() => {
    setCartErrors([]);
  }, []);

  return {
    cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalPrice,
    getTotalItems,
    getItemInCart,
    getSubtotal,
    getTax,
    validateCartStock,
    cartErrors,
    clearErrors,
  };
};