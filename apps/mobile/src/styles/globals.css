@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom global styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  /* Custom component styles that work with Ionic */
  .pos-grid {
    @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4;
  }
  
  .menu-item-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow;
  }
  
  .cart-item {
    @apply flex items-center justify-between p-3 border-b border-gray-100;
  }
  
  .inventory-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 space-y-2;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-badge-danger {
    @apply bg-red-100 text-red-800;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-balance {
    text-wrap: balance;
  }
}