/* Ionic Variables and Theming. For more info, please see:
http://ionicframework.com/docs/theming/ */

/** Ionic CSS Variables **/
:root {
  /** Primary Color **/
  --ion-color-primary: #3b82f6;
  --ion-color-primary-rgb: 59, 130, 246;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #3470d8;
  --ion-color-primary-tint: #4f90f7;

  /** Secondary Color **/
  --ion-color-secondary: #64748b;
  --ion-color-secondary-rgb: 100, 116, 139;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #58667a;
  --ion-color-secondary-tint: #748195;

  /** Tertiary Color **/
  --ion-color-tertiary: #8b5cf6;
  --ion-color-tertiary-rgb: 139, 92, 246;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #7a51d8;
  --ion-color-tertiary-tint: #976df7;

  /** Success Color **/
  --ion-color-success: #22c55e;
  --ion-color-success-rgb: 34, 197, 94;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #1eae52;
  --ion-color-success-tint: #38cb6e;

  /** Warning Color **/
  --ion-color-warning: #f59e0b;
  --ion-color-warning-rgb: 245, 158, 11;
  --ion-color-warning-contrast: #ffffff;
  --ion-color-warning-contrast-rgb: 255, 255, 255;
  --ion-color-warning-shade: #d8890a;
  --ion-color-warning-tint: #f6a823;

  /** Danger Color **/
  --ion-color-danger: #ef4444;
  --ion-color-danger-rgb: 239, 68, 68;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #d23c3c;
  --ion-color-danger-tint: #f15757;

  /** Light Color **/
  --ion-color-light: #f8fafc;
  --ion-color-light-rgb: 248, 250, 252;
  --ion-color-light-contrast: #1e293b;
  --ion-color-light-contrast-rgb: 30, 41, 59;
  --ion-color-light-shade: #dad9db;
  --ion-color-light-tint: #f9fbfc;

  /** Medium Color **/
  --ion-color-medium: #94a3b8;
  --ion-color-medium-rgb: 148, 163, 184;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #838fa2;
  --ion-color-medium-tint: #9facbf;

  /** Dark Color **/
  --ion-color-dark: #1e293b;
  --ion-color-dark-rgb: 30, 41, 59;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1a2433;
  --ion-color-dark-tint: #353e4f;

  /** Custom FoodPrepAI Colors **/
  --ion-color-foodprepai: #2563eb;
  --ion-color-foodprepai-rgb: 37, 99, 235;
  --ion-color-foodprepai-contrast: #ffffff;
  --ion-color-foodprepai-contrast-rgb: 255, 255, 255;
  --ion-color-foodprepai-shade: #2154ce;
  --ion-color-foodprepai-tint: #3b72ed;

  /** Background Colors **/
  --ion-background-color: #ffffff;
  --ion-background-color-rgb: 255, 255, 255;

  --ion-text-color: #1e293b;
  --ion-text-color-rgb: 30, 41, 59;

  --ion-border-color: #e2e8f0;
  --ion-item-border-color: #e2e8f0;

  /** Toolbar Colors **/
  --ion-toolbar-background: #ffffff;
  --ion-toolbar-border-color: #e2e8f0;
  --ion-toolbar-color: #1e293b;

  /** Tab Bar Colors **/
  --ion-tab-bar-background: #ffffff;
  --ion-tab-bar-border-color: #e2e8f0;
  --ion-tab-bar-color: #64748b;
  --ion-tab-bar-color-selected: #3b82f6;

  /** Card Colors **/
  --ion-card-background: #ffffff;
  --ion-card-color: #1e293b;

  /** Item Colors **/
  --ion-item-background: #ffffff;
  --ion-item-color: #1e293b;

  /** Input Colors **/
  --ion-input-background: #ffffff;
  --ion-input-color: #1e293b;
  --ion-input-border-color: #e2e8f0;

  /** Button Colors **/
  --ion-button-border-radius: 0.5rem;

  /** Modal Colors **/
  --ion-modal-background: #ffffff;
  --ion-modal-color: #1e293b;

  /** Popover Colors **/
  --ion-popover-background: #ffffff;
  --ion-popover-color: #1e293b;

  /** Typography **/
  --ion-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    /** Background Colors **/
    --ion-background-color: #0f172a;
    --ion-background-color-rgb: 15, 23, 42;

    --ion-text-color: #f1f5f9;
    --ion-text-color-rgb: 241, 245, 249;

    --ion-border-color: #334155;
    --ion-item-border-color: #334155;

    /** Toolbar Colors **/
    --ion-toolbar-background: #1e293b;
    --ion-toolbar-border-color: #334155;
    --ion-toolbar-color: #f1f5f9;

    /** Tab Bar Colors **/
    --ion-tab-bar-background: #1e293b;
    --ion-tab-bar-border-color: #334155;
    --ion-tab-bar-color: #94a3b8;
    --ion-tab-bar-color-selected: #60a5fa;

    /** Card Colors **/
    --ion-card-background: #1e293b;
    --ion-card-color: #f1f5f9;

    /** Item Colors **/
    --ion-item-background: #1e293b;
    --ion-item-color: #f1f5f9;

    /** Input Colors **/
    --ion-input-background: #334155;
    --ion-input-color: #f1f5f9;
    --ion-input-border-color: #475569;

    /** Modal Colors **/
    --ion-modal-background: #1e293b;
    --ion-modal-color: #f1f5f9;

    /** Popover Colors **/
    --ion-popover-background: #1e293b;
    --ion-popover-color: #f1f5f9;

    /** Adjust medium color for dark mode **/
    --ion-color-medium: #64748b;
    --ion-color-medium-shade: #58667a;
    --ion-color-medium-tint: #748195;

    /** Adjust light color for dark mode **/
    --ion-color-light: #334155;
    --ion-color-light-shade: #2d3748;
    --ion-color-light-tint: #475569;
  }
}

/* iOS specific styles */
.ios {
  --ion-button-border-radius: 0.75rem;
  --ion-card-border-radius: 1rem;
  --ion-modal-border-radius: 1rem;
  --ion-popover-border-radius: 0.75rem;
}

/* Material Design specific styles */
.md {
  --ion-button-border-radius: 0.5rem;
  --ion-card-border-radius: 0.5rem;
  --ion-modal-border-radius: 0.5rem;
  --ion-popover-border-radius: 0.5rem;
}

/* Custom utility classes */
.ion-color-foodprepai {
  --ion-color-base: var(--ion-color-foodprepai);
  --ion-color-base-rgb: var(--ion-color-foodprepai-rgb);
  --ion-color-contrast: var(--ion-color-foodprepai-contrast);
  --ion-color-contrast-rgb: var(--ion-color-foodprepai-contrast-rgb);
  --ion-color-shade: var(--ion-color-foodprepai-shade);
  --ion-color-tint: var(--ion-color-foodprepai-tint);
}

/* Responsive typography */
@media (max-width: 576px) {
  :root {
    --ion-font-size: 14px;
  }
}

@media (min-width: 768px) {
  :root {
    --ion-font-size: 16px;
  }
}

/* Custom spacing utilities */
.ion-padding-xs {
  --ion-padding-top: 0.25rem;
  --ion-padding-end: 0.25rem;
  --ion-padding-bottom: 0.25rem;
  --ion-padding-start: 0.25rem;
}

.ion-padding-sm {
  --ion-padding-top: 0.5rem;
  --ion-padding-end: 0.5rem;
  --ion-padding-bottom: 0.5rem;
  --ion-padding-start: 0.5rem;
}

.ion-padding-lg {
  --ion-padding-top: 1.5rem;
  --ion-padding-end: 1.5rem;
  --ion-padding-bottom: 1.5rem;
  --ion-padding-start: 1.5rem;
}

.ion-padding-xl {
  --ion-padding-top: 2rem;
  --ion-padding-end: 2rem;
  --ion-padding-bottom: 2rem;
  --ion-padding-start: 2rem;
}