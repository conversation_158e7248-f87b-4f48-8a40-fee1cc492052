export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  isAvailable?: boolean;
  preparationTime?: number; // in minutes
  ingredients?: string[];
  allergens?: string[];
  customizations?: MenuItemModifier[];
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
}

export interface MenuCategory {
  id: string;
  name: string;
  description?: string;
  sortOrder: number;
  isActive: boolean;
}

export type MenuItemModifier = {
  id: string;
  name: string;
  price: number;
  isRequired: boolean;
  options?: ModifierOption[];
};

export type ModifierOption = {
  id: string;
  name: string;
  priceAdjustment: number;
};

export interface CartItem extends MenuItem {
  quantity: number;
  modifiers?: MenuItemModifier[];
  specialInstructions?: string;
  timestamp: number; // for unique identification in cart
}