// Core cubetypes based on FoodPrepAI backend structure
export interface BranchInventoryItem {
  id: string;
  name: string;
  description?: string;
  category: string;
  price: number;
  cost?: number;
  isAvailable: boolean;
  stockLevel?: number;
  unit?: string;
  barcode?: string;
  imageUrl?: string;
  alternatives?: BranchInventoryAlternative[];
  sellingOptions?: BranchInventorySellingOption[];
  // Additional cubemanagement fields
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  location?: string; // storage location
  lastRestocked?: Date;
  expiryDate?: Date;
  supplier?: string;
  supplierContact?: string;
  sku?: string;
  notes?: string;
}

export interface BranchInventoryAlternative {
  id: string;
  name: string;
  description?: string;
  price: number;
  cost?: number;
  isAvailable: boolean;
  stockLevel?: number;
  unit?: string;
}

export interface BranchInventorySellingOption {
  id: string;
  name: string;
  description?: string;
  price: number;
  cost?: number;
  unit?: string;
  conversionFactor?: number; // For UOM conversions
}

export interface BranchInventoryCategory {
  category: string;
  items: BranchInventoryItem[];
}

// Legacy type for backward compatibility
export interface InventoryItem extends BranchInventoryItem {
  currentStock: number;
  minStock: number;
  maxStock: number;
  costPerUnit: number;
  isActive: boolean;
}

export interface StockTransaction {
  id: string;
  inventoryItemId: string;
  type: 'restock' | 'usage' | 'waste' | 'adjustment' | 'COUNT' | 'WASTAGE' | 'TRANSFER' | 'SALE';
  quantity: number;
  reason?: string;
  reasonCode?: WastageReason;
  performedBy: string;
  timestamp: Date;
  cost?: number;
  unitOfMeasure?: string;
  batchNumber?: string;
  expiryDate?: Date;
  notes?: string;
  photoUrl?: string;
  companyId?: string;
  locationId?: string;
}

// Inventory Movement for FoodPrepAI API
export interface InventoryMovement {
  itemId: string;
  quantity: number;
  transactionType: 'COUNT' | 'WASTAGE' | 'TRANSFER' | 'SALE' | 'PURCHASE' | 'ADJUSTMENT';
  reason?: string;
  reasonCode?: string;
  unitOfMeasure?: string;
  cost?: number;
  batchNumber?: string;
  expiryDate?: string;
  notes?: string;
  photoUrl?: string;
}

// Stock Count types
export interface StockCountItem {
  inventoryItemId: string;
  systemQuantity: number;
  countedQuantity: number;
  variance: number;
  unit: string;
  reason?: string;
  notes?: string;
}

export interface StockCountSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  performedBy: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  items: StockCountItem[];
  totalVariance: number;
  companyId: string;
  locationId: string;
}

// Wastage types
export interface WastageReason {
  code: string;
  label: string;
  requiresPhoto?: boolean;
  requiresNote?: boolean;
}

export interface WastageRecord {
  id: string;
  inventoryItemId: string;
  quantity: number;
  unit: string;
  reason: WastageReason;
  notes?: string;
  photoUrl?: string;
  performedBy: string;
  timestamp: Date;
  cost?: number;
  companyId: string;
  locationId: string;
}

// Common wastage reasons
export const WASTAGE_REASONS: WastageReason[] = [
  { code: 'EXPIRED', label: 'Expired', requiresPhoto: true },
  { code: 'DAMAGED', label: 'Damaged', requiresPhoto: true },
  { code: 'SPILLED', label: 'Spilled/Dropped', requiresNote: true },
  { code: 'CONTAMINATED', label: 'Contaminated', requiresPhoto: true, requiresNote: true },
  { code: 'OVERCOOKED', label: 'Overcooked', requiresNote: true },
  { code: 'CUSTOMER_RETURN', label: 'Customer Return', requiresNote: true },
  { code: 'TEMPERATURE_ABUSE', label: 'Temperature Abuse', requiresPhoto: true },
  { code: 'MOLD', label: 'Mold/Spoiled', requiresPhoto: true },
  { code: 'PEST_DAMAGE', label: 'Pest Damage', requiresPhoto: true, requiresNote: true },
  { code: 'OTHER', label: 'Other', requiresNote: true },
];

export interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  email?: string;
  call: string;
  address?: string;
  paymentTerms?: string;
  isActive: boolean;
}

export type InventoryAlert = {
  id: string;
  inventoryItemId: string;
  type: 'low_stock' | 'expiring_soon' | 'expired' | 'overstock';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  createdAt: Date;
  isResolved: boolean;
};