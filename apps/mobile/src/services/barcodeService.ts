import { BarcodeScanner, SupportedFormat } from '@capacitor-community/barcode-scanner';
import { Capacitor } from '@capacitor/core';

export interface ScanResult {
  success: boolean;
  data?: string;
  format?: string;
  error?: string;
}

export interface ScanOptions {
  formats?: SupportedFormat[];
  targetedFormats?: SupportedFormat[];
  prompt?: string;
  cameraDirection?: 'front' | 'back';
}

class BarcodeService {
  private isScanning = false;

  /**
   * Check if barcode scanning is supported on this device
   */
  async isSupported(): Promise<boolean> {
    try {
      if (!Capacitor.isNativePlatform()) {
        return false; // Web platform doesn't support camera scanning
      }
      
      const result = await BarcodeScanner.isSupported();
      return result.supported;
    } catch (error) {
      console.error('Error checking barcode scanner support:', error);
      return false;
    }
  }

  /**
   * Check camera permissions
   */
  async checkPermissions(): Promise<{ granted: boolean; message?: string }> {
    try {
      const status = await BarcodeScanner.checkPermission({ force: false });
      
      if (status.granted) {
        return { granted: true };
      }
      
      if (status.denied) {
        return { 
          granted: false, 
          message: 'Camera permission denied. Please enable camera access in settings.' 
        };
      }
      
      if (status.asked) {
        return { 
          granted: false, 
          message: 'Camera permission was previously denied.' 
        };
      }
      
      if (status.neverAsked) {
        return { 
          granted: false, 
          message: 'Camera permission not requested yet.' 
        };
      }
      
      return { 
        granted: false, 
        message: 'Camera permission status unknown.' 
      };
    } catch (error) {
      console.error('Error checking camera permissions:', error);
      return { 
        granted: false, 
        message: 'Failed to check camera permissions.' 
      };
    }
  }

  /**
   * Request camera permissions
   */
  async requestPermissions(): Promise<{ granted: boolean; message?: string }> {
    try {
      const status = await BarcodeScanner.checkPermission({ force: true });
      
      if (status.granted) {
        return { granted: true };
      }
      
      return { 
        granted: false, 
        message: 'Camera permission denied. Please enable camera access in settings to use barcode scanning.' 
      };
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return { 
        granted: false, 
        message: 'Failed to request camera permissions.' 
      };
    }
  }

  /**
   * Start barcode scanning
   */
  async startScan(options: ScanOptions = {}): Promise<ScanResult> {
    try {
      // Check if already scanning
      if (this.isScanning) {
        return {
          success: false,
          error: 'Scanner is already active'
        };
      }

      // Check if supported
      const supported = await this.isSupported();
      if (!supported) {
        return {
          success: false,
          error: 'Barcode scanning is not supported on this device'
        };
      }

      // Check/request permissions
      const permissions = await this.checkPermissions();
      if (!permissions.granted) {
        const requestResult = await this.requestPermissions();
        if (!requestResult.granted) {
          return {
            success: false,
            error: requestResult.message || 'Camera permission denied'
          };
        }
      }

      this.isScanning = true;

      // Hide background to make scanner visible
      await BarcodeScanner.hideBackground();

      // Configure scan options
      const scanOptions = {
        targetedFormats: options.formats || options.targetedFormats || [
          SupportedFormat.QR_CODE,
          SupportedFormat.EAN_13,
          SupportedFormat.EAN_8,
          SupportedFormat.CODE_128,
          SupportedFormat.CODE_39,
          SupportedFormat.UPC_A,
          SupportedFormat.UPC_E,
        ],
        cameraDirection: options.cameraDirection || 'back',
      };

      // Start scanning
      const result = await BarcodeScanner.startScan(scanOptions);

      this.isScanning = false;

      if (result.hasContent) {
        return {
          success: true,
          data: result.content,
          format: result.format,
        };
      } else {
        return {
          success: false,
          error: 'No barcode detected'
        };
      }
    } catch (error) {
      this.isScanning = false;
      console.error('Error during barcode scanning:', error);
      
      // Show background again in case of error
      try {
        await BarcodeScanner.showBackground();
      } catch (bgError) {
        console.error('Error showing background:', bgError);
      }

      return {
        success: false,
        error: `Scanning failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Stop barcode scanning
   */
  async stopScan(): Promise<void> {
    try {
      if (this.isScanning) {
        await BarcodeScanner.stopScan();
        this.isScanning = false;
      }
      await BarcodeScanner.showBackground();
    } catch (error) {
      console.error('Error stopping barcode scan:', error);
      this.isScanning = false;
    }
  }

  /**
   * Check if currently scanning
   */
  getIsScanning(): boolean {
    return this.isScanning;
  }

  /**
   * Open device settings for app permissions
   */
  async openSettings(): Promise<void> {
    try {
      await BarcodeScanner.openAppSettings();
    } catch (error) {
      console.error('Error opening app settings:', error);
    }
  }

  /**
   * Enable torch/flashlight (if supported)
   */
  async enableTorch(): Promise<boolean> {
    try {
      await BarcodeScanner.enableTorch();
      return true;
    } catch (error) {
      console.error('Error enabling torch:', error);
      return false;
    }
  }

  /**
   * Disable torch/flashlight
   */
  async disableTorch(): Promise<boolean> {
    try {
      await BarcodeScanner.disableTorch();
      return true;
    } catch (error) {
      console.error('Error disabling torch:', error);
      return false;
    }
  }

  /**
   * Toggle torch/flashlight
   */
  async toggleTorch(): Promise<boolean> {
    try {
      await BarcodeScanner.toggleTorch();
      return true;
    } catch (error) {
      console.error('Error toggling torch:', error);
      return false;
    }
  }

  /**
   * Get supported formats for this device
   */
  getSupportedFormats(): SupportedFormat[] {
    return [
      SupportedFormat.QR_CODE,
      SupportedFormat.EAN_13,
      SupportedFormat.EAN_8,
      SupportedFormat.CODE_128,
      SupportedFormat.CODE_39,
      SupportedFormat.UPC_A,
      SupportedFormat.UPC_E,
      SupportedFormat.CODE_93,
      SupportedFormat.CODABAR,
      SupportedFormat.ITF,
      SupportedFormat.RSS14,
      SupportedFormat.PDF_417,
      SupportedFormat.AZTEC,
      SupportedFormat.DATA_MATRIX,
    ];
  }

  /**
   * Validate barcode format
   */
  validateBarcodeFormat(barcode: string, expectedFormat?: SupportedFormat): boolean {
    if (!barcode) return false;

    switch (expectedFormat) {
      case SupportedFormat.EAN_13:
        return /^\d{13}$/.test(barcode);
      case SupportedFormat.EAN_8:
        return /^\d{8}$/.test(barcode);
      case SupportedFormat.UPC_A:
        return /^\d{12}$/.test(barcode);
      case SupportedFormat.UPC_E:
        return /^\d{8}$/.test(barcode);
      case SupportedFormat.CODE_128:
        return barcode.length >= 1; // CODE_128 can vary in length
      case SupportedFormat.CODE_39:
        return /^[A-Z0-9\-. $/+%]*$/.test(barcode);
      case SupportedFormat.QR_CODE:
        return barcode.length >= 1; // QR codes can contain any data
      default:
        return barcode.length >= 1; // Basic validation
    }
  }
}

export const barcodeService = new BarcodeService();