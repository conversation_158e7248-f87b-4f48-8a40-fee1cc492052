import { apiService } from './api';
import { BranchInventoryCategory, BranchInventoryItem } from '@/types/api';
import { OrderItem, PaymentMethod } from '@/types/order';

export interface POSOrder {
  items: OrderItem[];
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  customerPhone?: string;
  tableNumber?: number;
  specialInstructions?: string;
}

export interface POSOrderResponse {
  orderId: string;
  receiptNumber: string;
  timestamp: string;
  total: number;
}

export interface InventoryMovement {
  productId: string;
  movementType: 'SALE' | 'RETURN' | 'ADJUSTMENT' | 'TRANSFER';
  quantity: number;
  reason?: string;
  notes?: string;
}

export interface DailySalesReport {
  date: string;
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  paymentMethods: {
    cash: number;
    card: number;
    digitalWallet: number;
    other: number;
  };
  topProducts: Array<{
    productId: string;
    name: string;
    quantitySold: number;
    revenue: number;
  }>;
}

class POSService {
  /**
   * Get branch cubefor POS operations
   */
  async getBranchInventory(): Promise<BranchInventoryCategory[]> {
    try {
      const response = await apiService.get<BranchInventoryCategory[]>('/pos/branch-inventories');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to fetch branch inventory');
    } catch (error) {
      console.error('Error fetching branch inventory', error);
      throw error;
    }
  }

  /**
   * Search products in branch inventory
   */
  async searchProducts(query: string, category?: string): Promise<BranchInventoryItem[]> {
    try {
      const params = new URLSearchParams();
      params.append('query', query);
      if (category) {
        params.append('category', category);
      }

      const response = await apiService.get<BranchInventoryItem[]>(`/pos/branch-inventories/search?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      return [];
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  }

  /**
   * Validate stock availability for cart items
   */
  async validateStock(items: Array<{ productId: string; quantity: number }>): Promise<{
    isValid: boolean;
    unavailableItems: Array<{ productId: string; availableStock: number; requestedQuantity: number }>;
  }> {
    try {
      const response = await apiService.post<{
        isValid: boolean;
        unavailableItems: Array<{ productId: string; availableStock: number; requestedQuantity: number }>;
      }>('/pos/validate-stock', { items });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      return { isValid: false, unavailableItems: [] };
    } catch (error) {
      console.error('Error validating stock:', error);
      return { isValid: false, unavailableItems: [] };
    }
  }

  /**
   * Process a POS order
   */
  async processOrder(
    order: POSOrder,
    companyId: string,
    locationId: string
  ): Promise<POSOrderResponse> {
    try {
      const response = await apiService.post<POSOrderResponse>(
        `/pos/${companyId}/locations/${locationId}/orders`,
        order
      );
      
      if (response.success && response.data) {
        // Update cubeafter successful order
        await this.updateInventoryForSale(order.items);
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to process order');
    } catch (error) {
      console.error('Error processing order:', error);
      throw error;
    }
  }

  /**
   * Update cubeafter a sale
   */
  private async updateInventoryForSale(items: OrderItem[]): Promise<void> {
    try {
      const credentials = apiService.getStoredCredentials();
      if (!credentials.companyId) {
        throw new Error('Company ID not available');
      }

      const movements: InventoryMovement[] = items.map(item => ({
        productId: item.id,
        movementType: 'SALE',
        quantity: -item.quantity, // Negative for sale (reduces stock)
        reason: 'POS Sale',
        notes: `Sold ${item.quantity} units of ${item.name}`
      }));

      await apiService.post(
        `/company/${credentials.companyId}/cubemovement/v2`,
        { movements }
      );
    } catch (error) {
      console.error('Error updating cubeafter sale:', error);
      // Don't throw - cubesync can happen later
    }
  }

  /**
   * Get daily sales report
   */
  async getDailySalesReport(date: string): Promise<DailySalesReport> {
    try {
      const response = await apiService.get<DailySalesReport>(`/pos/reports/daily?date=${date}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to fetch daily sales report');
    } catch (error) {
      console.error('Error fetching daily sales report:', error);
      throw error;
    }
  }

  /**
   * Get recent transactions
   */
  async getRecentTransactions(limit: number = 10): Promise<Array<{
    id: string;
    receiptNumber: string;
    total: number;
    paymentMethod: PaymentMethod;
    timestamp: string;
    customerName?: string;
  }>> {
    try {
      const response = await apiService.get<Array<{
        id: string;
        receiptNumber: string;
        total: number;
        paymentMethod: PaymentMethod;
        timestamp: string;
        customerName?: string;
      }>>(`/pos/transactions/recent?limit=${limit}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching recent transactions:', error);
      return [];
    }
  }

  /**
   * Get transaction by ID for receipt
   */
  async getTransactionById(transactionId: string): Promise<{
    id: string;
    receiptNumber: string;
    items: OrderItem[];
    subtotal: number;
    tax: number;
    total: number;
    paymentMethod: PaymentMethod;
    timestamp: string;
    customerName?: string;
    customerPhone?: string;
    tableNumber?: number;
    specialInstructions?: string;
    cashierName?: string;
  } | null> {
    try {
      const response = await apiService.get<{
        id: string;
        receiptNumber: string;
        items: OrderItem[];
        subtotal: number;
        tax: number;
        total: number;
        paymentMethod: PaymentMethod;
        timestamp: string;
        customerName?: string;
        customerPhone?: string;
        tableNumber?: number;
        specialInstructions?: string;
        cashierName?: string;
      }>(`/pos/transactions/${transactionId}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching transaction:', error);
      return null;
    }
  }

  /**
   * Process refund for a transaction
   */
  async processRefund(
    transactionId: string,
    reason: string,
    amount?: number
  ): Promise<{
    refundId: string;
    amount: number;
    timestamp: string;
  }> {
    try {
      const response = await apiService.post<{
        refundId: string;
        amount: number;
        timestamp: string;
      }>(`/pos/transactions/${transactionId}/refund`, {
        reason,
        amount
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to process refund');
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  /**
   * Calculate tax for given subtotal
   */
  calculateTax(subtotal: number, taxRate: number = 0.08): number {
    return Math.round(subtotal * taxRate * 100) / 100;
  }

  /**
   * Calculate change for cash payment
   */
  calculateChange(total: number, amountPaid: number): number {
    return Math.round((amountPaid - total) * 100) / 100;
  }

  /**
   * Format currency for display
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  /**
   * Generate receipt number
   */
  generateReceiptNumber(): string {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[-:T.]/g, '').slice(0, 14);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RCP${timestamp}${random}`;
  }
}

export const posService = new POSService();