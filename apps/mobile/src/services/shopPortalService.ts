import { apiService } from './api';
import { ApiResponse } from '@/types/api';

// Shop Portal Types
export interface OrderableItem {
  id: string;
  itemId: string;
  itemType: 'ingredient' | 'menu-item' | 'recipe';
  name: string;
  description?: string;
  category?: string;
  currentStock: number;
  centralKitchenStock: number;
  minOrderQuantity: number;
  maxOrderQuantity: number;
  orderingUOM: string;
  leadTimeDays: number;
  estimatedCost: number;
  isAvailable: boolean;
  baseUom: {
    name: string;
    shortCode: string;
  };
}

export interface ShopPortalOrder {
  id?: string;
  orderId: string;
  locationId: string;
  items: ShopPortalOrderItem[];
  requestedDeliveryDate: Date;
  notes?: string;
  status: 'pending' | 'approved' | 'dispatched' | 'delivered' | 'cancelled';
  totalCost: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ShopPortalOrderItem {
  itemId: string;
  itemType: string;
  name: string;
  quantity: number;
  unit: string;
  estimatedCost: number;
}

export interface DeliveryNote {
  id: string;
  deliveryNoteNumber: string;
  orderId: string;
  locationId: string;
  items: DeliveryNoteItem[];
  deliveryDate: Date;
  receivedDate?: Date;
  status: 'pending' | 'in-transit' | 'delivered' | 'received';
  notes?: string;
  receivedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DeliveryNoteItem {
  itemId: string;
  name: string;
  orderedQuantity: number;
  deliveredQuantity: number;
  receivedQuantity?: number;
  unit: string;
  hasDiscrepancy?: boolean;
  discrepancyNotes?: string;
}

class ShopPortalService {
  private companyId: string | null = null;
  private locationId: string | null = null;

  constructor() {
    // Get stored credentials from apiService
    const credentials = apiService.getStoredCredentials();
    this.companyId = credentials.companyId;
    this.locationId = credentials.locationId;
  }

  updateCredentials(companyId: string, locationId: string) {
    this.companyId = companyId;
    this.locationId = locationId;
  }

  // Get orderable items from central kitchen
  async getOrderableItems(params?: {
    search?: string;
    category?: string;
    itemType?: string;
  }): Promise<ApiResponse<OrderableItem[]>> {
    if (!this.companyId || !this.locationId) {
      throw new Error('Company ID and Location ID are required');
    }

    const queryParams = new URLSearchParams();
    queryParams.append('locationId', this.locationId);
    
    if (params?.search) queryParams.append('search', params.search);
    if (params?.category) queryParams.append('category', params.category);
    if (params?.itemType) queryParams.append('itemType', params.itemType);

    return apiService.get<OrderableItem[]>(
      `/company/${this.companyId}/shop-portal/orderable-items?${queryParams.toString()}`
    );
  }

  // Place an order to central kitchen
  async placeOrder(order: {
    items: ShopPortalOrderItem[];
    requestedDeliveryDate: Date;
    notes?: string;
  }): Promise<ApiResponse<ShopPortalOrder>> {
    if (!this.companyId || !this.locationId) {
      throw new Error('Company ID and Location ID are required');
    }

    return apiService.post<ShopPortalOrder>(
      `/company/${this.companyId}/shop-portal/orders`,
      {
        locationId: this.locationId,
        ...order,
        requestedDeliveryDate: order.requestedDeliveryDate.toISOString(),
      }
    );
  }

  // Get orders for the current location
  async getOrders(params?: {
    status?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<ApiResponse<ShopPortalOrder[]>> {
    if (!this.companyId || !this.locationId) {
      throw new Error('Company ID and Location ID are required');
    }

    const queryParams = new URLSearchParams();
    queryParams.append('locationId', this.locationId);
    
    if (params?.status) queryParams.append('status', params.status);
    if (params?.startDate) queryParams.append('startDate', params.startDate.toISOString());
    if (params?.endDate) queryParams.append('endDate', params.endDate.toISOString());

    return apiService.get<ShopPortalOrder[]>(
      `/company/${this.companyId}/shop-portal/orders?${queryParams.toString()}`
    );
  }

  // Get a specific order
  async getOrder(orderId: string): Promise<ApiResponse<ShopPortalOrder>> {
    if (!this.companyId) {
      throw new Error('Company ID is required');
    }

    return apiService.get<ShopPortalOrder>(
      `/company/${this.companyId}/shop-portal/orders/${orderId}`
    );
  }

  // Cancel an order
  async cancelOrder(orderId: string, reason?: string): Promise<ApiResponse<ShopPortalOrder>> {
    if (!this.companyId) {
      throw new Error('Company ID is required');
    }

    return apiService.patch<ShopPortalOrder>(
      `/company/${this.companyId}/shop-portal/orders/${orderId}/cancel`,
      { reason }
    );
  }

  // Get delivery notes for the location
  async getDeliveryNotes(params?: {
    status?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<ApiResponse<DeliveryNote[]>> {
    if (!this.companyId || !this.locationId) {
      throw new Error('Company ID and Location ID are required');
    }

    const queryParams = new URLSearchParams();
    queryParams.append('locationId', this.locationId);
    
    if (params?.status) queryParams.append('status', params.status);
    if (params?.startDate) queryParams.append('startDate', params.startDate.toISOString());
    if (params?.endDate) queryParams.append('endDate', params.endDate.toISOString());

    return apiService.get<DeliveryNote[]>(
      `/company/${this.companyId}/delivery-notes?${queryParams.toString()}`
    );
  }

  // Get a specific delivery note
  async getDeliveryNote(deliveryNoteId: string): Promise<ApiResponse<DeliveryNote>> {
    if (!this.companyId) {
      throw new Error('Company ID is required');
    }

    return apiService.get<DeliveryNote>(
      `/company/${this.companyId}/delivery-notes/${deliveryNoteId}`
    );
  }

  // Receive a delivery
  async receiveDelivery(deliveryNoteId: string, receivedItems: {
    itemId: string;
    receivedQuantity: number;
    hasDiscrepancy?: boolean;
    discrepancyNotes?: string;
  }[]): Promise<ApiResponse<DeliveryNote>> {
    if (!this.companyId) {
      throw new Error('Company ID is required');
    }

    return apiService.patch<DeliveryNote>(
      `/company/${this.companyId}/delivery-notes/${deliveryNoteId}/receive`,
      {
        receivedItems,
        receivedDate: new Date().toISOString(),
        receivedBy: apiService.getStoredCredentials().token, // This should be the user ID
      }
    );
  }

  // Mark delivery as completed
  async completeDelivery(deliveryNoteId: string): Promise<ApiResponse<DeliveryNote>> {
    if (!this.companyId) {
      throw new Error('Company ID is required');
    }

    return apiService.patch<DeliveryNote>(
      `/company/${this.companyId}/delivery-notes/${deliveryNoteId}/complete`,
      {
        status: 'received',
        completedAt: new Date().toISOString(),
      }
    );
  }

  // Get quick reorder suggestions based on previous orders
  async getQuickReorderSuggestions(): Promise<ApiResponse<OrderableItem[]>> {
    if (!this.companyId || !this.locationId) {
      throw new Error('Company ID and Location ID are required');
    }

    return apiService.get<OrderableItem[]>(
      `/company/${this.companyId}/shop-portal/quick-reorder?locationId=${this.locationId}`
    );
  }

  // Get low stock items that need reordering
  async getLowStockItems(): Promise<ApiResponse<OrderableItem[]>> {
    if (!this.companyId || !this.locationId) {
      throw new Error('Company ID and Location ID are required');
    }

    return apiService.get<OrderableItem[]>(
      `/company/${this.companyId}/shop-portal/low-stock?locationId=${this.locationId}`
    );
  }
}

export const shopPortalService = new ShopPortalService();