import { ApiResponse, ApiError } from '@/types/api';

class ApiService {
  private baseURL: string;
  private token: string | null = null;
  private apiKey: string;
  private companyId: string | null = null;
  private locationId: string | null = null;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
    this.apiKey = import.meta.env.VITE_API_KEY || 'test_api_key_1';
    
    // Load stored credentials from localStorage
    this.loadStoredCredentials();
  }

  private loadStoredCredentials() {
    try {
      const storedToken = localStorage.getItem('foodprepai_token');
      const storedCompanyId = localStorage.getItem('foodprepai_company_id');
      const storedLocationId = localStorage.getItem('foodprepai_location_id');
      
      if (storedToken) this.token = storedToken;
      if (storedCompanyId) this.companyId = storedCompanyId;
      if (storedLocationId) this.locationId = storedLocationId;
    } catch (error) {
      console.warn('Failed to load stored credentials:', error);
    }
  }

  setAuthToken(token: string) {
    this.token = token;
    try {
      localStorage.setItem('foodprepai_token', token);
    } catch (error) {
      console.warn('Failed to store token:', error);
    }
  }

  removeAuthToken() {
    this.token = null;
    try {
      localStorage.removeItem('foodprepai_token');
    } catch (error) {
      console.warn('Failed to remove token:', error);
    }
  }

  setCompanyAndLocation(companyId: string, locationId: string) {
    this.companyId = companyId;
    this.locationId = locationId;
    try {
      localStorage.setItem('foodprepai_company_id', companyId);
      localStorage.setItem('foodprepai_location_id', locationId);
    } catch (error) {
      console.warn('Failed to store company/location:', error);
    }
  }

  clearCompanyAndLocation() {
    this.companyId = null;
    this.locationId = null;
    try {
      localStorage.removeItem('foodprepai_company_id');
      localStorage.removeItem('foodprepai_location_id');
    } catch (error) {
      console.warn('Failed to clear company/location:', error);
    }
  }

  getStoredCredentials() {
    return {
      token: this.token,
      companyId: this.companyId,
      locationId: this.locationId
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      'X-API-Key': this.apiKey,
    };

    // Add company and location headers if available
    if (this.companyId) {
      defaultHeaders['X-Company-Id'] = this.companyId;
    }
    
    if (this.locationId) {
      defaultHeaders['X-Location-Id'] = this.locationId;
    }

    // Add authorization header if token is available
    if (this.token) {
      defaultHeaders.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      headers: { ...defaultHeaders, ...options.headers },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // Check if response has content before trying to parse JSON
      let data: unknown = {};
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json();
        } catch (parseError) {
          console.warn('Failed to parse JSON response:', parseError);
          data = { success: false, message: 'Invalid JSON response' };
        }
      } else {
        // Handle non-JSON responses
        const text = await response.text();
        data = { success: response.ok, message: text || 'No content' };
      }

      if (!response.ok) {
        const errorMessage = data.message || data.error || `HTTP error! status: ${response.status}`;
        throw new ApiError(response.status, errorMessage, data);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      
      // Handle network errors and other non-HTTP errors
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(0, 'Network error or request failed', { originalError: error });
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Custom API Error class for better error handling
export class ApiError extends Error {
  public status: number;
  public data?: unknown;

  constructor(status: number, message: string, data?: unknown) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }

  get isNetworkError(): boolean {
    return this.status === 0;
  }

  get isAuthError(): boolean {
    return this.status === 401 || this.status === 403;
  }

  get isValidationError(): boolean {
    return this.status === 400 || this.status === 422;
  }

  get isNotFoundError(): boolean {
    return this.status === 404;
  }

  get isServerError(): boolean {
    return this.status >= 500;
  }
}

export const apiService = new ApiService();