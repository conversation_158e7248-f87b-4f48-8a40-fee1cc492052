import { apiService } from './api';
import { Order, OrderSummary, OrderFilter, ApiResponse, PaginatedResponse } from '@/types';

export class OrderService {
  private endpoint = '/orders';

  async getOrders(filter?: OrderFilter): Promise<ApiResponse<PaginatedResponse<Order>>> {
    const params = new URLSearchParams();
    
    if (filter?.status?.length) {
      params.append('status', filter.status.join(','));
    }
    if (filter?.orderType?.length) {
      params.append('orderType', filter.orderType.join(','));
    }
    if (filter?.dateRange) {
      params.append('startDate', filter.dateRange.start.toISOString());
      params.append('endDate', filter.dateRange.end.toISOString());
    }
    if (filter?.customerName) {
      params.append('customerName', filter.customerName);
    }

    const queryString = params.toString();
    const url = queryString ? `${this.endpoint}?${queryString}` : this.endpoint;
    
    return apiService.get<PaginatedResponse<Order>>(url);
  }

  async getOrder(id: string): Promise<ApiResponse<Order>> {
    return apiService.get<Order>(`${this.endpoint}/${id}`);
  }

  async createOrder(order: Omit<Order, 'id' | 'orderTime'>): Promise<ApiResponse<Order>> {
    return apiService.post<Order>(this.endpoint, order);
  }

  async updateOrderStatus(id: string, status: Order['status']): Promise<ApiResponse<Order>> {
    return apiService.patch<Order>(`${this.endpoint}/${id}/status`, { status });
  }

  async cancelOrder(id: string, reason?: string): Promise<ApiResponse<Order>> {
    return apiService.patch<Order>(`${this.endpoint}/${id}/cancel`, { reason });
  }

  async completeOrder(id: string): Promise<ApiResponse<Order>> {
    return apiService.patch<Order>(`${this.endpoint}/${id}/complete`, {});
  }

  async getOrderSummary(dateRange?: { start: Date; end: Date }): Promise<ApiResponse<OrderSummary>> {
    const params = new URLSearchParams();
    
    if (dateRange) {
      params.append('startDate', dateRange.start.toISOString());
      params.append('endDate', dateRange.end.toISOString());
    }

    const queryString = params.toString();
    const url = queryString ? `${this.endpoint}/summary?${queryString}` : `${this.endpoint}/summary`;
    
    return apiService.get<OrderSummary>(url);
  }

  async getActiveOrders(): Promise<ApiResponse<Order[]>> {
    return apiService.get<Order[]>(`${this.endpoint}/active`);
  }

  async updateEstimatedCompletion(id: string, estimatedCompletion: Date): Promise<ApiResponse<Order>> {
    return apiService.patch<Order>(`${this.endpoint}/${id}/estimated-completion`, {
      estimatedCompletion: estimatedCompletion.toISOString()
    });
  }

  async assignStaff(id: string, staffId: string): Promise<ApiResponse<Order>> {
    return apiService.patch<Order>(`${this.endpoint}/${id}/assign`, { staffId });
  }
}

export const orderService = new OrderService();