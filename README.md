This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Managing Superusers

Superusers in FoodPrepAI are managed through Firebase Custom Claims. To create a superuser:

1. Create user account through Firebase Authentication
2. Set custom claims for the user using Firebase Admin SDK:
```typescript
{
  role: 'platform_admin' | 'platform_support';
  isSuperUser: true;
  permissions: string[];
}
```

3. User will now be redirected to /superadmin/dashboard on login

Note: Changes to custom claims require a new sign-in for the user to take effect. Custom claims are verified on the server side and included in the user's ID token.

## Documentation

- [User Synchronization Architecture](./docs/user-sync-architecture.md) - Details on user sync between main platform and IonicPOS
- [Ionic App Implementation Guide](./docs/ionic-user-sync-implementation.md) - Implementation guide for the Ionic team
- [User Sync Testing Guide](./docs/user-sync-testing.md) - Comprehensive testing plan for user synchronization
- [User Sync Quick Test](./docs/user-sync-quick-test.md) - Quick guide for testing user sync between apps
- [Multi-Tenant Considerations](./docs/multi-tenant-considerations.md) - Guidelines for maintaining proper multi-tenant isolation
