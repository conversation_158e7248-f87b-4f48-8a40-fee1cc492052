{"$schema": "https://json.schemastore.org/tsconfig", "display": "Node.js TypeScript configuration", "extends": "./base.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "Node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "types": ["node"]}, "ts-node": {"esm": true}}