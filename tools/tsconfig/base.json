{"$schema": "https://json.schemastore.org/tsconfig", "display": "Base TypeScript configuration for FoodPrepAI monorepo", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "moduleDetection": "force", "allowJs": true, "checkJs": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "noEmit": true, "incremental": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "stripInternal": true, "declaration": false, "declarationMap": false, "sourceMap": true}, "exclude": ["node_modules", "dist", "build", ".next", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}