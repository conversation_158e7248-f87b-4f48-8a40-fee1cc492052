{"$schema": "https://json.schemastore.org/tsconfig", "display": "React TypeScript configuration for Ionic applications", "extends": "./base.json", "compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client"]}, "include": ["src", "**/*.ts", "**/*.tsx"]}