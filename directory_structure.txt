.
├── README.md
├── components.json
├── config
│   └── keys
│       └── marginchef-325b6-bb4a7a32c453.json
├── directory_structure.txt
├── next-env.d.ts
├── next.config.ts
├── package-lock.json
├── package.json
├── postcss.config.mjs
├── public
│   ├── file.svg
│   ├── globe.svg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── src
│   ├── app
│   │   ├── (auth)
│   │   │   ├── login
│   │   │   │   └── page.tsx
│   │   │   └── signup
│   │   │       └── page.tsx
│   │   ├── api
│   │   │   ├── auth
│   │   │   │   └── route.ts
│   │   │   ├── companies
│   │   │   │   └── route.ts
│   │   │   ├── llm
│   │   │   │   └── route.ts
│   │   │   ├── products
│   │   │   │   └── route.ts
│   │   │   └── suppliers
│   │   │       └── route.ts
│   │   ├── company
│   │   │   └── [companyId]
│   │   │       └── page.tsx
│   │   ├── dashboard
│   │   │   └── page.tsx
│   │   ├── favicon.ico
│   │   ├── fonts
│   │   │   ├── GeistMonoVF.woff
│   │   │   └── GeistVF.woff
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── superadmin
│   │       ├── companies
│   │       │   ├── [companyId]
│   │       │   │   └── page.tsx
│   │       │   └── page.tsx
│   │       └── page.tsx
│   ├── components
│   │   ├── CompanySelector.tsx
│   │   ├── DataTable.tsx
│   │   ├── FormField.tsx
│   │   ├── NavBar.tsx
│   │   ├── Sidebar.tsx
│   │   └── ui
│   │       ├── button.tsx
│   │       ├── dialog.tsx
│   │       ├── form.tsx
│   │       ├── label.tsx
│   │       ├── select.tsx
│   │       └── table.tsx
│   ├── lib
│   │   ├── auth.ts
│   │   ├── constants.ts
│   │   ├── firebaseAdmin.ts
│   │   ├── firebaseClient.ts
│   │   ├── firestoreAdmin.ts
│   │   ├── services
│   │   │   ├── companyService.ts
│   │   │   ├── llmAgent.ts
│   │   │   ├── poService.ts
│   │   │   ├── productService.ts
│   │   │   ├── supplierService.ts
│   │   │   └── userService.ts
│   │   ├── types
│   │   │   ├── company.ts
│   │   │   ├── index.ts
│   │   │   ├── llm.ts
│   │   │   ├── po.ts
│   │   │   ├── product.ts
│   │   │   ├── supplier.ts
│   │   │   └── user.ts
│   │   ├── utils.ts
│   │   └── validations.ts
│   └── middleware.ts
├── tailwind.config.ts
├── tree.txt
└── tsconfig.json

27 directories, 67 files
