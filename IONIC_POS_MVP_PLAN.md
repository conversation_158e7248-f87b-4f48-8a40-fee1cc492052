# FoodPrepAI Ionic POS - MVP Implementation Plan

## Overview
This document outlines the implementation plan for integrating the existing Ionic POS application with the FoodPrepAI backend system to create a production-ready Point of Sale system for retail shops.

## Current State Analysis

### ✅ What's Already Built in FoodPrepAI Backend

#### Backend Infrastructure
- **POS Authentication System**: PIN-based login (`/api/pos/auth/pin-login`)
- **Inventory Management APIs**: Comprehensive movement system (`/api/company/[companyId]/inventory/movement/v2`)
- **Ordering System**: Shop portal orders (`/api/company/[companyId]/shop-portal/orders`)
- **Goods Receiving**: Delivery note management (`/api/company/[companyId]/delivery-notes`)
- **Data Models**: Complete schemas for BranchInventory, Order, DeliveryNote, InventoryTransaction

#### API Endpoints Available
- `/api/pos/auth/pin-login` - PIN authentication
- `/api/pos/[companyId]/locations/[locationId]/orders` - POS order processing
- `/api/company/[companyId]/inventory/movement/v2` - Inventory movements
- `/api/company/[companyId]/shop-portal/orders` - Order placement
- `/src/pages/api/pos/branch-inventories.ts` - Inventory data for POS

### ✅ What's Already Built in Ionic POS Repository

#### Technical Foundation
- **Ionic React + TypeScript** with Vite build system
- **PWA-ready** with Capacitor configuration
- **Offline-first architecture** using Dexie (IndexedDB)
- **Modern UI** with Tailwind CSS + shadcn/ui components
- **Barcode scanning** capability
- **State management** with React Context

#### Application Structure
```
src/
├── pages/
│   ├── pos/
│   │   ├── Dashboard.tsx
│   │   ├── OrderEntry.tsx
│   │   ├── Payment.tsx
│   │   ├── Receipt.tsx
│   │   ├── Kitchen.tsx
│   │   └── TableManagement.tsx
│   ├── inventory/
│   │   ├── InventoryList.tsx
│   │   ├── OrderPlacement.tsx
│   │   ├── OrderReceiving.tsx
│   │   ├── StockCount.tsx
│   │   └── Wastage.tsx
│   ├── staff/
│   │   ├── ClockInOut.tsx
│   │   └── StaffDashboard.tsx
│   └── settings/
│       ├── AppPreferences.tsx
│       ├── PrinterSettings.tsx
│       └── UserPreferences.tsx
├── services/
│   ├── auth.service.ts
│   ├── db.ts (Dexie/IndexedDB)
│   ├── sync.ts
│   └── sqlite.service.ts
├── contexts/
│   ├── AuthContext.tsx
│   └── OfflineDataContext.tsx
└── components/ (Comprehensive UI components)
```

#### Database Layer
- Local IndexedDB with Dexie
- Sync queue for offline operations
- Data models for orders, products, categories
- Sync status tracking

### ❌ Critical Gaps for MVP

1. **API Integration**: No connection to FoodPrepAI backend
2. **Screen Implementation**: Most screens are empty placeholders
3. **Business Logic**: No actual POS, inventory, or ordering functionality
4. **Synchronization**: Sync queue not connected to real APIs

## Implementation Plan

### Phase 1: Backend Integration (Weeks 1-2)

#### 1.1 Migrate Ionic POS to FoodPrepAI Monorepo
```bash
# Navigate to FoodPrepAI directory
cd /path/to/foodprepai

# Clear existing mobile app directory
rm -rf apps/mobile/*

# Copy Ionic POS content
cp -r /path/to/ionicpos/* apps/mobile/

# Update workspace configuration
# Edit package.json to ensure mobile app is included in workspaces
```

#### 1.2 Configure API Integration
- Update `services/auth.service.ts`:
  ```typescript
  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';
  ```
- Implement company/location selection flow
- Configure all FoodPrepAI API endpoints

#### 1.3 Authentication System
- **Connect PIN login**: Update auth service to use `/api/pos/auth/pin-login`
- **Implement session management**: Token storage and refresh
- **Company/location selection**: UI flow for multi-tenant access

#### Deliverables
- [ ] Ionic POS integrated into FoodPrepAI monorepo
- [ ] API configuration complete
- [ ] Authentication flow working with backend
- [ ] Company/location selection functional

### Phase 2: Core POS Sales Operations (Weeks 3-4)

#### 2.1 Product Catalog Integration
- **Connect to branch inventories API**: `/src/pages/api/pos/branch-inventories.ts`
- **Implement OrderEntry screen**: Product selection, search, categories
- **Real-time stock validation**: Check availability before adding to cart

#### 2.2 Sales Processing
- **Shopping cart functionality**: Add/remove items, quantity adjustment
- **Price calculation**: Apply taxes, discounts, menu item pricing
- **Integration with inventory movements**: SALE transactions via `/api/company/[companyId]/inventory/movement/v2`

#### 2.3 Payment and Receipts
- **Payment screen implementation**: Multiple payment methods
- **Receipt generation**: Digital and printable receipts
- **Transaction completion**: Mark orders as completed

#### API Integration Points
```typescript
// Example API calls needed
const completeSale = async (orderData) => {
  // 1. Create sale transaction
  const saleResponse = await fetch('/api/pos/[companyId]/locations/[locationId]/orders', {
    method: 'POST',
    body: JSON.stringify(orderData)
  });

  // 2. Update inventory via movement API
  const inventoryResponse = await fetch('/api/company/[companyId]/inventory/movement/v2', {
    method: 'POST',
    body: JSON.stringify({
      transactionType: 'SALE',
      items: orderData.items
    })
  });
};
```

#### Deliverables
- [ ] OrderEntry screen with product catalog
- [ ] Shopping cart functionality
- [ ] Payment processing
- [ ] Receipt generation
- [ ] Sales automatically update inventory

### Phase 3: Inventory Management (Weeks 5-6)

#### 3.1 Inventory Display and Management
- **InventoryList screen**: Connect to real branch inventory data
- **Stock levels**: Real-time display of current stock
- **Inventory search and filtering**: By category, low stock alerts

#### 3.2 Stock Operations
- **StockCount screen**: Physical inventory counting interface
- **Wastage recording**: Wastage entry with reasons
- **Stock adjustments**: Manual adjustments with approval workflow

#### 3.3 Integration with Inventory Movement API
```typescript
// Inventory operations
const recordWastage = async (wasteData) => {
  return fetch('/api/company/[companyId]/inventory/movement/v2', {
    method: 'POST',
    body: JSON.stringify({
      transactionType: 'WASTAGE',
      items: wasteData.items,
      reason: wasteData.reason
    })
  });
};

const submitStockCount = async (countData) => {
  return fetch('/api/company/[companyId]/inventory/movement/v2', {
    method: 'POST',
    body: JSON.stringify({
      transactionType: 'COUNT',
      items: countData.items
    })
  });
};
```

#### Deliverables
- [ ] InventoryList with real data
- [ ] StockCount functionality
- [ ] Wastage recording
- [ ] Stock adjustments
- [ ] All operations sync with backend

### Phase 4: Ordering System (Weeks 7-8)

#### 4.1 Order Placement
- **OrderPlacement screen**: Browse items available from central kitchen
- **Order creation**: Select items, quantities, delivery preferences
- **Order submission**: Submit to `/api/company/[companyId]/shop-portal/orders`

#### 4.2 Order Receiving
- **OrderReceiving screen**: View incoming deliveries
- **Delivery note processing**: Item-by-item receiving
- **Inventory updates**: Automatic stock increases on receiving

#### 4.3 Order Status Tracking
- **Order history**: View past orders and their status
- **Delivery tracking**: Real-time status updates
- **Notifications**: Alerts for order status changes

#### API Integration
```typescript
// Order operations
const placeOrder = async (orderData) => {
  return fetch('/api/company/[companyId]/shop-portal/orders', {
    method: 'POST',
    body: JSON.stringify({
      items: orderData.items,
      requestedDeliveryDate: orderData.deliveryDate,
      notes: orderData.notes
    })
  });
};

const receiveDelivery = async (deliveryData) => {
  // Process delivery note
  return fetch('/api/company/[companyId]/delivery-notes/[id]', {
    method: 'PUT',
    body: JSON.stringify({
      status: 'COMPLETED',
      receivedItems: deliveryData.items
    })
  });
};
```

#### Deliverables
- [ ] OrderPlacement screen functional
- [ ] Order submission to central kitchen
- [ ] OrderReceiving interface
- [ ] Delivery processing
- [ ] Automatic inventory updates on receipt

### Phase 5: Offline Capabilities & Production Readiness (Weeks 9-10)

#### 5.1 Enhanced Offline Support
- **Critical operation queuing**: Sales, inventory movements
- **Sync queue enhancement**: Robust retry mechanisms
- **Conflict resolution**: Handle data conflicts during sync
- **Offline indicators**: Clear UI feedback for offline status

#### 5.2 Error Handling and Validation
- **Network error recovery**: Graceful handling of connection issues
- **Data validation**: Client-side validation before API calls
- **User feedback**: Loading states, error messages, success confirmations

#### 5.3 Performance Optimization
- **Data caching**: Intelligent caching strategies
- **Lazy loading**: Optimize initial app load time
- **Database optimization**: Efficient IndexedDB queries

#### 5.4 Testing and Quality Assurance
- **End-to-end testing**: Complete user workflows
- **Error scenario testing**: Network failures, data corruption
- **Performance testing**: Load testing with large datasets
- **Security audit**: Token management, data encryption

#### Deliverables
- [ ] Robust offline functionality
- [ ] Comprehensive error handling
- [ ] Performance optimization
- [ ] Full test coverage
- [ ] Production deployment guide

## Technical Requirements

### Environment Setup
```bash
# Development environment
npm install # Install dependencies
npm run dev # Start development server
npm run build # Build for production
```

### API Configuration
```typescript
// Environment variables needed
REACT_APP_API_URL=http://localhost:3000
REACT_APP_COMPANY_ID=your_company_id
REACT_APP_LOCATION_ID=your_location_id
```

### PWA Configuration
- Service worker for offline functionality
- App manifest for PWA installation
- Push notifications for order updates

## Success Criteria

### MVP Requirements
1. ✅ **Sales Operations**: Complete POS workflow from product selection to receipt
2. ✅ **Inventory Management**: Real-time stock tracking with automatic updates
3. ✅ **Ordering System**: Place orders to HQ and receive goods
4. ✅ **Offline Capability**: Critical operations work without internet
5. ✅ **Data Sync**: Reliable synchronization with backend when online

### Performance Targets
- **App load time**: < 3 seconds on mobile networks
- **Transaction processing**: < 1 second for sales completion
- **Offline operation**: 99% of critical functions available offline
- **Data sync**: < 30 seconds to sync after coming online

## Risk Mitigation

### Technical Risks
1. **API Integration Complexity**: Mitigated by existing comprehensive API documentation
2. **Offline Data Consistency**: Addressed by robust sync queue and conflict resolution
3. **Performance Issues**: Prevented by incremental optimization and testing

### Timeline Risks
1. **Scope Creep**: Controlled by focusing on MVP requirements first
2. **Integration Challenges**: Minimized by leveraging existing API structure
3. **Testing Delays**: Prevented by continuous testing throughout development

## Future Enhancements (Post-MVP)

1. **Advanced Reporting**: Daily/weekly sales reports
2. **Multi-location Support**: Manage inventory across multiple shops
3. **Advanced POS Features**: Customer management, loyalty programs
4. **Integration Enhancements**: Third-party payment processors, accounting systems
5. **Mobile App Distribution**: Native iOS/Android apps if needed

## Conclusion

The combination of the comprehensive FoodPrepAI backend APIs and the well-structured Ionic POS frontend provides an excellent foundation for rapid MVP development. The estimated timeline of 8-10 weeks is achievable given the existing infrastructure and clear implementation path outlined above.

---

*Last Updated: August 16, 2025*
*Status: Implementation Plan Approved*