#!/bin/bash

# FoodPrepAI Monorepo Development Setup Script
# This script automates the initial setup for new developers

set -e

echo "🚀 FoodPrepAI Monorepo Development Setup"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on macOS, Linux, or Windows (Git Bash)
detect_os() {
    case "$OSTYPE" in
        darwin*)  OS="macOS" ;;
        linux*)   OS="Linux" ;;
        msys*)    OS="Windows" ;;
        *)        OS="Unknown" ;;
    esac
    print_info "Detected OS: $OS"
}

# Check Node.js version
check_node() {
    print_info "Checking Node.js version..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed!"
        echo "Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version $NODE_VERSION is too old. Please upgrade to Node.js 18+."
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Check npm version
check_npm() {
    print_info "Checking npm version..."
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed!"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version | cut -d'.' -f1)
    if [ "$NPM_VERSION" -lt 8 ]; then
        print_warning "npm version $(npm --version) is old. Consider upgrading to npm 8+."
        print_info "Updating npm..."
        npm install -g npm@latest
    fi
    
    print_success "npm $(npm --version) is ready"
}

# Install dependencies
install_dependencies() {
    print_info "Installing dependencies (this may take a few minutes)..."
    
    if npm install --legacy-peer-deps; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
}

# Set up git hooks
setup_git_hooks() {
    print_info "Setting up git hooks..."
    
    if npm run prepare; then
        print_success "Git hooks set up successfully"
    else
        print_warning "Failed to set up git hooks"
    fi
}

# Create environment file
setup_environment() {
    print_info "Setting up environment variables..."
    
    if [ ! -f ".env.local" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env.local
            print_success "Created .env.local from template"
            print_warning "Please update .env.local with your local configuration"
        else
            print_warning "No .env.example found. You may need to create .env.local manually"
        fi
    else
        print_info ".env.local already exists"
    fi
}

# Verify setup
verify_setup() {
    print_info "Verifying setup..."
    
    echo "Running type check..."
    if npm run typecheck; then
        print_success "TypeScript configuration is valid"
    else
        print_error "TypeScript errors found - please fix before continuing"
        exit 1
    fi
    
    echo "Running linter..."
    if npm run lint; then
        print_success "Code passes linting checks"
    else
        print_warning "Linting issues found - these will be auto-fixed on commit"
    fi
}

# Install recommended VS Code extensions
install_vscode_extensions() {
    if command -v code &> /dev/null; then
        print_info "Installing recommended VS Code extensions..."
        
        extensions=(
            "ms-vscode.typescript-hero"
            "esbenp.prettier-vscode"
            "ms-vscode.vscode-typescript-next"
            "bradlc.vscode-tailwindcss"
            "ms-vscode.vscode-jest"
            "ms-vscode.vscode-eslint"
        )
        
        for ext in "${extensions[@]}"; do
            code --install-extension "$ext" --force > /dev/null 2>&1
        done
        
        print_success "VS Code extensions installed"
        print_info "Recommended: Open the workspace file 'foodprepai.code-workspace' in VS Code"
    else
        print_info "VS Code not found - skipping extension installation"
    fi
}

# Open applications
open_apps() {
    read -p "Would you like to start the development servers now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Starting development servers..."
        print_info "Web app will be available at http://localhost:3000"
        print_info "Mobile app will be available at http://localhost:8100"
        
        # Start in background so script can continue
        npm run dev &
        
        print_success "Development servers started!"
        print_info "Press Ctrl+C to stop the servers when done"
        
        # Keep script running to show logs
        wait
    fi
}

# Main setup process
main() {
    detect_os
    check_node
    check_npm
    install_dependencies
    setup_git_hooks
    setup_environment
    verify_setup
    install_vscode_extensions
    
    echo
    print_success "🎉 Setup completed successfully!"
    echo
    print_info "Next steps:"
    echo "1. Update .env.local with your configuration"
    echo "2. Run 'npm run dev' to start development servers"
    echo "3. Open 'foodprepai.code-workspace' in VS Code"
    echo "4. Read docs/development/DEVELOPER_ONBOARDING.md for more info"
    echo
    
    open_apps
}

# Run main function
main "$@"