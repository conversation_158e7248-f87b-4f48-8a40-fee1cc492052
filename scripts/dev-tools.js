#!/usr/bin/env node

/**
 * FoodPrepAI Monorepo Development Tools
 * Interactive CLI for common development tasks
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m', 
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}${colors.bright}${msg}${colors.reset}`)
};

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper to run commands
const runCommand = (command, options = {}) => {
  try {
    return execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options 
    });
  } catch (error) {
    if (!options.silent) {
      log.error(`Command failed: ${command}`);
    }
    throw error;
  }
};

// Helper to spawn long-running processes
const spawnProcess = (command, args, options = {}) => {
  return spawn(command, args, {
    stdio: 'inherit',
    shell: true,
    ...options
  });
};

// Development tools menu
const tools = {
  1: {
    name: 'Clean & Reinstall Dependencies',
    description: 'Remove node_modules and reinstall all dependencies',
    action: async () => {
      log.info('Cleaning node_modules...');
      runCommand('rm -rf node_modules apps/*/node_modules packages/*/node_modules tools/*/node_modules');
      runCommand('rm -f package-lock.json apps/*/package-lock.json packages/*/package-lock.json tools/*/package-lock.json'); 
      
      log.info('Reinstalling dependencies...');
      runCommand('npm install --legacy-peer-deps');
      log.success('Dependencies reinstalled successfully!');
    }
  },

  2: {
    name: 'Start Development Servers',
    description: 'Start web and mobile apps in development mode',
    action: async () => {
      log.info('Starting development servers...');
      log.info('Web app: http://localhost:3000');
      log.info('Mobile app: http://localhost:8100');
      log.info('Press Ctrl+C to stop servers');
      
      const devProcess = spawnProcess('npm', ['run', 'dev']);
      
      // Handle process termination
      process.on('SIGINT', () => {
        log.info('Stopping development servers...');
        devProcess.kill('SIGINT');
        process.exit(0);
      });
    }
  },

  3: {
    name: 'Run Full Test Suite',
    description: 'Run all tests across packages and apps',
    action: async () => {
      log.info('Running full test suite...');
      runCommand('npm run test');
      log.success('All tests completed!');
    }
  },

  4: {
    name: 'Type Check All Packages',
    description: 'Run TypeScript type checking across monorepo',
    action: async () => {
      log.info('Running TypeScript type checking...');
      runCommand('npm run typecheck');
      log.success('Type checking completed!');
    }
  },

  5: {
    name: 'Lint & Format Code',
    description: 'Run ESLint and Prettier on all code',
    action: async () => {
      log.info('Running linter...');
      runCommand('npm run lint');
      
      log.info('Formatting code...');
      runCommand('npm run format');
      log.success('Code linted and formatted!');
    }
  },

  6: {
    name: 'Build All Packages',
    description: 'Build all packages and applications',
    action: async () => {
      log.info('Building all packages...');
      runCommand('npm run build');
      log.success('Build completed successfully!');
    }
  },

  7: {
    name: 'Clear Turborepo Cache',
    description: 'Clear Turborepo build cache',
    action: async () => {
      log.info('Clearing Turborepo cache...');
      runCommand('npx turbo clean');
      log.success('Cache cleared!');
    }
  },

  8: {
    name: 'Generate New Component',
    description: 'Interactive component generator',
    action: async () => {
      const componentName = await askQuestion('Component name (PascalCase): ');
      const packageChoice = await askQuestion('Package (1: ui-components, 2: web app, 3: mobile app): ');
      
      let targetPath;
      switch(packageChoice) {
        case '1':
          targetPath = 'packages/ui-components/src';
          break;
        case '2':
          targetPath = 'apps/web/src/components';
          break;
        case '3':
          targetPath = 'apps/mobile/src/components';
          break;
        default:
          log.error('Invalid choice');
          return;
      }
      
      await generateComponent(componentName, targetPath);
    }
  },

  9: {
    name: 'Package Workspace Info',
    description: 'Show workspace structure and dependencies',
    action: async () => {
      log.info('Workspace structure:');
      runCommand('npm ls --depth=0');
      
      log.info('\nWorkspace dependencies:');
      runCommand('npm run --workspaces --if-present');
    }
  },

  10: {
    name: 'Health Check',
    description: 'Run comprehensive health check of the monorepo',
    action: async () => {
      log.title('🏥 Monorepo Health Check');
      
      // Check Node.js version
      try {
        const nodeVersion = runCommand('node --version', { silent: true }).trim();
        log.success(`Node.js: ${nodeVersion}`);
      } catch {
        log.error('Node.js not found');
      }
      
      // Check npm version
      try {
        const npmVersion = runCommand('npm --version', { silent: true }).trim();
        log.success(`npm: ${npmVersion}`);
      } catch {
        log.error('npm not found');
      }
      
      // Check if dependencies are installed
      if (fs.existsSync('node_modules')) {
        log.success('Root dependencies installed');
      } else {
        log.error('Root dependencies not installed');
      }
      
      // Check workspace packages
      const workspaces = ['apps/web', 'apps/mobile', 'packages/shared-types', 'packages/shared-utils', 'packages/api-client'];
      workspaces.forEach(workspace => {
        if (fs.existsSync(path.join(workspace, 'node_modules'))) {
          log.success(`${workspace}: dependencies installed`);
        } else {
          log.warning(`${workspace}: dependencies not installed`);
        }
      });
      
      // Check TypeScript
      try {
        runCommand('npm run typecheck', { silent: true });
        log.success('TypeScript: No type errors');
      } catch {
        log.error('TypeScript: Type errors found');
      }
      
      // Check linting
      try {
        runCommand('npm run lint', { silent: true });
        log.success('ESLint: No linting errors');
      } catch {
        log.warning('ESLint: Linting issues found');
      }
    }
  }
};

// Helper to ask questions
const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(`${colors.cyan}${question}${colors.reset} `, resolve);
  });
};

// Component generator
const generateComponent = async (name, targetPath) => {
  const componentDir = path.join(targetPath, name);
  
  if (fs.existsSync(componentDir)) {
    log.error(`Component ${name} already exists!`);
    return;
  }
  
  fs.mkdirSync(componentDir, { recursive: true });
  
  const componentContent = `import React from 'react';

interface ${name}Props {
  // Add props here
}

export const ${name}: React.FC<${name}Props> = (props) => {
  return (
    <div className="${name.toLowerCase()}">
      <h2>${name}</h2>
      {/* Component content */}
    </div>
  );
};

export default ${name};
`;
  
  const indexContent = `export { ${name}, ${name} as default } from './${name}';
export type { ${name}Props } from './${name}';
`;
  
  fs.writeFileSync(path.join(componentDir, `${name}.tsx`), componentContent);
  fs.writeFileSync(path.join(componentDir, 'index.ts'), indexContent);
  
  log.success(`Component ${name} generated at ${componentDir}`);
};

// Main menu
const showMenu = () => {
  log.title('\n🛠️  FoodPrepAI Development Tools');
  console.log('=====================================\n');
  
  Object.entries(tools).forEach(([key, tool]) => {
    console.log(`${colors.bright}${key}.${colors.reset} ${tool.name}`);
    console.log(`   ${colors.yellow}${tool.description}${colors.reset}\n`);
  });
  
  console.log(`${colors.bright}0.${colors.reset} Exit\n`);
};

// Main program
const main = async () => {
  while (true) {
    showMenu();
    
    const choice = await askQuestion('Select a tool: ');
    
    if (choice === '0') {
      log.info('Goodbye! 👋');
      process.exit(0);
    }
    
    const tool = tools[choice];
    if (tool) {
      console.log(`\n${colors.bright}Running: ${tool.name}${colors.reset}`);
      console.log('='.repeat(40));
      
      try {
        await tool.action();
        console.log('\n' + '='.repeat(40));
        log.success('Task completed!');
      } catch (error) {
        console.log('\n' + '='.repeat(40));
        log.error(`Task failed: ${error.message}`);
      }
      
      await askQuestion('\nPress Enter to continue...');
    } else {
      log.error('Invalid selection. Please try again.');
    }
  }
};

// Handle process termination
process.on('SIGINT', () => {
  log.info('\nExiting...');
  rl.close();
  process.exit(0);
});

// Start the program
if (require.main === module) {
  main().catch(error => {
    log.error(`Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { tools, runCommand, spawnProcess };