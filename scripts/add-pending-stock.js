require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');

async function addPendingStockField() {
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    console.error('MONGODB_URI environment variable not set');
    process.exit(1);
  }

  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const branchInventoryCollection = db.collection('branchinventories');
    
    // Count documents without pendingStock field
    const countMissing = await branchInventoryCollection.countDocuments({
      pendingStock: { $exists: false }
    });
    
    console.log(`Found ${countMissing} documents without pendingStock field`);
    
    if (countMissing > 0) {
      // Update all documents to add pendingStock field with value 0
      const result = await branchInventoryCollection.updateMany(
        { pendingStock: { $exists: false } },
        { $set: { pendingStock: 0 } }
      );
      
      console.log(`Updated ${result.modifiedCount} documents`);
    } else {
      console.log('All documents already have pendingStock field');
    }
    
    // Verify update
    const verifyCount = await branchInventoryCollection.countDocuments({
      pendingStock: { $exists: true }
    });
    
    console.log(`Now ${verifyCount} documents have pendingStock field`);
    
    // Show a sample document after update
    const sampleAfter = await branchInventoryCollection.findOne({});
    console.log('\nSample document after update:');
    console.log(JSON.stringify(sampleAfter, null, 2));
    
  } catch (error) {
    console.error('Error occurred during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

addPendingStockField();
