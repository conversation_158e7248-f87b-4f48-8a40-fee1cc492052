/**
 * Test script for user synchronization
 * 
 * This script runs tests to verify the user synchronization functionality
 * between the main app and the IonicPOS app.
 * 
 * Run with: 
 * npx ts-node scripts/test-user-sync.ts
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';

// Load environment variables
dotenv.config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/foodprepai';

// Test configuration
const TEST_COMPANY_ID = '67682466d436c5f697693330'; // Replace with your test company ID
const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'Test1234!',
    displayName: 'Test Manager',
    role: 'manager',
    pin: '1234'
  },
  {
    email: '<EMAIL>',
    password: 'Test1234!',
    displayName: 'Test Staff',
    role: 'user',
    pin: '5678'
  }
];

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Create test users
async function createTestUsers() {
  const User = mongoose.model('User');
  
  console.log('Creating test users...');
  
  for (const userData of TEST_USERS) {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      
      if (existingUser) {
        console.log(`User ${userData.email} already exists. Updating...`);
        
        // Update user
        const passwordHash = await bcrypt.hash(userData.password, 10);
        const pinHash = await bcrypt.hash(userData.pin, 10);
        
        await User.findByIdAndUpdate(existingUser._id, {
          passwordHash,
          displayName: userData.displayName,
          role: userData.role,
          pin: pinHash,
          lastModified: new Date(),
          syncStatus: 'pending',
          modifiedFields: ['displayName', 'role', 'pin']
        });
        
        console.log(`Updated user: ${userData.email}`);
      } else {
        // Create new user
        const passwordHash = await bcrypt.hash(userData.password, 10);
        const pinHash = await bcrypt.hash(userData.pin, 10);
        
        const newUser = new User({
          email: userData.email,
          passwordHash,
          displayName: userData.displayName,
          userType: 'company_user',
          companyId: new mongoose.Types.ObjectId(TEST_COMPANY_ID),
          role: userData.role,
          permissions: [],
          pin: pinHash,
          lastModified: new Date(),
          syncStatus: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        await newUser.save();
        console.log(`Created user: ${userData.email}`);
      }
    } catch (error) {
      console.error(`Error creating/updating user ${userData.email}:`, error);
    }
  }
}

// Delete test users
async function deleteTestUsers() {
  const User = mongoose.model('User');
  
  console.log('Cleaning up test users...');
  
  for (const userData of TEST_USERS) {
    try {
      // Mark as deleted instead of actually deleting
      await User.findOneAndUpdate(
        { email: userData.email }, 
        { 
          isDeleted: true,
          lastModified: new Date(),
          syncStatus: 'pending',
          modifiedFields: ['isDeleted']
        }
      );
      
      console.log(`Marked user as deleted: ${userData.email}`);
    } catch (error) {
      console.error(`Error marking user ${userData.email} as deleted:`, error);
    }
  }
}

// Main test function
async function runTests() {
  await connectToDatabase();
  
  // Register models
  try {
    // User model - try to avoid redefining if already defined
    if (!mongoose.models.User) {
      const UserSchema = new mongoose.Schema({
        email: { type: String, required: true, unique: true },
        passwordHash: { type: String, required: true },
        displayName: { type: String },
        userType: { type: String, required: true, enum: ['superuser', 'company_user'] },
        companyId: { type: mongoose.Schema.Types.ObjectId },
        role: { type: String, enum: ['owner', 'admin', 'manager', 'user', 'storekeeper'] },
        permissions: { type: [String] },
        pin: { type: String },
        isDeleted: { type: Boolean, default: false },
        lastModified: { type: Date, default: Date.now },
        syncStatus: { type: String, enum: ['pending', 'synced'], default: 'pending' },
        modifiedFields: { type: [String] },
        createdAt: { type: Date, default: Date.now },
        updatedAt: { type: Date, default: Date.now }
      });
      
      mongoose.model('User', UserSchema);
    }
  } catch (error) {
    console.warn('Error defining model (might already be defined):', error);
  }
  
  // Run tests
  try {
    // Create test users for sync testing
    await createTestUsers();
    
    console.log('\n===== TEST USERS CREATED =====');
    console.log('Now you can test the sync functionality:');
    console.log('1. Go to the Ionic app running at http://localhost:8101');
    console.log('2. Log in with your admin credentials');
    console.log('3. Navigate to the user sync section and trigger a sync');
    console.log('4. Verify that the test users appear in the Ionic app');
    console.log('\nAfter testing, run this script with the --cleanup flag to delete test users');
    console.log('Example: npx ts-node scripts/test-user-sync.ts --cleanup');
    
    // Check if cleanup is requested
    if (process.argv.includes('--cleanup')) {
      await deleteTestUsers();
      console.log('\nTest users marked as deleted. You can now test sync deletion.');
    }
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    // Close database connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the tests
runTests().catch(console.error);