#!/usr/bin/env node

/**
 * Component Generator for FoodPrepAI Monorepo
 * Interactive CLI for generating consistent React components
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}${colors.bright}${msg}${colors.reset}`)
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(`${colors.cyan}${question}${colors.reset} `, resolve);
  });
};

// Component templates
const templates = {
  basic: {
    name: 'Basic Component',
    description: 'Simple functional component with props',
    template: (name, props) => `import React from 'react';

interface ${name}Props {
  ${props.map(prop => `${prop.name}${prop.optional ? '?' : ''}: ${prop.type};`).join('\n  ')}
}

export const ${name}: React.FC<${name}Props> = ({
  ${props.map(prop => prop.name).join(',\n  ')}
}) => {
  return (
    <div className="${name.toLowerCase()}">
      <h2>${name}</h2>
      {/* Component content */}
    </div>
  );
};

export default ${name};
`
  },

  form: {
    name: 'Form Component',
    description: 'Form component with validation using react-hook-form',
    template: (name, props) => `import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, FormField } from '@foodprepai/ui-components';

const ${name.toLowerCase()}Schema = z.object({
  // Define your form schema here
});

type ${name}FormData = z.infer<typeof ${name.toLowerCase()}Schema>;

interface ${name}Props {
  onSubmit: (data: ${name}FormData) => void;
  defaultValues?: Partial<${name}FormData>;
  isLoading?: boolean;
}

export const ${name}: React.FC<${name}Props> = ({
  onSubmit,
  defaultValues,
  isLoading = false
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<${name}FormData>({
    resolver: zodResolver(${name.toLowerCase()}Schema),
    defaultValues
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <FormField
        name="example"
        label="Example Field"
        control={control}
        error={errors.example?.message}
      />
      
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline">
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={isSubmitting || isLoading}
        >
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </Button>
      </div>
    </form>
  );
};

export default ${name};
`
  },

  list: {
    name: 'List Component',
    description: 'List component with search and pagination',
    template: (name, props) => `import React, { useState, useMemo } from 'react';
import { Button, SearchInput } from '@foodprepai/ui-components';

interface ${name}Item {
  id: string;
  // Add your item properties here
}

interface ${name}Props {
  items: ${name}Item[];
  onItemClick?: (item: ${name}Item) => void;
  onEdit?: (item: ${name}Item) => void;
  onDelete?: (item: ${name}Item) => void;
  isLoading?: boolean;
}

export const ${name}: React.FC<${name}Props> = ({
  items,
  onItemClick,
  onEdit,
  onDelete,
  isLoading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredItems = useMemo(() => {
    if (!searchTerm) return items;
    return items.filter(item => 
      // Customize search logic here
      JSON.stringify(item).toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [items, searchTerm]);

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="${name.toLowerCase()}">
      <div className="mb-4">
        <SearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search..."
        />
      </div>
      
      <div className="space-y-2">
        {filteredItems.map(item => (
          <div 
            key={item.id}
            className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
            onClick={() => onItemClick?.(item)}
          >
            {/* Customize item display */}
            <div className="flex justify-between items-center">
              <div>
                {/* Item content */}
              </div>
              
              <div className="flex gap-2">
                {onEdit && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(item);
                    }}
                  >
                    Edit
                  </Button>
                )}
                {onDelete && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(item);
                    }}
                  >
                    Delete
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {filteredItems.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No items found
        </div>
      )}
    </div>
  );
};

export default ${name};
`
  },

  modal: {
    name: 'Modal Component',
    description: 'Modal component with backdrop and animations',
    template: (name, props) => `import React from 'react';
import { Modal } from '@foodprepai/ui-components';

interface ${name}Props {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
}

export const ${name}: React.FC<${name}Props> = ({
  isOpen,
  onClose,
  title,
  children,
  footer
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {title && (
          <div className="px-6 py-4 border-b">
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
        )}
        
        <div className="px-6 py-4">
          {children}
        </div>
        
        {footer && (
          <div className="px-6 py-4 border-t bg-gray-50 rounded-b-lg">
            {footer}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ${name};
`
  }
};

// Available locations
const locations = {
  'shared': {
    path: 'packages/ui-components/src',
    description: 'Shared UI component (available to all apps)'
  },
  'web': {
    path: 'apps/web/src/components',
    description: 'Web app component'
  },
  'mobile': {
    path: 'apps/mobile/src/components',
    description: 'Mobile app component'
  },
  'web-feature': {
    path: 'apps/web/src/features',
    description: 'Web app feature component (specify feature name)'
  }
};

const validateComponentName = (name) => {
  if (!name) return 'Component name is required';
  if (!/^[A-Z][a-zA-Z0-9]*$/.test(name)) {
    return 'Component name must be PascalCase (start with uppercase letter)';
  }
  return null;
};

const generateComponent = async () => {
  log.title('🎨 Component Generator');
  console.log('Generate a new React component with consistent structure\n');

  // Get component name
  let componentName;
  while (true) {
    componentName = await askQuestion('Component name (PascalCase): ');
    const validation = validateComponentName(componentName);
    if (validation) {
      log.error(validation);
      continue;
    }
    break;
  }

  // Choose template
  log.info('Available templates:');
  Object.entries(templates).forEach(([key, template], index) => {
    console.log(`${index + 1}. ${template.name} - ${template.description}`);
  });

  const templateChoice = await askQuestion('Choose template (1-4): ');
  const templateKeys = Object.keys(templates);
  const selectedTemplate = templates[templateKeys[parseInt(templateChoice) - 1]];

  if (!selectedTemplate) {
    log.error('Invalid template choice');
    return;
  }

  // Choose location
  log.info('Available locations:');
  Object.entries(locations).forEach(([key, location], index) => {
    console.log(`${index + 1}. ${key} - ${location.description}`);
  });

  const locationChoice = await askQuestion('Choose location (1-4): ');
  const locationKeys = Object.keys(locations);
  const selectedLocation = locations[locationKeys[parseInt(locationChoice) - 1]];

  if (!selectedLocation) {
    log.error('Invalid location choice');
    return;
  }

  let targetPath = selectedLocation.path;

  // Handle feature-specific paths
  if (locationKeys[parseInt(locationChoice) - 1] === 'web-feature') {
    const featureName = await askQuestion('Feature name: ');
    targetPath = `${targetPath}/${featureName}/components`;
  }

  // Create directory
  const componentDir = path.join(targetPath, componentName);
  
  if (fs.existsSync(componentDir)) {
    log.error(`Component ${componentName} already exists at ${componentDir}`);
    return;
  }

  fs.mkdirSync(componentDir, { recursive: true });

  // Generate props (simplified for demo)
  const props = [
    { name: 'className', type: 'string', optional: true }
  ];

  // Generate component file
  const componentContent = selectedTemplate.template(componentName, props);
  fs.writeFileSync(path.join(componentDir, `${componentName}.tsx`), componentContent);

  // Generate test file
  const testContent = `import { render, screen } from '@testing-library/react';
import { ${componentName} } from './${componentName}';

describe('${componentName}', () => {
  it('renders correctly', () => {
    render(<${componentName} />);
    expect(screen.getByText('${componentName}')).toBeInTheDocument();
  });
});
`;
  fs.writeFileSync(path.join(componentDir, `${componentName}.test.tsx`), testContent);

  // Generate stories file (for Storybook)
  const storiesContent = `import type { Meta, StoryObj } from '@storybook/react';
import { ${componentName} } from './${componentName}';

const meta: Meta<typeof ${componentName}> = {
  title: 'Components/${componentName}',
  component: ${componentName},
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
`;
  fs.writeFileSync(path.join(componentDir, `${componentName}.stories.tsx`), storiesContent);

  // Generate index file
  const indexContent = `export { ${componentName} } from './${componentName}';
export type { ${componentName}Props } from './${componentName}';
`;
  fs.writeFileSync(path.join(componentDir, 'index.ts'), indexContent);

  // Update parent index.ts
  const parentIndexPath = path.join(targetPath, 'index.ts');
  if (fs.existsSync(parentIndexPath)) {
    const exportLine = `export * from './${componentName}';\\n`;
    fs.appendFileSync(parentIndexPath, exportLine);
  }

  log.success(`Component ${componentName} generated successfully!`);
  log.info(`Location: ${componentDir}`);
  log.info('Files created:');
  console.log(`  - ${componentName}.tsx (component)`);
  console.log(`  - ${componentName}.test.tsx (tests)`);
  console.log(`  - ${componentName}.stories.tsx (storybook)`);
  console.log(`  - index.ts (exports)`);

  log.info('Next steps:');
  console.log('1. Customize the component implementation');
  console.log('2. Add proper TypeScript types');
  console.log('3. Write comprehensive tests');
  console.log('4. Update component props interface');
  console.log('5. Add documentation and examples');
};

// Main execution
const main = async () => {
  try {
    await generateComponent();
  } catch (error) {
    log.error(`Generation failed: ${error.message}`);
  } finally {
    rl.close();
    process.exit(0);
  }
};

// Handle process termination
process.on('SIGINT', () => {
  log.info('\\nExiting generator...');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = { generateComponent, templates, locations };