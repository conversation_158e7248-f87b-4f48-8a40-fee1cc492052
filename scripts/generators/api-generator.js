#!/usr/bin/env node

/**
 * API Generator for FoodPrepAI Monorepo
 * Generate consistent API endpoints with types, client methods, and tests
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}${colors.bright}${msg}${colors.reset}`)
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(`${colors.cyan}${question}${colors.reset} `, resolve);
  });
};

// API templates
const templates = {
  crud: {
    name: 'CRUD API',
    description: 'Full CRUD operations (Create, Read, Update, Delete)',
    
    // Next.js API route template
    routeTemplate: (resourceName, ResourceName) => `import { NextRequest } from 'next/server';
import { ${ResourceName}Model } from '@foodprepai/database-models';
import { ${ResourceName}, Create${ResourceName}Request } from '@foodprepai/shared-types';
import { validateSession } from '@/lib/auth';
import { z } from 'zod';

const create${ResourceName}Schema = z.object({
  // Define validation schema here
});

// GET /api/${resourceName} - List all ${resourceName}
export async function GET(request: NextRequest) {
  try {
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';

    const query = search 
      ? { $text: { $search: search } }
      : {};

    const ${resourceName} = await ${ResourceName}Model
      .find(query)
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await ${ResourceName}Model.countDocuments(query);

    return Response.json({
      success: true,
      data: ${resourceName},
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('GET /${resourceName} error:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/${resourceName} - Create new ${resourceName}
export async function POST(request: NextRequest) {
  try {
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = create${ResourceName}Schema.parse(body);

    const ${resourceName.slice(0, -1)} = new ${ResourceName}Model({
      ...validatedData,
      createdBy: session.user.id,
      updatedBy: session.user.id
    });

    await ${resourceName.slice(0, -1)}.save();

    return Response.json({
      success: true,
      data: ${resourceName.slice(0, -1)}
    }, { status: 201 });
  } catch (error) {
    if (error.name === 'ValidationError') {
      return Response.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('POST /${resourceName} error:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}`,

    // Individual resource route template
    resourceRouteTemplate: (resourceName, ResourceName) => `import { NextRequest } from 'next/server';
import { ${ResourceName}Model } from '@foodprepai/database-models';
import { validateSession } from '@/lib/auth';
import { z } from 'zod';

const update${ResourceName}Schema = z.object({
  // Define update validation schema here
});

// GET /api/${resourceName}/[id] - Get single ${resourceName}
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ${resourceName.slice(0, -1)} = await ${ResourceName}Model.findById(params.id);
    
    if (!${resourceName.slice(0, -1)}) {
      return Response.json({ error: 'Not found' }, { status: 404 });
    }

    return Response.json({
      success: true,
      data: ${resourceName.slice(0, -1)}
    });
  } catch (error) {
    console.error('GET /${resourceName}/[id] error:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/${resourceName}/[id] - Update ${resourceName}
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = update${ResourceName}Schema.parse(body);

    const ${resourceName.slice(0, -1)} = await ${ResourceName}Model.findByIdAndUpdate(
      params.id,
      {
        ...validatedData,
        updatedBy: session.user.id,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    );

    if (!${resourceName.slice(0, -1)}) {
      return Response.json({ error: 'Not found' }, { status: 404 });
    }

    return Response.json({
      success: true,
      data: ${resourceName.slice(0, -1)}
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      return Response.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('PUT /${resourceName}/[id] error:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/${resourceName}/[id] - Delete ${resourceName}
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ${resourceName.slice(0, -1)} = await ${ResourceName}Model.findByIdAndDelete(params.id);

    if (!${resourceName.slice(0, -1)}) {
      return Response.json({ error: 'Not found' }, { status: 404 });
    }

    return Response.json({
      success: true,
      message: '${ResourceName} deleted successfully'
    });
  } catch (error) {
    console.error('DELETE /${resourceName}/[id] error:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}`,

    // API client template
    clientTemplate: (resourceName, ResourceName) => `import { ${ResourceName}, Create${ResourceName}Request, Update${ResourceName}Request, PaginatedResponse } from '@foodprepai/shared-types';
import { apiClient } from './base';

export const ${resourceName}Client = {
  // Get all ${resourceName} with pagination
  getAll: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<PaginatedResponse<${ResourceName}>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);

    const response = await apiClient.get<PaginatedResponse<${ResourceName}>>(
      \`/${resourceName}?\${searchParams.toString()}\`
    );
    return response.data;
  },

  // Get single ${resourceName} by ID
  getById: async (id: string): Promise<${ResourceName}> => {
    const response = await apiClient.get<${ResourceName}>(\`/${resourceName}/\${id}\`);
    return response.data;
  },

  // Create new ${resourceName}
  create: async (data: Create${ResourceName}Request): Promise<${ResourceName}> => {
    const response = await apiClient.post<${ResourceName}>(\`/${resourceName}\`, data);
    return response.data;
  },

  // Update existing ${resourceName}
  update: async (id: string, data: Update${ResourceName}Request): Promise<${ResourceName}> => {
    const response = await apiClient.put<${ResourceName}>(\`/${resourceName}/\${id}\`, data);
    return response.data;
  },

  // Delete ${resourceName}
  delete: async (id: string): Promise<void> => {
    await apiClient.delete(\`/${resourceName}/\${id}\`);
  },

  // Search ${resourceName}
  search: async (query: string): Promise<${ResourceName}[]> => {
    const response = await apiClient.get<PaginatedResponse<${ResourceName}>>(
      \`/${resourceName}?search=\${encodeURIComponent(query)}\`
    );
    return response.data.data;
  }
};`,

    // Types template
    typesTemplate: (ResourceName) => `export interface ${ResourceName} {
  id: string;
  // Add your resource properties here
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export interface Create${ResourceName}Request {
  // Add required fields for creation
}

export interface Update${ResourceName}Request {
  // Add optional fields for updates (Partial<> of creation fields)
}

export interface ${ResourceName}ListFilters {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}`,

    // Database model template
    modelTemplate: (resourceName, ResourceName) => `import { Schema, model, Document } from 'mongoose';
import { ${ResourceName} } from '@foodprepai/shared-types';

interface ${ResourceName}Document extends ${ResourceName}, Document {}

const ${resourceName.slice(0, -1)}Schema = new Schema<${ResourceName}Document>({
  // Define your schema fields here
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  createdBy: { type: String, required: true },
  updatedBy: { type: String, required: true }
}, {
  timestamps: true,
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Add indexes for better performance
${resourceName.slice(0, -1)}Schema.index({ createdAt: -1 });
${resourceName.slice(0, -1)}Schema.index({ '$**': 'text' }); // Text search index

export const ${ResourceName}Model = model<${ResourceName}Document>('${ResourceName}', ${resourceName.slice(0, -1)}Schema);`,

    // Test template
    testTemplate: (resourceName, ResourceName) => `import { GET, POST } from '../route';
import { PUT, DELETE } from '../[id]/route';
import { NextRequest } from 'next/server';
import { ${ResourceName}Model } from '@foodprepai/database-models';

// Mock the database model
jest.mock('@foodprepai/database-models');
const mock${ResourceName}Model = ${ResourceName}Model as jest.Mocked<typeof ${ResourceName}Model>;

// Mock authentication
jest.mock('@/lib/auth', () => ({
  validateSession: jest.fn(),
}));

import { validateSession } from '@/lib/auth';
const mockValidateSession = validateSession as jest.MockedFunction<typeof validateSession>;

describe('/api/${resourceName}', () => {
  const mockSession = {
    user: { id: 'user-1', name: 'Test User' },
  };

  const mock${ResourceName} = {
    id: '1',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user-1',
    updatedBy: 'user-1'
  };

  beforeEach(() => {
    mockValidateSession.mockResolvedValue(mockSession);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('returns ${resourceName} list for authenticated user', async () => {
      mock${ResourceName}Model.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue([mock${ResourceName}]),
      } as any);
      
      mock${ResourceName}Model.countDocuments.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/${resourceName}');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual([mock${ResourceName}]);
    });

    it('returns 401 for unauthenticated requests', async () => {
      mockValidateSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/${resourceName}');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('POST', () => {
    it('creates new ${resourceName}', async () => {
      const mockSave = jest.fn().mockResolvedValue(mock${ResourceName});
      mock${ResourceName}Model.mockImplementation(() => ({
        save: mockSave
      } as any));

      const request = new NextRequest('http://localhost:3000/api/${resourceName}', {
        method: 'POST',
        body: JSON.stringify({ /* test data */ })
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(mockSave).toHaveBeenCalled();
    });
  });
});`
  },

  simple: {
    name: 'Simple API',
    description: 'Single endpoint with GET/POST operations',
    // ... simplified templates
  }
};

const generateAPI = async () => {
  log.title('🔌 API Generator');
  console.log('Generate consistent API endpoints with types and client methods\n');

  // Get resource name
  const resourceName = await askQuestion('Resource name (plural, e.g., "users", "orders"): ');
  if (!resourceName) {
    log.error('Resource name is required');
    return;
  }

  const ResourceName = resourceName.charAt(0).toUpperCase() + resourceName.slice(1, -1);

  // Choose template
  log.info('Available templates:');
  Object.entries(templates).forEach(([key, template], index) => {
    console.log(`${index + 1}. ${template.name} - ${template.description}`);
  });

  const templateChoice = await askQuestion('Choose template (1-2): ');
  const templateKeys = Object.keys(templates);
  const selectedTemplate = templates[templateKeys[parseInt(templateChoice) - 1]];

  if (!selectedTemplate) {
    log.error('Invalid template choice');
    return;
  }

  log.info(`Generating ${selectedTemplate.name} for ${resourceName}...`);

  // Generate files
  const files = [];

  // 1. Types
  const typesPath = `packages/shared-types/src/${resourceName.slice(0, -1)}.ts`;
  const typesContent = selectedTemplate.typesTemplate(ResourceName);
  fs.writeFileSync(typesPath, typesContent);
  files.push(typesPath);

  // 2. Database Model
  const modelPath = `packages/database-models/src/${ResourceName}.ts`;
  const modelContent = selectedTemplate.modelTemplate(resourceName, ResourceName);
  fs.writeFileSync(modelPath, modelContent);
  files.push(modelPath);

  // 3. API Client
  const clientPath = `packages/api-client/src/${resourceName}.ts`;
  const clientContent = selectedTemplate.clientTemplate(resourceName, ResourceName);
  fs.writeFileSync(clientPath, clientContent);
  files.push(clientPath);

  // 4. API Routes
  const routePath = `apps/web/src/app/api/${resourceName}`;
  fs.mkdirSync(routePath, { recursive: true });
  
  const routeFile = path.join(routePath, 'route.ts');
  const routeContent = selectedTemplate.routeTemplate(resourceName, ResourceName);
  fs.writeFileSync(routeFile, routeContent);
  files.push(routeFile);

  // 5. Individual resource route
  const resourceRoutePath = path.join(routePath, '[id]');
  fs.mkdirSync(resourceRoutePath, { recursive: true });
  
  const resourceRouteFile = path.join(resourceRoutePath, 'route.ts');
  const resourceRouteContent = selectedTemplate.resourceRouteTemplate(resourceName, ResourceName);
  fs.writeFileSync(resourceRouteFile, resourceRouteContent);
  files.push(resourceRouteFile);

  // 6. Tests
  const testPath = path.join(routePath, '__tests__');
  fs.mkdirSync(testPath, { recursive: true });
  
  const testFile = path.join(testPath, 'route.test.ts');
  const testContent = selectedTemplate.testTemplate(resourceName, ResourceName);
  fs.writeFileSync(testFile, testContent);
  files.push(testFile);

  // Update index files
  const updateIndexFile = (filePath, exportLine) => {
    if (fs.existsSync(filePath)) {
      fs.appendFileSync(filePath, `${exportLine}\n`);
    }
  };

  updateIndexFile('packages/shared-types/src/index.ts', `export * from './${resourceName.slice(0, -1)}';`);
  updateIndexFile('packages/database-models/src/index.ts', `export { ${ResourceName}Model } from './${ResourceName}';`);
  updateIndexFile('packages/api-client/src/index.ts', `export * from './${resourceName}';`);

  log.success(`API for ${resourceName} generated successfully!`);
  log.info('Files created:');
  files.forEach(file => console.log(`  - ${file}`));

  log.info('\nNext steps:');
  console.log('1. Build the packages:');
  console.log('   npm run build --workspace=packages/shared-types');
  console.log('   npm run build --workspace=packages/database-models');
  console.log('   npm run build --workspace=packages/api-client');
  console.log('2. Customize the types and schema fields');
  console.log('3. Add proper validation schemas');
  console.log('4. Test the API endpoints');
  console.log('5. Add authorization logic if needed');
};

// Main execution
const main = async () => {
  try {
    await generateAPI();
  } catch (error) {
    log.error(`Generation failed: ${error.message}`);
  } finally {
    rl.close();
    process.exit(0);
  }
};

// Handle process termination
process.on('SIGINT', () => {
  log.info('\nExiting generator...');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = { generateAPI, templates };