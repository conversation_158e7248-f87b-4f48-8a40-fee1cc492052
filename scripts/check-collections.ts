import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function connectToMongoDB() {
  try {
    const uri = process.env.MONGODB_URI;
    
    if (!uri) {
      console.error('MONGODB_URI is not defined in environment variables');
      process.exit(1);
    }
    
    await mongoose.connect(uri);
    console.log('Connected to MongoDB');
    
    // List all collections
    if (!mongoose.connection.db) {
      console.error('MongoDB connection is not established.');
      process.exit(1);
    }
    // Use non-null assertion as we've checked for null above
    const collections = await mongoose.connection.db!.listCollections().toArray();
    console.log('\nAvailable collections:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Ask about specific collections
    const collectionsToCheck = [
      'branchInventories',
      'inventoryItems',
      'inventoryTransactions',
      'items'
    ];
    
    for (const collectionName of collectionsToCheck) {
      if (collections.some(c => c.name === collectionName)) {
        if (!mongoose.connection.db) {
          console.error('MongoDB connection is not established.');
          continue;
        }
        const count = await mongoose.connection.db!.collection(collectionName).countDocuments();
        console.log(`\nCollection "${collectionName}" exists with ${count} documents`);
        
        // Sample a document if available
        if (count > 0) {
          const sampleDoc = await mongoose.connection.db!.collection(collectionName).findOne({});
          console.log('Sample document structure:');
          console.log(JSON.stringify(sampleDoc, null, 2));
        }
      } else {
        console.log(`\nCollection "${collectionName}" does not exist`);
      }
    }
    
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\nMongoDB connection closed');
  }
}

connectToMongoDB();
