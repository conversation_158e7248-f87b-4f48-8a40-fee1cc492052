require('dotenv').config();
const { MongoClient } = require('mongodb');

async function migrateInventoryModels() {
  const uri = process.env.MONGODB_URI;
  if (!uri) {
    console.error('MONGODB_URI environment variable not set');
    process.exit(1);
  }

  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    
    // Migrate ingredients
    const ingredientsCollection = db.collection('ingredients');
    const ingredientsCount = await ingredientsCollection.countDocuments({
      $or: [
        { currentStock: { $exists: false } },
        { pendingStock: { $exists: false } }
      ]
    });
    
    console.log(`Found ${ingredientsCount} ingredients without stock fields`);
    
    if (ingredientsCount > 0) {
      const ingredientsResult = await ingredientsCollection.updateMany(
        { 
          $or: [
            { currentStock: { $exists: false } },
            { pendingStock: { $exists: false } }
          ]
        },
        { 
          $set: { 
            currentStock: 0,
            pendingStock: 0
          } 
        }
      );
      
      console.log(`Updated ${ingredientsResult.modifiedCount} ingredients`);
    }
    
    // Migrate recipes
    const recipesCollection = db.collection('recipes');
    const recipesCount = await recipesCollection.countDocuments({
      $or: [
        { currentStock: { $exists: false } },
        { pendingStock: { $exists: false } }
      ]
    });
    
    console.log(`Found ${recipesCount} recipes without stock fields`);
    
    if (recipesCount > 0) {
      const recipesResult = await recipesCollection.updateMany(
        { 
          $or: [
            { currentStock: { $exists: false } },
            { pendingStock: { $exists: false } }
          ]
        },
        { 
          $set: { 
            currentStock: 0,
            pendingStock: 0
          } 
        }
      );
      
      console.log(`Updated ${recipesResult.modifiedCount} recipes`);
    }
    
    // Verify a random ingredient
    if (ingredientsCount > 0) {
      const sampleIngredient = await ingredientsCollection.findOne({});
      console.log('\nSample ingredient after update:');
      console.log(JSON.stringify(sampleIngredient, null, 2));
    }
    
    // Verify a random recipe
    if (recipesCount > 0) {
      const sampleRecipe = await recipesCollection.findOne({});
      console.log('\nSample recipe after update:');
      console.log(JSON.stringify(sampleRecipe, null, 2));
    }
    
  } catch (error) {
    console.error('Error occurred during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

migrateInventoryModels();
