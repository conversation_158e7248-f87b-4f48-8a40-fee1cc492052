// scripts/create-test-user.js
// Run with: node scripts/create-test-user.js
const mongoose = require('mongoose');
const { Schema } = mongoose;
const bcrypt = require('bcrypt');

// Configuration - update these as needed
const MONGODB_URI = 'mongodb://localhost:27017/foodprepai';
const COMPANY_ID = '67682466d436c5f697693330'; // Your test company ID

async function main() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Define User schema if not already defined
    const UserSchema = new Schema({
      email: { type: String, required: true, unique: true },
      passwordHash: { type: String, required: true },
      displayName: { type: String },
      userType: { type: String, required: true, enum: ['superuser', 'company_user'] },
      companyId: { type: Schema.Types.ObjectId },
      role: { type: String, enum: ['owner', 'admin', 'manager', 'user', 'storekeeper'] },
      permissions: { type: [String] },
      lastModified: { type: Date, default: Date.now },
      syncStatus: { type: String, enum: ['pending', 'synced'], default: 'pending' },
      modifiedFields: { type: [String] },
      pin: { type: String },
      isDeleted: { type: Boolean, default: false },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });

    // Use the model if it exists or create it
    const User = mongoose.models.User || mongoose.model('User', UserSchema);

    // Create a test user
    const testUser = {
      email: '<EMAIL>',
      password: 'Test1234!',
      displayName: 'Test User',
      role: 'manager'
    };

    // Check if user exists
    const existingUser = await User.findOne({ email: testUser.email });
    
    if (existingUser) {
      console.log(`User ${testUser.email} already exists. Updating...`);
      
      // Hash password
      const passwordHash = await bcrypt.hash(testUser.password, 10);
      
      // Update user
      await User.findByIdAndUpdate(existingUser._id, {
        passwordHash,
        displayName: testUser.displayName,
        role: testUser.role,
        lastModified: new Date(),
        syncStatus: 'pending',
        modifiedFields: ['displayName', 'role']
      });
      
      console.log(`Updated user: ${testUser.email}`);
    } else {
      // Hash password
      const passwordHash = await bcrypt.hash(testUser.password, 10);
      
      // Create new user
      const newUser = new User({
        email: testUser.email,
        passwordHash,
        displayName: testUser.displayName,
        userType: 'company_user',
        companyId: new mongoose.Types.ObjectId(COMPANY_ID),
        role: testUser.role,
        permissions: [],
        lastModified: new Date(),
        syncStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await newUser.save();
      console.log(`Created user: ${testUser.email}`);
    }
    
    // Create a test user with PIN
    const pinUser = {
      email: '<EMAIL>',
      password: 'Test1234!',
      displayName: 'PIN User',
      role: 'user',
      pin: '1234'
    };

    // Check if PIN user exists
    const existingPinUser = await User.findOne({ email: pinUser.email });
    
    if (existingPinUser) {
      console.log(`User ${pinUser.email} already exists. Updating...`);
      
      // Hash password and PIN
      const passwordHash = await bcrypt.hash(pinUser.password, 10);
      const pinHash = await bcrypt.hash(pinUser.pin, 10);
      
      // Update user
      await User.findByIdAndUpdate(existingPinUser._id, {
        passwordHash,
        displayName: pinUser.displayName,
        role: pinUser.role,
        pin: pinHash,
        lastModified: new Date(),
        syncStatus: 'pending',
        modifiedFields: ['displayName', 'role', 'pin']
      });
      
      console.log(`Updated user with PIN: ${pinUser.email}`);
    } else {
      // Hash password and PIN
      const passwordHash = await bcrypt.hash(pinUser.password, 10);
      const pinHash = await bcrypt.hash(pinUser.pin, 10);
      
      // Create new PIN user
      const newPinUser = new User({
        email: pinUser.email,
        passwordHash,
        displayName: pinUser.displayName,
        userType: 'company_user',
        companyId: new mongoose.Types.ObjectId(COMPANY_ID),
        role: pinUser.role,
        permissions: [],
        pin: pinHash,
        lastModified: new Date(),
        syncStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await newPinUser.save();
      console.log(`Created user with PIN: ${pinUser.email}`);
    }

    console.log('\nTest users created successfully!');
    console.log('\nTest credentials:');
    console.log(`- Regular user: ${testUser.email} / ${testUser.password}`);
    console.log(`- PIN user: ${pinUser.email} / PIN: ${pinUser.pin}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close MongoDB connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
main().catch(console.error);