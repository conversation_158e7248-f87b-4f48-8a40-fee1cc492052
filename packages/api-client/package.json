{"name": "@foodprepai/api-client", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest"}, "dependencies": {"@foodprepai/shared-types": "*", "@foodprepai/shared-utils": "*", "axios": "^1.7.9"}, "devDependencies": {"@foodprepai/tsconfig": "*", "@types/jest": "^29.5.14", "jest": "^29.7.0", "typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./base": {"types": "./dist/base.d.ts", "default": "./dist/base.js"}, "./auth": {"types": "./dist/auth.d.ts", "default": "./dist/auth.js"}, "./inventory": {"types": "./dist/inventory.d.ts", "default": "./dist/inventory.js"}, "./orders": {"types": "./dist/orders.d.ts", "default": "./dist/orders.js"}, "./users": {"types": "./dist/users.d.ts", "default": "./dist/users.js"}}}