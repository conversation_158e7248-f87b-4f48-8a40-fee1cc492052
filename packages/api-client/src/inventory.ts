// Inventory API client
import { APIClient, APIResponse } from './base';
import type { Ingredient } from '@foodprepai/shared-types';

export class InventoryAPIClient extends APIClient {
  async getIngredients(): Promise<APIResponse<Ingredient[]>> {
    return this.request('/inventory/ingredients');
  }

  async getIngredient(id: string): Promise<APIResponse<Ingredient>> {
    return this.request(`/inventory/ingredients/${id}`);
  }

  async createIngredient(ingredient: Partial<Ingredient>): Promise<APIResponse<Ingredient>> {
    return this.request('/inventory/ingredients', {
      method: 'POST',
      body: JSON.stringify(ingredient),
    });
  }

  async updateIngredient(id: string, ingredient: Partial<Ingredient>): Promise<APIResponse<Ingredient>> {
    return this.request(`/inventory/ingredients/${id}`, {
      method: 'PUT',
      body: JSON.stringify(ingredient),
    });
  }

  async deleteIngredient(id: string): Promise<APIResponse<void>> {
    return this.request(`/inventory/ingredients/${id}`, {
      method: 'DELETE',
    });
  }
}

export const inventoryClient = new InventoryAPIClient();