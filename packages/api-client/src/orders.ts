// Orders API client
import { APIClient, APIResponse } from './base';
import type { Order } from '@foodprepai/shared-types';

export class OrdersAPIClient extends APIClient {
  async getOrders(): Promise<APIResponse<Order[]>> {
    return this.request('/orders');
  }

  async getOrder(id: string): Promise<APIResponse<Order>> {
    return this.request(`/orders/${id}`);
  }

  async createOrder(order: Partial<Order>): Promise<APIResponse<Order>> {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(order),
    });
  }

  async updateOrder(id: string, order: Partial<Order>): Promise<APIResponse<Order>> {
    return this.request(`/orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(order),
    });
  }

  async deleteOrder(id: string): Promise<APIResponse<void>> {
    return this.request(`/orders/${id}`, {
      method: 'DELETE',
    });
  }
}

export const ordersClient = new OrdersAPIClient();