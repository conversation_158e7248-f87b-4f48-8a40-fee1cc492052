// Authentication API client
import { APIClient, APIResponse } from './base';
import type { User, LoginRequest, SignupRequest } from '@foodprepai/shared-types';

export class AuthAPIClient extends APIClient {
  async login(credentials: LoginRequest): Promise<APIResponse<{ user: User; token: string }>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(data: SignupRequest): Promise<APIResponse<{ user: User; token: string }>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async logout(): Promise<APIResponse<void>> {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  async refreshToken(): Promise<APIResponse<{ token: string }>> {
    return this.request('/auth/refresh', {
      method: 'POST',
    });
  }
}

export const authClient = new AuthAPIClient();