// Users API client
import { APIClient, APIResponse } from './base';
import type { User } from '@foodprepai/shared-types';

export class UsersAPIClient extends APIClient {
  async getUsers(): Promise<APIResponse<User[]>> {
    return this.request('/users');
  }

  async getUser(id: string): Promise<APIResponse<User>> {
    return this.request(`/users/${id}`);
  }

  async updateUser(id: string, user: Partial<User>): Promise<APIResponse<User>> {
    return this.request(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(user),
    });
  }

  async deleteUser(id: string): Promise<APIResponse<void>> {
    return this.request(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  async getUserProfile(): Promise<APIResponse<User>> {
    return this.request('/users/profile');
  }

  async updateUserProfile(user: Partial<User>): Promise<APIResponse<User>> {
    return this.request('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(user),
    });
  }
}

export const usersClient = new UsersAPIClient();