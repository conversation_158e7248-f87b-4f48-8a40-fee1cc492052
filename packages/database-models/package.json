{"name": "@foodprepai/database-models", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest"}, "dependencies": {"@foodprepai/shared-types": "*", "mongoose": "^8.9.2", "zod": "^3.24.1"}, "devDependencies": {"@foodprepai/tsconfig": "*", "@types/jest": "^29.5.14", "jest": "^29.7.0", "typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./User": {"types": "./dist/User.d.ts", "default": "./dist/User.js"}, "./Order": {"types": "./dist/Order.d.ts", "default": "./dist/Order.js"}, "./Inventory": {"types": "./dist/Inventory.d.ts", "default": "./dist/Inventory.js"}, "./Company": {"types": "./dist/Company.d.ts", "default": "./dist/Company.js"}}}