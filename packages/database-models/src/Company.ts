// Company database model
import { Schema, model } from 'mongoose';

export interface Company {
  _id: string;
  name: string;
  subdomain: string;
  companyCode?: string;
  settings?: {
    timezone: string;
    currency: string;
    weekStartDay: number;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const CompanySchema = new Schema({
  // Basic company info
  name: { type: String, required: true },
  subdomain: { type: String, required: true, unique: true },
  companyCode: { type: String },
  
  // Company settings
  settings: {
    timezone: { type: String, default: 'UTC' },
    currency: { type: String, default: 'USD' },
    weekStartDay: { type: Number, default: 1 } // Monday
  },
  
  // Status
  isActive: { type: Boolean, default: true },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

export const CompanyModel = model('Company', CompanySchema);