// User database model
import { Schema, model } from 'mongoose';

const UserSchema = new Schema({
  // Basic user info
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  name: { type: String },
  displayName: { type: String },
  
  // User type and role
  userType: { 
    type: String, 
    enum: ['superuser', 'company_user'],
    required: true 
  },
  role: { type: String },
  
  // Company and location associations
  companyId: { type: Schema.Types.ObjectId, ref: 'Company' },
  locationIds: [{ type: Schema.Types.ObjectId, ref: 'Location' }],
  primaryLocationId: { type: Schema.Types.ObjectId, ref: 'Location' },
  
  // Access controls
  canUseIonicApp: { type: Boolean, default: false },
  posAccess: { type: Boolean, default: false },
  isActive: { type: Boolean, default: true },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

export const UserModel = model('User', UserSchema);