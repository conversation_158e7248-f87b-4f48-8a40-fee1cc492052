// Order database model
import { Schema, model } from 'mongoose';

const OrderSchema = new Schema({
  // Order identification
  orderNumber: { type: String, required: true, unique: true },
  
  // Customer and company info
  customerId: { type: Schema.Types.ObjectId, ref: 'Customer' },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  locationId: { type: Schema.Types.ObjectId, ref: 'Location' },
  
  // Order details
  items: [{
    productId: { type: Schema.Types.ObjectId, ref: 'Product' },
    quantity: { type: Number, required: true },
    price: { type: Number, required: true },
    total: { type: Number, required: true }
  }],
  
  // Financial info
  subtotal: { type: Number, required: true },
  tax: { type: Number, default: 0 },
  discount: { type: Number, default: 0 },
  total: { type: Number, required: true },
  
  // Status and fulfillment
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'],
    default: 'pending'
  },
  
  // Timestamps
  orderDate: { type: Date, default: Date.now },
  deliveryDate: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

export const OrderModel = model('Order', OrderSchema);