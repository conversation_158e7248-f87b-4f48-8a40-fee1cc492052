// Inventory database model
import { Schema, model } from 'mongoose';

const InventorySchema = new Schema({
  // Basic ingredient info
  name: { type: String, required: true },
  description: { type: String },
  category: { type: String, required: true },
  
  // Company and location
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  locationId: { type: Schema.Types.ObjectId, ref: 'Location' },
  
  // Inventory tracking
  currentStock: { type: Number, default: 0 },
  minStock: { type: Number, default: 0 },
  maxStock: { type: Number },
  
  // Units and pricing
  unit: { type: String, required: true },
  cost: { type: Number },
  
  // Supplier info
  supplierId: { type: Schema.Types.ObjectId, ref: 'Supplier' },
  
  // Status
  isActive: { type: Boolean, default: true },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

export const InventoryModel = model('Inventory', InventorySchema);