# @foodprepai/shared-utils

Comprehensive utility functions and helpers for the FoodPrepAI ecosystem. This package provides type-safe utility functions that complement the shared types package.

## Installation

```bash
npm install @foodprepai/shared-utils
```

## Usage

### Date Utilities

```typescript
import { formatDate, getRelativeTime, addBusinessDays, DATE_FORMATS } from '@foodprepai/shared-utils';

// Format dates consistently
const formatted = formatDate(new Date(), 'DISPLAY'); // "Jan 15, 2024"
const isoDate = formatDate(new Date(), 'ISO'); // "2024-01-15"

// Get relative time
const relative = getRelativeTime(new Date(Date.now() - 3600000)); // "1 hour ago"

// Business day calculations
const deliveryDate = addBusinessDays(new Date(), 5); // 5 business days from now
```

### Validation Utilities

```typescript
import {
  isValidEmail,
  validatePasswordStrength,
  isValidObjectId,
  validateWithSchema
} from '@foodprepai/shared-utils';

// Email validation
if (isValidEmail('<EMAIL>')) {
  // Valid email
}

// Password strength validation
const strength = validatePasswordStrength('MySecureP@ssw0rd!');
console.log(`Password strength: ${strength.score}/4`);

// Schema validation
const result = validateWithSchema(userData, CreateUserSchema);
if (result.isValid) {
  // Process valid data
}
```

### Formatting Utilities

```typescript
import {
  formatCurrency,
  formatPhoneNumber,
  formatQuantity,
  truncateText,
  maskSensitiveInfo
} from '@foodprepai/shared-utils';

// Currency formatting
const price = formatCurrency(1234.56, 'USD'); // "$1,234.56"

// Phone number formatting
const phone = formatPhoneNumber('1234567890'); // "(*************"

// Quantity with units
const qty = formatQuantity(15.5, 'kg', 1); // "15.5 kg"

// Text truncation
const short = truncateText('Long description...', 20); // "Long description..."

// Mask sensitive data
const masked = maskSensitiveInfo('1234567890123456', 4, 4); // "1234********3456"
```

### Inventory Utilities

```typescript
import {
  calculateInventoryValue,
  calculateReorderQuantity,
  calculateABCAnalysis,
  analyzeStockLevels
} from '@foodprepai/shared-utils';

// Calculate inventory value
const value = calculateInventoryValue(100, 12.50); // $1,250.00

// Reorder calculations
const reorder = calculateReorderQuantity(
  50,    // current stock
  30,    // reorder point
  7,     // lead time days
  5,     // daily consumption
  7      // safety stock days
);

// ABC Analysis
const items = [
  { itemId: '1', itemName: 'Item A', annualValue: 50000, annualQuantity: 1000, unitCost: 50 },
  { itemId: '2', itemName: 'Item B', annualValue: 30000, annualQuantity: 2000, unitCost: 15 },
  // ... more items
];
const analysis = calculateABCAnalysis(items);

// Stock level analysis
const issues = analyzeStockLevels(10, 20, 100, 2, 45);
issues.forEach(issue => {
  console.log(`${issue.type}: ${issue.message}`);
});
```

## Utility Categories

### Date Utilities (`date/`)
- **formatDate**: Format dates using predefined formats
- **parseDate**: Safely parse date strings
- **getRelativeTime**: Get human-readable relative time
- **addBusinessDays**: Add business days (excluding weekends)
- **getBusinessDaysBetween**: Calculate business days between dates
- **getWeekRange/getMonthRange**: Get date ranges
- **isToday/isPast/isFuture**: Date comparison utilities

### Validation Utilities (`validation/`)
- **isValidEmail**: Comprehensive email validation
- **isValidPhoneNumber**: International phone number validation
- **isValidObjectId**: MongoDB ObjectId validation
- **validatePasswordStrength**: Password strength analysis
- **isValidPIN**: 4-digit PIN validation
- **validateWithSchema**: Generic Zod schema validation
- **sanitizeString**: String sanitization for security
- **normalizeEmail/normalizePhoneNumber**: Data normalization

### Formatting Utilities (`formatting/`)
- **formatCurrency**: Locale-aware currency formatting
- **formatNumber**: Number formatting with decimals
- **formatPercentage**: Percentage formatting
- **formatFileSize**: Human-readable file sizes
- **formatPhoneNumber**: Phone number display formatting
- **formatName**: Proper case name formatting
- **truncateText**: Text truncation with ellipsis
- **formatAddress**: Address formatting
- **formatList**: List formatting with conjunctions
- **createSlug**: URL-friendly slug creation
- **maskSensitiveInfo**: Sensitive data masking

### Inventory Utilities (`inventory/`)
- **calculateInventoryValue**: Stock value calculations
- **convertUOMQuantity**: Unit of measure conversions
- **calculateReorderQuantity**: Reorder point calculations
- **calculateInventoryTurnover**: Turnover ratio calculations
- **calculateABCAnalysis**: ABC inventory classification
- **calculateEOQ**: Economic Order Quantity
- **analyzeStockLevels**: Stock level issue detection
- **calculateSafetyStock**: Statistical safety stock calculation

## Constants and Type Guards

```typescript
import { Constants, TypeGuards } from '@foodprepai/shared-utils';

// Use predefined constants
const maxImageSize = Constants.FILE_SIZES.MAX_IMAGE;
const emailRegex = Constants.REGEX.EMAIL;

// Type guards for runtime checking
if (TypeGuards.isString(value)) {
  // TypeScript knows value is string
}

if (!TypeGuards.isEmpty(data)) {
  // Data is not null, undefined, empty string, or empty array/object
}
```

## Utility Helpers

```typescript
import { Utils } from '@foodprepai/shared-utils';

// Deep clone objects
const cloned = Utils.deepClone(originalObject);

// Debounce function calls
const debouncedSearch = Utils.debounce(searchFunction, 300);

// Throttle function calls
const throttledScroll = Utils.throttle(scrollHandler, 100);

// Generate random strings
const randomId = Utils.generateRandomString(12);
const uuid = Utils.generateUUID();

// Async utilities
await Utils.sleep(1000); // Wait 1 second

// Retry with exponential backoff
const result = await Utils.retry(
  () => apiCall(),
  3,    // max attempts
  1000  // base delay
);
```

## Advanced Usage

### Custom Validation

```typescript
import { validateCrossFields } from '@foodprepai/shared-utils';

const errors = validateCrossFields(orderData, [
  {
    condition: (data) => data.deliveryDate > data.orderDate,
    message: 'Delivery date must be after order date'
  },
  {
    condition: (data) => data.items.length > 0,
    message: 'Order must contain at least one item'
  }
]);
```

### File Validation

```typescript
import { validateFile } from '@foodprepai/shared-utils';

const validation = validateFile(uploadedFile, {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png'],
  allowedExtensions: ['jpg', 'jpeg', 'png']
});

if (validation.isValid) {
  // Process valid file
} else {
  console.error('File validation errors:', validation.errors);
}
```

### Inventory Analysis

```typescript
import { calculateReorderQuantity, ReorderCalculation } from '@foodprepai/shared-utils';

const analysis: ReorderCalculation = calculateReorderQuantity(
  25,   // current stock
  50,   // reorder point
  5,    // lead time days
  10,   // daily consumption
  7     // safety stock days
);

console.log(`Recommended action: ${analysis.recommendedAction}`);
console.log(`Reorder quantity: ${analysis.reorderQuantity}`);
```

## Integration with Shared Types

This package works seamlessly with `@foodprepai/shared-types`:

```typescript
import { User, CreateUserSchema } from '@foodprepai/shared-types';
import { validateWithSchema, normalizeEmail } from '@foodprepai/shared-utils';

// Validate and normalize user data
const createUser = (userData: unknown): User | null => {
  // Normalize email before validation
  if (typeof userData === 'object' && userData && 'email' in userData) {
    (userData as any).email = normalizeEmail((userData as any).email);
  }
  
  // Validate with schema
  const validation = validateWithSchema(userData, CreateUserSchema);
  
  return validation.isValid ? validation.data as User : null;
};
```

## Performance Considerations

- Date utilities use `date-fns` for efficient date operations
- Validation functions are optimized for common use cases
- Formatting utilities support internationalization
- Inventory calculations use precise decimal arithmetic
- Utility functions are tree-shakable for optimal bundle size

## Testing

```typescript
import { formatCurrency, isValidEmail } from '@foodprepai/shared-utils';

describe('Utility functions', () => {
  test('currency formatting', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56');
  });
  
  test('email validation', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('invalid-email')).toBe(false);
  });
});
```

## Contributing

When adding new utilities:

1. Follow existing patterns and conventions
2. Include comprehensive JSDoc comments
3. Add unit tests for all functions
4. Consider internationalization for formatting functions
5. Ensure functions are pure when possible
6. Update this README with usage examples

## Related Packages

- `@foodprepai/shared-types` - TypeScript type definitions
- `@foodprepai/api-client` - API client that uses these utilities
- `@foodprepai/ui-components` - UI components that use formatting utilities