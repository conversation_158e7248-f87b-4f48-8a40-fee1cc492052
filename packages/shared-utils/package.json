{"name": "@foodprepai/shared-utils", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest"}, "dependencies": {"@foodprepai/shared-types": "*", "date-fns": "^4.1.0", "zod": "^3.24.1"}, "devDependencies": {"@foodprepai/tsconfig": "*", "@types/jest": "^29.5.14", "jest": "^29.7.0", "typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./date": {"types": "./dist/date.d.ts", "default": "./dist/date.js"}, "./validation": {"types": "./dist/validation.d.ts", "default": "./dist/validation.js"}, "./formatting": {"types": "./dist/formatting.d.ts", "default": "./dist/formatting.js"}, "./inventory": {"types": "./dist/inventory.d.ts", "default": "./dist/inventory.js"}}}