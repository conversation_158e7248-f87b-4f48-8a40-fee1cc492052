/**
 * Inventory-specific utility functions for FoodPrepAI ecosystem
 */

import { UOM } from '@foodprepai/shared-types';

/**
 * Calculate the value of inventory stock
 */
export const calculateInventoryValue = (
  quantity: number,
  unitCost: number,
  decimals: number = 2
): number => {
  const value = quantity * unitCost;
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

/**
 * Convert quantity between different units of measure
 */
export const convertUOMQuantity = (
  quantity: number,
  fromUOM: UOM,
  toUOM: UOM,
  baseUOM: UOM
): number => {
  // If converting to the same UOM, return original quantity
  if (fromUOM._id === toUOM._id) {
    return quantity;
  }
  
  // Convert from source UOM to base UOM
  let quantityInBase: number;
  if (fromUOM._id === baseUOM._id) {
    // Already in base UOM
    quantityInBase = quantity;
  } else if (fromUOM.baseUnit === baseUOM._id && fromUOM.conversionFactor) {
    // Convert from derived UOM to base UOM
    quantityInBase = quantity * fromUOM.conversionFactor;
  } else {
    throw new Error(`Cannot convert from ${fromUOM.name} to base UOM ${baseUOM.name}`);
  }
  
  // Convert from base UOM to target UOM
  if (toUOM._id === baseUOM._id) {
    // Target is base UOM
    return quantityInBase;
  } else if (toUOM.baseUnit === baseUOM._id && toUOM.conversionFactor) {
    // Convert from base UOM to derived UOM
    return quantityInBase / toUOM.conversionFactor;
  } else {
    throw new Error(`Cannot convert from base UOM ${baseUOM.name} to ${toUOM.name}`);
  }
};

/**
 * Calculate reorder quantity based on consumption patterns
 */
export interface ReorderCalculation {
  reorderQuantity: number;
  safetyStock: number;
  estimatedDaysUntilDepletion: number;
  recommendedAction: 'NO_ACTION' | 'ORDER_SOON' | 'ORDER_NOW' | 'URGENT_ORDER';
}

export const calculateReorderQuantity = (
  currentStock: number,
  reorderPoint: number,
  leadTimeDays: number,
  averageDailyConsumption: number,
  safetyStockDays: number = 7
): ReorderCalculation => {
  const safetyStock = averageDailyConsumption * safetyStockDays;
  const leadTimeStock = averageDailyConsumption * leadTimeDays;
  const optimalOrderQuantity = leadTimeStock + safetyStock;
  
  let recommendedAction: ReorderCalculation['recommendedAction'] = 'NO_ACTION';
  const estimatedDaysUntilDepletion = averageDailyConsumption > 0 
    ? currentStock / averageDailyConsumption 
    : Infinity;
  
  if (currentStock <= reorderPoint * 0.5) {
    recommendedAction = 'URGENT_ORDER';
  } else if (currentStock <= reorderPoint) {
    recommendedAction = 'ORDER_NOW';
  } else if (estimatedDaysUntilDepletion <= leadTimeDays + safetyStockDays) {
    recommendedAction = 'ORDER_SOON';
  }
  
  return {
    reorderQuantity: Math.max(0, optimalOrderQuantity - currentStock),
    safetyStock,
    estimatedDaysUntilDepletion,
    recommendedAction
  };
};

/**
 * Calculate inventory turnover ratio
 */
export const calculateInventoryTurnover = (
  costOfGoodsSold: number,
  averageInventoryValue: number
): number => {
  if (averageInventoryValue === 0) return 0;
  return costOfGoodsSold / averageInventoryValue;
};

/**
 * Calculate days sales of inventory (DSI)
 */
export const calculateDaysSalesInventory = (
  averageInventoryValue: number,
  costOfGoodsSold: number,
  daysInPeriod: number = 365
): number => {
  if (costOfGoodsSold === 0) return Infinity;
  return (averageInventoryValue / costOfGoodsSold) * daysInPeriod;
};

/**
 * Calculate ABC analysis classification
 */
export interface ABCAnalysisItem {
  itemId: string;
  itemName: string;
  annualValue: number;
  annualQuantity: number;
  unitCost: number;
}

export interface ABCAnalysisResult extends ABCAnalysisItem {
  classification: 'A' | 'B' | 'C';
  cumulativePercentage: number;
  valuePercentage: number;
}

export const calculateABCAnalysis = (
  items: ABCAnalysisItem[],
  aThreshold: number = 80,
  bThreshold: number = 95
): ABCAnalysisResult[] => {
  // Sort items by annual value in descending order
  const sortedItems = [...items].sort((a, b) => b.annualValue - a.annualValue);
  
  // Calculate total value
  const totalValue = sortedItems.reduce((sum, item) => sum + item.annualValue, 0);
  
  // Calculate cumulative percentages and classifications
  let cumulativeValue = 0;
  
  return sortedItems.map(item => {
    cumulativeValue += item.annualValue;
    const cumulativePercentage = (cumulativeValue / totalValue) * 100;
    const valuePercentage = (item.annualValue / totalValue) * 100;
    
    let classification: 'A' | 'B' | 'C';
    if (cumulativePercentage <= aThreshold) {
      classification = 'A';
    } else if (cumulativePercentage <= bThreshold) {
      classification = 'B';
    } else {
      classification = 'C';
    }
    
    return {
      ...item,
      classification,
      cumulativePercentage,
      valuePercentage
    };
  });
};

/**
 * Calculate economic order quantity (EOQ)
 */
export const calculateEOQ = (
  annualDemand: number,
  orderingCost: number,
  holdingCostPerUnit: number
): number => {
  if (holdingCostPerUnit === 0) return 0;
  return Math.sqrt((2 * annualDemand * orderingCost) / holdingCostPerUnit);
};

/**
 * Calculate carrying cost
 */
export const calculateCarryingCost = (
  averageInventory: number,
  carryingCostRate: number,
  unitCost: number
): number => {
  return averageInventory * unitCost * carryingCostRate;
};

/**
 * Validate stock levels and identify issues
 */
export interface StockIssue {
  type: 'LOW_STOCK' | 'OVERSTOCK' | 'DEAD_STOCK' | 'NEGATIVE_STOCK';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  recommendedAction: string;
}

export const analyzeStockLevels = (
  currentStock: number,
  reorderPoint: number,
  maxStock: number,
  averageDailyConsumption: number,
  lastMovementDays: number
): StockIssue[] => {
  const issues: StockIssue[] = [];
  
  // Check for negative stock
  if (currentStock < 0) {
    issues.push({
      type: 'NEGATIVE_STOCK',
      severity: 'CRITICAL',
      message: 'Stock level is negative',
      recommendedAction: 'Immediate stock correction required'
    });
  }
  
  // Check for low stock
  if (currentStock <= reorderPoint) {
    const severity = currentStock <= reorderPoint * 0.5 ? 'CRITICAL' : 'HIGH';
    issues.push({
      type: 'LOW_STOCK',
      severity,
      message: `Stock is below reorder point (${reorderPoint})`,
      recommendedAction: 'Place order immediately'
    });
  }
  
  // Check for overstock
  if (maxStock > 0 && currentStock > maxStock) {
    issues.push({
      type: 'OVERSTOCK',
      severity: 'MEDIUM',
      message: `Stock exceeds maximum level (${maxStock})`,
      recommendedAction: 'Consider reducing future orders or promoting sales'
    });
  }
  
  // Check for dead stock (no movement for extended period)
  const deadStockThreshold = 90; // days
  if (lastMovementDays > deadStockThreshold && currentStock > 0) {
    const severity = lastMovementDays > 180 ? 'HIGH' : 'MEDIUM';
    issues.push({
      type: 'DEAD_STOCK',
      severity,
      message: `No movement for ${lastMovementDays} days`,
      recommendedAction: 'Consider liquidation or promotional pricing'
    });
  }
  
  return issues;
};

/**
 * Calculate optimal safety stock using statistical methods
 */
export const calculateSafetyStock = (
  leadTimeDays: number,
  averageDemand: number,
  demandStandardDeviation: number,
  serviceLevel: number = 0.95 // 95% service level
): number => {
  // Z-score for different service levels
  const zScores: { [key: number]: number } = {
    0.90: 1.28,
    0.95: 1.65,
    0.98: 2.05,
    0.99: 2.33
  };
  
  const zScore = zScores[serviceLevel] || 1.65;
  const leadTimeDemandStdDev = Math.sqrt(leadTimeDays) * demandStandardDeviation;
  
  return zScore * leadTimeDemandStdDev;
};

/**
 * Calculate shortage cost impact
 */
export const calculateShortageCost = (
  shortageQuantity: number,
  unitCost: number,
  shortageCostMultiplier: number = 1.5
): number => {
  return shortageQuantity * unitCost * shortageCostMultiplier;
};

/**
 * Format inventory metrics for display
 */
export const formatInventoryMetrics = (metrics: {
  turnoverRatio: number;
  daysSalesInventory: number;
  fillRate: number;
  stockoutDays: number;
}): {
  turnoverRatio: string;
  daysSalesInventory: string;
  fillRate: string;
  stockoutDays: string;
} => {
  return {
    turnoverRatio: metrics.turnoverRatio.toFixed(2),
    daysSalesInventory: Math.round(metrics.daysSalesInventory).toString(),
    fillRate: `${(metrics.fillRate * 100).toFixed(1)}%`,
    stockoutDays: metrics.stockoutDays.toString()
  };
};