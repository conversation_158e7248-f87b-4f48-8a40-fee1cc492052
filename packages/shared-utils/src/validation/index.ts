/**
 * Validation utility functions for FoodPrepAI ecosystem
 */

import { z } from 'zod';
import { ValidationResult } from '@foodprepai/shared-types';

/**
 * Validate an email address using a comprehensive regex
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
};

/**
 * Validate a phone number (supports international formats)
 */
export const isValidPhoneNumber = (phone: string): boolean => {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if it's between 7 and 15 digits (international standard)
  return cleanPhone.length >= 7 && cleanPhone.length <= 15;
};

/**
 * Validate a MongoDB ObjectId
 */
export const isValidObjectId = (id: string): boolean => {
  return /^[0-9a-fA-F]{24}$/.test(id);
};

/**
 * Validate a subdomain (for company subdomains)
 */
export const isValidSubdomain = (subdomain: string): boolean => {
  // Must be 3-50 characters, lowercase letters, numbers, and hyphens only
  // Cannot start or end with hyphen
  const subdomainRegex = /^[a-z0-9]([a-z0-9-]{1,48}[a-z0-9])?$/;
  return subdomainRegex.test(subdomain);
};

/**
 * Validate a password strength
 */
export interface PasswordStrength {
  isValid: boolean;
  score: number; // 0-4
  feedback: string[];
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
}

export const validatePasswordStrength = (password: string): PasswordStrength => {
  const feedback: string[] = [];
  const requirements = {
    minLength: password.length >= 8,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumber: /[0-9]/.test(password),
    hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  };
  
  let score = 0;
  
  if (!requirements.minLength) {
    feedback.push('Password must be at least 8 characters long');
  } else {
    score++;
  }
  
  if (!requirements.hasUppercase) {
    feedback.push('Password must contain at least one uppercase letter');
  } else {
    score++;
  }
  
  if (!requirements.hasLowercase) {
    feedback.push('Password must contain at least one lowercase letter');
  } else {
    score++;
  }
  
  if (!requirements.hasNumber) {
    feedback.push('Password must contain at least one number');
  } else {
    score++;
  }
  
  if (!requirements.hasSpecialChar) {
    feedback.push('Password must contain at least one special character');
  } else {
    score++;
  }
  
  // Additional checks for common patterns
  if (password.length < 12) {
    feedback.push('Consider using a longer password for better security');
    score = Math.min(score, 3);
  }
  
  if (/(.)\1{2,}/.test(password)) {
    feedback.push('Avoid repeating characters');
    score = Math.max(0, score - 1);
  }
  
  const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'welcome'];
  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
    feedback.push('Avoid common password patterns');
    score = Math.max(0, score - 2);
  }
  
  const isValid = Object.values(requirements).every(req => req);
  
  return {
    isValid,
    score: Math.max(0, Math.min(4, score)),
    feedback,
    requirements
  };
};

/**
 * Validate a PIN (4-digit numeric)
 */
export const isValidPIN = (pin: string): boolean => {
  return /^\d{4}$/.test(pin);
};

/**
 * Validate a SKU format
 */
export const isValidSKU = (sku: string): boolean => {
  // SKU should be 3-50 characters, alphanumeric with hyphens and underscores
  return /^[A-Za-z0-9_-]{3,50}$/.test(sku);
};

/**
 * Validate a company code format
 */
export const isValidCompanyCode = (code: string): boolean => {
  // Company code should be 3-10 characters, alphanumeric
  return /^[A-Za-z0-9]{3,10}$/.test(code);
};

/**
 * Validate a quantity value
 */
export const isValidQuantity = (quantity: number): boolean => {
  return Number.isFinite(quantity) && quantity > 0;
};

/**
 * Validate a price value
 */
export const isValidPrice = (price: number): boolean => {
  return Number.isFinite(price) && price >= 0;
};

/**
 * Validate a percentage value
 */
export const isValidPercentage = (percentage: number): boolean => {
  return Number.isFinite(percentage) && percentage >= 0 && percentage <= 100;
};

/**
 * Generic validation function using Zod schemas
 */
export const validateWithSchema = <T>(
  data: unknown, 
  schema: z.ZodSchema<T>
): ValidationResult<T> => {
  try {
    const validatedData = schema.parse(data);
    return {
      isValid: true,
      data: validatedData
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    
    return {
      isValid: false,
      errors: ['Unknown validation error']
    };
  }
};

/**
 * Sanitize a string for safe storage/display
 */
export const sanitizeString = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/[\x00-\x1f\x7f-\x9f]/g, ''); // Remove control characters
};

/**
 * Normalize a phone number to a standard format
 */
export const normalizePhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Add country code if not present (assumes US)
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+${cleaned}`;
  }
  
  return `+${cleaned}`;
};

/**
 * Validate and normalize an email address
 */
export const normalizeEmail = (email: string): string => {
  return email.toLowerCase().trim();
};

/**
 * Check if a string contains only alphanumeric characters
 */
export const isAlphaNumeric = (str: string): boolean => {
  return /^[a-zA-Z0-9]+$/.test(str);
};

/**
 * Check if a string is a valid URL
 */
export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate file upload constraints
 */
export interface FileValidationOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
}

export const validateFile = (
  file: File, 
  options: FileValidationOptions = {}
): ValidationResult<File> => {
  const errors: string[] = [];
  
  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`File size exceeds maximum allowed size of ${options.maxSize} bytes`);
  }
  
  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  if (options.allowedExtensions) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !options.allowedExtensions.includes(extension)) {
      errors.push(`File extension is not allowed`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    data: errors.length === 0 ? file : undefined,
    errors: errors.length > 0 ? errors : undefined
  };
};

/**
 * Validate a date range
 */
export const isValidDateRange = (startDate: Date, endDate: Date): boolean => {
  return startDate <= endDate;
};

/**
 * Cross-field validation helper
 */
export const validateCrossFields = <T extends Record<string, any>>(
  data: T,
  validations: Array<{
    condition: (data: T) => boolean;
    message: string;
  }>
): string[] => {
  const errors: string[] = [];
  
  for (const validation of validations) {
    if (!validation.condition(data)) {
      errors.push(validation.message);
    }
  }
  
  return errors;
};