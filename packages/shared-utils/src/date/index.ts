/**
 * Date utility functions for FoodPrepAI ecosystem
 */

import { format, parseISO, isValid, addDays, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

// Standard date formats used across the application
export const DATE_FORMATS = {
  ISO: 'yyyy-MM-dd',
  DISPLAY: 'MMM dd, yyyy',
  LONG: 'EEEE, MMMM do, yyyy',
  SHORT: 'MM/dd/yyyy',
  TIME: 'HH:mm',
  DATETIME: 'MMM dd, yyyy HH:mm',
  DATETIME_FULL: 'EEEE, MMMM do, yyyy \'at\' HH:mm',
  API: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
} as const;

/**
 * Format a date using predefined formats
 */
export const formatDate = (
  date: Date | string | number,
  formatKey: keyof typeof DATE_FORMATS = 'DISPLAY'
): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
  
  if (!isValid(dateObj)) {
    throw new Error('Invalid date provided to formatDate');
  }
  
  return format(dateObj, DATE_FORMATS[formatKey]);
};

/**
 * Parse a date string safely
 */
export const parseDate = (dateString: string): Date | null => {
  if (!dateString) return null;
  
  const parsed = parseISO(dateString);
  return isValid(parsed) ? parsed : null;
};

/**
 * Check if a date is valid
 */
export const isValidDate = (date: any): date is Date => {
  return date instanceof Date && isValid(date);
};

/**
 * Get the start and end of a week for a given date
 */
export const getWeekRange = (date: Date, weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6 = 1): { start: Date; end: Date } => {
  return {
    start: startOfWeek(date, { weekStartsOn }),
    end: endOfWeek(date, { weekStartsOn })
  };
};

/**
 * Get the start and end of a month for a given date
 */
export const getMonthRange = (date: Date): { start: Date; end: Date } => {
  return {
    start: startOfMonth(date),
    end: endOfMonth(date)
  };
};

/**
 * Add business days to a date (excludes weekends)
 */
export const addBusinessDays = (date: Date, days: number): Date => {
  let result = new Date(date);
  let remainingDays = days;
  
  while (remainingDays > 0) {
    result = addDays(result, 1);
    
    // Skip weekends (Saturday = 6, Sunday = 0)
    if (result.getDay() !== 0 && result.getDay() !== 6) {
      remainingDays--;
    }
  }
  
  return result;
};

/**
 * Calculate the number of business days between two dates
 */
export const getBusinessDaysBetween = (startDate: Date, endDate: Date): number => {
  let days = 0;
  let current = new Date(startDate);
  
  while (current < endDate) {
    // Skip weekends
    if (current.getDay() !== 0 && current.getDay() !== 6) {
      days++;
    }
    current = addDays(current, 1);
  }
  
  return days;
};

/**
 * Check if a date is within a range
 */
export const isDateInRange = (date: Date, startDate: Date, endDate: Date): boolean => {
  return date >= startDate && date <= endDate;
};

/**
 * Get relative time string (e.g., "2 hours ago", "in 3 days")
 */
export const getRelativeTime = (date: Date, baseDate: Date = new Date()): string => {
  const diffInMs = date.getTime() - baseDate.getTime();
  const diffInMinutes = Math.round(diffInMs / (1000 * 60));
  const diffInHours = Math.round(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.round(diffInMs / (1000 * 60 * 60 * 24));
  
  if (Math.abs(diffInMinutes) < 1) {
    return 'just now';
  } else if (Math.abs(diffInMinutes) < 60) {
    return diffInMinutes > 0 ? `in ${diffInMinutes} minutes` : `${Math.abs(diffInMinutes)} minutes ago`;
  } else if (Math.abs(diffInHours) < 24) {
    return diffInHours > 0 ? `in ${diffInHours} hours` : `${Math.abs(diffInHours)} hours ago`;
  } else if (Math.abs(diffInDays) < 7) {
    return diffInDays > 0 ? `in ${diffInDays} days` : `${Math.abs(diffInDays)} days ago`;
  } else {
    return formatDate(date, 'DISPLAY');
  }
};

/**
 * Convert timezone-aware date to local date string
 */
export const toLocalDateString = (date: Date, timezone?: string): string => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: timezone
  };
  
  return date.toLocaleDateString('en-US', options);
};

/**
 * Get the fiscal year for a date (assumes fiscal year starts in January)
 */
export const getFiscalYear = (date: Date, fiscalYearStartMonth: number = 1): number => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // getMonth() returns 0-11
  
  return month >= fiscalYearStartMonth ? year : year - 1;
};

/**
 * Check if a date is today
 */
export const isToday = (date: Date): boolean => {
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

/**
 * Check if a date is in the past
 */
export const isPast = (date: Date): boolean => {
  return date < new Date();
};

/**
 * Check if a date is in the future
 */
export const isFuture = (date: Date): boolean => {
  return date > new Date();
};

/**
 * Get date range options for common filters
 */
export const getDateRangeOptions = (baseDate: Date = new Date()) => {
  return {
    today: {
      start: new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()),
      end: new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate(), 23, 59, 59)
    },
    yesterday: {
      start: subDays(new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()), 1),
      end: subDays(new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate(), 23, 59, 59), 1)
    },
    thisWeek: getWeekRange(baseDate),
    thisMonth: getMonthRange(baseDate),
    last7Days: {
      start: subDays(baseDate, 6),
      end: baseDate
    },
    last30Days: {
      start: subDays(baseDate, 29),
      end: baseDate
    }
  };
};