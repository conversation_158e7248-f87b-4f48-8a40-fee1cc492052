/**
 * Platform Detection and Configuration
 * 
 * Detects the current platform and provides configuration utilities
 * for adapting components to web vs mobile environments
 */

export type Platform = 'web' | 'ionic' | 'auto';

export interface PlatformConfig {
  platform: Platform;
  isMobile: boolean;
  isIonic: boolean;
  isWeb: boolean;
  preferIonic: boolean;
}

/**
 * Detects the current platform based on environment
 */
export function detectPlatform(): Platform {
  // Server-side rendering check
  if (typeof window === 'undefined') {
    return 'web';
  }

  // Check for Ionic environment
  if (typeof window !== 'undefined' && (window as any).Ionic) {
    return 'ionic';
  }

  // Check for Capacitor (mobile app environment)
  if (typeof window !== 'undefined' && (window as any).Capacitor) {
    return 'ionic';
  }

  // Check for mobile user agent
  const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );

  // Check for touch device
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  // Check for small screen
  const isSmallScreen = window.innerWidth <= 768;

  // Default to web for desktop/tablet, consider ionic for mobile
  if (isMobileUserAgent || (isTouchDevice && isSmallScreen)) {
    // Could be either, but default to web unless explicitly set
    return 'web';
  }

  return 'web';
}

/**
 * Creates platform configuration
 */
export function createPlatformConfig(platform: Platform = 'auto'): PlatformConfig {
  const detectedPlatform = platform === 'auto' ? detectPlatform() : platform;
  
  return {
    platform: detectedPlatform,
    isMobile: detectedPlatform === 'ionic',
    isIonic: detectedPlatform === 'ionic',
    isWeb: detectedPlatform === 'web',
    preferIonic: detectedPlatform === 'ionic',
  };
}

/**
 * Global platform configuration
 */
let globalPlatformConfig: PlatformConfig | null = null;

/**
 * Gets the current global platform configuration
 */
export function getPlatformConfig(): PlatformConfig {
  if (!globalPlatformConfig) {
    globalPlatformConfig = createPlatformConfig();
  }
  return globalPlatformConfig;
}

/**
 * Sets the global platform configuration
 */
export function setPlatformConfig(config: Partial<PlatformConfig> | Platform): void {
  if (typeof config === 'string') {
    globalPlatformConfig = createPlatformConfig(config);
  } else {
    const currentConfig = getPlatformConfig();
    globalPlatformConfig = { ...currentConfig, ...config };
  }
}

/**
 * Resets platform configuration to auto-detect
 */
export function resetPlatformConfig(): void {
  globalPlatformConfig = null;
}

/**
 * Hook for getting platform configuration (for React components)
 */
export function usePlatformConfig(): PlatformConfig {
  return getPlatformConfig();
}

/**
 * Utility function to conditionally return values based on platform
 */
export function platformSelect<T>(values: {
  web?: T;
  ionic?: T;
  default: T;
}): T {
  const config = getPlatformConfig();
  
  if (config.isWeb && values.web !== undefined) {
    return values.web;
  }
  
  if (config.isIonic && values.ionic !== undefined) {
    return values.ionic;
  }
  
  return values.default;
}

/**
 * Utility function to check if we're running in a specific platform
 */
export function isPlatform(platform: Platform): boolean {
  const config = getPlatformConfig();
  return config.platform === platform;
}

/**
 * Environment detection utilities
 */
export const env = {
  isServer: typeof window === 'undefined',
  isBrowser: typeof window !== 'undefined',
  isCapacitor: typeof window !== 'undefined' && !!(window as any).Capacitor,
  isIonic: typeof window !== 'undefined' && !!(window as any).Ionic,
  isCordova: typeof window !== 'undefined' && !!(window as any).cordova,
  isElectron: typeof window !== 'undefined' && !!(window as any).require,
};