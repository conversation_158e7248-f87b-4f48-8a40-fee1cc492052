/**
 * Core Component Types and Interfaces
 * 
 * Defines the unified component API that works across both web and mobile platforms
 */

import { ReactNode, ComponentProps, CSSProperties } from 'react';
import { VariantProps } from 'class-variance-authority';

// Base component props that all components should extend
export interface BaseComponentProps {
  /**
   * Additional CSS classes to apply
   */
  className?: string;
  
  /**
   * Inline styles (use sparingly, prefer className)
   */
  style?: CSSProperties;
  
  /**
   * Child elements
   */
  children?: ReactNode;
  
  /**
   * Test ID for automated testing
   */
  testId?: string;
  
  /**
   * Platform override - force component to render for specific platform
   */
  platform?: 'web' | 'ionic' | 'auto';
}

// Size variants used across components
export type SizeVariant = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Color variants used across components
export type ColorVariant = 
  | 'primary'
  | 'secondary' 
  | 'success'
  | 'warning'
  | 'error'
  | 'neutral'
  | 'ghost';

// Button-specific types
export interface ButtonProps extends BaseComponentProps {
  /**
   * Button variant style
   */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  
  /**
   * Button size
   */
  size?: SizeVariant;
  
  /**
   * Whether button is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether button is in loading state
   */
  loading?: boolean;
  
  /**
   * Icon to display (icon name for Ionic, React element for web)
   */
  icon?: string | ReactNode;
  
  /**
   * Icon position
   */
  iconPosition?: 'start' | 'end';
  
  /**
   * Whether button should take full width
   */
  fullWidth?: boolean;
  
  /**
   * Click handler
   */
  onClick?: () => void;
  
  /**
   * Button type
   */
  type?: 'button' | 'submit' | 'reset';
}

// Table-specific types
export interface TableColumn<T = any> {
  /**
   * Unique key for the column
   */
  key: string;
  
  /**
   * Column header text
   */
  header: string;
  
  /**
   * Column width (CSS width value)
   */
  width?: string;
  
  /**
   * Whether column is sortable
   */
  sortable?: boolean;
  
  /**
   * Custom render function for cell content
   */
  render?: (value: any, item: T, index: number) => ReactNode;
  
  /**
   * Accessor function for getting cell value
   */
  accessor?: (item: T) => any;
  
  /**
   * Column alignment
   */
  align?: 'left' | 'center' | 'right';
}

export interface TableProps<T = any> extends BaseComponentProps {
  /**
   * Table columns configuration
   */
  columns: TableColumn<T>[];
  
  /**
   * Table data
   */
  data: T[];
  
  /**
   * Whether table is in loading state
   */
  loading?: boolean;
  
  /**
   * Number of skeleton rows to show when loading
   */
  loadingRows?: number;
  
  /**
   * Message to show when no data
   */
  emptyMessage?: string;
  
  /**
   * Row click handler
   */
  onRowClick?: (item: T, index: number) => void;
  
  /**
   * Whether rows are selectable
   */
  selectable?: boolean;
  
  /**
   * Selected row IDs
   */
  selectedRows?: string[] | number[];
  
  /**
   * Selection change handler
   */
  onSelectionChange?: (selectedRows: string[] | number[]) => void;
  
  /**
   * Sort configuration
   */
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
}

// Modal/Dialog types
export interface ModalProps extends BaseComponentProps {
  /**
   * Whether modal is open
   */
  isOpen: boolean;
  
  /**
   * Function to call when modal should close
   */
  onClose: () => void;
  
  /**
   * Modal title
   */
  title?: string;
  
  /**
   * Modal size
   */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /**
   * Whether modal can be closed by clicking backdrop
   */
  closeOnBackdropClick?: boolean;
  
  /**
   * Whether modal can be closed with Escape key
   */
  closeOnEscape?: boolean;
  
  /**
   * Whether to show close button
   */
  showCloseButton?: boolean;
  
  /**
   * Custom header content
   */
  header?: ReactNode;
  
  /**
   * Custom footer content
   */
  footer?: ReactNode;
  
  /**
   * Whether modal should be centered
   */
  centered?: boolean;
}

// Input/Form types
export interface InputProps extends BaseComponentProps {
  /**
   * Input type
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  
  /**
   * Input value
   */
  value?: string;
  
  /**
   * Default value (uncontrolled)
   */
  defaultValue?: string;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Whether input is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether input is readonly
   */
  readonly?: boolean;
  
  /**
   * Whether input is required
   */
  required?: boolean;
  
  /**
   * Input size
   */
  size?: SizeVariant;
  
  /**
   * Error state
   */
  error?: boolean;
  
  /**
   * Error message
   */
  errorMessage?: string;
  
  /**
   * Helper text
   */
  helperText?: string;
  
  /**
   * Input label
   */
  label?: string;
  
  /**
   * Change handler
   */
  onChange?: (value: string) => void;
  
  /**
   * Blur handler
   */
  onBlur?: () => void;
  
  /**
   * Focus handler
   */
  onFocus?: () => void;
  
  /**
   * Icon to show in input (start position)
   */
  startIcon?: string | ReactNode;
  
  /**
   * Icon to show in input (end position)
   */
  endIcon?: string | ReactNode;
}

// Select types
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
}

export interface SelectProps extends BaseComponentProps {
  /**
   * Select options
   */
  options: SelectOption[];
  
  /**
   * Selected value
   */
  value?: string | number;
  
  /**
   * Default value (uncontrolled)
   */
  defaultValue?: string | number;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Whether select is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether select is required
   */
  required?: boolean;
  
  /**
   * Select size
   */
  size?: SizeVariant;
  
  /**
   * Error state
   */
  error?: boolean;
  
  /**
   * Error message
   */
  errorMessage?: string;
  
  /**
   * Helper text
   */
  helperText?: string;
  
  /**
   * Select label
   */
  label?: string;
  
  /**
   * Change handler
   */
  onChange?: (value: string | number) => void;
  
  /**
   * Whether multiple selection is allowed
   */
  multiple?: boolean;
  
  /**
   * Multiple selected values
   */
  multipleValue?: (string | number)[];
  
  /**
   * Multiple change handler
   */
  onMultipleChange?: (values: (string | number)[]) => void;
}

// Card types
export interface CardProps extends BaseComponentProps {
  /**
   * Card header content
   */
  header?: ReactNode;
  
  /**
   * Card title
   */
  title?: string;
  
  /**
   * Card subtitle
   */
  subtitle?: string;
  
  /**
   * Card footer content
   */
  footer?: ReactNode;
  
  /**
   * Whether card is clickable
   */
  clickable?: boolean;
  
  /**
   * Click handler
   */
  onClick?: () => void;
  
  /**
   * Card padding size
   */
  padding?: SizeVariant;
  
  /**
   * Whether card has border
   */
  bordered?: boolean;
  
  /**
   * Whether card has shadow
   */
  shadow?: boolean | 'sm' | 'md' | 'lg';
}

// Platform-specific prop types
export type WebComponentProps<T = {}> = T & {
  /**
   * Web-specific props that only apply to shadcn/ui components
   */
  asChild?: boolean;
  variant?: string;
};

export type IonicComponentProps<T = {}> = T & {
  /**
   * Ionic-specific props
   */
  fill?: 'clear' | 'outline' | 'solid';
  expand?: 'block' | 'full';
  shape?: 'round';
  color?: string;
  strong?: boolean;
  slot?: string;
};

// Component factory type for creating platform-adaptive components
export interface ComponentFactory<TProps extends BaseComponentProps> {
  (props: TProps): ReactNode;
  displayName?: string;
}

// Utility type for extracting props from component factory
export type ExtractProps<T> = T extends ComponentFactory<infer P> ? P : never;