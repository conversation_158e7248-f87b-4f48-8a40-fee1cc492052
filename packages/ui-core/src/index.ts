/**
 * @foodprepai/ui-core
 * 
 * Core abstractions and platform-agnostic component APIs
 * This package provides the foundation for the unified component system
 */

// Platform detection and configuration
export * from './platform';

// Core component types and interfaces
export * from './types';

// Utility functions and helpers
export * from './utils';

// Component base classes and abstractions
export * from './components';

// Re-export design tokens for convenience
export { colors, typography, spacing, borderRadius, boxShadow, components } from '@foodprepai/design-tokens';