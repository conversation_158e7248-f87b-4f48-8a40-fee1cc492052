/**
 * Component Base Classes and Abstractions
 * 
 * Provides base functionality for creating platform-adaptive components
 */

import React, { ReactNode } from 'react';
import { BaseComponentProps, ComponentFactory } from '../types';
import { getPlatformConfig, platformSelect } from '../platform';
import { cn } from '../utils';

/**
 * Base component class that provides platform-adaptive rendering
 */
export abstract class BaseComponent<TProps extends BaseComponentProps> {
  protected abstract renderWeb(props: TProps): ReactNode;
  protected abstract renderIonic(props: TProps): ReactNode;

  public render(props: TProps): ReactNode {
    const platformConfig = getPlatformConfig();
    const { platform = 'auto', className, testId, ...restProps } = props;

    // Override platform if specified in props
    const effectivePlatform = platform === 'auto' 
      ? platformConfig.platform
      : platform;

    const baseProps = {
      className: cn(className),
      'data-testid': testId,
      ...restProps,
    };

    if (effectivePlatform === 'ionic') {
      return this.renderIonic({ ...props, ...baseProps } as TProps);
    } else {
      return this.renderWeb({ ...props, ...baseProps } as TProps);
    }
  }
}

/**
 * Factory function for creating platform-adaptive components
 */
export function createPlatformComponent<TProps extends BaseComponentProps>(
  webRenderer: (props: TProps) => ReactNode,
  ionicRenderer: (props: TProps) => ReactNode,
  displayName?: string
): ComponentFactory<TProps> {
  const PlatformComponent: ComponentFactory<TProps> = (props: TProps) => {
    const { platform = 'auto', className, testId, ...restProps } = props;
    const platformConfig = getPlatformConfig();

    // Override platform if specified in props
    const effectivePlatform = platform === 'auto' 
      ? platformConfig.platform
      : platform;

    const baseProps = {
      className: cn(className),
      'data-testid': testId,
      ...restProps,
    } as unknown as TProps;

    return platformSelect({
      web: webRenderer(baseProps),
      ionic: ionicRenderer(baseProps),
      default: webRenderer(baseProps),
    });
  };

  if (displayName) {
    PlatformComponent.displayName = displayName;
  }

  return PlatformComponent;
}

/**
 * Higher-order component for adding platform awareness
 */
export function withPlatform<TProps extends BaseComponentProps>(
  Component: React.ComponentType<TProps>
) {
  const WithPlatformComponent = React.forwardRef<any, TProps>((props, ref) => {
    const platformConfig = getPlatformConfig();
    
    return (
      <Component
        {...(props as any)}
        ref={ref}
        data-platform={platformConfig.platform}
        data-is-mobile={platformConfig.isMobile}
      />
    );
  });

  WithPlatformComponent.displayName = `withPlatform(${Component.displayName || Component.name})`;
  
  return WithPlatformComponent;
}

/**
 * Hook for creating platform-adaptive class names
 */
export function usePlatformClasses(baseClasses: string = ''): {
  webClasses: string;
  ionicClasses: string;
  platformClasses: string;
} {
  const platformConfig = getPlatformConfig();
  
  const webClasses = cn(baseClasses, 'platform-web');
  const ionicClasses = cn(baseClasses, 'platform-ionic');
  
  const platformClasses = platformConfig.isIonic ? ionicClasses : webClasses;
  
  return {
    webClasses,
    ionicClasses,
    platformClasses,
  };
}

/**
 * Component wrapper that applies platform-specific styling
 */
export interface PlatformWrapperProps extends BaseComponentProps {
  webClassName?: string;
  ionicClassName?: string;
  as?: keyof JSX.IntrinsicElements;
}

export const PlatformWrapper: React.FC<PlatformWrapperProps> = ({
  children,
  className,
  webClassName,
  ionicClassName,
  as: Component = 'div',
  testId,
  platform = 'auto',
  ...props
}) => {
  const platformConfig = getPlatformConfig();
  const effectivePlatform = platform === 'auto' 
    ? platformConfig.platform
    : platform;

  const platformSpecificClass = effectivePlatform === 'ionic' 
    ? ionicClassName 
    : webClassName;

  return (
    <Component
      className={cn(className, platformSpecificClass)}
      data-testid={testId}
      data-platform={effectivePlatform}
      {...props}
    >
      {children}
    </Component>
  );
};

PlatformWrapper.displayName = 'PlatformWrapper';

/**
 * Utility for creating conditional platform props
 */
export function createPlatformProps<TWebProps, TIonicProps>(
  webProps: TWebProps,
  ionicProps: TIonicProps
): TWebProps | TIonicProps {
  const result = platformSelect<TWebProps | TIonicProps>({
    web: webProps,
    ionic: ionicProps,
    default: webProps,
  });
  return result;
}

/**
 * Component that only renders on specific platforms
 */
export interface PlatformOnlyProps extends BaseComponentProps {
  platform: 'web' | 'ionic';
}

export const PlatformOnly: React.FC<PlatformOnlyProps> = ({
  children,
  platform: targetPlatform,
}) => {
  const platformConfig = getPlatformConfig();
  
  if (platformConfig.platform !== targetPlatform) {
    return null;
  }
  
  return <>{children}</>;
};

PlatformOnly.displayName = 'PlatformOnly';

/**
 * Type utilities for component development
 */
export type PlatformComponentProps<TProps> = TProps & {
  platform?: 'web' | 'ionic' | 'auto';
};

export type WebOnlyProps<TProps> = TProps & {
  platform: 'web';
};

export type IonicOnlyProps<TProps> = TProps & {
  platform: 'ionic';
};