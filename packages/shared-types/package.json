{"name": "@foodprepai/shared-types", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "devDependencies": {"@foodprepai/tsconfig": "*", "typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./api": {"types": "./dist/api.d.ts", "default": "./dist/api.js"}, "./auth": {"types": "./dist/auth.d.ts", "default": "./dist/auth.js"}, "./inventory": {"types": "./dist/inventory.d.ts", "default": "./dist/inventory.js"}, "./orders": {"types": "./dist/orders.d.ts", "default": "./dist/orders.js"}, "./user": {"types": "./dist/user.d.ts", "default": "./dist/user.js"}}}