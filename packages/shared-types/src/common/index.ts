/**
 * Common utility types and base interfaces used across the FoodPrepAI ecosystem
 */

// MongoDB ObjectId type
export type ObjectId = string;

// Base document interface for all MongoDB documents
export interface BaseDocument {
  _id: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Utility type to make certain fields optional
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Utility type to make certain fields required
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Extract non-function properties from a type
export type NonFunctionPropertyNames<T> = {
  [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];

export type NonFunctionProperties<T> = Pick<T, NonFunctionPropertyNames<T>>;

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// API Response wrapper types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  path?: string[];
}

// Status types used across the application
export type SyncStatus = 'NOT_SYNCED' | 'PENDING_SYNC' | 'SYNCED' | 'SYNC_FAILED';
export type EntityStatus = 'ACTIVE' | 'INACTIVE' | 'DELETED' | 'ARCHIVED';

// Location types
export type LocationType = 'CENTRAL_KITCHEN' | 'RETAIL_SHOP' | 'SINGLE_LOCATION';

// User types
export type UserType = 'superuser' | 'company_user';
export type SystemRole = 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper';

// Order status types
export type OrderStatus = 
  | 'DRAFT' 
  | 'INCOMING' 
  | 'CONFIRMED' 
  | 'NOT_DELIVERED' 
  | 'PARTIALLY_DELIVERED' 
  | 'DELIVERED' 
  | 'CANCELLED' 
  | 'APPROVED' 
  | 'DELETED';

// Entity types for buyers/sellers
export type EntityType = 'CUSTOMER' | 'SUPPLIER' | 'BRANCH' | 'CENTRAL_KITCHEN';

// Item types
export type ItemType = 'INGREDIENT' | 'RECIPE';

// Source types for ordering/selling
export type SourceType = 'CENTRAL_KITCHEN' | 'EXTERNAL_SUPPLIER' | 'BOTH';

// Markup types
export type MarkupType = 'AT_COST' | 'MARKUP';

// Tax categories
export type TaxCategory = 'STANDARD' | 'REDUCED' | 'ZERO' | 'EXEMPT';

// Visibility types for selling options
export type VisibilityType = 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';

// Role types
export type RoleType = 'hq' | 'branch';

// Validation helper types
export type ValidationResult<T> = {
  isValid: boolean;
  data?: T;
  errors?: string[];
};

// Deep partial type for nested objects
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends (infer U)[]
    ? DeepPartial<U>[]
    : T[P] extends readonly (infer U)[]
    ? readonly DeepPartial<U>[]
    : T[P] extends object
    ? DeepPartial<T[P]>
    : T[P];
};

// Branded types for type safety
export type Brand<K, T> = K & { __brand: T };
export type CompanyId = Brand<ObjectId, 'CompanyId'>;
export type UserId = Brand<ObjectId, 'UserId'>;
export type LocationId = Brand<ObjectId, 'LocationId'>;
export type OrderId = Brand<ObjectId, 'OrderId'>;
export type IngredientId = Brand<ObjectId, 'IngredientId'>;
export type RecipeId = Brand<ObjectId, 'RecipeId'>;
export type SupplierId = Brand<ObjectId, 'SupplierId'>;
export type UOMId = Brand<ObjectId, 'UOMId'>;
export type RoleId = Brand<ObjectId, 'RoleId'>;

// Permission system types
export interface Permission {
  name: string;
  description: string;
  category: string;
}

// Helper functions for branded types
export const createCompanyId = (id: string): CompanyId => id as CompanyId;
export const createUserId = (id: string): UserId => id as UserId;
export const createLocationId = (id: string): LocationId => id as LocationId;
export const createOrderId = (id: string): OrderId => id as OrderId;
export const createIngredientId = (id: string): IngredientId => id as IngredientId;
export const createRecipeId = (id: string): RecipeId => id as RecipeId;
export const createSupplierId = (id: string): SupplierId => id as SupplierId;
export const createUOMId = (id: string): UOMId => id as UOMId;
export const createRoleId = (id: string): RoleId => id as RoleId;