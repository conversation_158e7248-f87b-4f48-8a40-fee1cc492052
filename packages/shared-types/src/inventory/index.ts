/**
 * Inventory-related type definitions for FoodPrepAI ecosystem
 */

import { z } from 'zod';
import { 
  BaseDocument, 
  ObjectId, 
  SyncStatus, 
  SourceType, 
  MarkupType, 
  TaxCategory, 
  VisibilityType,
  CompanyId,
  LocationId,
  IngredientId,
  SupplierId,
  UOMId,
  UserId
} from '../common';

// Unit of Measure (UOM) interface
export interface UOM extends BaseDocument {
  _id: UOMId;
  name: string;
  shortCode: string;
  category: string;
  baseUnit?: UOMId; // Reference to base unit if this is a conversion unit
  conversionFactor?: number; // Factor to convert to base unit
  companyId?: CompanyId; // Optional for custom UOMs
  isStandard: boolean; // Whether this is a standard system UOM
}

// Unit schema for pricing and ordering
export interface Unit {
  unitOfMeasure: UOMId;
  quantityInBaseUom: number;
  price: number;
  pricePerBaseUom: number;
}

// Supplier details for ingredients
export interface SupplierDetail {
  supplierId: SupplierId;
  unitsOfOrdering: Unit[];
  leadTimeDays?: number;
  minimumOrderQuantity?: number;
  preferredSupplier?: boolean;
}

// Selling option details for ingredients
export interface SellingOption {
  unitOfSelling: UOMId;
  priceWithoutTax: number;
  priceWithTax: number;
  taxRate: number;
  taxCategory: TaxCategory;
  conversionFactor: number;
  visibility: SellingVisibility;
  sourceType: SourceType;
  markupType?: MarkupType;
  markupPercentage?: number;
  isActive?: boolean;
}

// Visibility settings for selling options
export interface SellingVisibility {
  type: VisibilityType;
  locations: LocationId[];
  externalAccess: boolean;
}

// Core Ingredient interface
export interface Ingredient extends BaseDocument {
  _id: IngredientId;
  name: string;
  description: string;
  reorderPoint: number | null;
  baseUomId: UOMId;
  category: string;
  SKU: string;
  companyId: CompanyId;
  defaultSupplierId: SupplierId | null;
  currentStock: number;
  pendingStock: number;
  supplierDetails: SupplierDetail[];
  canBeSold: boolean;
  sellingDetails: SellingOption[];
  isActive: boolean;
  // Additional metadata
  tags?: string[];
  allergens?: string[];
  nutritionalInfo?: NutritionalInfo;
  storageInstructions?: string;
  shelfLifeDays?: number;
}

// Nutritional information for ingredients
export interface NutritionalInfo {
  caloriesPerBaseUnit: number;
  proteinGrams: number;
  fatGrams: number;
  carbohydrateGrams: number;
  fiberGrams?: number;
  sugarGrams?: number;
  sodiumMg?: number;
}

// Inventory transaction for tracking stock movements
export interface InventoryTransaction extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  locationId: LocationId;
  ingredientId: IngredientId;
  transactionType: InventoryTransactionType;
  quantity: number;
  unitPrice?: number;
  totalCost?: number;
  uomId: UOMId;
  referenceType?: InventoryReferenceType;
  referenceId?: ObjectId;
  notes?: string;
  performedBy: UserId;
  batchNumber?: string;
  expiryDate?: Date;
  // Sync fields
  syncStatus?: SyncStatus;
  lastSyncTimestamp?: Date;
}

// Types of inventory transactions
export type InventoryTransactionType = 
  | 'STOCK_IN' 
  | 'STOCK_OUT' 
  | 'ADJUSTMENT' 
  | 'TRANSFER_OUT' 
  | 'TRANSFER_IN' 
  | 'WASTAGE' 
  | 'SALES' 
  | 'RETURN' 
  | 'COUNT_ADJUSTMENT';

// Reference types for inventory transactions
export type InventoryReferenceType = 
  | 'ORDER' 
  | 'TRANSFER' 
  | 'SALE' 
  | 'COUNT' 
  | 'ADJUSTMENT' 
  | 'WASTAGE';

// Branch inventory for location-specific stock
export interface BranchInventory extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  locationId: LocationId;
  ingredientId: IngredientId;
  currentStock: number;
  pendingStock: number;
  reservedStock: number;
  reorderPoint?: number;
  maxStock?: number;
  lastCountDate?: Date;
  lastMovementDate?: Date;
  averageCost: number;
  // Sync fields
  syncStatus?: SyncStatus;
  lastSyncTimestamp?: Date;
}

// Stock count interface for inventory counting
export interface StockCount extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  locationId: LocationId;
  countNumber: string;
  status: StockCountStatus;
  countType: StockCountType;
  countDate: Date;
  performedBy: UserId;
  approvedBy?: UserId;
  approvedAt?: Date;
  items: StockCountItem[];
  notes?: string;
  discrepancies?: StockCountDiscrepancy[];
}

// Stock count status
export type StockCountStatus = 'DRAFT' | 'IN_PROGRESS' | 'COMPLETED' | 'APPROVED' | 'CANCELLED';

// Stock count type
export type StockCountType = 'FULL' | 'PARTIAL' | 'CYCLE' | 'SPOT';

// Individual item in stock count
export interface StockCountItem {
  ingredientId: IngredientId;
  expectedQuantity: number;
  countedQuantity: number;
  uomId: UOMId;
  variance: number;
  variancePercentage: number;
  notes?: string;
  countedBy?: UserId;
  countedAt?: Date;
}

// Stock count discrepancy
export interface StockCountDiscrepancy {
  ingredientId: IngredientId;
  expectedQuantity: number;
  countedQuantity: number;
  variance: number;
  variancePercentage: number;
  reason?: string;
  action: 'ACCEPT' | 'REJECT' | 'RECOUNT';
  actionBy?: UserId;
  actionAt?: Date;
}

// Transfer request between locations
export interface TransferRequest extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  transferNumber: string;
  fromLocationId: LocationId;
  toLocationId: LocationId;
  status: TransferStatus;
  requestedBy: UserId;
  approvedBy?: UserId;
  approvedAt?: Date;
  items: TransferItem[];
  notes?: string;
  expectedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  // Costing fields
  totalCost: number;
  markupPercentage?: number;
  markupAmount?: number;
}

// Transfer status
export type TransferStatus = 
  | 'DRAFT' 
  | 'REQUESTED' 
  | 'APPROVED' 
  | 'IN_TRANSIT' 
  | 'DELIVERED' 
  | 'PARTIALLY_DELIVERED' 
  | 'CANCELLED';

// Individual item in transfer
export interface TransferItem {
  ingredientId: IngredientId;
  requestedQuantity: number;
  approvedQuantity?: number;
  deliveredQuantity?: number;
  uomId: UOMId;
  unitCost: number;
  totalCost: number;
  markupPercentage?: number;
  markupAmount?: number;
  finalPrice: number;
  notes?: string;
}

// Markup rule for transfers
export interface MarkupRule extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  name: string;
  description?: string;
  fromLocationId?: LocationId; // null means applies to all source locations
  toLocationId?: LocationId; // null means applies to all destination locations  
  ingredientCategories?: string[]; // null means applies to all categories
  markupType: MarkupType;
  markupPercentage: number;
  minimumMarkup?: number;
  maximumMarkup?: number;
  isActive: boolean;
  priority: number; // Higher number = higher priority
}

// Input types for creating/updating ingredients
export interface CreateIngredientInput {
  name: string;
  description?: string;
  category: string;
  SKU: string;
  baseUomId: UOMId;
  reorderPoint?: number;
  defaultSupplierId?: SupplierId;
  supplierDetails?: SupplierDetail[];
  canBeSold?: boolean;
  sellingDetails?: SellingOption[];
  tags?: string[];
  allergens?: string[];
  nutritionalInfo?: NutritionalInfo;
  storageInstructions?: string;
  shelfLifeDays?: number;
}

export interface UpdateIngredientInput extends Partial<CreateIngredientInput> {
  isActive?: boolean;
}

// Ingredient with populated references
export interface IngredientWithRelations extends Ingredient {
  baseUom?: UOM;
  defaultSupplier?: {
    _id: SupplierId;
    name: string;
  };
  branchInventories?: BranchInventory[];
  totalStock?: number;
  totalValue?: number;
}

// Zod schemas for validation
export const CreateIngredientSchema = z.object({
  name: z.string().min(1, 'Ingredient name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  SKU: z.string().min(1, 'SKU is required'),
  baseUomId: z.string(),
  reorderPoint: z.number().min(0).optional(),
  defaultSupplierId: z.string().optional(),
  canBeSold: z.boolean().optional().default(false),
  tags: z.array(z.string()).optional(),
  allergens: z.array(z.string()).optional(),
  storageInstructions: z.string().optional(),
  shelfLifeDays: z.number().min(1).optional()
});

export const CreateTransferRequestSchema = z.object({
  fromLocationId: z.string(),
  toLocationId: z.string(),
  items: z.array(z.object({
    ingredientId: z.string(),
    requestedQuantity: z.number().min(0.001),
    uomId: z.string(),
    unitCost: z.number().min(0),
    notes: z.string().optional()
  })).min(1, 'At least one item is required'),
  notes: z.string().optional(),
  expectedDeliveryDate: z.date().optional()
});

export const StockCountItemSchema = z.object({
  ingredientId: z.string(),
  expectedQuantity: z.number().min(0),
  countedQuantity: z.number().min(0),
  uomId: z.string(),
  notes: z.string().optional()
});

export const CreateStockCountSchema = z.object({
  locationId: z.string(),
  countType: z.enum(['FULL', 'PARTIAL', 'CYCLE', 'SPOT']),
  items: z.array(StockCountItemSchema).min(1, 'At least one item is required'),
  notes: z.string().optional()
});

// Helper functions
export const calculateStockValue = (quantity: number, averageCost: number): number => {
  return Math.round((quantity * averageCost) * 100) / 100;
};

export const calculateVariance = (expected: number, counted: number): number => {
  return counted - expected;
};

export const calculateVariancePercentage = (expected: number, counted: number): number => {
  if (expected === 0) return counted > 0 ? 100 : 0;
  return Math.round(((counted - expected) / expected) * 10000) / 100;
};

export const isLowStock = (currentStock: number, reorderPoint: number | null): boolean => {
  return reorderPoint !== null && currentStock <= reorderPoint;
};

export const convertQuantity = (
  quantity: number, 
  fromUom: UOM, 
  toUom: UOM,
  baseUom: UOM
): number => {
  // Convert to base unit first, then to target unit
  const quantityInBase = fromUom.baseUnit 
    ? quantity * (fromUom.conversionFactor || 1)
    : quantity;
  
  if (toUom._id === baseUom._id) {
    return quantityInBase;
  }
  
  return toUom.conversionFactor 
    ? quantityInBase / toUom.conversionFactor
    : quantityInBase;
};

export const calculateSellingPrice = (
  cost: number,
  markupType: MarkupType,
  markupPercentage: number,
  taxRate: number
): { priceWithoutTax: number; priceWithTax: number } => {
  const priceWithoutTax = markupType === 'MARKUP' 
    ? cost * (1 + markupPercentage / 100)
    : cost;
  
  const priceWithTax = priceWithoutTax * (1 + taxRate / 100);
  
  return {
    priceWithoutTax: Math.round(priceWithoutTax * 100) / 100,
    priceWithTax: Math.round(priceWithTax * 100) / 100
  };
};