/**
 * Company-related type definitions for FoodPrepAI ecosystem
 */

import { z } from 'zod';
import { 
  BaseDocument, 
  ObjectId, 
  EntityStatus, 
  LocationType,
  CompanyId,
  UserId,
  LocationId
} from '../common';

// Core Company interface
export interface Company extends BaseDocument {
  _id: CompanyId;
  name: string;
  subdomain: string;
  ownerId: UserId;
  companyCode?: string;
  posPassword?: string;
  weekStartDay?: number; // 0-6, where 0 is Sunday
  status?: EntityStatus;
  subscription?: CompanySubscription;
}

// Company subscription details
export interface CompanySubscription {
  plan: string;
  startDate: Date;
  expiresAt: Date;
  isActive: boolean;
  features: string[];
  limits: SubscriptionLimits;
}

// Subscription limits
export interface SubscriptionLimits {
  maxUsers: number;
  maxLocations: number;
  maxOrders: number;
  maxInventoryItems: number;
  storageLimit: number; // in GB
}

// Location interface
export interface Location extends BaseDocument {
  _id: LocationId;
  companyId: CompanyId;
  name: string;
  locationType: LocationType;
  canSellToExternal: boolean;
  canDoTransfers: boolean;
  canBuyfromExternalSuppliers: boolean;
  address?: string;
  contactInfo?: LocationContact;
  isActive?: boolean;
  settings?: LocationSettings;
}

// Location contact information
export interface LocationContact {
  phone?: string;
  email?: string;
  managerName?: string;
  managerEmail?: string;
}

// Location-specific settings
export interface LocationSettings {
  taxRate?: number;
  currency?: string;
  timezone?: string;
  operatingHours?: OperatingHours[];
  inventorySettings?: LocationInventorySettings;
  posSettings?: LocationPOSSettings;
}

// Operating hours for locations
export interface OperatingHours {
  dayOfWeek: number; // 0-6, where 0 is Sunday
  openTime: string; // HH:MM format
  closeTime: string; // HH:MM format
  isClosed: boolean;
}

// Location inventory settings
export interface LocationInventorySettings {
  autoReorder: boolean;
  reorderThreshold: number;
  defaultMarkup: number;
  allowNegativeStock: boolean;
}

// Location POS settings
export interface LocationPOSSettings {
  enablePOS: boolean;
  defaultTaxRate: number;
  enableDiscounts: boolean;
  maxDiscountPercent: number;
  requireManagerApproval: boolean;
  receiptSettings: ReceiptSettings;
}

// Receipt settings for POS
export interface ReceiptSettings {
  companyName: string;
  address: string;
  phone: string;
  email: string;
  footerMessage: string;
  showTaxBreakdown: boolean;
}

// Company with populated relations
export interface CompanyWithRelations extends Company {
  owner?: {
    _id: UserId;
    name?: string;
    email: string;
  };
  locations?: Location[];
  userCount?: number;
  orderCount?: number;
}

// Company creation input
export interface CreateCompanyInput {
  name: string;
  subdomain: string;
  ownerId: UserId;
  companyCode?: string;
  posPassword?: string;
  weekStartDay?: number;
}

// Company update input
export interface UpdateCompanyInput {
  name?: string;
  subdomain?: string;
  companyCode?: string;
  posPassword?: string;
  weekStartDay?: number;
  status?: EntityStatus;
}

// Location creation input
export interface CreateLocationInput {
  companyId: CompanyId;
  name: string;
  locationType: LocationType;
  canSellToExternal: boolean;
  canDoTransfers: boolean;
  canBuyfromExternalSuppliers: boolean;
  address?: string;
  contactInfo?: LocationContact;
  settings?: LocationSettings;
}

// Location update input
export interface UpdateLocationInput {
  name?: string;
  locationType?: LocationType;
  canSellToExternal?: boolean;
  canDoTransfers?: boolean;
  canBuyfromExternalSuppliers?: boolean;
  address?: string;
  contactInfo?: LocationContact;
  settings?: LocationSettings;
  isActive?: boolean;
}

// Company settings that can be modified
export interface CompanySettings {
  general: {
    name: string;
    weekStartDay: number;
    timezone: string;
    currency: string;
  };
  inventory: {
    defaultReorderThreshold: number;
    allowNegativeStock: boolean;
    requireApprovalForAdjustments: boolean;
  };
  orders: {
    requireApprovalThreshold: number;
    autoApproveInternalOrders: boolean;
    defaultOrderValidityDays: number;
  };
  pos: {
    enablePOS: boolean;
    defaultTaxRate: number;
    enableDiscounts: boolean;
    maxDiscountPercent: number;
  };
  notifications: {
    lowStockAlerts: boolean;
    orderStatusUpdates: boolean;
    userActivity: boolean;
    emailNotifications: boolean;
  };
}

// Role management within company
export interface Role extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  name: string;
  description: string;
  type: 'hq' | 'branch';
  isSystemRole: boolean;
  permissions: string[];
}

// Company statistics and metrics
export interface CompanyMetrics {
  totalUsers: number;
  activeUsers: number;
  totalLocations: number;
  totalOrders: number;
  totalRevenue: number;
  inventoryValue: number;
  lowStockItems: number;
  pendingOrders: number;
  monthlyGrowth: {
    users: number;
    orders: number;
    revenue: number;
  };
}

// Zod schemas for validation
export const CreateCompanySchema = z.object({
  name: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
  subdomain: z.string()
    .min(3, 'Subdomain must be at least 3 characters')
    .max(50, 'Subdomain too long')
    .regex(/^[a-z0-9-]+$/, 'Subdomain can only contain lowercase letters, numbers, and hyphens'),
  ownerId: z.string(),
  companyCode: z.string().optional(),
  posPassword: z.string().optional(),
  weekStartDay: z.number().min(0).max(6).optional().default(1)
});

export const UpdateCompanySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  subdomain: z.string()
    .min(3)
    .max(50)
    .regex(/^[a-z0-9-]+$/)
    .optional(),
  companyCode: z.string().optional(),
  posPassword: z.string().optional(),
  weekStartDay: z.number().min(0).max(6).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DELETED', 'ARCHIVED']).optional()
});

export const CreateLocationSchema = z.object({
  companyId: z.string(),
  name: z.string().min(1, 'Location name is required').max(100, 'Location name too long'),
  locationType: z.enum(['CENTRAL_KITCHEN', 'RETAIL_SHOP', 'SINGLE_LOCATION']),
  canSellToExternal: z.boolean(),
  canDoTransfers: z.boolean(),
  canBuyfromExternalSuppliers: z.boolean(),
  address: z.string().optional(),
  contactInfo: z.object({
    phone: z.string().optional(),
    email: z.string().email().optional(),
    managerName: z.string().optional(),
    managerEmail: z.string().email().optional()
  }).optional()
});

export const UpdateLocationSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  locationType: z.enum(['CENTRAL_KITCHEN', 'RETAIL_SHOP', 'SINGLE_LOCATION']).optional(),
  canSellToExternal: z.boolean().optional(),
  canDoTransfers: z.boolean().optional(),
  canBuyfromExternalSuppliers: z.boolean().optional(),
  address: z.string().optional(),
  contactInfo: z.object({
    phone: z.string().optional(),
    email: z.string().email().optional(),
    managerName: z.string().optional(),
    managerEmail: z.string().email().optional()
  }).optional(),
  isActive: z.boolean().optional()
});

// Helper functions
export const isLocationActive = (location: Location): boolean => {
  return location.isActive !== false; // Default to true if not specified
};

export const canLocationSellExternally = (location: Location): boolean => {
  return location.canSellToExternal && isLocationActive(location);
};

export const canLocationDoTransfers = (location: Location): boolean => {
  return location.canDoTransfers && isLocationActive(location);
};

export const getLocationsByType = (locations: Location[], type: LocationType): Location[] => {
  return locations.filter(location => location.locationType === type && isLocationActive(location));
};

export const getCentralKitchens = (locations: Location[]): Location[] => {
  return getLocationsByType(locations, 'CENTRAL_KITCHEN');
};

export const getRetailShops = (locations: Location[]): Location[] => {
  return getLocationsByType(locations, 'RETAIL_SHOP');
};