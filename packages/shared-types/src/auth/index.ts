/**
 * Authentication and authorization type definitions for FoodPrepAI ecosystem
 */

import { z } from 'zod';
import { 
  ObjectId, 
  UserType, 
  SystemRole,
  CompanyId,
  LocationId,
  UserId,
  RoleId,
  Permission
} from '../common';

// Core authentication session data
export interface AuthSession {
  user: AuthUser;
  token: string;
  refreshToken?: string;
  expiresAt: Date;
  issuedAt: Date;
  company?: AuthCompany;
  permissions: string[];
  locationAccess: LocationId[];
}

// Authenticated user data
export interface AuthUser {
  _id: UserId;
  email: string;
  name?: string;
  displayName?: string;
  userType: UserType;
  role?: SystemRole | ObjectId;
  companyId?: CompanyId;
  locationIds: LocationId[];
  primaryLocationId?: LocationId;
  canUseIonicApp: boolean;
  posAccess: boolean;
  isActive: boolean;
}

// Company data for authenticated sessions
export interface AuthCompany {
  _id: CompanyId;
  name: string;
  subdomain: string;
  companyCode?: string;
  settings?: {
    timezone: string;
    currency: string;
    weekStartDay: number;
  };
}

// JWT token payload structure
export interface JWTPayload {
  // Standard JWT claims
  iss?: string; // Issuer
  sub: UserId; // Subject (User ID)
  aud?: string; // Audience
  exp: number; // Expiration time
  iat: number; // Issued at
  jti?: string; // JWT ID
  
  // Custom claims
  userId: UserId;
  email: string;
  userType: UserType;
  role?: SystemRole | ObjectId;
  companyId?: CompanyId;
  locationId?: LocationId; // Primary location
  locationIds?: LocationId[]; // All accessible locations
  permissions?: string[];
  
  // POS-specific claims
  posAccess?: boolean;
  pin?: string; // Hashed PIN for POS authentication
  
  // Session metadata
  sessionId?: string;
  deviceId?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Refresh token data
export interface RefreshToken {
  _id: ObjectId;
  userId: UserId;
  token: string;
  expiresAt: Date;
  isRevoked: boolean;
  deviceId?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  lastUsedAt: Date;
}

// Authentication context for API requests
export interface AuthContext {
  isAuthenticated: boolean;
  user?: AuthUser;
  company?: AuthCompany;
  permissions: string[];
  locationAccess: LocationId[];
  token?: string;
  sessionId?: string;
}

// Permission-based authorization
// Permission interface moved to common/index.ts

export type PermissionCategory = 
  | 'system' 
  | 'company' 
  | 'inventory' 
  | 'orders' 
  | 'users' 
  | 'reports' 
  | 'pos' 
  | 'settings';

export type PermissionAction = 
  | 'create' 
  | 'read' 
  | 'update' 
  | 'delete' 
  | 'approve' 
  | 'export' 
  | 'import' 
  | 'sync';

export type PermissionScope = 
  | 'global' 
  | 'company' 
  | 'location' 
  | 'own';

// Role-based access control
export interface Role {
  _id: RoleId;
  name: string;
  description: string;
  type: 'system' | 'custom';
  companyId?: CompanyId;
  permissions: string[];
  locationRestrictions?: LocationId[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Authentication request types
export interface LoginRequest {
  email: string;
  password: string;
  companyCode?: string;
  deviceId?: string;
  rememberMe?: boolean;
}

export interface SignupRequest {
  email: string;
  password: string;
  name?: string;
  companyName: string;
  subdomain: string;
  deviceId?: string;
}

export interface PINLoginRequest {
  pin: string;
  companyId: CompanyId;
  locationId?: LocationId;
  deviceId?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
  deviceId?: string;
}

export interface LogoutRequest {
  token?: string;
  refreshToken?: string;
  allDevices?: boolean;
}

// Authentication response types
export interface LoginResponse {
  success: boolean;
  session: AuthSession;
  requiresSetup?: boolean;
  twoFactorRequired?: boolean;
  message?: string;
}

export interface RefreshResponse {
  success: boolean;
  token: string;
  refreshToken?: string;
  expiresAt: Date;
}

export interface LogoutResponse {
  success: boolean;
  message?: string;
}

// Two-factor authentication types
export interface TwoFactorSetup {
  userId: UserId;
  secret: string;
  qrCode: string;
  backupCodes: string[];
  isEnabled: boolean;
}

export interface TwoFactorVerifyRequest {
  userId: UserId;
  code: string;
  backupCode?: string;
}

// Password reset types
export interface PasswordResetRequest {
  email: string;
  companyCode?: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// API key authentication for external systems
export interface APIKey {
  _id: ObjectId;
  name: string;
  key: string;
  secret: string;
  companyId: CompanyId;
  permissions: string[];
  locationRestrictions?: LocationId[];
  isActive: boolean;
  expiresAt?: Date;
  lastUsedAt?: Date;
  usageCount: number;
  rateLimitPerHour: number;
  createdBy: UserId;
  createdAt: Date;
}

// Session management
export interface UserSession {
  _id: ObjectId;
  userId: UserId;
  sessionId: string;
  deviceId?: string;
  deviceName?: string;
  ipAddress: string;
  userAgent: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  isActive: boolean;
  lastActivityAt: Date;
  createdAt: Date;
  expiresAt: Date;
}

// Authentication middleware context
export interface AuthMiddlewareContext {
  user: AuthUser;
  company: AuthCompany;
  permissions: string[];
  locationAccess: LocationId[];
  session: UserSession;
  apiKey?: APIKey;
}

// Authorization result
export interface AuthorizationResult {
  authorized: boolean;
  reason?: string;
  requiredPermissions?: string[];
  missingPermissions?: string[];
}

// Zod schemas for validation
export const LoginRequestSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  companyCode: z.string().optional(),
  deviceId: z.string().optional(),
  rememberMe: z.boolean().optional().default(false)
});

export const SignupRequestSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  name: z.string().min(1, 'Name is required').optional(),
  companyName: z.string().min(1, 'Company name is required'),
  subdomain: z.string()
    .min(3, 'Subdomain must be at least 3 characters')
    .max(50, 'Subdomain must be less than 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Subdomain can only contain lowercase letters, numbers, and hyphens'),
  deviceId: z.string().optional()
});

export const PINLoginRequestSchema = z.object({
  pin: z.string()
    .length(4, 'PIN must be exactly 4 digits')
    .regex(/^\d{4}$/, 'PIN must contain only digits'),
  companyId: z.string(),
  locationId: z.string().optional(),
  deviceId: z.string().optional()
});

export const ChangePasswordRequestSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// Helper functions for authentication
export const authHasPermission = (context: AuthContext, permission: string): boolean => {
  return context.permissions.includes(permission);
};

export const authHasAnyPermission = (context: AuthContext, permissions: string[]): boolean => {
  return permissions.some(permission => context.permissions.includes(permission));
};

export const authHasAllPermissions = (context: AuthContext, permissions: string[]): boolean => {
  return permissions.every(permission => context.permissions.includes(permission));
};

export const authCanAccessLocation = (context: AuthContext, locationId: LocationId): boolean => {
  return context.locationAccess.includes(locationId);
};

export const isSystemUser = (user: AuthUser): boolean => {
  return user.userType === 'superuser';
};

export const isCompanyUser = (user: AuthUser): boolean => {
  return user.userType === 'company_user';
};

export const authIsSystemRole = (role: string): role is SystemRole => {
  return ['owner', 'admin', 'manager', 'user', 'storekeeper'].includes(role);
};

export const canManageUsers = (context: AuthContext): boolean => {
  return authHasAnyPermission(context, ['users:write', 'users:delete', 'system:admin']);
};

export const canManageCompany = (context: AuthContext): boolean => {
  return authHasAnyPermission(context, ['company:write', 'settings:company', 'system:admin']);
};

export const canAccessPOS = (context: AuthContext): boolean => {
  return context.user?.posAccess === true && authHasPermission(context, 'pos:access');
};

// Token utilities
export const isTokenExpired = (token: JWTPayload): boolean => {
  return Date.now() >= token.exp * 1000;
};

export const getTokenTimeToExpire = (token: JWTPayload): number => {
  return Math.max(0, token.exp * 1000 - Date.now());
};

export const shouldRefreshToken = (token: JWTPayload, threshold: number = 300000): boolean => {
  return getTokenTimeToExpire(token) < threshold; // 5 minutes default
};