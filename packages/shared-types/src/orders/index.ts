/**
 * Order-related type definitions for FoodPrepAI ecosystem
 */

import { z } from 'zod';
import { 
  BaseDocument, 
  ObjectId, 
  OrderStatus, 
  EntityType, 
  ItemType, 
  SyncStatus,
  CompanyId,
  LocationId,
  OrderId,
  IngredientId,
  RecipeId,
  UOMId,
  UserId
} from '../common';

// Entity information for buyers and sellers
export interface OrderEntity {
  entityType: EntityType;
  entityId: string;
  name?: string; // Populated name
  entityName?: string; // Alternative name field
}

// Individual item in an order
export interface OrderItem {
  itemType: ItemType;
  itemId: IngredientId | RecipeId;
  description: string;
  quantity: number;
  deliveredQuantity: number;
  uomId: UOMId;
  unitPrice: number;
  lineTotal: number;
  // Additional fields
  notes?: string;
  discountPercent?: number;
  discountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
}

// Core Order interface
export interface Order extends BaseDocument {
  _id: OrderId;
  companyId: CompanyId;
  orderNumber: string;
  originalOrderNumber?: string; // From external systems like POS
  status: OrderStatus;
  buyer: OrderEntity;
  seller?: OrderEntity;
  sellerLocationId: LocationId;
  buyerLocationId?: LocationId;
  items: OrderItem[];
  deliveryNoteIds: ObjectId[];
  
  // Financial information
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  
  // Dates
  orderDate: Date;
  requestedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  
  // Sync-related fields for external systems integration
  orderSource?: OrderSource;
  syncStatus?: SyncStatus;
  lastSyncId?: string;
  lastSyncTimestamp?: Date;
  branchId?: string; // External branch ID
  localId?: number; // Local ID in external system
  version?: number; // Version for conflict resolution
  
  // Audit fields
  createdBy?: UserId;
  modifiedBy?: UserId;
  
  // Additional metadata
  priority?: OrderPriority;
  tags?: string[];
  internalNotes?: string;
  customerNotes?: string;
}

// Order source systems
export type OrderSource = 'WEB' | 'IONIC' | 'POS' | 'API' | 'IMPORT';

// Order priority levels
export type OrderPriority = 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';

// Delivery note for tracking deliveries
export interface DeliveryNote extends BaseDocument {
  _id: ObjectId;
  companyId: CompanyId;
  deliveryNoteNumber: string;
  orderId?: OrderId;
  orderIds?: OrderId[]; // Support for multiple orders in one delivery
  fromLocationId: LocationId;
  toLocationId: LocationId;
  status: DeliveryNoteStatus;
  deliveryDate: Date;
  items: DeliveryNoteItem[];
  
  // Personnel information
  driverName?: string;
  driverPhone?: string;
  receivedBy?: string;
  receivedAt?: Date;
  
  // Additional information
  vehicleInfo?: string;
  notes?: string;
  specialInstructions?: string;
  
  // Audit fields
  createdBy: UserId;
  approvedBy?: UserId;
  approvedAt?: Date;
}

// Delivery note status
export type DeliveryNoteStatus = 
  | 'DRAFT' 
  | 'READY' 
  | 'IN_TRANSIT' 
  | 'DELIVERED' 
  | 'PARTIALLY_DELIVERED' 
  | 'CANCELLED';

// Individual item in delivery note
export interface DeliveryNoteItem {
  itemType: ItemType;
  itemId: IngredientId | RecipeId;
  description: string;
  orderedQuantity: number;
  deliveredQuantity: number;
  uomId: UOMId;
  unitPrice: number;
  lineTotal: number;
  notes?: string;
  // Batch/lot tracking
  batchNumber?: string;
  expiryDate?: Date;
  // Quality information
  qualityGrade?: string;
  qualityNotes?: string;
}

// Order with populated relations
export interface OrderWithRelations extends Order {
  sellerLocation?: {
    _id: LocationId;
    name: string;
    locationType: string;
  };
  buyerLocation?: {
    _id: LocationId;
    name: string;
    locationType: string;
  };
  deliveryNotes?: DeliveryNote[];
  creator?: {
    _id: UserId;
    name?: string;
    email: string;
  };
  // Populated items with full details
  populatedItems?: OrderItemWithDetails[];
}

// Order item with populated details
export interface OrderItemWithDetails extends OrderItem {
  item?: {
    _id: IngredientId | RecipeId;
    name: string;
    description?: string;
    category?: string;
    SKU?: string;
  };
  uom?: {
    _id: UOMId;
    name: string;
    shortCode: string;
  };
}

// Order summary for lists and reports
export interface OrderSummary {
  _id: OrderId;
  orderNumber: string;
  status: OrderStatus;
  orderDate: Date;
  requestedDeliveryDate?: Date;
  totalAmount: number;
  itemCount: number;
  buyerName: string;
  sellerLocationName: string;
  priority?: OrderPriority;
}

// Order statistics and metrics
export interface OrderMetrics {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalValue: number;
  averageOrderValue: number;
  onTimeDeliveryRate: number;
  topItems: Array<{
    itemId: string;
    itemName: string;
    totalQuantity: number;
    totalValue: number;
  }>;
  dailyOrderCounts: Array<{
    date: Date;
    orderCount: number;
    totalValue: number;
  }>;
}

// Input types for creating/updating orders
export interface CreateOrderInput {
  orderNumber?: string; // Will be generated if not provided
  buyer: OrderEntity;
  seller?: OrderEntity;
  sellerLocationId: LocationId;
  buyerLocationId?: LocationId;
  items: CreateOrderItemInput[];
  requestedDeliveryDate?: Date;
  priority?: OrderPriority;
  customerNotes?: string;
  internalNotes?: string;
  tags?: string[];
}

export interface CreateOrderItemInput {
  itemType: ItemType;
  itemId: IngredientId | RecipeId;
  description: string;
  quantity: number;
  uomId: UOMId;
  unitPrice: number;
  notes?: string;
  discountPercent?: number;
  taxRate?: number;
}

export interface UpdateOrderInput {
  status?: OrderStatus;
  items?: UpdateOrderItemInput[];
  requestedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  priority?: OrderPriority;
  customerNotes?: string;
  internalNotes?: string;
  tags?: string[];
}

export interface UpdateOrderItemInput extends CreateOrderItemInput {
  deliveredQuantity?: number;
}

// Delivery note input types
export interface CreateDeliveryNoteInput {
  orderId?: OrderId;
  orderIds?: OrderId[];
  fromLocationId: LocationId;
  toLocationId: LocationId;
  deliveryDate: Date;
  items: CreateDeliveryNoteItemInput[];
  driverName?: string;
  driverPhone?: string;
  vehicleInfo?: string;
  notes?: string;
  specialInstructions?: string;
}

export interface CreateDeliveryNoteItemInput {
  itemType: ItemType;
  itemId: IngredientId | RecipeId;
  description: string;
  orderedQuantity: number;
  deliveredQuantity: number;
  uomId: UOMId;
  unitPrice: number;
  batchNumber?: string;
  expiryDate?: Date;
  qualityGrade?: string;
  qualityNotes?: string;
}

// Order sync data for external systems
export interface OrderSyncData {
  operation: 'CREATE' | 'UPDATE' | 'DELETE';
  order: Order | Partial<Order>;
  timestamp: Date;
  source: OrderSource;
  syncId?: string;
}

// Zod schemas for validation
export const CreateOrderItemSchema = z.object({
  itemType: z.enum(['INGREDIENT', 'RECIPE']),
  itemId: z.string(),
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(0.001, 'Quantity must be greater than 0'),
  uomId: z.string(),
  unitPrice: z.number().min(0, 'Unit price cannot be negative'),
  notes: z.string().optional(),
  discountPercent: z.number().min(0).max(100).optional(),
  taxRate: z.number().min(0).max(100).optional()
});

export const CreateOrderSchema = z.object({
  buyer: z.object({
    entityType: z.enum(['CUSTOMER', 'SUPPLIER', 'BRANCH', 'CENTRAL_KITCHEN']),
    entityId: z.string(),
    entityName: z.string().optional()
  }),
  seller: z.object({
    entityType: z.enum(['CUSTOMER', 'SUPPLIER', 'BRANCH', 'CENTRAL_KITCHEN']),
    entityId: z.string(),
    entityName: z.string().optional()
  }).optional(),
  sellerLocationId: z.string(),
  buyerLocationId: z.string().optional(),
  items: z.array(CreateOrderItemSchema).min(1, 'At least one item is required'),
  requestedDeliveryDate: z.date().optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  customerNotes: z.string().optional(),
  internalNotes: z.string().optional(),
  tags: z.array(z.string()).optional()
});

export const UpdateOrderSchema = z.object({
  status: z.enum([
    'DRAFT', 'INCOMING', 'CONFIRMED', 'NOT_DELIVERED', 
    'PARTIALLY_DELIVERED', 'DELIVERED', 'CANCELLED', 'APPROVED', 'DELETED'
  ]).optional(),
  requestedDeliveryDate: z.date().optional(),
  actualDeliveryDate: z.date().optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  customerNotes: z.string().optional(),
  internalNotes: z.string().optional(),
  tags: z.array(z.string()).optional()
});

export const CreateDeliveryNoteSchema = z.object({
  orderId: z.string().optional(),
  orderIds: z.array(z.string()).optional(),
  fromLocationId: z.string(),
  toLocationId: z.string(),
  deliveryDate: z.date(),
  items: z.array(z.object({
    itemType: z.enum(['INGREDIENT', 'RECIPE']),
    itemId: z.string(),
    description: z.string().min(1),
    orderedQuantity: z.number().min(0),
    deliveredQuantity: z.number().min(0),
    uomId: z.string(),
    unitPrice: z.number().min(0),
    batchNumber: z.string().optional(),
    expiryDate: z.date().optional(),
    qualityGrade: z.string().optional(),
    qualityNotes: z.string().optional()
  })).min(1, 'At least one item is required'),
  driverName: z.string().optional(),
  driverPhone: z.string().optional(),
  vehicleInfo: z.string().optional(),
  notes: z.string().optional(),
  specialInstructions: z.string().optional()
});

// Helper functions
export const calculateOrderTotals = (items: OrderItem[]): {
  subtotal: number;
  totalTax: number;
  totalDiscount: number;
  total: number;
} => {
  const subtotal = items.reduce((sum, item) => sum + item.lineTotal, 0);
  const totalTax = items.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
  const totalDiscount = items.reduce((sum, item) => sum + (item.discountAmount || 0), 0);
  const total = subtotal + totalTax - totalDiscount;
  
  return {
    subtotal: Math.round(subtotal * 100) / 100,
    totalTax: Math.round(totalTax * 100) / 100,
    totalDiscount: Math.round(totalDiscount * 100) / 100,
    total: Math.round(total * 100) / 100
  };
};

export const calculateItemLineTotal = (
  quantity: number,
  unitPrice: number,
  discountPercent: number = 0,
  taxRate: number = 0
): { lineTotal: number; discountAmount: number; taxAmount: number } => {
  const baseAmount = quantity * unitPrice;
  const discountAmount = baseAmount * (discountPercent / 100);
  const netAmount = baseAmount - discountAmount;
  const taxAmount = netAmount * (taxRate / 100);
  const lineTotal = netAmount + taxAmount;
  
  return {
    lineTotal: Math.round(lineTotal * 100) / 100,
    discountAmount: Math.round(discountAmount * 100) / 100,
    taxAmount: Math.round(taxAmount * 100) / 100
  };
};

export const isOrderComplete = (order: Order): boolean => {
  return order.items.every(item => item.deliveredQuantity >= item.quantity);
};

export const isOrderPartiallyDelivered = (order: Order): boolean => {
  return order.items.some(item => item.deliveredQuantity > 0) && 
         order.items.some(item => item.deliveredQuantity < item.quantity);
};

export const getOrderCompletionPercentage = (order: Order): number => {
  const totalOrdered = order.items.reduce((sum, item) => sum + item.quantity, 0);
  const totalDelivered = order.items.reduce((sum, item) => sum + item.deliveredQuantity, 0);
  
  if (totalOrdered === 0) return 0;
  return Math.round((totalDelivered / totalOrdered) * 100);
};

export const canCancelOrder = (order: Order): boolean => {
  return ['DRAFT', 'INCOMING', 'CONFIRMED'].includes(order.status);
};

export const canEditOrder = (order: Order): boolean => {
  return ['DRAFT', 'INCOMING'].includes(order.status);
};

export const generateOrderNumber = (prefix: string = 'ORD', timestamp: Date = new Date()): string => {
  const year = timestamp.getFullYear();
  const month = String(timestamp.getMonth() + 1).padStart(2, '0');
  const day = String(timestamp.getDate()).padStart(2, '0');
  const time = String(timestamp.getTime()).slice(-6); // Last 6 digits of timestamp
  
  return `${prefix}-${year}${month}${day}-${time}`;
};