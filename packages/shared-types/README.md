# @foodprepai/shared-types

Comprehensive TypeScript type definitions for the FoodPrepAI ecosystem. This package provides type safety and consistency across all applications in the FoodPrepAI monorepo.

## Installation

```bash
npm install @foodprepai/shared-types
```

## Usage

### Basic Types

```typescript
import {
  User,
  Company,
  Order,
  Ingredient,
  ApiResponse,
  CompanyId,
  UserId,
  LocationId
} from '@foodprepai/shared-types';

// Use branded types for better type safety
const companyId: CompanyId = createCompanyId('507f1f77bcf86cd799439011');
const userId: UserId = createUserId('507f1f77bcf86cd799439012');
```

### API Types

```typescript
import { AuthAPI, UserAPI, OrderAPI, ApiResponse } from '@foodprepai/shared-types';

// Authentication
const loginRequest: AuthAPI.LoginRequest = {
  email: '<EMAIL>',
  password: 'securepassword',
  companyCode: 'DEMO'
};

// API Responses
const userResponse: UserAPI.GetUserResponse = {
  success: true,
  data: user,
  timestamp: new Date().toISOString()
};
```

### Validation with Zod

```typescript
import { CreateUserSchema, LoginRequestSchema } from '@foodprepai/shared-types';

// Validate user creation data
const result = CreateUserSchema.safeParse(userData);
if (result.success) {
  const validUser = result.data;
  // Process valid user data
} else {
  console.error('Validation errors:', result.error.errors);
}
```

### Helper Functions

```typescript
import {
  hasPermission,
  canAccessLocation,
  calculateOrderTotals,
  isOrderComplete
} from '@foodprepai/shared-types';

// Check user permissions
if (hasPermission(authContext, 'inventory:write')) {
  // User can modify inventory
}

// Calculate order totals
const totals = calculateOrderTotals(order.items);
console.log(`Order total: ${totals.total}`);
```

## Package Structure

```
src/
├── common/          # Base types and utilities
├── user/           # User and authentication types
├── company/        # Company and location types
├── inventory/      # Inventory and UOM types
├── orders/         # Order and delivery types
├── api/            # API request/response types
├── auth/           # Authentication and authorization types
└── index.ts        # Main exports
```

## Core Type Categories

### Common Types
- `BaseDocument` - Base interface for all MongoDB documents
- `ObjectId` - MongoDB ObjectId type
- `ApiResponse<T>` - Standardized API response wrapper
- `PaginatedResponse<T>` - Paginated data responses
- Branded ID types for type safety

### User Types
- `User` - Core user interface
- `UserProfile` - Public user information
- `UserAuth` - Authentication data
- `POSSettings` - POS-specific user settings
- Permission and role management types

### Company Types
- `Company` - Company entity
- `Location` - Location/branch entity
- `CompanySettings` - Configuration options
- `Role` - Custom role definitions

### Inventory Types
- `Ingredient` - Inventory item definition
- `UOM` - Unit of measure
- `InventoryTransaction` - Stock movement tracking
- `StockCount` - Inventory counting
- `TransferRequest` - Inter-location transfers

### Order Types
- `Order` - Core order entity
- `OrderItem` - Individual order line items
- `DeliveryNote` - Delivery documentation
- Order status and workflow types

### API Types
- Request/response interfaces for all endpoints
- Error handling types
- Authentication API types
- Pagination and filtering types

### Authentication Types
- `AuthSession` - User session data
- `JWTPayload` - Token structure
- `Permission` - Permission definitions
- Two-factor authentication types

## Type Safety Features

### Branded Types
Prevent ID mixups with branded types:

```typescript
const companyId: CompanyId = createCompanyId('...');
const userId: UserId = createUserId('...');

// This would cause a TypeScript error:
// someFunction(userId) where parameter expects CompanyId
```

### Utility Types
Advanced TypeScript patterns for flexibility:

```typescript
// Make certain fields optional
type PartialUser = PartialBy<User, 'createdAt' | 'updatedAt'>;

// Make certain fields required
type RequiredUserData = RequiredBy<CreateUserInput, 'email' | 'password'>;

// Deep partial for nested updates
type DeepPartialOrder = DeepPartial<Order>;
```

### Runtime Validation
Zod schemas for runtime type checking:

```typescript
import { CreateOrderSchema } from '@foodprepai/shared-types';

const validateOrder = (data: unknown) => {
  const result = CreateOrderSchema.safeParse(data);
  return result.success ? result.data : null;
};
```

## Constants and Enums

```typescript
import { Constants } from '@foodprepai/shared-types';

// Use predefined constants
const permissions = Constants.STANDARD_PERMISSIONS;
const orderStatuses = Constants.ORDER_STATUSES;
const systemRoles = Constants.SYSTEM_ROLES;
```

## Advanced Patterns

### Conditional Types
```typescript
// User type based on user type
type UserWithCompany<T extends UserType> = T extends 'company_user' 
  ? User & { companyId: CompanyId }
  : User;
```

### Mapped Types
```typescript
// Create partial update types
type UpdateFields<T> = {
  [K in keyof T]?: T[K];
};
```

### Template Literal Types
```typescript
// Permission string patterns
type InventoryPermission = `inventory:${string}`;
type OrderPermission = `orders:${string}`;
```

## Migration from Existing Types

When migrating existing code to use shared types:

1. Replace local type definitions with shared types
2. Update import statements
3. Use branded types for IDs
4. Replace manual validation with Zod schemas
5. Use helper functions for common operations

## Best Practices

1. **Use Branded Types**: Prevent ID confusion with branded types
2. **Validate at Boundaries**: Use Zod schemas for API validation
3. **Leverage Helpers**: Use provided helper functions for common operations
4. **Type Guards**: Use type guards for runtime type checking
5. **Consistent Naming**: Follow the established naming conventions

## Contributing

When adding new types:

1. Follow existing patterns and conventions
2. Add Zod validation schemas where appropriate
3. Include helper functions for common operations
4. Update this README with examples
5. Add tests for complex type logic

## Related Packages

- `@foodprepai/shared-utils` - Utility functions that complement these types
- `@foodprepai/api-client` - Type-safe API client using these types
- `@foodprepai/database-models` - Mongoose models based on these types