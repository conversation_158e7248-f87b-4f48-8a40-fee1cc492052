/**
 * Ionic-specific Design Token Mappings
 * CSS custom properties that map our design tokens to Ionic CSS variables
 */

:root {
  /* Ionic Color Variables - Primary Palette */
  --ion-color-primary: #0ea5e9;
  --ion-color-primary-rgb: 14, 165, 233;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #0c94ce;
  --ion-color-primary-tint: #26aee9;

  /* Ionic Color Variables - Secondary Palette */
  --ion-color-secondary: #64748b;
  --ion-color-secondary-rgb: 100, 116, 139;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #58667a;
  --ion-color-secondary-tint: #747d8c;

  /* Ionic Color Variables - Tertiary */
  --ion-color-tertiary: #a3a3a3;
  --ion-color-tertiary-rgb: 163, 163, 163;
  --ion-color-tertiary-contrast: #000000;
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #8f8f8f;
  --ion-color-tertiary-tint: #acacac;

  /* Ionic Color Variables - Success */
  --ion-color-success: #22c55e;
  --ion-color-success-rgb: 34, 197, 94;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #1eac52;
  --ion-color-success-tint: #38cb6e;

  /* Ionic Color Variables - Warning */
  --ion-color-warning: #f59e0b;
  --ion-color-warning-rgb: 245, 158, 11;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #d8890a;
  --ion-color-warning-tint: #f6a823;

  /* Ionic Color Variables - Danger */
  --ion-color-danger: #ef4444;
  --ion-color-danger-rgb: 239, 68, 68;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #d23c3c;
  --ion-color-danger-tint: #f15757;

  /* Ionic Color Variables - Light */
  --ion-color-light: #f5f5f5;
  --ion-color-light-rgb: 245, 245, 245;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d8d8d8;
  --ion-color-light-tint: #f6f6f6;

  /* Ionic Color Variables - Medium */
  --ion-color-medium: #a3a3a3;
  --ion-color-medium-rgb: 163, 163, 163;
  --ion-color-medium-contrast: #000000;
  --ion-color-medium-contrast-rgb: 0, 0, 0;
  --ion-color-medium-shade: #8f8f8f;
  --ion-color-medium-tint: #acacac;

  /* Ionic Color Variables - Dark */
  --ion-color-dark: #262626;
  --ion-color-dark-rgb: 38, 38, 38;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #212121;
  --ion-color-dark-tint: #3c3c3c;

  /* Background Colors */
  --ion-background-color: #fafafa;
  --ion-background-color-rgb: 250, 250, 250;

  /* Text Colors */
  --ion-text-color: #171717;
  --ion-text-color-rgb: 23, 23, 23;

  /* Step Colors (used for subtle backgrounds) */
  --ion-color-step-50: #f8fafc;
  --ion-color-step-100: #f1f5f9;
  --ion-color-step-150: #e2e8f0;
  --ion-color-step-200: #cbd5e1;
  --ion-color-step-250: #94a3b8;
  --ion-color-step-300: #64748b;
  --ion-color-step-350: #475569;
  --ion-color-step-400: #334155;
  --ion-color-step-450: #1e293b;
  --ion-color-step-500: #0f172a;
  --ion-color-step-550: #020617;
  --ion-color-step-600: #000000;

  /* Ionic Item Colors */
  --ion-item-background: var(--ion-color-step-50);
  --ion-item-color: var(--ion-text-color);

  /* Ionic Toolbar Colors */
  --ion-toolbar-background: var(--ion-background-color);
  --ion-toolbar-color: var(--ion-text-color);
  --ion-toolbar-border-color: var(--ion-color-step-200);

  /* Ionic Tab Bar Colors */
  --ion-tab-bar-background: var(--ion-background-color);
  --ion-tab-bar-color: var(--ion-color-medium);
  --ion-tab-bar-color-selected: var(--ion-color-primary);
  --ion-tab-bar-border-color: var(--ion-color-step-200);

  /* Ionic Card Colors */
  --ion-card-background: #ffffff;
  --ion-card-color: var(--ion-text-color);

  /* Ionic Modal Colors */
  --ion-modal-background: var(--ion-background-color);
  --ion-modal-color: var(--ion-text-color);

  /* Ionic Popover Colors */
  --ion-popover-background: #ffffff;
  --ion-popover-color: var(--ion-text-color);

  /* Ionic Loading Colors */
  --ion-loading-background: rgba(0, 0, 0, 0.4);
  --ion-loading-color: #ffffff;

  /* Ionic Toast Colors */
  --ion-toast-background: var(--ion-color-step-550);
  --ion-toast-color: var(--ion-color-step-50);

  /* Border and Separator Colors */
  --ion-border-color: var(--ion-color-step-200);
  --ion-separator-color: var(--ion-color-step-200);

  /* Font Configuration */
  --ion-font-family: var(--font-family-sans);

  /* Safe Area Variables for mobile */
  --ion-safe-area-top: env(safe-area-inset-top);
  --ion-safe-area-right: env(safe-area-inset-right);
  --ion-safe-area-bottom: env(safe-area-inset-bottom);
  --ion-safe-area-left: env(safe-area-inset-left);
}

/* Dark mode overrides for Ionic */
@media (prefers-color-scheme: dark) {
  :root {
    /* Background Colors - Dark Mode */
    --ion-background-color: #0a0a0a;
    --ion-background-color-rgb: 10, 10, 10;

    /* Text Colors - Dark Mode */
    --ion-text-color: #fafafa;
    --ion-text-color-rgb: 250, 250, 250;

    /* Step Colors - Dark Mode */
    --ion-color-step-50: #171717;
    --ion-color-step-100: #262626;
    --ion-color-step-150: #404040;
    --ion-color-step-200: #525252;
    --ion-color-step-250: #737373;
    --ion-color-step-300: #a3a3a3;
    --ion-color-step-350: #d4d4d4;
    --ion-color-step-400: #e5e5e5;
    --ion-color-step-450: #f5f5f5;
    --ion-color-step-500: #fafafa;
    --ion-color-step-550: #ffffff;
    --ion-color-step-600: #ffffff;

    /* Ionic Item Colors - Dark Mode */
    --ion-item-background: var(--ion-color-step-100);
    --ion-item-color: var(--ion-text-color);

    /* Ionic Toolbar Colors - Dark Mode */
    --ion-toolbar-background: var(--ion-color-step-100);
    --ion-toolbar-color: var(--ion-text-color);
    --ion-toolbar-border-color: var(--ion-color-step-200);

    /* Ionic Tab Bar Colors - Dark Mode */
    --ion-tab-bar-background: var(--ion-color-step-100);
    --ion-tab-bar-color: var(--ion-color-step-300);
    --ion-tab-bar-color-selected: var(--ion-color-primary);
    --ion-tab-bar-border-color: var(--ion-color-step-200);

    /* Ionic Card Colors - Dark Mode */
    --ion-card-background: var(--ion-color-step-100);
    --ion-card-color: var(--ion-text-color);

    /* Ionic Modal Colors - Dark Mode */
    --ion-modal-background: var(--ion-background-color);
    --ion-modal-color: var(--ion-text-color);

    /* Ionic Popover Colors - Dark Mode */
    --ion-popover-background: var(--ion-color-step-100);
    --ion-popover-color: var(--ion-text-color);

    /* Border and Separator Colors - Dark Mode */
    --ion-border-color: var(--ion-color-step-200);
    --ion-separator-color: var(--ion-color-step-200);

    /* Primary color adjustments for dark mode */
    --ion-color-primary: #38bdf8;
    --ion-color-primary-rgb: 56, 189, 248;
    --ion-color-primary-shade: #32a5d8;
    --ion-color-primary-tint: #4cc4f9;
  }
}

/* Custom Ionic component styling utilities */
.ion-color-foodprepai-primary {
  --ion-color-base: var(--ion-color-primary);
  --ion-color-base-rgb: var(--ion-color-primary-rgb);
  --ion-color-contrast: var(--ion-color-primary-contrast);
  --ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb);
  --ion-color-shade: var(--ion-color-primary-shade);
  --ion-color-tint: var(--ion-color-primary-tint);
}

.ion-color-foodprepai-success {
  --ion-color-base: var(--ion-color-success);
  --ion-color-base-rgb: var(--ion-color-success-rgb);
  --ion-color-contrast: var(--ion-color-success-contrast);
  --ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb);
  --ion-color-shade: var(--ion-color-success-shade);
  --ion-color-tint: var(--ion-color-success-tint);
}

.ion-color-foodprepai-warning {
  --ion-color-base: var(--ion-color-warning);
  --ion-color-base-rgb: var(--ion-color-warning-rgb);
  --ion-color-contrast: var(--ion-color-warning-contrast);
  --ion-color-contrast-rgb: var(--ion-color-warning-contrast-rgb);
  --ion-color-shade: var(--ion-color-warning-shade);
  --ion-color-tint: var(--ion-color-warning-tint);
}