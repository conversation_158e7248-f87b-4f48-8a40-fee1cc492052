/**
 * Design Tokens CSS Variables
 * Web-focused CSS custom properties for Next.js applications
 */

:root {
  /* Color Tokens - Primary */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  --color-primary-950: #082f49;

  /* Color Tokens - Secondary */
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-secondary-950: #020617;

  /* Status Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-900: #78350f;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-900: #7f1d1d;

  /* Neutral Colors */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;

  /* Semantic Color Mappings for shadcn/ui compatibility */
  --background: var(--color-neutral-50);
  --foreground: var(--color-neutral-900);
  --card: #ffffff;
  --card-foreground: var(--color-neutral-900);
  --popover: #ffffff;
  --popover-foreground: var(--color-neutral-900);
  --primary: var(--color-primary-500);
  --primary-foreground: var(--color-neutral-50);
  --secondary: var(--color-secondary-100);
  --secondary-foreground: var(--color-secondary-900);
  --muted: var(--color-secondary-100);
  --muted-foreground: var(--color-secondary-500);
  --accent: var(--color-secondary-100);
  --accent-foreground: var(--color-secondary-900);
  --destructive: var(--color-error-500);
  --destructive-foreground: var(--color-neutral-50);
  --border: var(--color-secondary-200);
  --input: var(--color-secondary-200);
  --ring: var(--color-primary-500);
  --radius: 0.5rem;

  /* Spacing Tokens */
  --spacing-0: 0px;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1_5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2_5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3_5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;
  --spacing-40: 10rem;
  --spacing-48: 12rem;
  --spacing-56: 14rem;
  --spacing-64: 16rem;

  /* Typography Tokens */
  --font-family-sans: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  --font-family-mono: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-xs: 1rem;
  --line-height-sm: 1.25rem;
  --line-height-base: 1.5rem;
  --line-height-lg: 1.75rem;
  --line-height-xl: 1.75rem;
  --line-height-2xl: 2rem;

  /* Border Radius Tokens */
  --radius-none: 0px;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadow Tokens */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Z-Index Tokens */
  --z-hide: -1;
  --z-base: 0;
  --z-docked: 10;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-banner: 1030;
  --z-overlay: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-toast: 1080;
  --z-tooltip: 1090;

  /* Animation Tokens */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;

  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Component Tokens */
  --button-height-sm: 2rem;
  --button-height-md: 2.25rem;
  --button-height-lg: 2.5rem;
  --button-height-xl: 3rem;

  --input-height-sm: 2rem;
  --input-height-md: 2.5rem;
  --input-height-lg: 3rem;
}

/* Dark mode overrides */
@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-neutral-950);
    --foreground: var(--color-neutral-50);
    --card: var(--color-neutral-900);
    --card-foreground: var(--color-neutral-50);
    --popover: var(--color-neutral-900);
    --popover-foreground: var(--color-neutral-50);
    --primary: var(--color-primary-400);
    --primary-foreground: var(--color-neutral-900);
    --secondary: var(--color-secondary-800);
    --secondary-foreground: var(--color-secondary-50);
    --muted: var(--color-secondary-800);
    --muted-foreground: var(--color-secondary-400);
    --accent: var(--color-secondary-800);
    --accent-foreground: var(--color-secondary-50);
    --destructive: var(--color-error-600);
    --destructive-foreground: var(--color-neutral-50);
    --border: var(--color-secondary-800);
    --input: var(--color-secondary-800);
    --ring: var(--color-primary-400);
  }
}

/* Utility classes for direct token usage */
.text-primary { color: var(--primary); }
.text-secondary { color: var(--color-secondary-600); }
.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }
.text-muted { color: var(--muted-foreground); }

.bg-primary { background-color: var(--primary); }
.bg-secondary { background-color: var(--secondary); }
.bg-success { background-color: var(--color-success-500); }
.bg-warning { background-color: var(--color-warning-500); }
.bg-error { background-color: var(--color-error-500); }
.bg-muted { background-color: var(--muted); }