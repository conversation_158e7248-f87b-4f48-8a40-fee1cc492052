{"name": "@foodprepai/design-tokens", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist", "css"], "scripts": {"build": "tsc && npm run build:css", "build:css": "mkdir -p dist/css && cp css/*.css dist/css/", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {}, "devDependencies": {"@foodprepai/tsconfig": "*", "typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./css/tokens.css": "./css/tokens.css", "./css/ionic-tokens.css": "./css/ionic-tokens.css"}}