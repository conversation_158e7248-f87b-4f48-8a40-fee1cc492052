/**
 * Ionic-specific Modal implementation using IonModal
 * Optimized for mobile experience with native feel
 */

import React from 'react';
import { ModalProps } from '@foodprepai/ui-core/types';
import { cn } from '@foodprepai/ui-core/utils';

// Mock Ionic components for demonstration
const IonModal = React.forwardRef<HTMLIonModalElement, any>(({ children, ...props }, ref) => (
  <ion-modal {...props} ref={ref}>
    {children}
  </ion-modal>
));

const IonHeader = ({ children, ...props }: any) => (
  <ion-header {...props}>
    {children}
  </ion-header>
);

const IonToolbar = ({ children, ...props }: any) => (
  <ion-toolbar {...props}>
    {children}
  </ion-toolbar>
);

const IonTitle = ({ children, ...props }: any) => (
  <ion-title {...props}>
    {children}
  </ion-title>
);

const IonButtons = ({ slot, children, ...props }: any) => (
  <ion-buttons slot={slot} {...props}>
    {children}
  </ion-buttons>
);

const IonButton = ({ fill, children, onClick, ...props }: any) => (
  <ion-button fill={fill} onClick={onClick} {...props}>
    {children}
  </ion-button>
);

const IonContent = ({ children, className, ...props }: any) => (
  <ion-content class={className} {...props}>
    {children}
  </ion-content>
);

const IonFooter = ({ children, ...props }: any) => (
  <ion-footer {...props}>
    {children}
  </ion-footer>
);

const IonIcon = ({ icon, slot }: { icon: string; slot?: string }) => (
  <ion-icon icon={icon} slot={slot} />
);

// Type definitions for Ionic components
interface HTMLIonModalElement extends HTMLElement {
  isOpen: boolean;
  onDidDismiss: (callback: () => void) => void;
  dismiss: () => Promise<boolean>;
}

const getBreakpoints = (size: ModalProps['size']): number[] => {
  switch (size) {
    case 'sm':
      return [0.3, 0.5];
    case 'md':
      return [0.5, 0.75];
    case 'lg':
      return [0.75, 0.9];
    case 'xl':
      return [0.9, 1];
    case 'full':
      return [1];
    default:
      return [0.5, 0.75];
  }
};

const getInitialBreakpoint = (size: ModalProps['size']): number => {
  switch (size) {
    case 'sm':
      return 0.3;
    case 'md':
      return 0.5;
    case 'lg':
      return 0.75;
    case 'xl':
      return 0.9;
    case 'full':
      return 1;
    default:
      return 0.5;
  }
};

export const IonicModal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  size = 'md',
  closeOnBackdropClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  header,
  footer,
  centered = true,
  children,
  className,
  testId,
  ...props
}) => {
  const modalRef = React.useRef<HTMLIonModalElement>(null);

  const handleDidDismiss = () => {
    onClose();
  };

  const handleBackButtonClick = () => {
    onClose();
  };

  const breakpoints = getBreakpoints(size);
  const initialBreakpoint = getInitialBreakpoint(size);

  // Handle escape key for web environment
  React.useEffect(() => {
    if (!closeOnEscape) return () => {};
    
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, closeOnEscape, onClose]);

  return (
    <IonModal
      ref={modalRef}
      isOpen={isOpen}
      onDidDismiss={handleDidDismiss}
      breakpoints={size !== 'full' ? breakpoints : undefined}
      initialBreakpoint={size !== 'full' ? initialBreakpoint : undefined}
      showBackdrop={true}
      backdropDismiss={closeOnBackdropClick}
      data-testid={testId}
      className={cn("ionic-modal", className)}
      {...props}
    >
      {/* Header */}
      {(title || header || showCloseButton) && (
        <IonHeader>
          <IonToolbar>
            {showCloseButton && (
              <IonButtons slot="start">
                <IonButton fill="clear" onClick={handleBackButtonClick}>
                  <IonIcon icon="arrow-back" />
                </IonButton>
              </IonButtons>
            )}
            
            {title && !header && (
              <IonTitle>{title}</IonTitle>
            )}
            
            {header && (
              <div className="flex items-center justify-between w-full px-4">
                {header}
              </div>
            )}
            
            {showCloseButton && (
              <IonButtons slot="end">
                <IonButton fill="clear" onClick={onClose}>
                  <IonIcon icon="close" />
                </IonButton>
              </IonButtons>
            )}
          </IonToolbar>
        </IonHeader>
      )}

      {/* Content */}
      <IonContent className={cn("ion-padding", size === 'full' && "fullscreen")}>
        <div className={cn(
          "modal-content",
          !centered && "pt-4"
        )}>
          {children}
        </div>
      </IonContent>

      {/* Footer */}
      {footer && (
        <IonFooter>
          <IonToolbar>
            <div className="flex items-center justify-end w-full px-4 py-2 space-x-2">
              {footer}
            </div>
          </IonToolbar>
        </IonFooter>
      )}
    </IonModal>
  );
};

IonicModal.displayName = 'IonicModal';