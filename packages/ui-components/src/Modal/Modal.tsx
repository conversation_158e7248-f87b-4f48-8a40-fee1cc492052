/**
 * Unified Modal Component
 * 
 * Platform-adaptive modal that renders as Radix Dialog on web
 * and IonModal on mobile for native feel
 */

import React from 'react';
import { createPlatformComponent } from '@foodprepai/ui-core';
import { ModalProps } from '@foodprepai/ui-core/types';
import { WebModal } from './WebModal';
import { IonicModal } from './IonicModal';

/**
 * Unified Modal component that adapts to the current platform
 * - Web: Renders as Radix Dialog with shadcn/ui styling
 * - Mobile: Renders as IonModal for native mobile experience
 */
export const Modal = createPlatformComponent<ModalProps>(
  WebModal,
  IonicModal,
  'Modal'
);

Modal.displayName = 'Modal';

export type { ModalProps } from '@foodprepai/ui-core/types';
export default Modal;