/**
 * Web-specific Table implementation using HTML table with shadcn/ui styling
 */

import React from 'react';
import { TableProps, TableColumn } from '@foodprepai/ui-core/types';
import { cn } from '@foodprepai/ui-core/utils';

// Table component definitions (based on shadcn/ui table)
const TableRoot = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <div className="relative w-full overflow-auto">
    <table
      ref={ref}
      className={cn("w-full caption-bottom text-sm", className)}
      {...props}
    />
  </div>
));
TableRoot.displayName = "TableRoot";

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
));
TableHeader.displayName = "TableHeader";

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
));
TableBody.displayName = "TableBody";

const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement> & {
    clickable?: boolean;
  }
>(({ className, clickable, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
      clickable && "cursor-pointer",
      className
    )}
    {...props}
  />
));
TableRow.displayName = "TableRow";

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement> & {
    sortable?: boolean;
    sortDirection?: 'asc' | 'desc';
  }
>(({ className, sortable, sortDirection, children, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
      sortable && "cursor-pointer hover:text-foreground select-none",
      className
    )}
    {...props}
  >
    <div className="flex items-center space-x-1">
      <span>{children}</span>
      {sortable && (
        <span className="ml-1 text-xs">
          {sortDirection === 'asc' ? '↑' : sortDirection === 'desc' ? '↓' : '↕'}
        </span>
      )}
    </div>
  </th>
));
TableHead.displayName = "TableHead";

const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn(
      "p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
      className
    )}
    {...props}
  />
));
TableCell.displayName = "TableCell";

// Loading skeleton component
const TableSkeleton: React.FC<{ columns: number; rows: number }> = ({ columns, rows }) => (
  <TableRoot>
    <TableHeader>
      <TableRow>
        {Array.from({ length: columns }).map((_, index) => (
          <TableHead key={`skeleton-header-${index}`}>
            <div className="h-4 bg-muted animate-pulse rounded w-20" />
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
    <TableBody>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <TableRow key={`skeleton-row-${rowIndex}`}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <TableCell key={`skeleton-cell-${rowIndex}-${colIndex}`}>
              <div className="h-4 bg-muted animate-pulse rounded w-full" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  </TableRoot>
);

export const WebTable = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  loadingRows = 3,
  emptyMessage = 'No data available',
  onRowClick,
  selectable = false,
  selectedRows = [] as any[],
  onSelectionChange,
  sortBy,
  sortDirection,
  onSort,
  className,
  testId,
  ...props
}: TableProps<T>) => {
  const handleRowClick = (item: T, index: number) => {
    if (onRowClick) {
      onRowClick(item, index);
    }
  };

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return;
    
    const newDirection = sortBy === column.key 
      ? (sortDirection === 'asc' ? 'desc' : 'asc')
      : 'asc';
    
    onSort(column.key, newDirection);
  };

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return;
    
    if (checked) {
      const allIds = data.map((item, index) => item.id || index);
      onSelectionChange(allIds);
    } else {
      onSelectionChange([]);
    }
  };

  const handleRowSelect = (item: T, index: number, checked: boolean) => {
    if (!onSelectionChange) return;
    
    const itemId = item.id || index;
    const newSelection = checked
      ? [...selectedRows, itemId]
      : selectedRows.filter(id => id !== itemId);
    
    onSelectionChange(newSelection);
  };

  if (loading) {
    return (
      <div className={cn(className)} data-testid={testId} {...props}>
        <TableSkeleton columns={columns.length} rows={loadingRows} />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={cn("text-center py-8 text-muted-foreground", className)} data-testid={testId}>
        {emptyMessage}
      </div>
    );
  }

  const isAllSelected = data.length > 0 && selectedRows.length === data.length;
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < data.length;

  return (
    <div className={cn(className)} data-testid={testId} {...props}>
      <TableRoot>
        <TableHeader>
          <TableRow>
            {selectable && (
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = isIndeterminate;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-muted-foreground"
                />
              </TableHead>
            )}
            {columns.map((column) => (
              <TableHead
                key={column.key}
                style={{ width: column.width }}
                sortable={column.sortable}
                sortDirection={sortBy === column.key ? sortDirection : undefined}
                onClick={() => handleSort(column)}
                className={cn(
                  column.align === 'center' && 'text-center',
                  column.align === 'right' && 'text-right'
                )}
              >
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item, index) => {
            const isSelected = (selectedRows as any[]).includes(((item as any).id || index));
            
            return (
              <TableRow
                key={(item as any).id || index}
                clickable={!!onRowClick}
                onClick={() => handleRowClick(item, index)}
                data-state={isSelected ? 'selected' : undefined}
              >
                {selectable && (
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => handleRowSelect(item, index, e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                      className="rounded border-muted-foreground"
                    />
                  </TableCell>
                )}
                {columns.map((column) => {
                  const value = column.accessor ? column.accessor(item) : item[column.key];
                  const displayValue = column.render ? column.render(value, item, index) : String(value);
                  
                  return (
                    <TableCell
                      key={`${item.id || index}-${column.key}`}
                      className={cn(
                        column.align === 'center' && 'text-center',
                        column.align === 'right' && 'text-right'
                      )}
                    >
                      {displayValue}
                    </TableCell>
                  );
                })}
              </TableRow>
            );
          })}
        </TableBody>
      </TableRoot>
    </div>
  );
};

WebTable.displayName = 'WebTable';