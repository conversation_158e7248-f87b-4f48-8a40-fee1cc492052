/**
 * Unified Table Component
 * 
 * Platform-adaptive table that renders as HTML table on web
 * and IonList with IonItems on mobile for better touch interaction
 */

import React from 'react';
import { createPlatformComponent } from '@foodprepai/ui-core';
import { TableProps } from '@foodprepai/ui-core/types';
import { WebTable } from './WebTable';
import { IonicTable } from './IonicTable';

/**
 * Unified Table component that adapts to the current platform
 * - Web: Renders as HTML table with shadcn/ui styling
 * - Mobile: Renders as IonList with IonItems for touch-friendly interaction
 */
export const Table = createPlatformComponent<TableProps>(
  WebTable,
  IonicTable,
  'Table'
);

Table.displayName = 'Table';

export type { TableProps, TableColumn } from '@foodprepai/ui-core/types';
export default Table;