/**
 * Unified Button Component
 * 
 * Platform-adaptive button that renders as shadcn/ui Button on web
 * and Ionic IonButton on mobile
 */

import React from 'react';
import { createPlatformComponent } from '@foodprepai/ui-core';
import { ButtonProps } from '@foodprepai/ui-core/types';
import { WebButton } from './WebButton';
import { IonicButton } from './IonicButton';

/**
 * Unified Button component that adapts to the current platform
 */
export const Button = createPlatformComponent<ButtonProps>(
  WebButton,
  IonicButton,
  'Button'
);

Button.displayName = 'Button';

export type { ButtonProps } from '@foodprepai/ui-core/types';
export default Button;