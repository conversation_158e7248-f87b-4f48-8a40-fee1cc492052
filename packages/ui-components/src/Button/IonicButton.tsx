/**
 * Ionic-specific Button implementation using IonButton
 */

import React from 'react';
import { ButtonProps } from '@foodprepai/ui-core/types';
import { cn } from '@foodprepai/ui-core/utils';

// Mock Ionic components for demonstration - in real implementation, these would be imported from @ionic/react
const IonButton = React.forwardRef<HTMLIonButtonElement, any>(({ children, ...props }, ref) => (
  <ion-button {...props} ref={ref}>
    {children}
  </ion-button>
));

const IonIcon = ({ icon, slot }: { icon: string; slot?: string }) => (
  <ion-icon icon={icon} slot={slot} />
);

const IonSpinner = ({ name }: { name?: string }) => (
  <ion-spinner name={name} />
);

// Type definitions for Ionic components (normally these would come from @ionic/react)
interface HTMLIonButtonElement extends HTMLElement {
  disabled: boolean;
  fill?: 'clear' | 'outline' | 'solid';
  expand?: 'block' | 'full';
  size?: 'small' | 'default' | 'large';
  color?: string;
  strong?: boolean;
}

const getIonicVariant = (variant: ButtonProps['variant']): {
  fill?: 'clear' | 'outline' | 'solid';
  color?: string;
} => {
  switch (variant) {
    case 'primary':
      return { fill: 'solid', color: 'primary' };
    case 'secondary':
      return { fill: 'outline', color: 'primary' };
    case 'outline':
      return { fill: 'outline' };
    case 'ghost':
      return { fill: 'clear' };
    case 'destructive':
      return { fill: 'solid', color: 'danger' };
    default:
      return { fill: 'solid', color: 'primary' };
  }
};

const getIonicSize = (size: ButtonProps['size']): 'small' | 'default' | 'large' => {
  switch (size) {
    case 'xs':
    case 'sm':
      return 'small';
    case 'lg':
    case 'xl':
      return 'large';
    default:
      return 'default';
  }
};

export const IonicButton = React.forwardRef<HTMLIonButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    disabled = false,
    loading = false,
    fullWidth = false,
    icon,
    iconPosition = 'start',
    children,
    onClick,
    type = 'button',
    testId,
    ...props 
  }, ref) => {
    const ionicVariant = getIonicVariant(variant);
    const ionicSize = getIonicSize(size);
    const isDisabled = disabled || loading;
    
    // Handle icon - in Ionic, icons are typically strings referencing Ionicons
    const iconName = typeof icon === 'string' ? icon : undefined;
    
    return (
      <IonButton
        className={cn(className)}
        ref={ref}
        disabled={isDisabled}
        onClick={onClick}
        type={type}
        fill={ionicVariant.fill}
        color={ionicVariant.color}
        size={ionicSize}
        expand={fullWidth ? 'block' : undefined}
        strong={variant === 'primary'}
        data-testid={testId}
        {...props}
      >
        {loading ? (
          <IonSpinner name="crescent" />
        ) : (
          <>
            {iconName && iconPosition === 'start' && (
              <IonIcon icon={iconName} slot="start" />
            )}
            {children}
            {iconName && iconPosition === 'end' && (
              <IonIcon icon={iconName} slot="end" />
            )}
          </>
        )}
      </IonButton>
    );
  }
);

IonicButton.displayName = 'IonicButton';