/**
 * @foodprepai/ui-components
 * 
 * Unified component library that works across Next.js and Ionic React platforms
 * Components automatically adapt to the current platform for optimal user experience
 */

// Core platform utilities - re-exported for convenience
export { 
  getPlatformConfig, 
  setPlatformConfig, 
  usePlatformConfig, 
  platformSelect,
  isPlatform,
  env 
} from '@foodprepai/ui-core/platform';

export { cn } from '@foodprepai/ui-core/utils';

// Design tokens - re-exported for convenience
export { colors, typography, spacing, borderRadius, components } from '@foodprepai/design-tokens';

// Component exports
export { Button } from './Button';
export { Table } from './Table';
export { Modal } from './Modal';

// Type exports
export type { 
  ButtonProps,
  TableProps,
  TableColumn,
  ModalProps,
  BaseComponentProps,
  SizeVariant,
  ColorVariant
} from '@foodprepai/ui-core/types';

// Platform configuration helper
export { createPlatformComponent, PlatformWrapper, PlatformOnly } from '@foodprepai/ui-core/components';

/**
 * Initialize platform configuration
 * Call this in your app's entry point to set up the component library
 */
export function initializePlatform(platform?: 'web' | 'ionic' | 'auto') {
  const { setPlatformConfig } = require('@foodprepai/ui-core/platform');
  setPlatformConfig(platform || 'auto');
}

/**
 * Component library version
 */
export const VERSION = '0.1.0';