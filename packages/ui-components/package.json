{"name": "@foodprepai/ui-components", "version": "0.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@foodprepai/shared-types": "*", "@foodprepai/ui-core": "*", "@foodprepai/design-tokens": "*", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-slot": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@foodprepai/tsconfig": "*", "@ionic/react": "^8.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/jest": "^29.5.14", "jest": "^29.7.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./Button": {"types": "./dist/Button.d.ts", "default": "./dist/Button.js"}, "./Modal": {"types": "./dist/Modal.d.ts", "default": "./dist/Modal.js"}, "./Table": {"types": "./dist/Table.d.ts", "default": "./dist/Table.js"}, "./forms": {"types": "./dist/forms/index.d.ts", "default": "./dist/forms/index.js"}}}