module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // Custom rules for the monorepo
    'scope-enum': [
      2,
      'always',
      [
        'web',
        'mobile',
        'shared-types',
        'shared-utils',
        'api-client',
        'ui-components',
        'database-models',
        'tools',
        'ci',
        'docs',
        'deps',
        'monorepo',
      ],
    ],
    'subject-case': [2, 'never', ['sentence-case', 'start-case', 'pascal-case']],
    'subject-min-length': [2, 'always', 10],
    'subject-max-length': [2, 'always', 100],
    'body-max-line-length': [2, 'always', 100],
  },
};