{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*", "DATABASE_URL", "NEXTAUTH_*"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_*", "DATABASE_URL", "NEXTAUTH_*"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "typecheck": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"], "outputs": ["coverage/**"]}, "test:watch": {"cache": false, "persistent": true}, "clean": {"cache": false}, "format": {"outputs": [], "cache": false}, "format:check": {"outputs": []}}, "remoteCache": {"signature": true}, "globalEnv": ["NODE_ENV", "CI", "VERCEL", "DATABASE_URL", "NEXTAUTH_SECRET", "NEXTAUTH_URL"], "ui": "tui"}