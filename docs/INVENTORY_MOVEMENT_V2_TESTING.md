# Inventory Movement V2 API Testing Guide

This guide covers testing the new v2 Inventory Movement API which properly handles the inventory structure outlined in the [Inventory Structure](./INVENTORY_STRUCTURE.md) document.

## Latest Updates: Catalog-Based Inventory Transfers

The inventory transfer system now supports a catalog-based approach where:

1. Branches can view available items from the central kitchen via a catalog API
2. Branch inventory records are automatically created during transfer operations
3. Transfers support markup with percentage or fixed amount options
4. Branch-to-branch transfers work without requiring manual record creation

### Postman Collection

A complete Postman collection for testing these features is available at:
```
/docs/postman/InventoryTransfers.postman_collection.json
```

The collection includes requests for:
- Viewing the central kitchen catalog
- Executing transfers with different markup types
- Testing branch-to-branch transfers
- Checking inventory after transfers

### Using the Postman Collection

1. **Import the Collection**:
   - In Postman, click "Import" and select the JSON file
   - The collection contains variables you'll need to configure

2. **Configure Collection Variables**:
   - `baseUrl`: Your API base URL (default: http://localhost:3000)
   - `companyId`: Your company ID
   - `centralKitchenId`: ID of your central kitchen location
   - `branchLocationId`: ID of your first branch location
   - `secondBranchId`: ID of another branch for branch-to-branch tests
   - `authToken`: Your authentication token

3. **Test Flow**:
   - Start with "1. View Central Kitchen Catalog" to see available items
   - Use "2. Get Locations" to find location IDs if needed
   - Run tests in sequence (3-5, 6-7, 8-9) to test different transfer types
   - Use "10. Get Transfer Revenue Report" to verify markup revenue

### Central Kitchen Catalog API

The new catalog API endpoint allows branches to view items available from the central kitchen:

```
GET /api/company/[companyId]/central-kitchen/catalog
```

Query parameters:
- `locationId`: (Required) The branch location ID requesting the catalog
- `type`: (Optional) Filter by INGREDIENT or RECIPE
- `search`: (Optional) Search term to filter items by name

The response includes:
- All items marked with `canBeSold: true` in central inventory
- Only items visible to the requesting branch based on visibility rules
- Current stock levels from central inventory
- Complete details including units and pricing information

## API Endpoint

```
POST /api/company/{companyId}/inventory/movement/v2
```

## Key Improvements in V2

1. **Correct Collection Updates**:
   - Updates pendingStock and currentStock in the proper collections (Ingredients or Recipes)
   - BranchInventory is only updated for branch-specific inventory transactions

2. **Complete Transaction Support**:
   - PURCHASE and RECEIVED for central inventory
   - PRODUCTION_INPUT and PRODUCTION_OUTPUT for recipe production
   - WASTAGE and ADJUSTMENT for inventory corrections
   - TRANSFER_IN and TRANSFER_OUT for branch management
   - COUNT for inventory counts
   - SALE for sales transactions

## Testing Different Transaction Types

### 1. PURCHASE Transaction (Central Inventory)

This transaction increases `pendingStock` for an ingredient or recipe in the central inventory.

```json
{
  "transactionType": "PURCHASE",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{ingredientId}}",
      "itemType": "INGREDIENT",
      "quantity": 10,
      "unitId": "{{unitId}}",
      "cost": 15.99
    }
  ],
  "supplierName": "Main Supplier Co.",
  "invoiceNumber": "INV-12345",
  "notes": "Regular weekly order"
}
```

### 2. RECEIVED Transaction (Central Inventory)

This transaction moves stock from `pendingStock` to `currentStock` in the central inventory.

```json
{
  "transactionType": "RECEIVED",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{ingredientId}}",
      "itemType": "INGREDIENT",
      "quantity": 10,
      "unitId": "{{unitId}}"
    }
  ],
  "reference": {
    "type": "PURCHASE_ORDER",
    "id": "{{purchaseOrderId}}"
  },
  "notes": "Full order received"
}
```

### 3. PRODUCTION_INPUT Transaction (Recipe Production)

This transaction decreases `currentStock` for ingredients used in recipe production.

```json
{
  "transactionType": "PRODUCTION_INPUT",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{ingredientId}}",
      "itemType": "INGREDIENT",
      "quantity": 5,
      "unitId": "{{unitId}}"
    }
  ],
  "reference": {
    "type": "PRODUCTION",
    "id": "{{productionId}}"
  },
  "recipeId": "{{recipeId}}",
  "notes": "Ingredients used for batch 12345"
}
```

### 4. PRODUCTION_OUTPUT Transaction (Recipe Production)

This transaction increases `pendingStock` for the recipe being produced.

```json
{
  "transactionType": "PRODUCTION_OUTPUT",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{recipeId}}",
      "itemType": "RECIPE",
      "quantity": 2,
      "unitId": "{{unitId}}"
    }
  ],
  "reference": {
    "type": "PRODUCTION",
    "id": "{{productionId}}"
  },
  "recipeId": "{{recipeId}}",
  "notes": "Output from batch 12345"
}
```

### 5. WASTAGE Transaction (Central or Branch Inventory)

This transaction reduces `currentStock` for an ingredient, recipe, or branch inventory item.

```json
{
  "transactionType": "WASTAGE",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{itemId}}",
      "itemType": "INGREDIENT", // or "RECIPE"
      "quantity": 1,
      "unitId": "{{unitId}}",
      "reason": "Expired"
    }
  ],
  "disposalMethod": "Discard",
  "notes": "Product past expiration date"
}
```

### 6. ADJUSTMENT Transaction (Central or Branch Inventory)

This transaction sets the `currentStock` to a specific value based on the `expectedQuantity`.

```json
{
  "transactionType": "ADJUSTMENT",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{itemId}}",
      "itemType": "INGREDIENT", // or "RECIPE"
      "unitId": "{{unitId}}",
      "expectedQuantity": 15,
      "reason": "Correction"
    }
  ],
  "notes": "Correcting inventory after physical count"
}
```

### 7. TRANSFER_OUT Transaction (Branch Inventory)

This transaction moves stock from one location to another, creating a pending transfer.

```json
{
  "transactionType": "TRANSFER_OUT",
  "locationId": "{{sourceLocationId}}",
  "destinationLocationId": "{{destinationLocationId}}",
  "items": [
    {
      "itemId": "{{itemId}}",
      "itemType": "INGREDIENT", // or "RECIPE"
      "quantity": 5,
      "unitId": "{{unitId}}",
      "cost": 20.00  // Optional cost per unit, used for markup calculations
    }
  ],
  "priceMarkup": { // Optional markup configuration
    "type": "PERCENTAGE", // or "FIXED"
    "value": 15, // 15% for PERCENTAGE or currency amount for FIXED
    "applyTax": false // Whether to apply tax to the markup
  },
  "transferReference": "TRF-123",
  "notes": "Weekly transfer to branch"
}
```

### 8. TRANSFER_IN Transaction (Branch Inventory)

This transaction confirms receipt of a transfer, moving stock from pending to current at the destination.

```json
{
  "transactionType": "TRANSFER_IN",
  "locationId": "{{destinationLocationId}}",
  "sourceLocationId": "{{sourceLocationId}}",
  "items": [
    {
      "itemId": "{{itemId}}",
      "itemType": "INGREDIENT", // or "RECIPE"
      "quantity": 5,
      "unitId": "{{unitId}}"
    }
  ],
  "reference": {
    "type": "TRANSFER",
    "id": "{{transferId}}"
  },
  "notes": "Transfer received in full"
}
```

### 9. COUNT Transaction (Central or Branch Inventory)

This transaction updates the current stock based on a physical count.

```json
{
  "transactionType": "COUNT",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{itemId}}",
      "itemType": "INGREDIENT", // or "RECIPE"
      "quantity": 8, // The counted amount
      "unitId": "{{unitId}}"
    }
  ],
  "countBatch": "COUNT-123",
  "notes": "Monthly inventory count"
}
```

### 10. SALE Transaction (Branch Inventory)

This transaction decreases stock for items sold at a branch.

```json
{
  "transactionType": "SALE",
  "locationId": "{{locationId}}",
  "items": [
    {
      "itemId": "{{itemId}}",
      "itemType": "RECIPE", // Usually RECIPE for most sales
      "quantity": 1,
      "unitId": "{{unitId}}"
    }
  ],
  "reference": {
    "type": "ORDER",
    "id": "{{orderId}}"
  },
  "notes": "Sale to customer"
}
```

## Verification Steps

After each transaction:

1. **Check the API Response**:
   - Confirm `success: true` and appropriate message
   - Review `affectedItems` for changes to stock levels

2. **Verify Database Changes**:
   - For central inventory transactions (PURCHASE, RECEIVED, PRODUCTION):
     - Check the Ingredient or Recipe collection for updated stock
   - For branch transactions (TRANSFER, SALE):
     - Check the BranchInventory collection for the relevant location

3. **View Transaction History**:
   - Check the InventoryTransaction collection for the recorded transaction
   - Verify that previousStock, newStock, and other fields are correctly recorded

## Common Error Scenarios

- **Insufficient Stock**: Attempting to reduce stock by more than is available
- **Missing Fields**: Required fields for specific transaction types (e.g., expectedQuantity for ADJUSTMENT)
- **Invalid Reference**: Incorrect IDs for referenced items
- **Type Mismatch**: Using an incorrect itemType (e.g., attempting PRODUCTION_OUTPUT on an INGREDIENT)

## Testing Flow Example

A complete testing flow might look like:

1. PURCHASE ingredients
2. RECEIVED ingredients
3. PRODUCTION_INPUT (consume ingredients)
4. PRODUCTION_OUTPUT (create recipe)
5. TRANSFER_OUT from central to branch
6. TRANSFER_IN at branch
7. SALE at branch

This simulates the entire inventory lifecycle from purchasing raw materials to selling the final product.

## Notes on Testing & Implementation

1. All transaction data is stored in the `inventoryTransactions` collection for audit purposes
2. Item data is stored in either `ingredients`, `recipes`, or `branchInventories` collections
3. Test thoroughly with proper item IDs and units to ensure accurate inventory management

## Known Issues & Fixes

### TRANSFER_OUT Stock Access and Markup Issues

**Problem**: The TRANSFER_OUT API endpoint had two critical issues:
1. Stock values weren't being properly read from ingredients/recipes collections for central kitchen transfers
2. The transferMarkup field wasn't being properly saved in transaction documents

**Root Causes**:
1. **Stock Access Issue**: When handling TRANSFER_OUT from central kitchen, the code wasn't correctly reading or updating the currentStock value from the Ingredient/Recipe documents
2. **Markup Field Issue**: The transferMarkup nested object wasn't being properly included in the transaction document creation

**Fix Implementation**:

1. **Stock Value Solution**:
   - Added a robust item stock retrieval function that uses `lean()` for efficiency
   - Implemented explicit type conversions (Number()) to ensure stock values are properly handled
   - Added validation steps to verify stock updates were successful

2. **Transfer Markup Solution**:
   - Modified the transaction creation to explicitly include the transferMarkup object
   - Used the `create()` method with a pre-structured document object
   - Added proper error handling to ensure the field is saved

**Example of the Fixed API Response**:
```json
{
  "success": true,
  "message": "Successfully processed TRANSFER_OUT transaction",
  "transactionId": "67efc8e73d7c1785028bf205",
  "movementDate": "2025-04-04T12:09:09.730Z",
  "transactionType": "TRANSFER_OUT",
  "affectedItems": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "itemName": "Betteraves (kg)",
      "previousStock": 25,
      "newStock": 20,
      "difference": -5,
      "pendingStock": 40,
      "transferDetails": {
        "originalCost": 20,
        "markupType": "PERCENTAGE",
        "markupValue": 15,
        "destinationLocationId": "676976a081f62ee1ec7c4bef"
      }
    }
  ]
}
```

**Implementation Notes**:
- The fix is in the `/src/app/api/company/[companyId]/inventory/movement/v2/route.ts` file
- It specifically addresses the TRANSFER_OUT transaction type with price markup
- The solution uses proper database access patterns and explicit type handling for robustness

## Key Implementation Details

### Automatic Branch Inventory Creation

With the latest updates, the system will automatically:

1. Create branch inventory records during TRANSFER_OUT if they don't exist
2. Create or update branch inventory records during TRANSFER_IN
3. Link to the appropriate baseUomId from the source item
4. Apply markup rules when specified in the request

This eliminates the need for manual branch inventory setup before transfers.

### Transfer Markup Support

Transfers now support two types of markup:

1. **Percentage Markup**: 
   - Adds a percentage to the cost (e.g., 15%)
   - Example: 10 units at $5 with 15% markup = 10 units at $5.75 each

2. **Fixed Markup**:
   - Adds a fixed amount per unit (e.g., $2.50)
   - Example: 10 units at $5 with $2.50 fixed markup = 10 units at $7.50 each

Markup is applied during TRANSFER_OUT and stored with the branch inventory record and transaction.
