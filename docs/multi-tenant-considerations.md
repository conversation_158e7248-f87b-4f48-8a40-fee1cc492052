# Multi-Tenant Considerations for User Synchronization

This document outlines important considerations and implementation details for maintaining proper multi-tenant isolation in the FoodPrepAI platform and IonicPOS app.

## Core Multi-Tenant Architecture

### 1. Company-Based Isolation

All data in the system is scoped to a specific company (tenant). The company ID serves as the primary tenant identifier:

```typescript
// Example MongoDB schema with company isolation
const UserSchema = new Schema<IUser>({
  email: { type: String, required: true },
  // ... other fields
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true },
});

// Example API route with company isolation
export async function getUsers(req: NextRequest, context: { params: { companyId: string } }) {
  const { companyId } = context.params;
  
  // Always filter by companyId
  const users = await User.find({ companyId: new mongoose.Types.ObjectId(companyId) });
  
  return Response.json({ users });
}
```

### 2. Location-Based Sub-Tenancy

Within a company, locations serve as sub-tenants:

```typescript
// User with location assignment
interface IU<PERSON> extends Document {
  // ... other fields
  companyId: mongoose.Types.ObjectId;
  locationId?: mongoose.Types.ObjectId; // Associated location within the company
}
```

## Authentication & Authorization

### 1. Company Context in Authentication

All authentication tokens must include the company context:

```typescript
// JWT payload structure
const payload = {
  id: user._id.toString(),
  userType: user.userType,
  companyId: user.companyId.toString(),
  locationId: user.locationId?.toString(),
  // ... other claims
};
```

### 2. Request Headers for Tenant Context

Every API request should include the company ID in the headers:

```typescript
// Example request headers
{
  'Authorization': 'Bearer <jwt_token>',
  'company-id': '507f1f77bcf86cd799439011',
  'location-id': '507f1f77bcf86cd799439012' // Optional
}
```

### 3. Middleware for Tenant Validation

```typescript
// Middleware to validate tenant access
export async function validateTenantAccess(
  req: NextRequest,
  companyId: string,
  locationId?: string
) {
  // Get company ID from authentication token
  const authToken = req.headers.get('Authorization')?.replace('Bearer ', '');
  const decoded = decodeToken(authToken);
  
  if (!decoded || decoded.companyId !== companyId) {
    throw new Error('Company ID mismatch or unauthorized access');
  }
  
  // If location ID is provided, validate location access
  if (locationId && decoded.locationId !== locationId) {
    // Check if user has access to this location
    const hasAccess = await checkLocationAccess(decoded.id, locationId);
    if (!hasAccess) {
      throw new Error('Unauthorized location access');
    }
  }
  
  return true;
}
```

## Implementation for IonicPOS

### 1. Company Selection During Setup

The IonicPOS app should require company selection during initial setup:

```typescript
// Company selection service
async selectCompany(companyId: string) {
  // Store company ID in secure storage
  await Preferences.set({
    key: 'selected_company_id',
    value: companyId
  });
  
  // Store in memory for active session
  this.currentCompanyId = companyId;
  
  // Clear and re-initialize database for this company
  await this.dbService.resetDatabase();
  await this.dbService.initForCompany(companyId);
  
  // Trigger initial sync
  await this.syncService.performFullSync();
}
```

### 2. Location Assignment

POS devices are typically assigned to a specific location:

```typescript
// Location assignment
async assignToLocation(locationId: string) {
  // Store location ID in secure storage
  await Preferences.set({
    key: 'assigned_location_id',
    value: locationId
  });
  
  // Store in memory
  this.currentLocationId = locationId;
  
  // Update authentication context
  this.authService.updateLocationContext(locationId);
  
  // Trigger location-specific sync
  await this.syncService.syncLocationData(locationId);
}
```

### 3. Tenant Context in All API Requests

```typescript
// HTTP interceptor to add tenant context
@Injectable()
export class TenantInterceptor implements HttpInterceptor {
  constructor(private tenantService: TenantService) {}
  
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Get current tenant context
    const companyId = this.tenantService.getCurrentCompanyId();
    const locationId = this.tenantService.getCurrentLocationId();
    
    if (!companyId) {
      // No tenant context - might be accessing public API
      return next.handle(req);
    }
    
    // Clone request with tenant headers
    let tenantReq = req.clone({
      headers: req.headers.set('company-id', companyId)
    });
    
    // Add location ID if available
    if (locationId) {
      tenantReq = tenantReq.clone({
        headers: tenantReq.headers.set('location-id', locationId)
      });
    }
    
    return next.handle(tenantReq);
  }
}
```

### 4. Data Isolation in Local Storage

```typescript
// Database service with tenant isolation
export class DbService {
  private currentCompanyId: string | null = null;
  
  async initForCompany(companyId: string) {
    this.currentCompanyId = companyId;
    
    // Initialize database with company prefix
    await this.storage.create({
      name: `foodprep_${companyId}`,
      driverOrder: ['indexeddb', 'sqlite', 'websql']
    });
  }
  
  // All storage operations are scoped to the current company
  async getItem(key: string): Promise<any> {
    if (!this.currentCompanyId) {
      throw new Error('No company context');
    }
    
    return this.storage.get(`${this.currentCompanyId}:${key}`);
  }
  
  async setItem(key: string, value: any): Promise<void> {
    if (!this.currentCompanyId) {
      throw new Error('No company context');
    }
    
    await this.storage.set(`${this.currentCompanyId}:${key}`, value);
  }
}
```

## Sync Service with Tenant Context

```typescript
// User sync service with tenant context
export class UserSyncService {
  constructor(
    private tenantService: TenantService,
    private http: HttpClient,
    private authService: AuthService
  ) {}
  
  async syncUsers() {
    const companyId = this.tenantService.getCurrentCompanyId();
    const locationId = this.tenantService.getCurrentLocationId();
    
    if (!companyId) {
      throw new Error('Company context required for sync');
    }
    
    // Build URL with tenant context
    let url = `${this.apiUrl}/company/${companyId}/users/sync`;
    
    // Add location filter if applicable
    if (locationId) {
      url += `?locationId=${locationId}`;
    }
    
    // Get auth headers (which include tenant headers via interceptor)
    const headers = await this.authService.getAuthHeaders();
    
    // Perform sync with proper tenant context
    return this.http.get(url, { headers }).toPromise();
  }
}
```

## Development Considerations

### 1. Clear Tenant Identification in UI

The active company and location should be clearly displayed in the app:

```html
<!-- Example tenant indicator in app header -->
<ion-header>
  <ion-toolbar>
    <ion-title>
      <span class="company-name">{{ currentCompany?.name }}</span>
      <span class="location-badge" *ngIf="currentLocation">
        {{ currentLocation.name }}
      </span>
    </ion-title>
    
    <!-- Company switcher for multi-company users -->
    <ion-buttons slot="end" *ngIf="userHasMultipleCompanies">
      <ion-button (click)="showCompanySwitcher()">
        <ion-icon name="swap-horizontal"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
```

### 2. Tenant-Aware Log System

All logging should include tenant context:

```typescript
// Logging service with tenant context
export class LoggingService {
  constructor(private tenantService: TenantService) {}
  
  log(level: 'info' | 'warn' | 'error', message: string, data?: any) {
    const companyId = this.tenantService.getCurrentCompanyId();
    const locationId = this.tenantService.getCurrentLocationId();
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      context: {
        companyId,
        locationId,
        userId: this.authService.getCurrentUserId()
      }
    };
    
    // Log locally
    console[level](logEntry);
    
    // Send to server if needed
    this.sendLogToServer(logEntry);
  }
}
```

### 3. Tenant Switching

Handle tenant switching gracefully:

```typescript
// Company switching service
async switchCompany(newCompanyId: string) {
  // 1. Sync pending changes for current company
  await this.syncService.pushPendingChanges();
  
  // 2. Store current state
  await this.stateService.persistCurrentState();
  
  // 3. Log out from current company
  await this.authService.clearCurrentSession();
  
  // 4. Initialize for new company
  await this.tenantService.selectCompany(newCompanyId);
  
  // 5. Re-authenticate with new company context
  await this.authService.reauthenticate();
  
  // 6. Sync data for new company
  await this.syncService.performFullSync();
}
```

## Security Considerations

### 1. Tenant Data Leakage Prevention

- Consistent use of tenant filtering in all queries
- Middleware validation of tenant context
- Proper validation in sync endpoints

### 2. Tenant Validation in Authentication

```typescript
// Tenant validation during login
async validateLogin(email: string, password: string, companyId: string) {
  // First find user by email
  const user = await this.userModel.findOne({ email });
  
  if (!user) {
    throw new Error('User not found');
  }
  
  // Validate password
  const isValid = await this.passwordService.verify(password, user.passwordHash);
  
  if (!isValid) {
    throw new Error('Invalid credentials');
  }
  
  // Validate company access
  if (user.companyId.toString() !== companyId) {
    throw new Error('User does not have access to this company');
  }
  
  // If all validations pass, generate token with tenant context
  return this.generateToken(user, companyId);
}
```

### 3. Cross-Tenant Request Prevention

```typescript
// API route with explicit tenant validation
app.get('/api/company/:companyId/users', async (req, res) => {
  const { companyId } = req.params;
  const userCompanyId = req.user.companyId;
  
  // Strict equality check
  if (companyId !== userCompanyId) {
    return res.status(403).json({ error: 'Cross-tenant access denied' });
  }
  
  // Proceed with request
});
```

## Continued Development Guidelines

### 1. API Design Guidelines

Follow these patterns for all new API endpoints:

- Include company ID in route path: `/api/company/:companyId/resource`
- Validate company access in all endpoints
- Include location filtering where appropriate
- Document tenant context requirements

### 2. Database Schema Guidelines

For all new models:

- Include `companyId` as a required field
- Add appropriate tenant-based indexes
- Consider adding `locationId` where appropriate
- Implement appropriate tenant isolation in queries

### 3. Client-Side Guidelines

For Ionic app development:

- Always validate active tenant context before operations
- Clear cache when switching tenants
- Include tenant headers in all API calls
- Implement proper error handling for tenant access issues

### 4. Testing Guidelines

For multi-tenant testing:

- Test with multiple companies in the same database
- Verify data isolation between tenants
- Test tenant switching
- Verify proper error messages for cross-tenant access attempts