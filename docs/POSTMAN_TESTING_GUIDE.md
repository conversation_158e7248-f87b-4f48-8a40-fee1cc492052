# Testing FoodPrepAI APIs with Postman and Curl

This guide provides step-by-step instructions for testing the POS authentication and inventory APIs using Postman.

## JWT Token Authentication (Web App)

The web application uses JWT tokens stored in cookies for authentication. These tokens contain the following key fields:

- `id`: The user's MongoDB ObjectId
- `email`: The user's email address
- `role`: The user's role in the company ('owner', 'admin', 'manager', etc.)
- `companyId`: The MongoDB ObjectId of the user's company
- `userType`: The type of user ('company_user' or 'superuser')

For testing, you can use the test API key authentication to generate valid JWT tokens.

## Prerequisites

1. Install [Postman](https://www.postman.com/downloads/)
2. Get valid test credentials:
   - `companyId` - A valid MongoDB ObjectId for a company
   - `locationId` - A valid MongoDB ObjectId for a location
   - `userId` - A valid MongoDB ObjectId for a user with `canUseIonicApp: true`
   - `testApiKey` - Your test API key for authentication (`81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb`)

## Authentication Methods

There are two ways to authenticate with the FoodPrepAI API when testing:

### 1. Using Test API Key for Direct Requests

For simple API tests, you can use the test API key directly:

```
Header: x-test-api-key: 81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb
Header: company-id: 67682466d436c5f697693330
```

### 2. Generate a JWT Token from the API Key

For endpoints that require more complex authentication with user context:

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/auth/test-token`
- Headers:
  ```
  x-test-api-key: 81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb
  Content-Type: application/json
  ```
- Body:
  ```json
  {
    "companyId": "67682466d436c5f697693330"
  }
  ```

**Response:** (Example)
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "67682464d436c5f69769332d",
    "name": "Test Owner",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

Then use the token in subsequent requests:
```
Header: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Header: company-id: 67682466d436c5f697693330
```

## Testing Inventory Management APIs

The inventory management API can now be tested using the test API key. These APIs are designed to sync inventory between the central system and branch locations as described in the inventory management pattern.

### Inventory Movement API

This endpoint processes inventory transactions for multiple types of movements including sales, wastage, and adjustments.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/company/{companyId}/inventory/movement`
- Headers:
  ```
  x-test-api-key: 81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb
  Content-Type: application/json
  company-id: 67682466d436c5f697693330
  ```
- Body (Sale Transaction):
  ```json
  {
    "locationId": "67697ac879a92509b1f9f245",
    "transactionType": "SALE",
    "items": [
      {
        "itemId": "YOUR_INVENTORY_ITEM_ID",
        "itemType": "INGREDIENT",
        "quantity": 2,
        "unitId": "YOUR_UNIT_ID",
        "menuItemId": "YOUR_MENU_ITEM_ID"
      }
    ],
    "timestamp": "2025-03-30T11:00:00.000Z",
    "salesId": "POS123456"
  }
  ```

- Body (Wastage Transaction):
  ```json
  {
    "locationId": "67697ac879a92509b1f9f245",
    "transactionType": "WASTAGE",
    "items": [
      {
        "itemId": "YOUR_INVENTORY_ITEM_ID",
        "itemType": "INGREDIENT",
        "quantity": 1.5,
        "unitId": "YOUR_UNIT_ID",
        "reason": "Expired"
      }
    ],
    "timestamp": "2025-03-30T11:30:00.000Z"
  }
  ```
  
  > Note: You'll need valid IDs for inventory items and units. You may need to create these first if they don't exist.

- Body (Adjustment Transaction):
  ```json
  {
    "locationId": "67697ac879a92509b1f9f245",
    "transactionType": "ADJUSTMENT",
    "items": [
      {
        "itemId": "YOUR_INVENTORY_ITEM_ID",
        "itemType": "INGREDIENT",
        "quantity": 10,
        "unitId": "YOUR_UNIT_ID",
        "reason": "Stock count correction"
      }
    ],
    "timestamp": "2025-03-30T12:00:00.000Z"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "transactionId": "67e926c150770d62cbbde192",
  "message": "Inventory transaction processed successfully",
  "updatedItems": [
    {
      "itemId": "67e91234d436c5f697693340",
      "currentStock": 18.5,
      "previousStock": 20
    }
  ]
}
```

## Testing Authentication Endpoints

### 1. Company Token Authentication

This endpoint retrieves a company-level token for initial authentication.

**Request:**
- Method: `GET`
- URL: `http://localhost:3000/api/pos/company/{companyId}/token`
- Headers:
  ```
  X-API-Key: test_api_key_1
  ```

**Response:** (Example)
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "message": "Company token generated successfully"
}
```

### 2. Set PIN (First-time setup)

This endpoint is used to set a user's PIN for the first time.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/pos/auth/set-pin`
- Headers:
  ```
  X-API-Key: test_api_key_1
  Content-Type: application/json
  ```
- Body:
  ```json
  {
    "userId": "user_mongodb_id",
    "companyId": "company_mongodb_id",
    "pin": "123456"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "PIN set successfully",
  "timestamp": "2025-03-27T10:28:00.000Z"
}
```

### 3. Verify PIN

This endpoint allows you to verify a PIN without logging in.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/pos/auth/verify-pin`
- Headers:
  ```
  X-API-Key: test_api_key_1
  Content-Type: application/json
  ```
- Body:
  ```json
  {
    "userId": "user_mongodb_id",
    "companyId": "company_mongodb_id",
    "pin": "123456"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "pinValid": true,
  "message": "PIN is valid",
  "timestamp": "2025-03-27T10:29:00.000Z"
}
```

### 4. PIN Login

This endpoint authenticates a user with their PIN and returns a JWT token.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/pos/auth/pin-login`
- Headers:
  ```
  X-API-Key: test_api_key_1
  Content-Type: application/json
  ```
- Body:
  ```json
  {
    "userId": "user_mongodb_id",
    "companyId": "company_mongodb_id",
    "pin": "123456"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_mongodb_id",
    "displayName": "John Doe",
    "email": "<EMAIL>",
    "role": "pos_user",
    "locationIds": ["location_id_1", "location_id_2"],
    "posSettings": {}
  },
  "timestamp": "2025-03-27T10:30:00.000Z"
}
```

### 5. Reset PIN

This endpoint allows a user to change their PIN.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/pos/auth/reset-pin`
- Headers:
  ```
  X-API-Key: test_api_key_1
  Content-Type: application/json
  ```
- Body:
  ```json
  {
    "userId": "user_mongodb_id",
    "companyId": "company_mongodb_id",
    "oldPin": "123456",
    "newPin": "654321"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "PIN reset successfully",
  "timestamp": "2025-03-27T10:31:00.000Z"
}
```

## Testing Web App Endpoints with Test API Keys (Recommended)

For simplified testing with Postman, you can now use our Test API Key authentication method which avoids the need to extract cookies from the browser.

### 1. Generate a Test API Key

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/company/{companyId}/test-api-keys`
- Headers:
  ```
  Content-Type: application/json
  Cookie: auth-token={cookie_value_from_browser}
  company-id: {companyId}
  ```
- Body:
  ```json
  {
    "userId": "{userId}",
    "role": "admin",
    "description": "Postman testing key",
    "expiryDays": 30
  }
  ```

**Response:** (Example)
```json
{
  "id": "api_key_id",
  "key": "f3d8a76e5b2c1d9...",
  "expiresAt": "2025-04-29T12:04:31.000Z"
}
```

**Important:** Save this key immediately as it won't be displayed again.

### 2. Set Up a Postman Environment

Create an environment in Postman with the following variables:

- `baseUrl`: `http://localhost:3000` (or your production URL)
- `companyId`: Your company ID
- `testApiKey`: The API key generated in step 1

### 3. Create a Pre-request Script for Your Collection

In your Postman collection settings, add this pre-request script:

```javascript
// Only run this script if we have a test API key and we don't have a valid token yet
if (pm.variables.get("testApiKey") && (!pm.variables.get("tempToken") || pm.variables.get("tokenExpiry") < new Date().getTime())) {
  pm.sendRequest({
      url: pm.variables.get("baseUrl") + "/api/auth/test-token",
      method: 'POST',
      header: {
          'Content-Type': 'application/json',
          'x-test-api-key': pm.variables.get("testApiKey")
      },
      body: {
          mode: 'raw',
          raw: JSON.stringify({
              companyId: pm.variables.get("companyId")
          })
      }
  }, function (err, res) {
      if (!err && res.code === 200) {
          const responseData = res.json();
          // Set token with 55 minute expiry (token lasts 1 hour)
          pm.variables.set("tempToken", responseData.token);
          pm.variables.set("tokenExpiry", new Date().getTime() + (55 * 60 * 1000));
      }
  });
}
```

### 4. Use in Your Requests

For all authenticated requests, use these headers:

```
Content-Type: application/json
company-id: {{companyId}}
Authorization: Bearer {{tempToken}}
```

**Benefits:**
- No need to extract cookies from browser
- Works across Postman environments
- Tokens automatically refresh when needed
- Can be used for automated testing

## Alternative: Manual Cookie Authentication

If you prefer not to use API keys, you can still use the traditional cookie method:

### 1. Extract Authentication Cookies from Browser

1. Log in to the web application in your browser (Chrome, Firefox, etc.)
2. Open Developer Tools (F12 or Right-click → Inspect)
3. Go to the Application tab (Chrome) or Storage tab (Firefox)
4. Under Cookies, find the domain for your application
5. Look for the `auth-token` cookie
6. Copy the value of this cookie

### 2. Configure Postman for Cookie Authentication

For requests to authenticated web app endpoints, set the following headers:

```
Content-Type: application/json
company-id: {companyId}
Cookie: auth-token={cookie_value_from_browser}
```

**Important Notes:**
- The `company-id` header MUST match the company ID in the URL
- Do not modify the cookie format; use it exactly as copied from the browser
- Cookie authentication is tied to user sessions and will expire

## Testing Inventory Count Endpoints with Curl

### Basic curl command structure

```bash
# Login to get the auth cookie (manually save the cookie value)
curl -X POST http://localhost:3000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}' \
  -v  # Use -v to see the Set-Cookie headers in the response

# Format for authenticated requests
curl -X METHOD http://localhost:3000/api/company/{companyId}/endpoint \
  -H "Content-Type: application/json" \
  -H "company-id: {companyId}" \
  -H "Cookie: auth-token={cookie_value}" \
  -d '{"key":"value"}'
```

### 1. Start Inventory Count

```bash
curl -X POST http://localhost:3000/api/company/67682466d436c5f697693330/inventory-count/start \
  -H "Content-Type: application/json" \
  -H "company-id: 67682466d436c5f697693330" \
  -H "Cookie: auth-token=your_auth_token_cookie" \
  -d '{"locationId":"67682466d436c5f697693331","notes":"Monthly inventory count"}'
```

### 2. Update Inventory Count

```bash
curl -X PUT http://localhost:3000/api/company/67682466d436c5f697693330/inventory-count/{countId}/update \
  -H "Content-Type: application/json" \
  -H "company-id: 67682466d436c5f697693330" \
  -H "Cookie: auth-token=your_auth_token_cookie" \
  -d '{"items":[{"itemId":"item-id-1","countedStock":42},{"itemId":"item-id-2","countedStock":18.5}]}'
```

### 3. Submit Inventory Count

```bash
curl -X POST http://localhost:3000/api/company/67682466d436c5f697693330/inventory-count/{countId}/submit \
  -H "Content-Type: application/json" \
  -H "company-id: 67682466d436c5f697693330" \
  -H "Cookie: auth-token=your_auth_token_cookie" \
  -d '{"notes":"Count completed"}'
```

### 4. Approve/Reject Inventory Count

```bash
curl -X POST http://localhost:3000/api/company/67682466d436c5f697693330/inventory-count/{countId}/review \
  -H "Content-Type: application/json" \
  -H "company-id: 67682466d436c5f697693330" \
  -H "Cookie: auth-token=your_auth_token_cookie" \
  -d '{"action":"APPROVE","notes":"Approved"}'
```

## Testing Inventory Count Endpoints with Postman

### 1. Start Inventory Count

This endpoint initiates a new inventory count for a location.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/company/{companyId}/inventory-count/start`
- Headers:
  ```
  Content-Type: application/json
  company-id: {companyId}
  Cookie: auth-token={cookie_value_from_browser}
  ```
- Body:
  ```json
  {
    "locationId": "{locationId}",
    "notes": "Monthly inventory count",
    "categoryFilter": "Optional category",
    "itemTypeFilter": "RECIPE" // Optional: 'RECIPE', 'INGREDIENT', or 'ALL'
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "Inventory count started successfully",
  "countId": "inventory_count_id"
}
```

### 2. Update Inventory Count

This endpoint updates the counted quantities for an in-progress count.

**Request:**
- Method: `PUT`
- URL: `http://localhost:3000/api/company/{companyId}/inventory-count/{countId}/update`
- Headers:
  ```
  Content-Type: application/json
  company-id: {companyId}
  Cookie: auth-token={cookie_value_from_browser}
  ```
- Body:
  ```json
  {
    "items": [
      {
        "itemId": "{itemId}",
        "countedStock": 42
      },
      {
        "itemId": "{anotherItemId}",
        "countedStock": 18.5
      }
    ]
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "Inventory count updated successfully"
}
```

### 3. Submit Inventory Count

This endpoint completes an inventory count and prepares it for approval.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/company/{companyId}/inventory-count/{countId}/submit`
- Headers:
  ```
  Content-Type: application/json
  company-id: {companyId}
  Cookie: auth-token={cookie_value_from_browser}
  ```
- Body:
  ```json
  {
    "notes": "Count completed with minor discrepancies"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "Inventory count submitted successfully"
}
```

### 4. Approve/Reject Inventory Count

This endpoint allows managers/owners to approve or reject a submitted count.

**Request:**
- Method: `POST`
- URL: `http://localhost:3000/api/company/{companyId}/inventory-count/{countId}/review`
- Headers:
  ```
  Content-Type: application/json
  company-id: {companyId}
  Cookie: auth-token={cookie_value_from_browser}
  ```
- Body:
  ```json
  {
    "action": "APPROVE", // or "REJECT"
    "notes": "Approved with minor adjustments"
  }
  ```

**Response:** (Example)
```json
{
  "success": true,
  "message": "Inventory count approved successfully"
}
```

## Testing Inventory Endpoints

### 6. Get Inventory Items

This endpoint retrieves inventory items for a specific location.

**Request:**
- Method: `GET`
- URL: `http://localhost:3000/api/company/{companyId}/location/{locationId}/inventory`
- Query Parameters (all optional):
  - `page=1`
  - `limit=20`
  - `search=chicken`
  - `itemType=RECIPE`
  - `category=Protein`
  - `belowReorder=true`
  - `includeSellingOptions=true`
- Headers:
  ```
  Authorization: Bearer {jwt_token_from_pin_login}
  company-id: {companyId}
  ```

**Response:** (Example)
```json
{
  "data": [
    {
      "_id": "inventory_item_id",
      "itemId": {
        "_id": "recipe_id",
        "name": "Chicken Teriyaki"
      },
      "itemType": "RECIPE",
      "currentStock": 8.5,
      "parLevel": 20,
      "reorderPoint": 10,
      "baseUomId": {
        "_id": "uom_id",
        "name": "Kilograms",
        "shortCode": "kg"
      },
      "name": "Chicken Teriyaki",
      "description": "Grilled chicken with teriyaki sauce",
      "category": "Protein",
      "belowReorderPoint": true,
      "isActive": true,
      "isLocked": false,
      "itemDetails": {
        "_id": "recipe_id",
        "name": "Chicken Teriyaki",
        "description": "Grilled chicken with teriyaki sauce",
        "category": "Protein",
        "yield": 1,
        "baseYieldUOM": {
          "_id": "uom_id",
          "name": "Kilograms",
          "shortCode": "kg"
        },
        "stockable": true,
        "canBeSold": true,
        "sellingDetails": [
          {
            "_id": "selling_detail_id",
            "unitOfSelling": {
              "_id": "uom_id",
              "name": "Portion",
              "shortCode": "portion"
            },
            "conversionFactor": 0.25
          }
        ]
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

## Setting Up a Postman Collection

For easier testing, you can set up a Postman collection with environment variables:

1. Create a new collection named "FoodPrepAI POS API"
2. Create a new environment with these variables:
   - `baseUrl`: `http://localhost:3000`
   - `apiKey`: `test_api_key_1`
   - `companyId`: Your company ID
   - `locationId`: Your location ID
   - `userId`: Your user ID
   - `pin`: Your PIN code
   - `companyToken`: (Leave empty, will be populated after authentication)
   - `userToken`: (Leave empty, will be populated after PIN login)

3. Set up a pre-request script for the inventory endpoints to automatically get tokens:

```javascript
// Pre-request Script for Inventory Endpoints
const getToken = {
    url: pm.environment.get("baseUrl") + "/api/pos/auth/pin-login",
    method: 'POST',
    header: {
        'Content-Type': 'application/json',
        'X-API-Key': pm.environment.get("apiKey")
    },
    body: {
        mode: 'raw',
        raw: JSON.stringify({
            userId: pm.environment.get("userId"),
            companyId: pm.environment.get("companyId"),
            pin: pm.environment.get("pin")
        })
    }
};

pm.sendRequest(getToken, function (err, res) {
    if (err) {
        console.error(err);
    } else {
        const responseJson = res.json();
        if (responseJson.success && responseJson.token) {
            pm.environment.set("userToken", responseJson.token);
            console.log("Token refreshed successfully");
        } else {
            console.error("Failed to get token:", responseJson.message);
        }
    }
});
```

4. Use environment variables in your requests:
   - URL: `{{baseUrl}}/api/company/{{companyId}}/location/{{locationId}}/inventory`
   - Headers: 
     - `X-API-Key: {{apiKey}}`
     - `Authorization: Bearer {{userToken}}`
     - `company-id: {{companyId}}`

## Troubleshooting

If you encounter issues:

1. **Authentication Errors (401)**
   - Verify API key is correct
   - Check that companyId, userId are valid
   - Ensure PIN is set correctly

2. **Permission Errors (403)**
   - Verify user has `canUseIonicApp: true`
   - Check that user belongs to the specified company
   - Ensure user has access to the specified location

3. **Not Found Errors (404)**
   - Double-check all IDs are valid MongoDB ObjectIds
   - Verify the endpoint paths are correct

4. **Server Errors (500)**
   - Check server logs for detailed error information
   - Ensure MongoDB connection is working

## Recommended Testing Flow

1. Get company token (for initial setup)
2. Set PIN (first time only)
3. Login with PIN
4. Use the token to access inventory endpoints
5. Test PIN verification or reset as needed
