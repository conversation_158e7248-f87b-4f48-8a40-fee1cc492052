# Component Migration Guide

This guide provides step-by-step instructions for migrating existing components from both the FoodPrepAI Next.js app and IonicPOS mobile app to the unified component library.

## Migration Strategy

### Phase 1: Foundation Components (Completed)
- ✅ Button
- ✅ Table/DataTable  
- ✅ Modal/Dialog
- ✅ Design tokens system
- ✅ Platform detection

### Phase 2: Form Components (Next)
- [ ] Input/FormField
- [ ] Select/Dropdown
- [ ] Checkbox
- [ ] Radio buttons
- [ ] Form validation

### Phase 3: Business Components
- [ ] LocationSelector
- [ ] CompanySelector  
- [ ] UserManagement
- [ ] InventoryTable
- [ ] OrderComponents

### Phase 4: Layout Components
- [ ] Card
- [ ] Layout containers
- [ ] Navigation components
- [ ] Sidebar/Menu

## Component-by-Component Migration

### 1. Button Component ✅

**Current State:**
- **FoodPrepAI**: Uses shadcn/ui Button with Radix primitives
- **IonicPOS**: Uses custom Button wrapper around IonButton

**Migration:** Completed - unified Button component available

**Usage Update:**
```typescript
// Before (FoodPrepAI)
import { Button } from '@/components/ui/button';

// Before (IonicPOS)  
import Button from '@/components/shared/Button';

// After (Both apps)
import { Button } from '@foodprepai/ui-components';
```

### 2. Table/DataTable Component ✅

**Current State:**
- **FoodPrepAI**: Uses shadcn/ui Table + custom DataTable component
- **IonicPOS**: Uses custom Table component with IonList

**Migration:** Completed - unified Table component available

**Key Changes:**
- Unified column definition format
- Consistent sorting and selection APIs
- Mobile-optimized card layout automatically applied

**Usage Update:**
```typescript
// Before (FoodPrepAI)
import { DataTable } from '@/components/DataTable';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// Before (IonicPOS)
import Table from '@/components/shared/Table';

// After (Both apps)
import { Table } from '@foodprepai/ui-components';

// Unified column format
const columns = [
  { 
    key: 'name', 
    header: 'Name', 
    sortable: true,
    render: (value, item) => <strong>{value}</strong>
  },
  { key: 'email', header: 'Email' },
  { key: 'status', header: 'Status' }
];
```

### 3. Modal/Dialog Component ✅

**Current State:**
- **FoodPrepAI**: Uses Radix Dialog with shadcn/ui styling
- **IonicPOS**: Custom modal implementations

**Migration:** Completed - unified Modal component available

**Usage Update:**
```typescript
// Before (FoodPrepAI)
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Before (IonicPOS)
import { IonModal, IonHeader, IonToolbar, IonTitle, IonContent } from '@ionic/react';

// After (Both apps)
import { Modal } from '@foodprepai/ui-components';

<Modal 
  isOpen={isOpen} 
  onClose={handleClose}
  title="My Modal"
  size="lg"
>
  <p>Modal content</p>
</Modal>
```

## Migrating Business Components

### LocationSelector

**Current Implementation Analysis:**
```typescript
// /Users/<USER>/Documents/GitHub/foodprepai/src/components/LocationSelector.tsx
// Uses: Select component, MapPin icon, company context
```

**Migration Plan:**
1. Extract to `packages/ui-components/src/LocationSelector/`
2. Create unified Select component first (dependency)
3. Abstract company context dependency
4. Support both single location display and multi-location selector

**Proposed API:**
```typescript
interface LocationSelectorProps {
  locations: Location[];
  selectedLocation?: string;
  onLocationChange: (locationId: string) => void;
  placeholder?: string;
  showIcon?: boolean;
  singleLocationDisplay?: boolean;
}
```

### CompanySelector

**Migration Plan:**
1. Similar to LocationSelector
2. Abstract company data fetching
3. Support company switching workflows

### UserManagement Components

**Current Implementation Analysis:**
```typescript
// /Users/<USER>/Documents/GitHub/foodprepai/src/components/admin/UserManagement.tsx
// Complex component with table, modals, forms
```

**Migration Strategy:**
1. Break into smaller components:
   - UserTable (using unified Table)
   - UserForm (using unified form components)
   - UserModal (using unified Modal)
2. Extract business logic to hooks
3. Make data source configurable

## Step-by-Step Migration Process

### Step 1: Component Analysis

For each component to migrate:

1. **Identify Dependencies:**
   ```bash
   # Find all imports
   grep -n "import" src/components/ComponentName.tsx
   ```

2. **List Platform-Specific Code:**
   - Web: Radix components, CSS classes, web-specific hooks
   - Mobile: Ionic components, mobile-specific gestures

3. **Extract Business Logic:**
   - API calls
   - State management
   - Data transformations

### Step 2: Create Unified Types

Add types to `packages/ui-core/src/types/index.ts`:

```typescript
export interface NewComponentProps extends BaseComponentProps {
  // Component-specific props
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  // ... other props
}
```

### Step 3: Implement Platform Versions

**Web Implementation:**
```typescript
// packages/ui-components/src/NewComponent/WebNewComponent.tsx
export const WebNewComponent: React.FC<NewComponentProps> = ({
  // ... props
}) => {
  // Use shadcn/ui components, Radix primitives
  return (
    <div className="web-specific-styles">
      {/* Web implementation */}
    </div>
  );
};
```

**Ionic Implementation:**
```typescript
// packages/ui-components/src/NewComponent/IonicNewComponent.tsx
export const IonicNewComponent: React.FC<NewComponentProps> = ({
  // ... props  
}) => {
  // Use Ionic components
  return (
    <IonItem>
      {/* Ionic implementation */}
    </IonItem>
  );
};
```

### Step 4: Create Unified Component

```typescript
// packages/ui-components/src/NewComponent/NewComponent.tsx
import { createPlatformComponent } from '@foodprepai/ui-core';
import { WebNewComponent } from './WebNewComponent';
import { IonicNewComponent } from './IonicNewComponent';

export const NewComponent = createPlatformComponent(
  WebNewComponent,
  IonicNewComponent,
  'NewComponent'
);
```

### Step 5: Update Exports

```typescript
// packages/ui-components/src/index.ts
export { NewComponent } from './NewComponent';
export type { NewComponentProps } from '@foodprepai/ui-core/types';
```

### Step 6: Update Applications

**Replace Imports:**
```typescript
// Find and replace across codebase
// Old: import { Component } from '@/components/Component';
// New: import { Component } from '@foodprepai/ui-components';
```

**Update Usage:**
```typescript
// Ensure props match new unified API
<NewComponent
  value={value}
  onChange={handleChange}
  // Remove platform-specific props
/>
```

## Handling Complex Migrations

### Components with Heavy Business Logic

For components like UserManagement:

1. **Extract Hooks:**
   ```typescript
   // packages/shared-utils/src/hooks/useUsers.ts
   export function useUsers() {
     // User management logic
     return { users, loading, createUser, updateUser, deleteUser };
   }
   ```

2. **Create Composable Sub-components:**
   ```typescript
   // Break large component into smaller pieces
   export function UserManagement() {
     const userState = useUsers();
     
     return (
       <div>
         <UserFilters />
         <UserTable {...userState} />
         <UserModal />
       </div>
     );
   }
   ```

### Components with Different UX Patterns

Some components may need significantly different UX on mobile vs web:

1. **Navigation Components:**
   - Web: Horizontal tabs
   - Mobile: Bottom tab bar or sheet

2. **Data Input:**
   - Web: Keyboard-optimized forms
   - Mobile: Touch-optimized with native inputs

**Strategy:** Create different interaction patterns while maintaining same data flow.

## Testing Migrated Components

### Unit Tests
```typescript
describe('NewComponent', () => {
  beforeEach(() => {
    // Reset platform config
    resetPlatformConfig();
  });

  it('renders web version', () => {
    setPlatformConfig('web');
    render(<NewComponent value="test" />);
    // Test web-specific behavior
  });

  it('renders ionic version', () => {
    setPlatformConfig('ionic');
    render(<NewComponent value="test" />);
    // Test ionic-specific behavior
  });
});
```

### Integration Tests
```typescript
describe('NewComponent Integration', () => {
  it('works with form submission', () => {
    const handleSubmit = jest.fn();
    render(
      <form onSubmit={handleSubmit}>
        <NewComponent name="field" />
        <Button type="submit">Submit</Button>
      </form>
    );
    // Test form integration
  });
});
```

## Migration Checklist

For each component migration:

- [ ] Component analysis completed
- [ ] Dependencies identified
- [ ] Types added to ui-core
- [ ] Web implementation created
- [ ] Ionic implementation created
- [ ] Unified component factory created
- [ ] Exports updated
- [ ] Unit tests written
- [ ] Integration tests written
- [ ] Documentation updated
- [ ] Old imports replaced
- [ ] Manual testing on both platforms
- [ ] Performance regression testing
- [ ] Accessibility testing

## Common Migration Challenges

### 1. State Management Integration

**Problem:** Components tightly coupled to specific state management (Redux, Context)

**Solution:** 
- Extract state logic to custom hooks
- Make components accept data as props
- Create adapters for different state sources

### 2. Platform-Specific Features

**Problem:** Components use platform-specific APIs

**Solution:**
- Create abstraction layer
- Use platform detection to conditionally enable features
- Graceful degradation for unsupported features

### 3. Styling Conflicts

**Problem:** CSS conflicts between platforms

**Solution:**
- Use design tokens consistently
- Scope styles properly
- Test styling isolation

### 4. Performance Differences

**Problem:** Different performance characteristics

**Solution:**
- Profile both implementations
- Use platform-specific optimizations
- Monitor bundle sizes

## Best Practices

1. **Start Small:** Migrate simple components first
2. **Test Thoroughly:** Both platforms, all use cases
3. **Document Changes:** Update usage examples
4. **Gradual Rollout:** Migrate incrementally, not all at once
5. **Monitor Impact:** Watch for regressions after migration
6. **Team Communication:** Keep team informed of API changes

## Getting Help

1. **Reference Implementation:** Look at Button, Table, Modal for patterns
2. **Architecture Guide:** Review `/docs/COMPONENT_LIBRARY_ARCHITECTURE.md`
3. **Team Discussion:** Discuss complex migrations with the team
4. **Testing:** Use both web and mobile test environments

This migration approach ensures a smooth transition while maintaining component quality and developer experience across both platforms.