# User Sync Testing Guide

This document outlines a comprehensive testing plan for the user synchronization system between the main FoodPrepAI platform and IonicPOS.

## Prerequisites

Before beginning testing, ensure you have:

1. **Main App Environment**: A development/staging environment for the main platform
2. **IonicPOS Environment**: A development build of the Ionic app
3. **Test Users**: Sample user accounts with various roles and permissions
4. **Network Control**: Ability to simulate network disruptions

## Test Scenarios

### 1. Basic Synchronization Tests

#### 1.1 Initial Sync

**Objective**: Verify that users from the main platform successfully sync to the Ionic app on first run.

**Steps**:
1. Create several test users in the main platform with different roles
2. Install and launch the Ionic app for the first time
3. Log in as an administrator
4. Trigger a full sync

**Expected Outcome**:
- All users should appear in the Ionic app
- User roles, permissions, and attributes should match the main platform

#### 1.2 Incremental Sync

**Objective**: Verify that only changed users are synchronized after the initial sync.

**Steps**:
1. Note the last sync timestamp in the Ionic app
2. Modify a subset of users in the main platform
3. Trigger a sync in the Ionic app

**Expected Outcome**:
- Only modified users should be updated
- Sync operation should complete faster than the initial sync
- The sync timestamp should be updated

### 2. User CRUD Operations

#### 2.1 Create User

**Objective**: Test synchronization of newly created users.

**Steps**:
1. Create a new user in the main platform
2. Trigger sync in the Ionic app

**Expected Outcome**:
- New user should appear in the Ionic app with correct attributes

#### 2.2 Update User

**Objective**: Test synchronization of user updates.

**Steps**:
1. Update an existing user's attributes in the main platform (name, email, role, etc.)
2. Trigger sync in the Ionic app

**Expected Outcome**:
- User attributes should be updated in the Ionic app

#### 2.3 Delete User

**Objective**: Test synchronization of user deletions.

**Steps**:
1. Delete a user in the main platform
2. Trigger sync in the Ionic app

**Expected Outcome**:
- User should be removed from the Ionic app or marked as deleted

### 3. Authentication Tests

#### 3.1 JWT Authentication

**Objective**: Verify JWT-based authentication works properly.

**Steps**:
1. Log in to the Ionic app with valid credentials
2. Examine the JWT token stored in the app
3. Use the app's features that require authentication

**Expected Outcome**:
- JWT token should be properly stored
- User should be able to access authorized features
- Token should include proper claims (role, permissions)

#### 3.2 PIN Authentication

**Objective**: Verify PIN-based authentication for POS users.

**Steps**:
1. Set up a PIN for a user in the main platform
2. Sync the user to the Ionic app
3. Attempt to log in using PIN-based authentication

**Expected Outcome**:
- User should be able to log in with the PIN
- Appropriate permissions should be applied
- Invalid PINs should be rejected

#### 3.3 Device Authentication

**Objective**: Verify device-specific authentication.

**Steps**:
1. Register a device in the system
2. Authenticate using device credentials
3. Test API access with device token

**Expected Outcome**:
- Device should receive a valid token
- Token should grant appropriate access
- Invalid device tokens should be rejected

### 4. Offline Operation Tests

#### 4.1 Sync Before Offline

**Objective**: Verify that data synchronized before going offline is accessible.

**Steps**:
1. Perform a full sync while online
2. Disconnect from the network
3. Attempt to use the app and access user data

**Expected Outcome**:
- All previously synced user data should be accessible
- Authentication should work in offline mode

#### 4.2 Offline Changes

**Objective**: Verify that changes made while offline are properly tracked.

**Steps**:
1. Disconnect from the network
2. Make changes to user data in the Ionic app
3. Reconnect to the network
4. Trigger a sync

**Expected Outcome**:
- Changes made offline should be marked as pending
- Upon reconnection, changes should sync to the main platform

### 5. Conflict Resolution Tests

#### 5.1 Concurrent Edits

**Objective**: Test how the system handles concurrent edits to the same user.

**Steps**:
1. Edit a user in the main platform
2. Without syncing, edit the same user in the Ionic app
3. Sync the changes

**Expected Outcome**:
- The system should follow the defined conflict resolution strategy
- Server changes should take precedence
- No data corruption should occur

#### 5.2 Conflicting Deletions

**Objective**: Test handling of users deleted in one system but modified in another.

**Steps**:
1. Delete a user in the main platform
2. Before syncing, modify the same user in the Ionic app
3. Sync the changes

**Expected Outcome**:
- The deletion should take precedence
- Appropriate error/warning should be shown for the failed update

### 6. Performance Tests

#### 6.1 Large User Set

**Objective**: Test sync performance with a large number of users.

**Steps**:
1. Create 1000+ users in the main platform
2. Perform initial and incremental syncs
3. Measure time and resource usage

**Expected Outcome**:
- Sync should complete within reasonable time
- App should remain responsive during sync
- Incremental sync should be significantly faster

#### 6.2 Pagination

**Objective**: Test that pagination works correctly for large data sets.

**Steps**:
1. Set a small page size in the app (e.g., 50 users)
2. Sync with 500+ users
3. Monitor network requests

**Expected Outcome**:
- Multiple paginated requests should be made
- All users should be properly synced
- Sync should be resumable if interrupted

### 7. Security Tests

#### 7.1 Permission Enforcement

**Objective**: Verify that permissions are properly enforced in the Ionic app.

**Steps**:
1. Create users with different permission sets
2. Sync to the Ionic app
3. Attempt to perform actions requiring specific permissions

**Expected Outcome**:
- Actions should be permitted/denied based on user permissions
- Permission changes in the main platform should be reflected after sync

#### 7.2 Data Protection

**Objective**: Verify that sensitive user data is properly protected.

**Steps**:
1. Examine the local storage of the Ionic app
2. Check how passwords and PINs are stored
3. Test token expiration and refresh

**Expected Outcome**:
- Passwords should never be stored in plaintext
- PINs should be securely hashed
- Expired tokens should require re-authentication

### 8. Multi-Tenant Tests

#### 8.1 Company Isolation

**Objective**: Verify that multi-tenant data isolation works correctly.

**Steps**:
1. Set up multiple companies in the main platform
2. Create users in each company
3. Log in to the Ionic app as users from different companies

**Expected Outcome**:
- Users should only see data from their own company
- Sync operations should only affect the current company's data

#### 8.2 Location Filtering

**Objective**: Test location-specific user filtering.

**Steps**:
1. Set up multiple locations within a company
2. Assign users to specific locations
3. Sync with location filtering

**Expected Outcome**:
- Only users for the selected location should be synced
- Location-specific permissions should be enforced

## Automated Testing Tools

### API Tests

Create automated API tests using tools like Postman or Jest:

```javascript
// Example Jest test for user sync endpoint
test('GET /api/company/:companyId/users/sync returns correct data structure', async () => {
  const response = await request(app)
    .get(`/api/company/${testCompanyId}/users/sync`)
    .set('Authorization', `Bearer ${adminToken}`)
    .set('company-id', testCompanyId);

  expect(response.status).toBe(200);
  expect(response.body).toHaveProperty('users');
  expect(response.body).toHaveProperty('deletedUserIds');
  expect(response.body).toHaveProperty('syncTimestamp');
});
```

### Ionic App Tests

Create Cypress tests for the Ionic app:

```javascript
// Example Cypress test for user sync
describe('User Sync', () => {
  beforeEach(() => {
    cy.login('<EMAIL>', 'password');
  });

  it('should sync users from the server', () => {
    // Force a sync
    cy.get('[data-cy=sync-button]').click();
    cy.get('[data-cy=sync-progress]').should('be.visible');
    
    // Wait for sync to complete
    cy.get('[data-cy=sync-complete]', { timeout: 10000 }).should('be.visible');
    
    // Verify users are displayed
    cy.get('[data-cy=user-list]').should('exist');
    cy.get('[data-cy=user-item]').should('have.length.gt', 0);
  });

  it('should apply user changes offline and sync when online', () => {
    // Go offline
    cy.goOffline();
    
    // Make a change
    cy.get('[data-cy=user-item]').first().click();
    cy.get('[data-cy=edit-button]').click();
    cy.get('[data-cy=display-name-input]').clear().type('Updated Offline');
    cy.get('[data-cy=save-button]').click();
    
    // Verify change is marked as pending
    cy.get('[data-cy=pending-changes-badge]').should('be.visible');
    
    // Go online and sync
    cy.goOnline();
    cy.get('[data-cy=sync-button]').click();
    
    // Verify pending changes are cleared
    cy.get('[data-cy=pending-changes-badge]').should('not.exist');
  });
});
```

## Troubleshooting Guide

### Common Issues and Resolutions

1. **Sync Not Working**
   - Check network connectivity
   - Verify authentication token validity
   - Check company ID in headers
   - Look for console errors

2. **Missing Users**
   - Verify company/location filters
   - Check user permissions
   - Ensure pagination is working correctly
   - Check for soft-deleted flags

3. **Authentication Failures**
   - Verify JWT expiration
   - Check token refresh logic
   - Confirm PIN hashing implementation
   - Verify server-side token validation

4. **Offline Mode Issues**
   - Verify local storage initialization
   - Check for storage quota limits
   - Verify IndexedDB/SQLite setup
   - Test offline detection mechanism

## Test Environment Setup

### Main Platform Setup

```bash
# Clone repository
git clone https://github.com/yourusername/foodprepai.git

# Install dependencies
cd foodprepai
npm install

# Set up development database
npm run dev:db:setup

# Create test users
npm run dev:seed:users

# Start the server
npm run dev
```

### Ionic App Setup

```bash
# Clone repository
git clone https://github.com/yourusername/foodprepai-ionic.git

# Install dependencies
cd foodprepai-ionic
npm install

# Set API URL to local server
cp env.example.ts env.ts
# Edit env.ts to point to local server

# Start the app
ionic serve
```

## Test Tracking Template

Use this template to track test results:

```
# User Sync Test Report

Date: YYYY-MM-DD
Tester: [Name]
App Version: [Version]
Server Version: [Version]

## Test Results

| Test ID | Description | Result | Notes |
|---------|-------------|--------|-------|
| 1.1     | Initial Sync | ✅/❌  |       |
| 1.2     | Incremental Sync | ✅/❌ |    |
| 2.1     | Create User | ✅/❌ |        |
| ...     | ... | ... |                  |

## Issues Found

1. [Issue description]
   - Severity: High/Medium/Low
   - Steps to reproduce: ...
   - Expected behavior: ...
   - Actual behavior: ...

## Recommendations

[Any recommendations for improvement]
```