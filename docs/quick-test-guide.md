# Quick Test Guide: User Synchronization

This guide provides step-by-step instructions for testing the user synchronization between your main FoodPrepAI application (running on http://localhost:3000) and the Ionic app (running on http://localhost:8101).

## Prerequisites

1. Main FoodPrepAI app running on http://localhost:3000
2. Ionic app running on http://localhost:8101
3. MongoDB database accessible by both applications
4. Admin account with full permissions

## Test 1: Initial User Sync

### On the Main App (http://localhost:3000):

1. Log in as an administrator
2. Navigate to user management (usually at `/admin/users` or similar path)
3. Create a new test user with the following details:
   - Email: `<EMAIL>`
   - Password: `Test1234!`
   - Role: `manager`
   - Assign to your test company
   - Assign any basic permissions

### On the Ionic App (http://localhost:8101):

1. Log in as administrator using the same admin credentials
2. Navigate to settings or sync management section
3. Look for a "Sync Users" or "Sync Data" button and tap it
4. Wait for the sync process to complete
5. Navigate to the users section or staff management
6. Verify that the new user `<EMAIL>` appears in the list
7. Check that the user's role and permissions match what was set in the main app

## Test 2: User Update Sync

### On the Main App:

1. Find the `<EMAIL>` user
2. Edit the user profile:
   - Change display name to "Sync Test Updated"
   - Update role to "admin"
   - Add an additional permission

### On the Ionic App:

1. Tap the sync button again to trigger an incremental sync
2. Navigate to the users section
3. Find the test user and verify that:
   - Display name has been updated to "Sync Test Updated"
   - Role has been changed to "admin"
   - The new permission is present

## Test 3: PIN-Based Authentication

### On the Main App:

1. Find the `<EMAIL>` user
2. Add a PIN code (e.g., "1234") to the user account
3. Save the changes

### On the Ionic App:

1. Perform a sync to get the latest user data
2. Log out of the admin account
3. On the login screen, look for a "PIN Login" option
4. Select the test user from the user list
5. Enter the PIN "1234"
6. Verify that:
   - Login succeeds
   - The user has access to appropriate screens based on their role
   - Permissions are properly applied

## Test 4: Offline Operation

### On the Ionic App:

1. Log in as the test user
2. Turn off network connectivity (either disable WiFi/data or use browser dev tools to simulate offline)
3. Try to access user profile and other screens
4. Verify that:
   - The app continues to function
   - User data is still accessible
   - A visual indicator shows that the app is in offline mode

## Test 5: Multi-Tenant Isolation

If you have multiple companies set up:

### On the Main App:

1. Create a second company named "Test Company 2"
2. Create a new user assigned to this second company:
   - Email: `<EMAIL>`
   - Password: `Test1234!`
   - Role: `manager`

### On the Ionic App:

1. Restore network connectivity
2. Log in as the administrator for the first company
3. Sync users
4. Verify that:
   - The `<EMAIL>` does NOT appear in the user list
   - Only users from the current company are visible

## Test 6: User Deletion Sync

### On the Main App:

1. Find the `<EMAIL>` user
2. Delete the user or mark them as inactive

### On the Ionic App:

1. Perform a sync
2. Navigate to the users section
3. Verify that:
   - The deleted user no longer appears or is marked as inactive

## Test 7: Conflict Resolution

### On the Ionic App:

1. Turn off network connectivity
2. Find another active user and edit their profile (e.g., change display name)
3. Make note of the changes

### On the Main App:

1. Find the same user
2. Make different changes to the same fields
3. Save the changes

### On the Ionic App:

1. Turn network connectivity back on
2. Perform a sync
3. Verify that:
   - The server changes take precedence
   - The user profile shows the changes made in the main app, not the Ionic app
   - A notification or log indicates the conflict was detected and resolved

## Test 8: Batch Operation

### On the Main App:

1. Create multiple users (at least 3) in quick succession
2. Modify some existing users

### On the Ionic App:

1. Perform a sync
2. Verify that:
   - All new users appear
   - All modifications are applied correctly
   - The sync completes in reasonable time

## Advanced Testing (Optional)

### Performance Testing:

1. Create 100+ user records in the main app
2. Time how long it takes to perform the initial sync on the Ionic app
3. Make changes to a subset of users (10-20)
4. Time how long an incremental sync takes

### Security Testing:

1. Attempt to access another company's data by manually modifying API requests
2. Verify that proper error messages are returned
3. Check that PINs are securely stored (not in plaintext)

## Troubleshooting Common Issues

### Sync Not Working:

1. Check console logs in both applications for errors
2. Verify network connectivity
3. Ensure authentication tokens are valid
4. Check that company ID headers are being sent correctly

### Authentication Issues:

1. Clear local storage/cookies in the Ionic app
2. Try re-authenticating with admin credentials
3. Verify JWT token format and expiration

### Missing User Data:

1. Check company ID and filters
2. Verify sync timestamps are being properly stored
3. Check API responses for pagination information

## Recording Test Results

Create a simple test report with:
- Date and time of testing
- Version of both applications
- Pass/fail status for each test
- Screenshots of key screens
- Any issues encountered and their resolution

Submit this report to the development team for analysis and issue resolution.