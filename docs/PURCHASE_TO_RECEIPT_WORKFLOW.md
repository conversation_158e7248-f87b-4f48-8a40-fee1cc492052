# Purchase to Receipt Workflow

## Overview

The FoodPrepAI system now implements a proper purchase-to-receipt workflow to track inventory items from order placement to physical receipt. This document explains how the system handles each stage of the process.

## Key Concepts

### 1. Pending Stock vs. Available Stock

The inventory system now tracks two types of stock for each item:

- **Current Stock (Available)**: Inventory that is physically available in the location and can be used immediately
- **Pending Stock**: Inventory that has been ordered but not yet physically received

### 2. Transaction Types

- **PURCHASE**: Records an order placed with a supplier
  - Increases `pendingStock`
  - Does NOT affect `currentStock`
  
- **RECEIVED**: Records the physical receipt of ordered items
  - Increases `currentStock`
  - Decreases `pendingStock`
  - Validates that sufficient `pendingStock` exists before allowing receipt

## Workflow

### Step 1: Place Purchase Order

When a purchase order is created:
- Create a PURCHASE transaction
- Increase the `pendingStock` for each item
- Record purchase details (supplier, invoice number, etc.)

```
Before:
  currentStock: 10
  pendingStock: 0

After PURCHASE of 5 units:
  currentStock: 10 (unchanged)
  pendingStock: 5 (increased)
```

### Step 2: Receive Goods

When ordered items are physically received:
- Create a RECEIVED transaction
- Move items from `pendingStock` to `currentStock`
- The system validates that sufficient `pendingStock` exists

```
Before:
  currentStock: 10
  pendingStock: 5

After RECEIVED of 5 units:
  currentStock: 15 (increased)
  pendingStock: 0 (decreased)
```

### Example: Partial Receipt

The system allows for partial receipts:

```
Before:
  currentStock: 10
  pendingStock: 5

After RECEIVED of 3 units:
  currentStock: 13 (increased by 3)
  pendingStock: 2 (decreased by 3)
```

## Validation Rules

1. **Cannot Receive More Than Pending**: When items are received, the quantity cannot exceed the pending stock.

2. **No Negative Stock**: Standard inventory rules still apply to prevent negative current stock.

## Benefits

This workflow provides several benefits:

1. **Accurate Inventory Planning**: Clearly distinguishes between what's on-hand vs. on-order
2. **Prevents Over-Receipt**: Ensures items can't be received without a corresponding purchase
3. **Support for Partial Deliveries**: Handles cases where suppliers deliver partial orders
4. **Better Financial Tracking**: Separates financial commitments from actual inventory

## API Usage

To use this workflow:

1. **Create a Purchase**:
   - Set transaction type to `PURCHASE`
   - Include supplier details and item quantities

2. **Receive Items**:
   - Set transaction type to `RECEIVED`
   - Reference the original purchase if applicable
   - Specify the received quantity (can be less than or equal to pending quantity)

## Data Model

The `BranchInventory` model has been updated with:

```typescript
pendingStock: {
  type: Number,
  required: true,
  default: 0
}
```

The `InventoryTransaction` model tracks both current and pending stock changes:

```typescript
previousStock: number;
newStock: number;
difference: number;
previousPendingStock?: number;
newPendingStock?: number;
pendingDifference?: number;
```
