# Ionic POS Authentication Guide

## Authentication Flow Overview

### 1. Login Page Design
- Simple, clean interface
- Input fields:
  - Company Code (text input)
  - Password (password input)
  - Login Button

### 2. Authentication Process

#### Step 1: Company Authentication
**Endpoint**: `/api/pos/company/authenticate`

**Request Payload**:
```typescript
{
  companyCode: string,
  password: string
}
```

**Response Scenarios**:
```typescript
// Successful Authentication
{
  success: true,
  data: {
    companyId: string,
    companyName: string
  },
  timestamp: string
}

// Failed Authentication
{
  success: false,
  message: string,
  timestamp: string
}
```

#### Offline vs Online Authentication Strategy

##### Online Authentication Flow
1. Send request to `/api/pos/company/authenticate`
2. Include `X-API-Key: test_api_key_1` header
3. Validate company code and password against server
4. If successful, store company information locally

##### Offline Authentication Flow
1. Check local encrypted storage for company credentials
2. Compare provided credentials with stored, hashed credentials
3. Validate without server connection

### 3. Offline Storage Considerations
- Use secure storage mechanisms (e.g., Ionic Secure Storage)
- Encrypt stored company and authentication data
- Store minimal necessary information:
  ```typescript
  interface OfflineCompanyAuth {
    companyId: string;
    companyName: string;
    companyCodeHash: string; // Securely hashed
    lastAuthenticatedAt: Date;
    locations: Location[]; // Cached locations
  }
  ```

### 4. Location Selection Process
**After Company Authentication**:
1. Fetch company locations
   - Endpoint: `/api/locations?companyId=<companyId>`
2. Display location selection screen
3. User chooses specific location
4. Initialize local database with location-specific data

### 5. Database Initialization
- Only initialize after:
  1. Company is authenticated
  2. Location is selected
- Steps:
  1. Create local SQLite/IndexedDB database
  2. Configure database schema
  3. Sync initial data from server
  4. Set up background sync mechanisms

### 6. Authentication Headers
```typescript
headers: {
  'X-API-Key': 'test_api_key_1',
  'X-Company-Id': '<companyId>',
  'X-Location-Id': '<locationId>'
}
```

### 7. Security Best Practices
- Use HTTPS for all communications
- Implement certificate pinning
- Encrypt sensitive local storage
- Implement automatic logout after inactivity
- Provide PIN/Biometric re-authentication

### 8. Error Handling
```typescript
enum AuthenticationErrors {
  INVALID_COMPANY_CODE = 'Invalid company code',
  INCORRECT_PASSWORD = 'Incorrect password',
  NETWORK_ERROR = 'No network connection',
  OFFLINE_AUTH_FAILED = 'Offline authentication failed'
}
```

### 9. Recommended Tech Stack
- Ionic Framework
- Capacitor
- NgRx (for state management)
- Ionic Secure Storage
- SQLite/IndexedDB for local storage

### 10. Sample Typescript Authentication Service
```typescript
@Injectable({
  providedIn: 'root'
})
export class AuthenticationService {
  async authenticateCompany(companyCode: string, password: string): Promise<AuthResult> {
    try {
      // Online authentication attempt
      const onlineResult = await this.authenticateOnline(companyCode, password);
      
      if (onlineResult.success) {
        await this.storeCompanyCredentials(onlineResult.data);
        return onlineResult;
      }
      
      // Fallback to offline authentication
      return this.authenticateOffline(companyCode, password);
    } catch (error) {
      // Handle various authentication scenarios
    }
  }

  private async authenticateOnline(companyCode: string, password: string) {
    // Implement online authentication logic
  }

  private async authenticateOffline(companyCode: string, password: string) {
    // Implement offline authentication logic
  }
}
```

### Recommended Implementation Sequence
1. Implement online authentication
2. Add offline authentication fallback
3. Implement location selection
4. Set up local database initialization
5. Add advanced security features

### Testing Checklist
- Online authentication
- Offline authentication
- Location selection
- Database initialization
- Error scenarios
- Security edge cases
