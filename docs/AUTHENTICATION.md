# Authentication System Documentation

## Overview
The application implements a flexible, multi-method authentication system supporting both web and mobile (IonicPOS) applications.

## Authentication Methods

### 1. Web Application Authentication
- **Mechanism**: JWT tokens stored in cookies
- **Authentication Flow**:
  1. User logs in with email/password
  2. Server generates a JWT token
  3. Token is stored in an HTTP-only secure cookie
  4. Subsequent requests use this cookie for authentication

### 2. IonicPOS App Authentication
#### Two-Step Authentication Process
1. **Company Authentication**
   - Endpoint: `/api/pos/company/authenticate`
   - Requires: 
     - Company code
     - Company password (hashed)
   - Returns company authentication token

2. **User Authentication**
   - Requires:
     - Company authentication
     - User PIN

### Authentication Middleware: `withAuth()`

#### Function Signature
```typescript
function withAuth(
  handler: Function, 
  allowedRoles: string[] = []
): NextApiHandler
```

#### Role Hierarchy
Supported roles (in order of increasing privilege):
1. `company_user`
2. `owner`
3. `admin`
4. `superuser`

#### Authentication Methods
The middleware supports two authentication methods:
1. **Standard Web Authentication**
   - Uses JWT token from session cookie
   - Validates user credentials
   - Checks user roles

2. **Device Authentication**
   - Supports X-API-Key for IonicPOS app
   - Validates device-specific tokens

#### Usage Example
```typescript
// Allow only admin and owner roles
export const GET = withAuth(
  async (req: NextRequest, user: any) => {
    // Your endpoint logic here
  },
  ["admin", "owner"]
);
```

## Authentication Headers

### Web Application
- `Authorization`: JWT token
- `company-id`: Current company context

### IonicPOS App
- `X-API-Key`: `test_api_key_1`
- `X-Company-Id`: Company identifier
- `company-id`: Company identifier (alternative)

## Security Considerations
- JWT tokens are short-lived
- Tokens stored in HTTP-only, secure cookies
- Rate limiting on authentication endpoints
- Bcrypt used for password hashing
- Support for multi-tenant, multi-location access

## Common Pitfalls
- Always specify allowed roles when using `withAuth()`
- Ensure `companyId` is validated in multi-tenant endpoints
- Use `dbConnect()` before database operations
- Handle potential authentication errors gracefully

## Troubleshooting
- 401 (Unauthorized): Invalid or expired token
- 403 (Forbidden): Insufficient role privileges
- Check browser console and server logs for detailed errors

## Environment Variables
- `JWT_SECRET`: Secret key for token signing
- `NODE_ENV`: Determines authentication behavior

## Best Practices
1. Never hardcode API keys or secrets
2. Use environment-specific configurations
3. Implement proper error handling
4. Log authentication attempts (without sensitive data)
