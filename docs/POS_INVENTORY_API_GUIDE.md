# Inventory API Guide for Ionic Team

## Authentication Flow

The POS application follows a multi-step authentication process:

1. **Company Authentication** - Authenticates at the company level
2. **Location Selection** - Identifies the specific location
3. **User Authentication** - Authenticates the individual user via PIN

### Authentication Methods

#### 1. Company Token Authentication
- **Endpoint**: `GET /api/pos/company/{companyId}/token`
- **Headers Required**: 
  - `X-API-Key: test_api_key_1`
- **Response**: A short-lived company token (15-minute expiration)

#### 2. User PIN Authentication
- **Endpoint**: `POST /api/pos/auth/pin-login`
- **Headers Required**:
  - `X-API-Key: test_api_key_1`
- **Request Body**:
  ```json
  {
    "userId": "user_mongodb_id",
    "companyId": "company_mongodb_id",
    "pin": "123456"
  }
  ```
- **Response**: JWT token for user authentication (8-hour expiration)

## Inventory Endpoints

### Get Branch Inventory Items

**Endpoint**: `GET /api/company/{companyId}/location/{locationId}/inventory`

#### Authentication Requirements
- **Headers Required**:
  - `Authorization: Bearer {jwt_token}` OR `company-id: {companyId}`
  - `X-API-Key: test_api_key_1`

#### URL Parameters
- `companyId`: MongoDB ObjectId of the company
- `locationId`: MongoDB ObjectId of the location

#### Query Parameters
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 100)
- `search`: Search term for inventory items
- `itemType`: Filter by item type (RECIPE or INGREDIENT)
- `category`: Filter by category
- `belowReorder`: Set to 'true' to only show items below reorder point
- `includeSellingOptions`: Set to 'true' to include selling options details

#### Response Format
```json
{
  "data": [
    {
      "_id": "inventory_item_id",
      "itemId": {
        "_id": "recipe_or_ingredient_id",
        "name": "Item Name"
      },
      "itemType": "RECIPE|INGREDIENT",
      "currentStock": 10.5,
      "parLevel": 20,
      "reorderPoint": 5,
      "baseUomId": {
        "_id": "uom_id",
        "name": "Unit Name",
        "shortCode": "UOM"
      },
      "name": "Item Name",
      "description": "Item Description",
      "category": "Item Category",
      "belowReorderPoint": false,
      "isActive": true,
      "isLocked": false,
      "itemDetails": {
        // Additional details about the recipe or ingredient
        // Including selling details if requested
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total": 250,
    "pages": 3
  }
}
```

### Inventory Sync Endpoint

**Endpoint**: `POST /api/company/{companyId}/location/{locationId}/inventory/sync`

#### Authentication Requirements
- **Headers Required**:
  - `Authorization: Bearer {jwt_token}` (from PIN login)
  - `company-id: {companyId}`
  - `X-API-Key: test_api_key_1`

> **Important**: Any authenticated user can perform inventory sync operations regardless of their permissions or role. No specific inventory permissions are required for syncing data.

#### URL Parameters
- `companyId`: MongoDB ObjectId of the company
- `locationId`: MongoDB ObjectId of the location

#### Request Body Format
```json
{
  "updates": [
    {
      "itemId": "inventory_item_id",
      "currentStock": 10.5,
      "transactions": [
        {
          "type": "sale",
          "quantity": 2,
          "menuItemId": "menu_item_id",
          "timestamp": "2025-03-27T11:30:00Z",
          "reason": "Sale through POS"
        },
        {
          "type": "wastage",
          "quantity": 0.5,
          "timestamp": "2025-03-27T12:15:00Z",
          "reason": "Expired product"
        }
      ]
    }
  ],
  "syncTimestamp": "2025-03-27T12:30:00Z"
}
```

#### Response Format
```json
{
  "status": "success",  // "success", "partial", or "error"
  "updatedItems": ["inventory_item_id1", "inventory_item_id2"],
  "errors": [
    {
      "itemId": "inventory_item_id3",
      "error": "Item not found"
    }
  ],
  "lastSyncTimestamp": "2025-03-27T12:30:00Z"
}
```

#### Notes on Inventory Sync
- The `transactions` array is optional but recommended for maintaining an audit trail
- Each transaction should include a `type` ("sale", "wastage", or "adjustment")
- For sales, include the `menuItemId` that was sold
- The `syncTimestamp` should represent when the sync batch was created
- If all updates succeed, the response `status` will be "success"
- If some updates fail, the response will include an `errors` array and `status` will be "partial"
- If all updates fail, `status` will be "error"

## Implementation Examples

### Step 1: Company Authentication
```typescript
async function getCompanyToken(companyId: string) {
  try {
    const response = await fetch(`/api/pos/company/${companyId}/token`, {
      method: 'GET',
      headers: {
        'X-API-Key': 'test_api_key_1'
      }
    });
    
    const data = await response.json();
    if (data.success && data.token) {
      // Store company token
      return data.token;
    }
    throw new Error('Failed to get company token');
  } catch (error) {
    console.error('Company authentication failed', error);
    throw error;
  }
}
```

### Step 2: User PIN Login
```typescript
async function loginWithPin(userId: string, companyId: string, pin: string) {
  try {
    const response = await fetch('/api/pos/auth/pin-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'test_api_key_1'
      },
      body: JSON.stringify({ userId, companyId, pin })
    });

    const result = await response.json();
    if (result.success) {
      // Store user token for subsequent requests
      localStorage.setItem('pos_auth_token', result.token);
      return result.user;
    }
    throw new Error(result.message || 'PIN login failed');
  } catch (error) {
    console.error('PIN login failed', error);
    throw error;
  }
}
```

### Step 3: Fetch Inventory
```typescript
async function fetchInventory(companyId: string, locationId: string, options = {}) {
  try {
    // Get user token from storage
    const userToken = localStorage.getItem('pos_auth_token');
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (options.page) queryParams.append('page', options.page);
    if (options.limit) queryParams.append('limit', options.limit);
    if (options.search) queryParams.append('search', options.search);
    if (options.itemType) queryParams.append('itemType', options.itemType);
    if (options.belowReorder) queryParams.append('belowReorder', 'true');
    
    const url = `/api/company/${companyId}/location/${locationId}/inventory?${queryParams}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'company-id': companyId,
        'X-API-Key': 'test_api_key_1'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Inventory fetch failed: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching inventory:', error);
    throw error;
  }
}
```

## Error Handling

### Common Error Status Codes
- `401 Unauthorized`: Invalid or missing authentication
- `403 Forbidden`: Valid authentication but insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error

### Authentication Errors
- Ensure you're using the correct API key
- Check that company token or JWT token is valid and not expired
- Verify the user has proper permissions to access inventory data

## Recommendations for Ionic Implementation

1. **Authentication Process**:
   - Always authenticate at company level first
   - Then perform user PIN authentication
   - Store JWT token securely (e.g., in secure storage)
   
2. **Inventory Data Handling**:
   - Implement pagination controls for large inventory datasets
   - Consider caching inventory data for offline access
   - Update local cache when sync is performed

3. **Error Handling**:
   - Implement proper token expiration handling
   - Add automatic retry logic for network failures
   - Show user-friendly error messages

4. **Performance Optimization**:
   - Use the search parameter for filtered queries instead of client-side filtering
   - Load inventory data incrementally as needed
   - Consider using websocket connections for real-time inventory updates if available

## Troubleshooting

If you encounter issues with these endpoints, please ensure:

1. All required headers are correctly set
2. Company ID in URL matches company ID in header
3. User has permissions to access the specified location
4. API key is valid

For additional support, contact the backend development team.
