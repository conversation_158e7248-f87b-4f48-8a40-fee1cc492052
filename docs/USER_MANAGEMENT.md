# User Management in FoodPrepAI

## Overview
The FoodPrepAI application implements a comprehensive user management system designed to support multi-tenant, multi-location operations with robust authentication and access control.

## User Model
The User model contains key attributes for managing user access and information:

```typescript
type User = {
  _id: string;
  email: string;
  displayName: string;
  userType: string;
  role?: SystemRole;
  customRoleId?: string;
  companyId: string;
  isActive?: boolean;
  canUseIonicApp?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}
```

### Key Attributes
- `email`: Unique identifier for the user
- `displayName`: User's display name
- `role`: System-defined role (e.g., 'admin', 'manager', 'user')
- `customRoleId`: Optional custom role for more granular permissions
- `companyId`: Tenant/company association
- `isActive`: Account status
- `canUseIonicApp`: Permission flag for Ionic POS app access

## Authentication Mechanisms

### Web Application
- Authentication via email/password
- JWT tokens stored in cookies
- Session management through Next.js authentication middleware

### Ionic POS App
- Two-step authentication:
  1. Company authentication (using company code)
  2. User authentication with PIN

## User Roles and Permissions

### System Roles
- `owner`: Top-level administrative access
- `admin`: Administrative privileges
- `manager`: Management-level access
- `user`: Standard user access
- `storekeeper`: Inventory and store-related permissions

### Custom Roles
- Supports custom role creation for more specific permission sets
- Can be defined per company
- Allows for flexible access control beyond system roles

## User Management Features

### Admin Dashboard
- Add, edit, and manage users
- Toggle IonicPOS app access
- Manage user roles and permissions

### Access Control
- Role-based access control (RBAC)
- Company-level user isolation
- Granular permission management

## IonicPOS App Access
- Administrators can enable/disable IonicPOS app access for individual users
- Controlled via the `canUseIonicApp` flag
- Prevents unauthorized access to the mobile application

## Security Considerations
- Passwords hashed using bcrypt
- JWT-based authentication
- Company-level isolation
- Rate limiting to prevent brute-force attacks

## API Endpoints

### User Management
- `/api/company/{companyId}/users`
  - GET: List users
  - POST: Create new user
- `/api/company/{companyId}/users/{userId}`
  - PUT: Update user details
  - DELETE: Remove user

### IonicPOS Access
- `/api/admin/users/ionic-access`
  - POST: Toggle IonicPOS app access for a user

## Best Practices
- Always use company-id header for multi-tenant operations
- Implement proper role checks before performing sensitive actions
- Regularly audit and review user access
