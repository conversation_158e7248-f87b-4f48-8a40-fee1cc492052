# Transfer with Markup Testing Guide

This document provides a guide for testing the new transfer with markup feature, which allows the central kitchen to apply a markup when transferring ingredients or recipes to branch locations.

## Overview

The transfer with markup feature allows central kitchens to:

1. Apply a percentage-based markup (e.g., 15%)
2. Apply a fixed amount markup (e.g., $5 per unit)
3. Track both original cost and marked-up cost at the branch level
4. Generate financial reports showing internal "sales" from central kitchen to branches

## Test Cases

### Test Case 1: Percentage-Based Markup Transfer

#### Step 1: TRANSFER_OUT with Percentage Markup

```json
POST /api/company/{companyId}/inventory/movement/v2

{
  "transactionType": "TRANSFER_OUT",
  "locationId": "67697ac879a92509b1f9f245",
  "destinationLocationId": "67697ac879a92509b1f9f246",
  "items": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "quantity": 2,
      "unitId": "676a982bd253bdfa34d2050c",
      "cost": 10.00
    }
  ],
  "priceMarkup": {
    "type": "PERCENTAGE",
    "value": 15,
    "applyTax": false
  },
  "notes": "Transfer to branch location with 15% markup"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Successfully processed TRANSFER_OUT transaction",
  "transactionId": "67ea71ab4d3103678bb2aae1",
  "transactionType": "TRANSFER_OUT",
  "affectedItems": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "itemName": "Test Recipe with Betteraves & Aluminium",
      "previousStock": 5,
      "newStock": 3,
      "difference": -2
    }
  ]
}
```

**Database Effects:**
1. Source location (central kitchen):
   - `currentStock` decreased by 2

2. Destination location (branch):
   - New or updated BranchInventory record with:
   - `pendingStock` increased by 2
   - `costBasis` set to 11.50 (10.00 + 15%)
   - `originalCost` set to 10.00
   - `markup.percentage` set to 15
   - `markup.appliedDate` set to current date

#### Step 2: TRANSFER_IN to Complete the Transfer

```json
POST /api/company/{companyId}/inventory/movement/v2

{
  "transactionType": "TRANSFER_IN",
  "locationId": "67697ac879a92509b1f9f246",
  "sourceLocationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "quantity": 2,
      "unitId": "676a982bd253bdfa34d2050c"
    }
  ],
  "notes": "Received transfer from central kitchen"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Successfully processed TRANSFER_IN transaction",
  "transactionId": "67ea71cd4d3103678bb2aae2",
  "transactionType": "TRANSFER_IN",
  "affectedItems": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "itemName": "Test Recipe with Betteraves & Aluminium",
      "previousStock": 0,
      "newStock": 2,
      "difference": 2,
      "previousPendingStock": 2, 
      "newPendingStock": 0,
      "pendingDifference": -2
    }
  ]
}
```

**Database Effects:**
1. Branch location:
   - `pendingStock` decreased by 2
   - `currentStock` increased by 2
   - Cost information preserved

### Test Case 2: Fixed Amount Markup Transfer

#### Step 1: TRANSFER_OUT with Fixed Markup

```json
POST /api/company/{companyId}/inventory/movement/v2

{
  "transactionType": "TRANSFER_OUT",
  "locationId": "67697ac879a92509b1f9f245",
  "destinationLocationId": "67697ac879a92509b1f9f246",
  "items": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "quantity": 5,
      "unitId": "676a982bd253bdfa34d2050c",
      "cost": 20.00
    }
  ],
  "priceMarkup": {
    "type": "FIXED",
    "value": 2.50,
    "applyTax": false
  },
  "notes": "Transfer to branch location with $2.50 fixed markup per unit"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Successfully processed TRANSFER_OUT transaction",
  "transactionId": "67ea72e14d3103678bb2aae3",
  "transactionType": "TRANSFER_OUT",
  "affectedItems": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "itemName": "Betteraves (kg)",
      "previousStock": 10,
      "newStock": 5,
      "difference": -5
    }
  ]
}
```

**Database Effects:**
1. Source location (central kitchen):
   - `currentStock` decreased by 5

2. Destination location (branch):
   - `pendingStock` increased by 5
   - `costBasis` set to 22.50 (20.00 + 2.50)
   - `originalCost` set to 20.00
   - `markup.fixedAmount` set to 2.50
   - `markup.appliedDate` set to current date

## Verification Queries

To verify that the markup was correctly applied, you can check the BranchInventory and InventoryTransaction collections:

### Check BranchInventory for Applied Markup

```javascript
db.branchInventories.findOne({
  companyId: ObjectId("67682466d436c5f697693330"),
  locationId: ObjectId("67697ac879a92509b1f9f246"),
  itemId: ObjectId("67ea6e5a54a3a2d7aab3c1a3"),
  itemType: "RECIPE"
})
```

Expected result should include:

```json
{
  "_id": "...",
  "companyId": "67682466d436c5f697693330",
  "locationId": "67697ac879a92509b1f9f246",
  "itemId": "67ea6e5a54a3a2d7aab3c1a3",
  "itemType": "RECIPE",
  "currentStock": 2,
  "pendingStock": 0,
  "costBasis": 11.50,
  "originalCost": 10.00,
  "markup": {
    "percentage": 15,
    "fixedAmount": null,
    "appliedDate": "2025-03-31T...",
    "transferId": "67ea71ab4d3103678bb2aae1"
  },
  "lastUpdated": "2025-03-31T..."
}
```

### Check InventoryTransaction for Markup Details

```javascript
db.inventoryTransactions.findOne({
  "metadata.companyId": ObjectId("67682466d436c5f697693330"),
  "metadata.transactionType": "TRANSFER_OUT",
  "_id": ObjectId("67ea71ab4d3103678bb2aae1")
})
```

Expected result should include:

```json
{
  "_id": "67ea71ab4d3103678bb2aae1",
  "metadata": {
    "companyId": "67682466d436c5f697693330",
    "locationId": "67697ac879a92509b1f9f245",
    "itemId": "67ea6e5a54a3a2d7aab3c1a3",
    "itemType": "RECIPE",
    "transactionType": "TRANSFER_OUT",
    "userId": "..."
  },
  "previousStock": 5,
  "newStock": 3,
  "difference": -2,
  "notes": "Transfer to branch location with 15% markup",
  "unitId": "676a982bd253bdfa34d2050c",
  "destinationLocationId": "67697ac879a92509b1f9f246",
  "transferMarkup": {
    "originalCost": 10.00,
    "markedUpCost": 11.50,
    "markupType": "PERCENTAGE",
    "markupValue": 15,
    "taxIncluded": false
  }
}
```

## Reporting Considerations

The markup feature enables several new reporting capabilities:

1. **Central Kitchen as Profit Center**: Track "revenue" generated by internal transfers with markup
2. **True Cost of Goods at Branches**: Include the markup in cost calculations for branch sales
3. **Internal Supply Chain Analytics**: Analyze markup trends and profitability by branch and product

To implement these reports, query the InventoryTransaction collection for transactions with transferMarkup details.
