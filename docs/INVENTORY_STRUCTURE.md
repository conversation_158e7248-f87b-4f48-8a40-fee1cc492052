# Inventory Structure Documentation

This document outlines the inventory structure and relationships between different collections in the FoodPrepAI system.

## Overview

The inventory system consists of three primary components:

1. **Central System**: Maintains master records of ingredients and recipes
2. **Branch Inventories**: Tracks actual stock levels at different branch locations
3. **Ionic POS App**: Handles real-time updates to branch inventory at each location

## Collection Structure

### Ingredients Collection

- Raw materials purchased from suppliers
- Contains core product information (name, description, base unit, etc.)
- Tracks central stock levels with `currentStock` field
- Uses `pendingStock` field to track purchase orders
- When ingredients are received, they move from `pendingStock` to `currentStock`

### Recipes Collection

- Composed of ingredients and/or sub-recipes
- Contains recipe information (name, yield, components, etc.)
- Tracks central stock levels with `currentStock` field
- Uses `pendingStock` field for tracking production in progress
- When production completes, items move from `pendingStock` to `currentStock`

### BranchInventories Collection

- Tracks inventory of ingredients and recipes AT specific branch locations
- Each record is linked to a specific branch via `locationId`
- Updates based on sales, transfers, etc. at the branch level
- Synced with the Ionic POS app at branches

## Key Relationships

The BranchInventories collection uses a one-to-one relationship between items and locations:

- If an ingredient is sold at two different locations, there will be two separate records in the BranchInventories collection
- Each record has its own `locationId`, `itemId`, and stock information
- Each branch/location tracks its own inventory levels independently

This design allows:
1. Each branch to have its own current stock levels
2. Different par levels and reorder points per branch
3. Independent tracking of inventory movements at each location

### Example

The same ingredient "Flour" sold at both "Branch A" and "Branch B" would have:

- One record in BranchInventories with `locationId` = Branch A, `itemId` = Flour, `currentStock` = X
- Another record in BranchInventories with `locationId` = Branch B, `itemId` = Flour, `currentStock` = Y

This is why the compound index on (`companyId`, `locationId`, `itemId`) exists in the schema - to efficiently look up inventory for specific items at specific locations.

## Inventory Movement Workflows

### Purchase to Receipt Workflow

1. **PURCHASE Transaction**:
   - Increases `pendingStock` in the Ingredient collection
   - Does not change `currentStock`
   - Creates a transaction record

2. **RECEIVED Transaction**:
   - Decreases `pendingStock` in the Ingredient collection
   - Increases `currentStock` in the Ingredient collection
   - Creates a transaction record
   - Validates that you can't receive more than what's pending

### Production Workflow

1. **PRODUCTION_INPUT Transaction**:
   - Decreases `currentStock` for ingredients used in production
   - Creates a transaction record

2. **PRODUCTION_OUTPUT Transaction**:
   - Increases `currentStock` for the produced recipe
   - Creates a transaction record

### Branch Inventory Sync

- Ionic POS app maintains real-time inventory at branches
- Periodically syncs inventory levels back to central system
- Updates the BranchInventories collection with current branch stock levels
