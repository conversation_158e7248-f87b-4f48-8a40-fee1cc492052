# Ionic App Integration Guide for User Synchronization

This guide outlines what the Ionic app team needs to implement to integrate with the user synchronization architecture.

## 1. Authentication Integration

### JWT Token Authentication

1. **Implement Token Storage**
   ```typescript
   // Store authentication token securely
   import { Preferences } from '@capacitor/preferences';

   async function storeAuthToken(token: string) {
     await Preferences.set({
       key: 'auth_token',
       value: token
     });
   }

   async function getAuthToken(): Promise<string | null> {
     const { value } = await Preferences.get({ key: 'auth_token' });
     return value;
   }
   ```

2. **Authentication Service**
   ```typescript
   // auth.service.ts
   import { Injectable } from '@angular/core';
   import { HttpClient, HttpHeaders } from '@angular/common/http';
   import { Observable, BehaviorSubject } from 'rxjs';
   import { map, tap } from 'rxjs/operators';
   import { Preferences } from '@capacitor/preferences';
   import { environment } from '../environments/environment';

   @Injectable({
     providedIn: 'root'
   })
   export class AuthService {
     private currentUserSubject = new BehaviorSubject<any>(null);
     public currentUser$ = this.currentUserSubject.asObservable();
     
     constructor(private http: HttpClient) {
       this.loadStoredUser();
     }
     
     private async loadStoredUser() {
       const storedToken = await this.getAuthToken();
       if (storedToken) {
         // Parse JWT payload to get user info (without verification - server does that)
         try {
           const payload = JSON.parse(atob(storedToken.split('.')[1]));
           this.currentUserSubject.next(payload);
         } catch (e) {
           console.error('Invalid token format');
         }
       }
     }
     
     login(email: string, password: string): Observable<any> {
       return this.http.post<any>(`${environment.apiUrl}/api/auth/signin`, { email, password })
         .pipe(
           tap(async response => {
             if (response && response.token) {
               await this.storeAuthToken(response.token);
               // Parse JWT payload
               const payload = JSON.parse(atob(response.token.split('.')[1]));
               this.currentUserSubject.next(payload);
             }
           })
         );
     }
     
     loginWithPin(userId: string, pin: string): Observable<any> {
       return this.http.post<any>(`${environment.apiUrl}/api/auth/pin-signin`, { userId, pin })
         .pipe(
           tap(async response => {
             if (response && response.token) {
               await this.storeAuthToken(response.token);
               const payload = JSON.parse(atob(response.token.split('.')[1]));
               this.currentUserSubject.next(payload);
             }
           })
         );
     }
     
     async logout() {
       await Preferences.remove({ key: 'auth_token' });
       this.currentUserSubject.next(null);
     }
     
     async storeAuthToken(token: string) {
       await Preferences.set({
         key: 'auth_token',
         value: token
       });
     }
     
     async getAuthToken(): Promise<string | null> {
       const { value } = await Preferences.get({ key: 'auth_token' });
       return value;
     }
     
     // Helper to get auth headers for API requests
     async getAuthHeaders(): Promise<HttpHeaders> {
       const token = await this.getAuthToken();
       let headers = new HttpHeaders();
       
       if (token) {
         headers = headers.set('Authorization', `Bearer ${token}`);
       }
       
       // Always include company ID if available
       const companyId = this.currentUserSubject.value?.companyId;
       if (companyId) {
         headers = headers.set('company-id', companyId);
       }
       
       return headers;
     }
   }
   ```

3. **HTTP Interceptor for Authentication**
   ```typescript
   // auth.interceptor.ts
   import { Injectable } from '@angular/core';
   import {
     HttpEvent, HttpInterceptor, HttpHandler,
     HttpRequest, HttpErrorResponse
   } from '@angular/common/http';
   import { Observable, from, throwError } from 'rxjs';
   import { catchError, switchMap } from 'rxjs/operators';
   import { AuthService } from './auth.service';

   @Injectable()
   export class AuthInterceptor implements HttpInterceptor {
     constructor(private authService: AuthService) {}
     
     intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
       return from(this.authService.getAuthToken()).pipe(
         switchMap(token => {
           // Clone the request with auth headers if token exists
           if (token) {
             const companyId = this.authService.currentUserSubject?.value?.companyId;
             let authReq = req.clone({
               headers: req.headers.set('Authorization', `Bearer ${token}`)
             });
             
             // Add company ID header if available
             if (companyId) {
               authReq = authReq.clone({
                 headers: authReq.headers.set('company-id', companyId)
               });
             }
             
             return next.handle(authReq);
           }
           
           return next.handle(req);
         }),
         catchError((error: HttpErrorResponse) => {
           // Handle 401 Unauthorized errors
           if (error.status === 401) {
             // Token expired or invalid
             this.authService.logout();
             // Redirect to login
           }
           return throwError(() => error);
         })
       );
     }
   }
   ```

## 2. User Synchronization Service

```typescript
// user-sync.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthService } from './auth.service';
import { NetworkService } from './network.service';
import { DbService } from './db.service';
import { environment } from '../environments/environment';
import { v4 as uuidv4 } from 'uuid';
import { Observable, from, of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserSyncService {
  private lastSyncTimestamp: string | null = null;

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private networkService: NetworkService,
    private dbService: DbService
  ) {
    this.loadLastSyncTimestamp();
  }

  private async loadLastSyncTimestamp() {
    const timestamp = await this.dbService.getSetting('lastUserSyncTimestamp');
    this.lastSyncTimestamp = timestamp || null;
  }

  private async saveLastSyncTimestamp(timestamp: string) {
    this.lastSyncTimestamp = timestamp;
    await this.dbService.saveSetting('lastUserSyncTimestamp', timestamp);
  }

  // Perform a full sync with the server
  syncUsers(forceFullSync = false): Observable<any> {
    // Use network service to check connectivity
    if (!this.networkService.isOnline()) {
      console.log('Offline: Using cached user data');
      return of({ status: 'offline', message: 'Using cached data' });
    }

    // Get auth headers and company ID
    return from(this.authService.getAuthHeaders()).pipe(
      switchMap(headers => {
        const companyId = this.authService.currentUserSubject?.value?.companyId;
        if (!companyId) {
          return throwError(() => new Error('Company ID not available'));
        }

        // Build sync URL with timestamp if not a force full sync
        let url = `${environment.apiUrl}/api/company/${companyId}/users/sync`;
        if (!forceFullSync && this.lastSyncTimestamp) {
          url += `?since=${this.lastSyncTimestamp}`;
        }

        // Request updated users
        return this.http.get(url, { headers }).pipe(
          tap(async (response: any) => {
            // Store sync timestamp
            if (response.syncTimestamp) {
              await this.saveLastSyncTimestamp(response.syncTimestamp);
            }

            // Process updated users
            if (response.users && Array.isArray(response.users)) {
              await this.processUpdatedUsers(response.users);
            }

            // Process deleted users
            if (response.deletedUserIds && Array.isArray(response.deletedUserIds)) {
              await this.processDeletedUsers(response.deletedUserIds);
            }
          }),
          catchError(error => {
            console.error('User sync failed:', error);
            return throwError(() => error);
          })
        );
      })
    );
  }

  // Process users from server
  private async processUpdatedUsers(users: any[]) {
    // Store in local database
    for (const user of users) {
      await this.dbService.saveUser(user);
    }
    console.log(`Processed ${users.length} updated users`);
  }

  // Process deleted users
  private async processDeletedUsers(userIds: string[]) {
    for (const userId of userIds) {
      await this.dbService.markUserDeleted(userId);
    }
    console.log(`Processed ${userIds.length} deleted users`);
  }

  // Push local user changes to server
  pushLocalChanges(): Observable<any> {
    if (!this.networkService.isOnline()) {
      return of({ status: 'offline', message: 'Cannot push changes while offline' });
    }

    return from(this.getLocalChanges()).pipe(
      switchMap(changes => {
        if (!changes.length) {
          return of({ status: 'success', message: 'No changes to push' });
        }

        const companyId = this.authService.currentUserSubject?.value?.companyId;
        if (!companyId) {
          return throwError(() => new Error('Company ID not available'));
        }

        return from(this.authService.getAuthHeaders()).pipe(
          switchMap(headers => {
            const syncId = uuidv4();
            const payload = {
              syncId,
              userUpdates: changes
            };

            return this.http.post(
              `${environment.apiUrl}/api/company/${companyId}/users/sync`, 
              payload, 
              { headers }
            ).pipe(
              tap(async (response: any) => {
                if (response.status === 'success' || response.status === 'partial') {
                  // Clear the pending changes flag for successfully synced users
                  for (const result of response.updatedUsers) {
                    if (result.status === 'success') {
                      await this.dbService.clearUserPendingChanges(result.userId);
                    }
                  }
                }
              })
            );
          })
        );
      })
    );
  }

  // Get local changes that need to be pushed to server
  private async getLocalChanges(): Promise<any[]> {
    return this.dbService.getPendingUserChanges();
  }

  // Schedule periodic sync
  setupPeriodicSync(intervalMinutes = 15) {
    // Set up periodic sync when online
    setInterval(() => {
      if (this.networkService.isOnline()) {
        this.syncUsers().subscribe({
          next: () => console.log('Periodic user sync completed'),
          error: err => console.error('Periodic sync failed:', err)
        });
      }
    }, intervalMinutes * 60 * 1000);
  }
}
```

## 3. Local Database Service

```typescript
// db.service.ts
import { Injectable } from '@angular/core';
import { Storage } from '@ionic/storage-angular';

@Injectable({
  providedIn: 'root'
})
export class DbService {
  private _storage: Storage | null = null;
  private initialized = false;

  constructor(private storage: Storage) {
    this.init();
  }

  async init() {
    if (this.initialized) return;
    
    const storage = await this.storage.create();
    this._storage = storage;
    this.initialized = true;
  }

  // Settings management
  async getSetting(key: string): Promise<any> {
    await this.init();
    return this._storage?.get(`setting:${key}`);
  }

  async saveSetting(key: string, value: any): Promise<void> {
    await this.init();
    await this._storage?.set(`setting:${key}`, value);
  }

  // User management
  async getUsers(): Promise<any[]> {
    await this.init();
    const keys = await this._storage?.keys() || [];
    const userKeys = keys.filter(key => key.startsWith('user:'));
    
    const users = [];
    for (const key of userKeys) {
      const user = await this._storage?.get(key);
      if (user && !user.isDeleted) {
        users.push(user);
      }
    }
    
    return users;
  }

  async getUser(userId: string): Promise<any> {
    await this.init();
    return this._storage?.get(`user:${userId}`);
  }

  async saveUser(user: any): Promise<void> {
    await this.init();
    await this._storage?.set(`user:${user._id}`, user);
  }

  async markUserDeleted(userId: string): Promise<void> {
    await this.init();
    const user = await this.getUser(userId);
    if (user) {
      user.isDeleted = true;
      await this.saveUser(user);
    }
  }

  async updateUserLocally(userId: string, changes: any): Promise<void> {
    await this.init();
    const user = await this.getUser(userId);
    if (user) {
      const updatedUser = { ...user, ...changes, hasPendingChanges: true };
      await this.saveUser(updatedUser);
    }
  }

  async getPendingUserChanges(): Promise<any[]> {
    await this.init();
    const users = await this.getUsers();
    return users
      .filter(user => user.hasPendingChanges)
      .map(({ hasPendingChanges, ...userData }) => userData);
  }

  async clearUserPendingChanges(userId: string): Promise<void> {
    await this.init();
    const user = await this.getUser(userId);
    if (user && user.hasPendingChanges) {
      user.hasPendingChanges = false;
      await this.saveUser(user);
    }
  }
}
```

## 4. Network Service

```typescript
// network.service.ts
import { Injectable } from '@angular/core';
import { Network } from '@capacitor/network';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NetworkService {
  private networkStatus = new BehaviorSubject<boolean>(true);
  public networkStatus$ = this.networkStatus.asObservable();

  constructor() {
    this.initNetworkMonitor();
  }

  private async initNetworkMonitor() {
    // Get initial network status
    const status = await Network.getStatus();
    this.networkStatus.next(status.connected);

    // Add event listener for network changes
    Network.addListener('networkStatusChange', status => {
      this.networkStatus.next(status.connected);
      
      if (status.connected) {
        // Trigger sync when connection is restored
        this.onConnectionRestored();
      }
    });
  }

  isOnline(): boolean {
    return this.networkStatus.value;
  }

  private onConnectionRestored() {
    // This can trigger user sync when connection is restored
    console.log('Connection restored - consider syncing data');
    // You could emit an event here that your sync service listens to
  }
}
```

## 5. Multi-Tenant Support

```typescript
// tenant.service.ts
import { Injectable } from '@angular/core';
import { Preferences } from '@capacitor/preferences';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TenantService {
  private currentCompanySubject = new BehaviorSubject<any>(null);
  public currentCompany$ = this.currentCompanySubject.asObservable();
  
  private currentLocationSubject = new BehaviorSubject<any>(null);
  public currentLocation$ = this.currentLocationSubject.asObservable();

  constructor() {
    this.loadStoredTenantInfo();
  }

  private async loadStoredTenantInfo() {
    const [companyData, locationData] = await Promise.all([
      this.getStoredCompany(),
      this.getStoredLocation()
    ]);
    
    if (companyData) {
      this.currentCompanySubject.next(JSON.parse(companyData));
    }
    
    if (locationData) {
      this.currentLocationSubject.next(JSON.parse(locationData));
    }
  }

  async setCurrentCompany(company: any) {
    this.currentCompanySubject.next(company);
    await Preferences.set({
      key: 'current_company',
      value: JSON.stringify(company)
    });
  }
  
  async setCurrentLocation(location: any) {
    this.currentLocationSubject.next(location);
    await Preferences.set({
      key: 'current_location',
      value: JSON.stringify(location)
    });
  }

  async getStoredCompany(): Promise<string | null> {
    const { value } = await Preferences.get({ key: 'current_company' });
    return value;
  }
  
  async getStoredLocation(): Promise<string | null> {
    const { value } = await Preferences.get({ key: 'current_location' });
    return value;
  }
  
  async clearTenantInfo() {
    await Promise.all([
      Preferences.remove({ key: 'current_company' }),
      Preferences.remove({ key: 'current_location' })
    ]);
    
    this.currentCompanySubject.next(null);
    this.currentLocationSubject.next(null);
  }
  
  getCurrentCompanyId(): string | null {
    const company = this.currentCompanySubject.value;
    return company ? company._id : null;
  }
  
  getCurrentLocationId(): string | null {
    const location = this.currentLocationSubject.value;
    return location ? location._id : null;
  }
}
```

## 6. PIN-Based Login UI

Create a PIN-based login screen for quick POS access:

```typescript
// pin-login.component.ts
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ToastController } from '@ionic/angular';
import { AuthService } from '../services/auth.service';
import { DbService } from '../services/db.service';

@Component({
  selector: 'app-pin-login',
  templateUrl: './pin-login.component.html',
  styleUrls: ['./pin-login.component.scss']
})
export class PinLoginComponent {
  pin: string = '';
  selectedUser: any = null;
  users: any[] = [];
  loading = false;

  constructor(
    private authService: AuthService,
    private dbService: DbService,
    private router: Router,
    private toastCtrl: ToastController
  ) {}

  async ionViewWillEnter() {
    this.loadUsers();
  }

  async loadUsers() {
    // Load users from local database
    this.users = await this.dbService.getUsers();
  }

  selectUser(user: any) {
    this.selectedUser = user;
    this.pin = '';
  }

  appendToPin(digit: string) {
    if (this.pin.length < 4) {
      this.pin += digit;
      
      // Auto-submit when PIN is complete
      if (this.pin.length === 4) {
        this.login();
      }
    }
  }

  clearPin() {
    this.pin = '';
  }

  backspace() {
    this.pin = this.pin.substring(0, this.pin.length - 1);
  }

  async login() {
    if (!this.selectedUser) {
      this.showToast('Please select a user first');
      return;
    }

    if (this.pin.length !== 4) {
      this.showToast('Please enter a 4-digit PIN');
      return;
    }

    this.loading = true;

    try {
      await this.authService.loginWithPin(this.selectedUser._id, this.pin).toPromise();
      this.router.navigate(['/dashboard']);
    } catch (error) {
      console.error('PIN login failed:', error);
      this.showToast('Invalid PIN');
      this.pin = '';
    } finally {
      this.loading = false;
    }
  }

  async showToast(message: string) {
    const toast = await this.toastCtrl.create({
      message,
      duration: 2000,
      position: 'top'
    });
    await toast.present();
  }
}
```

## 7. User Profile Management

```typescript
// user-profile.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthService } from './auth.service';
import { DbService } from './db.service';
import { UserSyncService } from './user-sync.service';
import { environment } from '../environments/environment';
import { Observable, from } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserProfileService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private dbService: DbService,
    private userSyncService: UserSyncService
  ) {}

  // Update user profile (online mode)
  updateProfile(userId: string, updates: any): Observable<any> {
    return from(this.authService.getAuthHeaders()).pipe(
      switchMap(headers => {
        const companyId = this.authService.currentUserSubject?.value?.companyId;
        
        return this.http.patch(
          `${environment.apiUrl}/api/company/${companyId}/users?userId=${userId}`,
          updates,
          { headers }
        ).pipe(
          tap(async () => {
            // Update local database as well
            await this.dbService.updateUserLocally(userId, updates);
          })
        );
      })
    );
  }

  // Update PIN
  updatePin(userId: string, newPin: string): Observable<any> {
    return from(this.authService.getAuthHeaders()).pipe(
      switchMap(headers => {
        const companyId = this.authService.currentUserSubject?.value?.companyId;
        
        return this.http.patch(
          `${environment.apiUrl}/api/company/${companyId}/users?userId=${userId}`,
          { pin: newPin },
          { headers }
        );
      })
    );
  }

  // Offline update
  async updateProfileOffline(userId: string, updates: any): Promise<void> {
    // Store changes locally with a pending flag
    await this.dbService.updateUserLocally(userId, updates);
    
    // Try to sync if online
    this.userSyncService.pushLocalChanges().subscribe({
      next: result => console.log('Push changes result:', result),
      error: err => console.error('Failed to push changes:', err)
    });
  }
}
```

## 8. Integration Testing

Implement test utilities to verify sync works properly:

```typescript
// sync-test.service.ts
import { Injectable } from '@angular/core';
import { UserSyncService } from './user-sync.service';
import { DbService } from './db.service';
import { NetworkService } from './network.service';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class SyncTestService {
  constructor(
    private userSyncService: UserSyncService,
    private dbService: DbService,
    private networkService: NetworkService,
    private authService: AuthService
  ) {}

  // Test full sync cycle
  async testFullSync(): Promise<any> {
    const report = {
      startTime: new Date().toISOString(),
      networkStatus: this.networkService.isOnline(),
      initialUserCount: 0,
      syncResult: null,
      finalUserCount: 0,
      errors: []
    };

    try {
      // Get initial user count
      const initialUsers = await this.dbService.getUsers();
      report.initialUserCount = initialUsers.length;

      // Force a full sync
      const syncResult = await this.userSyncService.syncUsers(true).toPromise();
      report.syncResult = syncResult;

      // Get final user count
      const finalUsers = await this.dbService.getUsers();
      report.finalUserCount = finalUsers.length;

      report.success = true;
    } catch (error) {
      report.success = false;
      report.errors.push(error.message || 'Unknown error');
    }

    report.endTime = new Date().toISOString();
    return report;
  }

  // Check user PIN access
  async testPinAccess(userId: string, pin: string): Promise<any> {
    const report = {
      startTime: new Date().toISOString(),
      userId,
      success: false,
      error: null
    };

    try {
      const result = await this.authService.loginWithPin(userId, pin).toPromise();
      report.success = !!result.token;
    } catch (error) {
      report.error = error.message || 'Login failed';
    }

    report.endTime = new Date().toISOString();
    return report;
  }
}
```

## Implementation Checklist

1. [ ] Set up local database using Ionic Storage
2. [ ] Implement authentication service with JWT and PIN support
3. [ ] Create network detection service
4. [ ] Implement user synchronization service
5. [ ] Add tenant/company management
6. [ ] Create PIN-based login UI
7. [ ] Add user profile management
8. [ ] Implement offline data access
9. [ ] Set up periodic sync mechanism
10. [ ] Add sync status indicators in UI