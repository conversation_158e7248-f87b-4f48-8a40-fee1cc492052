# Inventory Transfers Implementation Guide

This document outlines the enhanced inventory transfer system implemented for FoodPrepAI, focused on transfers with markup and branch-to-branch transfers.

## Features Implemented

### 1. Transfers with Markup

The central kitchen can now transfer inventory to branches with markup applied in two ways:
- **Percentage-based markup**: Adds a percentage to the original cost (e.g., 15%)
- **Fixed amount markup**: Adds a flat amount per unit (e.g., $2.50)

#### Key Models Updated:
- `BranchInventory`: Added `costBasis`, `originalCost`, and `markup` fields
- `InventoryTransaction`: Added `transferMarkup` field for financial tracking

#### API Endpoints:
- `/api/company/{companyId}/inventory/movement/v2`: Updated to support markup parameters

### 2. Branch-to-Branch Transfers with Approval

Branches can now request and transfer inventory between each other, with approval workflows for both source and destination locations.

#### New Models:
- `TransferRequest`: Tracks transfer requests, approvals, and status

#### New API Endpoints:
- `/api/company/{companyId}/inventory/transfer-request`
  - `GET`: View transfer requests
  - `POST`: Create a new transfer request
  - `PATCH`: Update a request (approve, reject)
  - `PUT`: Process an approved request

### 3. Category-Based Markup Rules

Flexible markup rules can now be defined based on categories, locations, or item types.

#### New Models:
- `MarkupRule`: Defines conditions for automatic markup application

#### New API Endpoints:
- `/api/company/{companyId}/markup-rules`
  - `GET`: View markup rules
  - `POST`: Create a new markup rule
  - `PATCH`: Update an existing rule
  - `DELETE`: Remove a rule

### 4. Transfer Revenue Reporting

Track internal "revenue" generated by transfers with markup between locations.

#### New API Endpoints:
- `/api/company/{companyId}/reports/transfer-revenue`
  - `GET`: Generate reports on transfer revenue with filtering options

## Data Flow

### Transfer with Markup Flow

1. **TRANSFER_OUT Request**: Central kitchen initiates transfer with markup specification
    ```json
    {
      "transactionType": "TRANSFER_OUT",
      "locationId": "central-kitchen-id",
      "destinationLocationId": "branch-id",
      "items": [...],
      "priceMarkup": {
        "type": "PERCENTAGE",
        "value": 15,
        "applyTax": false
      }
    }
    ```

2. **System Processes Transfer_OUT**:
   - Decreases stock at central kitchen
   - Creates pending stock at branch with markup applied
   - Records original and marked-up costs

3. **TRANSFER_IN Request**: Branch confirms receipt
    ```json
    {
      "transactionType": "TRANSFER_IN",
      "locationId": "branch-id",
      "sourceLocationId": "central-kitchen-id",
      "items": [...]
    }
    ```

4. **System Processes Transfer_IN**:
   - Moves items from pending to current stock at branch
   - Maintains markup information for cost accounting

### Branch-to-Branch Transfer with Approval Flow

1. **Create Transfer Request**:
   - Branch creates request for items from another branch
   - System validates availability and creates pending request

2. **Approval Process**:
   - Source branch approves release of inventory
   - Destination branch approves receipt
   - Both approvals required to proceed

3. **Process Transfer**:
   - When fully approved, system executes the transfer
   - Creates TRANSFER_OUT and TRANSFER_IN transactions
   - Updates inventory levels at both branches

## Usage Examples

### Creating a Markup Rule

```json
POST /api/company/{companyId}/markup-rules

{
  "name": "Bakery Items Markup",
  "description": "15% markup on all bakery items",
  "priority": 10,
  "conditions": [
    {
      "type": "CATEGORY",
      "value": "Bakery"
    }
  ],
  "markupType": "PERCENTAGE",
  "markupValue": 15,
  "applyTax": false
}
```

### Generating a Transfer Revenue Report

```
GET /api/company/{companyId}/reports/transfer-revenue?startDate=2025-01-01&endDate=2025-03-31&groupBy=month
```

## Testing

A comprehensive test suite has been created in:
- `src/__tests__/api/inventory-movement.test.ts`

This tests the core inventory movement functionality, including transfers with markup.

## Technical Implementation Notes

1. **Cost Tracking**:
   - Each item maintains both original cost and marked-up cost
   - This enables accurate COGS tracking at branches while showing "revenue" at central kitchen

2. **Performance Considerations**:
   - Markup rules are indexed for efficient retrieval
   - Transfer request approvals use a two-phase process to maintain data integrity

3. **Security**:
   - All endpoints validate location access rights
   - Only users with access to source/destination can approve transfers
