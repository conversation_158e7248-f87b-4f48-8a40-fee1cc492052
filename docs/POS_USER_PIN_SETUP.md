# POS User PIN Setup Guide

## Overview
After company authentication, each POS user needs to set up a unique PIN for access.

## User PIN Setup Workflow

### Important: User-Driven Process
**PIN Setup MUST be implemented within the Ionic POS App**
- Each user sets up their own PIN after initial company authentication
- This is a mandatory, one-time setup for new users
- Administrators do NOT set PINs for users

## User Experience Flow
1. User logs into Ionic POS app
2. If no PIN is set, app MUST:
   - Prompt user to set a 4-6 digit PIN
   - Prevent further app access until PIN is set
3. User enters and confirms PIN
4. App calls `/api/pos/auth/set-pin` endpoint
5. Successful PIN setup allows full app access

## Why User-Driven PIN Setup?
- Enhanced security through personal authentication
- Ensures each user creates their own unique PIN
- Prevents potential security risks of admin-set credentials
- Provides a clear, guided first-time user experience

## Implementation Requirements for Ionic Team
- Create a dedicated PIN setup screen
- Validate PIN on client-side (4-6 digits)
- Prevent navigation away from PIN setup
- Handle potential error scenarios gracefully
- Provide clear user instructions

## Technical Constraints
- PIN can only be set ONCE
- Requires a valid company authentication first
- User must have IonicPOS app access enabled

## API Endpoint
`POST /api/pos/auth/set-pin`

## Authentication Requirements
- Valid API Key (`test_api_key_1`) must be sent in `X-API-Key` header
- Requires company-specific authentication

## Request Payload
```typescript
{
  userId: string,      // User's MongoDB ObjectId
  companyId: string,   // Company's MongoDB ObjectId
  pin: string          // 4-6 digit PIN
}
```

## Validation Rules
- PIN must be 4-6 digits long
- User must belong to the specified company
- User must have IonicPOS app access enabled
- PIN can only be set once (must use reset PIN for subsequent changes)

## Response Formats

### Successful PIN Setup
```typescript
{
  success: true,
  message: "PIN set successfully",
  timestamp: string  // ISO date string
}
```

### Error Responses
- Invalid API Key: `401` status
- Missing/Invalid Parameters: `400` status
- User Not Found: `404` status
- PIN Already Set: `403` status
- Server Error: `500` status

## Security Measures
- PIN is hashed before storage
- Prevents setting PIN for users without IonicPOS access
- Enforces one-time PIN setup

## User Location Requirements

### Location Assignment
- Each user MUST be assigned to one or more locations
- Users can only access the IonicPOS app for their assigned locations
- Location assignment is done during user creation/editing in the web application

### Ionic App Behavior
- When a user logs in, the app will:
  1. Verify the user has at least one location assigned
  2. Display only the list of users for the assigned locations
  3. Prevent access if no locations are assigned

### Implementation Notes
- If a user has no locations assigned, they cannot:
  - Set up a PIN
  - Access the IonicPOS app
  - Perform any location-specific operations

## PIN Reset

### API Endpoint
`POST /api/pos/auth/reset-pin`

### Request Payload
```typescript
{
  userId: string,      // User's MongoDB ObjectId
  companyId: string,   // Company's MongoDB ObjectId
  oldPin: string,      // Current PIN
  newPin: string       // New 4-6 digit PIN
}
```

### Reset Validation Rules
- Requires current PIN for verification
- New PIN must be 4-6 digits
- Cannot reuse the same PIN
- Hashed securely before storage

### Example Reset Flow
```typescript
async function resetUserPin(userId: string, companyId: string, oldPin: string, newPin: string) {
  try {
    const response = await fetch('/api/pos/auth/reset-pin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'test_api_key_1'
      },
      body: JSON.stringify({ userId, companyId, oldPin, newPin })
    });

    const result = await response.json();
    return result.success;
  } catch (error) {
    console.error('PIN reset failed', error);
    return false;
  }
}
```

### UI/UX Guidelines for PIN Reset
- Require current PIN verification
- Validate new PIN complexity
- Confirm new PIN entry
- Provide clear feedback on success/failure

## PIN Login

### API Endpoint
`POST /api/pos/auth/pin-login`

### Authentication Requirements
- Valid API Key (`test_api_key_1`) must be sent in `X-API-Key` header

### Request Payload
```typescript
{
  userId: string,      // User's MongoDB ObjectId
  companyId: string,   // Company's MongoDB ObjectId
  pin: string          // User's 4-6 digit PIN
}
```

### Response Format

#### Successful Login
```typescript
{
  success: true,
  message: "Login successful",
  token: string,       // JWT token for subsequent authenticated requests
  user: {
    id: string,
    displayName: string,
    email: string,
    role: string,
    locationIds: string[],
    posSettings: object
  },
  timestamp: string    // ISO date string
}
```

#### Error Responses
- Invalid API Key: `401` status
- Missing/Invalid Parameters: `400` status
- User Not Found: `404` status
- PIN Not Set: `403` status
- Invalid PIN: `401` status
- Server Error: `500` status

### Example Client Code
```typescript
async function loginWithPin(userId: string, companyId: string, pin: string) {
  try {
    const response = await fetch('/api/pos/auth/pin-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'test_api_key_1'
      },
      body: JSON.stringify({ userId, companyId, pin })
    });

    const result = await response.json();
    if (result.success) {
      // Store token for subsequent requests
      localStorage.setItem('pos_auth_token', result.token);
      return result.user;
    }
    return null;
  } catch (error) {
    console.error('PIN login failed', error);
    return null;
  }
}
```

## PIN Verification

### API Endpoint
`POST /api/pos/auth/verify-pin`

### Authentication Requirements
- Valid API Key (`test_api_key_1`) must be sent in `X-API-Key` header

### Request Payload
```typescript
{
  userId: string,      // User's MongoDB ObjectId
  companyId: string,   // Company's MongoDB ObjectId
  pin: string          // User's 4-6 digit PIN to verify
}
```

### Response Format

#### Successful Verification
```typescript
{
  success: true,
  pinValid: boolean,   // Whether the PIN is correct
  message: string,     // "PIN is valid" or "Invalid PIN"
  timestamp: string    // ISO date string
}
```

#### Error Responses
- Invalid API Key: `401` status
- Missing/Invalid Parameters: `400` status
- User Not Found: `404` status
- PIN Not Set: `403` status
- Server Error: `500` status

### Example Client Code
```typescript
async function verifyUserPin(userId: string, companyId: string, pin: string) {
  try {
    const response = await fetch('/api/pos/auth/verify-pin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'test_api_key_1'
      },
      body: JSON.stringify({ userId, companyId, pin })
    });

    const result = await response.json();
    return result.pinValid;
  } catch (error) {
    console.error('PIN verification failed', error);
    return false;
  }
}
```

## UI/UX Guidelines
- Clear instructions
- PIN input with numeric keypad
- Validate PIN length
- Confirm PIN entry
- Provide feedback on success/failure

## Example Client Code for PIN Setup
```typescript
async function setupUserPin(userId: string, companyId: string, pin: string) {
  try {
    const response = await fetch('/api/pos/auth/set-pin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'test_api_key_1'
      },
      body: JSON.stringify({ userId, companyId, pin })
    });

    const result = await response.json();
    return result.success;
  } catch (error) {
    console.error('PIN setup failed', error);
    return false;
  }
}
