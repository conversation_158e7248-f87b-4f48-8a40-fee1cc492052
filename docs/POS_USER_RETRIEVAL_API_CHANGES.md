# POS User Retrieval API Changes

## Overview
This document outlines the significant changes made to the User Retrieval API for the Ionic POS application, focusing on improvements in data fetching, authentication, and security.

## Key Modifications

### 1. API Endpoint Enhancement
- **Endpoint**: `/api/pos/company/{companyId}/users`
- **Purpose**: Fetch users based on company and location
- **Authentication**: Requires company token authentication

### 2. MongoDB Query Improvements
#### Before Changes
- Incorrect field querying (`locationId` and `locationsAccess`)
- No filtering for Ionic app access

#### After Changes
- Correct field querying using `locationIds`
- Added filter `canUseIonicApp: true`
- Selected specific user fields:
  - `displayName`
  - `email`
  - `locationIds`

### 3. User Model Updates
#### New Fields Added
- `posAccess`: Determines POS application access
- `posSettings`: Configurable POS-specific user settings
- `locationsAccess`: Supports multi-location access

### 4. Authentication Enhancements
- Company-level token authentication
- Short-lived tokens (15 minutes)
- Strict validation of API keys and tokens

### 5. Security Considerations
- Users without `canUseIonicApp: true` are excluded
- Multi-tenant and multi-location support
- JWT-based authentication

## Implementation Details

### Query Transformation
```typescript
const users = await User.find({
  companyId: companyId,
  locationIds: { $in: requestedLocationIds },
  canUseIonicApp: true
}, {
  displayName: 1,
  email: 1,
  locationIds: 1
});
```

### Authentication Validation
```typescript
async function validateIonicAuth(req: NextRequest) {
  const authHeader = req.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return validateBearerToken(authHeader.substring(7));
  }
  return validateSessionCookie(req);
}
```

## New Authentication Endpoint

### PIN Login Implementation
- **Endpoint**: `/api/pos/auth/pin-login`
- **Purpose**: Authenticate users with their numeric PIN
- **Authentication**: Requires API key
- **Returns**: JWT token for authenticated requests

```typescript
// PIN login endpoint sample request
POST /api/pos/auth/pin-login
Headers: X-API-Key: test_api_key_1
Body: {
  userId: "user_mongodb_id",
  companyId: "company_mongodb_id",
  pin: "123456"
}
```

### JWT Token Generation
Upon successful PIN validation, the system:
1. Creates a payload with user details (ID, role, locations)
2. Signs the payload with the JWT secret
3. Returns the token with an 8-hour expiration

### PIN Verification Endpoint
- **Endpoint**: `/api/pos/auth/verify-pin`
- **Purpose**: Validate a user's PIN without logging in
- **Authentication**: Requires API key
- **Returns**: Boolean indicating PIN validity

```typescript
// PIN verification endpoint sample request
POST /api/pos/auth/verify-pin
Headers: X-API-Key: test_api_key_1
Body: {
  userId: "user_mongodb_id",
  companyId: "company_mongodb_id",
  pin: "123456"
}

// Sample response
{
  success: true,
  pinValid: true,
  message: "PIN is valid"
}
```

## Next Steps
1. Comprehensive testing of the new endpoints
2. Performance optimization (potential caching)
3. Continuous documentation updates

## Potential Future Improvements
- Implement caching mechanisms
- Add more granular permission controls
- Enhance logging and monitoring
