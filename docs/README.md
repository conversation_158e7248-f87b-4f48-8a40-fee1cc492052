# FoodPrepAI Monorepo

A unified development environment for FoodPrepAI web application and IonicPOS mobile application.

## 🏗️ Architecture

This monorepo contains:

- **Apps**
  - `apps/web/` - Next.js web application (FoodPrepAI)
  - `apps/mobile/` - Ionic React mobile application (IonicPOS)

- **Packages**
  - `packages/shared-types/` - Common TypeScript interfaces and types
  - `packages/shared-utils/` - Common utility functions
  - `packages/api-client/` - Shared API client library
  - `packages/ui-components/` - Shared UI component library
  - `packages/database-models/` - Shared database schemas and models

- **Tools**
  - `tools/eslint-config/` - Shared ESLint configurations
  - `tools/tsconfig/` - Shared TypeScript configurations
  - `tools/prettier-config/` - Shared Prettier configuration

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm 8+

### Installation

```bash
# Install all dependencies
npm install --legacy-peer-deps

# Install Husky git hooks
npm run prepare
```

### Development

```bash
# Start all apps in development mode
npm run dev

# Start specific app
npm run dev:web
npm run dev:mobile

# Build all packages and apps
npm run build

# Build specific app
npm run build:web
npm run build:mobile

# Run linting
npm run lint

# Run type checking
npm run typecheck

# Run tests
npm run test

# Format code
npm run format
```

## 🔧 Development Workflow

### Git Hooks

This repository uses Husky to enforce code quality:

- **pre-commit**: Runs lint-staged to format and lint changed files
- **commit-msg**: Validates commit messages using conventional commits
- **pre-push**: Runs tests and builds before pushing

### Commit Message Format

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

Examples:
feat(web): add user authentication
fix(mobile): resolve inventory sync issue
docs(monorepo): update setup instructions
```

Valid scopes: `web`, `mobile`, `shared-types`, `shared-utils`, `api-client`, `ui-components`, `database-models`, `tools`, `ci`, `docs`, `deps`, `monorepo`

### Adding Dependencies

```bash
# Add to root (tooling dependencies)
npm install -D <package>

# Add to specific workspace
npm install <package> --workspace=apps/web
npm install <package> --workspace=packages/shared-types
```

## 📦 Package Management

This monorepo uses npm workspaces. Each package has its own `package.json` and can depend on other packages using workspace references:

```json
{
  "dependencies": {
    "@foodprepai/shared-types": "workspace:*"
  }
}
```

## 🏃‍♂️ Build System

We use Turborepo for efficient builds with smart caching:

- **Incremental builds**: Only rebuilds what changed
- **Parallel execution**: Runs tasks across packages in parallel
- **Remote caching**: Shares build cache across team (when configured)
- **Task dependencies**: Ensures packages build in correct order

## 🧪 Testing Strategy

- **Unit tests**: Jest for all packages and apps
- **Integration tests**: API and cross-package integration
- **E2E tests**: Cypress/Playwright for full application flows
- **Type checking**: TypeScript strict mode across all packages

## 📚 Documentation

- [Development Guide](./development/)
- [API Documentation](./api/)
- [Deployment Guide](./deployment/)

## 🚀 Deployment

- **Web App**: Deployed to Vercel via GitHub Actions
- **Mobile App**: Built and distributed via Capacitor
- **CI/CD**: Automated testing and deployment on every push

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Ensure tests pass and code is formatted
4. Create a pull request

## 📄 License

MIT