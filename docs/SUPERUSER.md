# Managing Superusers in FoodPrepAI

## Overview
Superusers in FoodPrepAI are managed through Firebase Custom Claims. This approach integrates with the existing authentication system without requiring additional Firestore collections.

## Creating a Superuser

1. Create a user account through Firebase Authentication
2. Set custom claims for the user using Firebase Admin SDK:

```typescript
// Using Firebase Admin SDK
await admin.auth().setCustomUserClaims(uid, {
  role: 'platform_admin', // or 'platform_support'
  isSuperUser: true,
  permissions: [
    'manage_companies',
    'manage_users',
    // Add other permissions as needed
  ]
});
```

3. The user will now be redirected to `/superadmin/dashboard` on login

## Implementation Details

### Custom Claims Structure
```typescript
{
  role: 'platform_admin' | 'platform_support';
  isSuperUser: boolean;
  permissions: string[];
}
```

### Verification in Code
```typescript
// Check if user is superuser
const idTokenResult = await user.getIdTokenResult();
const isSuperUser = idTokenResult.claims.isSuperUser === true;
```

## Security Rules
Ensure Firestore security rules verify superuser claims:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isSuperUser() {
      return request.auth != null && request.auth.token.isSuperUser == true;
    }

    match /{document=**} {
      allow read: if isSuperUser();
    }
  }
}
```

## Managing Superusers via CLI
To manage superusers, use the Firebase Admin SDK in a secure environment:

```typescript
// Example CLI command implementation
async function makeSuperUser(email: string, role: 'platform_admin' | 'platform_support') {
  const user = await admin.auth().getUserByEmail(email);
  await admin.auth().setCustomUserClaims(user.uid, {
    role,
    isSuperUser: true,
    permissions: ['manage_companies', 'manage_users']
  });
}
```

## Notes
- Custom claims are limited to 1000 bytes per user
- Claims are included in the ID token and verified on the server
- Changes to custom claims require a new sign-in for the user to take effect
