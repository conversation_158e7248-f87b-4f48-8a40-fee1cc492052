# From Orders to Delivery Notes Workflow Documentation

## Overview
This document outlines the workflow for processing orders from branch locations to delivery notes in the central system. It details the roles of dispatch staff, the statuses of delivery notes, and the API endpoints involved.

## Workflow Steps
1. **Order Sync from Ionic App**
   - Orders are sent from the Ionic app to the central Next.js backend via the `/api/company/[companyId]/location/[locationId]/orders/sync` endpoint.
   - Each order includes necessary metadata and items.

2. **Delivery Note Creation**
   - Upon receiving orders, the system creates delivery notes with the following properties:
     - **Status**: Set to `PENDING_REVIEW` initially.
     - **Source**: Indicates the origin of the order (e.g., `IONIC`).
     - **Reviewed**: A boolean flag indicating whether the note has been reviewed by dispatch staff.

3. **Dispatch Dashboard**
   - Dispatch staff can access the pending delivery notes at `/company/[companyId]/dispatch/pending-orders`.
   - The dashboard displays:
     - List of all pending delivery notes.
     - Options to review, adjust quantities, finalize, or reject each delivery note.

4. **Review Process**
   - Staff can select a delivery note to review its details and make necessary adjustments.
   - Adjustments can include:
     - Modifying quantities for items that cannot be delivered.
     - Rejecting items that cannot be fulfilled.
   - Once reviewed, the staff can mark the note as reviewed or finalize it for processing.

5. **Finalization**
   - Finalizing a delivery note transitions its status to `FINALIZED` and allows it to enter the handover flow.
   - The handover flow includes steps for dispatch, driver, and shop confirmation.

## Delivery Note Statuses
- **PENDING_REVIEW**: Initial status for incoming orders requiring review.
- **FINALIZED**: Confirmed by dispatch staff and ready for delivery.
- **IN_PROGRESS**: Currently in the delivery process.
- **COMPLETED**: Successfully delivered.
- **CANCELLED**: Rejected by dispatch staff.

## API Endpoints
- **Sync Orders**: `POST /api/company/[companyId]/location/[locationId]/orders/sync`
- **Get Pending Delivery Notes**: `GET /api/company/[companyId]/delivery-notes/pending`
- **Process Delivery Note**: `PATCH /api/company/[companyId]/delivery-notes/pending`

## Conclusion
This workflow ensures that orders from branch locations are efficiently processed into delivery notes, allowing dispatch staff to manage and fulfill orders effectively. For any questions or further enhancements, please reach out to the development team.
