{"info": {"_postman_id": "3e7b2f4c-1d52-4a68-a4bc-b63fe7a19cad", "name": "FoodPrepAI Inventory Transfers", "description": "A collection for testing the enhanced inventory transfer system, including transfers with markup and branch-to-branch transfers.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "0. <PERSON> Auth Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-test-api-key", "value": "81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/test-token", "host": ["{{baseUrl}}"], "path": ["api", "auth", "test-token"]}, "description": "Get a test authentication token that will be used for all subsequent requests."}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// Parse the response body", "var jsonData = pm.response.json();", "", "// Check if the token exists in the response", "if (jsonData.token) {", "    // Set the token value to the environment variable", "    pm.environment.set(\"authToken\", jsonData.token);", "    console.log(\"Auth token saved to environment variable\");", "} else {", "    console.log(\"No token found in the response\");", "}"]}}]}, {"name": "1. View Central Kitchen Catalog", "request": {"method": "GET", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "x-test-api-key", "value": "81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb", "type": "text"}, {"key": "x-test-api-key", "value": "81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/central-kitchen/catalog?locationId={{branchLocationId}}&centralKitchenId={{centralKitchenId}}", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "central-kitchen", "catalog"], "query": [{"key": "locationId", "value": "{{branchLocationId}}"}, {"key": "centralKitchenId", "value": "{{centralKitchenId}}"}, {"key": "type", "value": "INGREDIENT", "disabled": true}, {"key": "search", "value": "betteraves", "disabled": true}]}, "description": "View available items in the central kitchen's catalog that are visible to a specific branch."}, "response": []}, {"name": "2. Get Locations", "request": {"method": "GET", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "x-test-api-key", "value": "81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/locations", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "locations"]}, "description": "Get all locations for the company to use in transfer tests."}, "response": []}, {"name": "3. Transfer with Percentage Markup (TRANSFER_OUT)", "request": {"method": "POST", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transactionType\": \"TRANSFER_OUT\",\n  \"locationId\": \"{{centralKitchenId}}\",\n  \"destinationLocationId\": \"{{branchLocationId}}\",\n  \"items\": [\n    {\n      \"itemId\": \"676aca53d253bdfa34d20544\",\n      \"itemType\": \"INGREDIENT\",\n      \"quantity\": 5,\n      \"unitId\": \"676a982bd253bdfa34d2050c\",\n      \"cost\": 20.00\n    }\n  ],\n  \"priceMarkup\": {\n    \"type\": \"PERCENTAGE\",\n    \"value\": 15,\n    \"applyTax\": false\n  },\n  \"notes\": \"Transfer with 15% percentage markup\"\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement/v2", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement", "v2"]}, "description": "Transfer out ingredients from central kitchen to branch with a 15% percentage markup."}, "response": []}, {"name": "4. Check Destination Inventory After Transfer Out", "request": {"method": "GET", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "x-test-api-key", "value": "81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/location/{{branchLocationId}}/inventory", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "location", "{{branchLocationId}}", "inventory"]}, "description": "Check the inventory at the destination location after transfer out to verify pending stock and markup details."}, "response": []}, {"name": "5. Complete Transfer with TRANSFER_IN", "request": {"method": "POST", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transactionType\": \"TRANSFER_IN\",\n  \"locationId\": \"{{branchLocationId}}\",\n  \"sourceLocationId\": \"{{centralKitchenId}}\",\n  \"items\": [\n    {\n      \"itemId\": \"676aca53d253bdfa34d20544\",\n      \"itemType\": \"INGREDIENT\",\n      \"quantity\": 5,\n      \"unitId\": \"676a982bd253bdfa34d2050c\"\n    }\n  ],\n  \"notes\": \"Received marked-up transfer from central kitchen\"\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement/v2", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement", "v2"]}, "description": "Complete the transfer by acknowledging receipt at the destination branch and moving items from pending to current stock."}, "response": []}, {"name": "6. Transfer with Fixed Markup (TRANSFER_OUT)", "request": {"method": "POST", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transactionType\": \"TRANSFER_OUT\",\n  \"locationId\": \"{{centralKitchenId}}\",\n  \"destinationLocationId\": \"{{branchLocationId}}\",\n  \"items\": [\n    {\n      \"itemId\": \"676aca54d253bdfa34d20545\",\n      \"itemType\": \"INGREDIENT\",\n      \"quantity\": 3,\n      \"unitId\": \"676a982bd253bdfa34d2050c\",\n      \"cost\": 30.00\n    }\n  ],\n  \"priceMarkup\": {\n    \"type\": \"FIXED\",\n    \"value\": 2.50,\n    \"applyTax\": false\n  },\n  \"notes\": \"Transfer with $2.50 fixed markup per unit\"\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement/v2", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement", "v2"]}, "description": "Transfer out ingredients from central kitchen to branch with a fixed amount markup of $2.50 per unit."}, "response": []}, {"name": "7. Complete Fixed Markup Transfer (TRANSFER_IN)", "request": {"method": "POST", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transactionType\": \"TRANSFER_IN\",\n  \"locationId\": \"{{branchLocationId}}\",\n  \"sourceLocationId\": \"{{centralKitchenId}}\",\n  \"items\": [\n    {\n      \"itemId\": \"676aca54d253bdfa34d20545\",\n      \"itemType\": \"INGREDIENT\",\n      \"quantity\": 3,\n      \"unitId\": \"676a982bd253bdfa34d2050c\"\n    }\n  ],\n  \"notes\": \"Received transfer with fixed markup from central kitchen\"\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement/v2", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement", "v2"]}, "description": "Complete the fixed markup transfer by acknowledging receipt at the destination branch."}, "response": []}, {"name": "8. Branch-to-Branch Transfer (TRANSFER_OUT)", "request": {"method": "POST", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transactionType\": \"TRANSFER_OUT\",\n  \"locationId\": \"{{branchLocationId}}\",\n  \"destinationLocationId\": \"{{secondBranchId}}\",\n  \"items\": [\n    {\n      \"itemId\": \"676aca53d253bdfa34d20544\",\n      \"itemType\": \"INGREDIENT\",\n      \"quantity\": 2,\n      \"unitId\": \"676a982bd253bdfa34d2050c\",\n      \"cost\": 23.00\n    }\n  ],\n  \"notes\": \"Branch to branch transfer (no markup)\"\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement/v2", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement", "v2"]}, "description": "Transfer inventory directly between two branch locations without going through central kitchen."}, "response": []}, {"name": "9. Complete Branch-to-Branch Transfer (TRANSFER_IN)", "request": {"method": "POST", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"transactionType\": \"TRANSFER_IN\",\n  \"locationId\": \"{{secondBranchId}}\",\n  \"sourceLocationId\": \"{{branchLocationId}}\",\n  \"items\": [\n    {\n      \"itemId\": \"676aca53d253bdfa34d20544\",\n      \"itemType\": \"INGREDIENT\",\n      \"quantity\": 2,\n      \"unitId\": \"676a982bd253bdfa34d2050c\"\n    }\n  ],\n  \"notes\": \"Received branch-to-branch transfer\"\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement/v2", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement", "v2"]}, "description": "Complete the branch-to-branch transfer by acknowledging receipt at the second branch."}, "response": []}, {"name": "10. Get Transfer Revenue Report", "request": {"method": "GET", "header": [{"key": "company-id", "value": "{{companyId}}", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}; session={{authToken}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "x-test-api-key", "value": "81a3ad98731ea87ea68207c54cb99ec235bb3524ed2b3984d46c991e1d25e5bb", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/reports/transfer-revenue?startDate=2025-03-01&endDate=2025-03-31", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "reports", "transfer-revenue"], "query": [{"key": "startDate", "value": "2025-03-01"}, {"key": "endDate", "value": "2025-03-31"}, {"key": "sourceLocationId", "value": "{{centralKitchenId}}", "disabled": true}, {"key": "destinationLocationId", "value": "{{branchLocationId}}", "disabled": true}, {"key": "groupBy", "value": "day", "disabled": true}]}, "description": "Generate a report of transfer revenue from markup applied to inventory transfers."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "companyId", "value": "67682466d436c5f697693330", "type": "string"}, {"key": "centralKitchenId", "value": "67697ac879a92509b1f9f245", "type": "string"}, {"key": "branchLocationId", "value": "", "type": "string", "description": "Replace with your branch location ID"}, {"key": "secondBranchId", "value": "", "type": "string", "description": "Replace with your second branch location ID"}, {"key": "authToken", "value": "", "type": "string", "description": "Your JWT auth token - will be set automatically by login request"}, {"key": "userEmail", "value": "", "type": "string", "description": "Your login email"}, {"key": "userPassword", "value": "", "type": "string", "description": "Your login password"}]}