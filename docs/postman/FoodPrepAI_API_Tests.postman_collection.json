{"info": {"name": "FoodPrepAI API Tests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "e0f9c5a3-8d6a-4f0e-b7c3-c9e3a2a1b2c3", "description": "Collection for testing FoodPrepAI APIs with cookie-based authentication using either direct API keys or JWT tokens"}, "item": [{"name": "Authentication", "item": [{"name": "Generate Test Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-test-api-key", "value": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}], "body": {"mode": "raw", "raw": "{\n    \"companyId\": \"{{companyId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/test-token", "host": ["{{baseUrl}}"], "path": ["api", "auth", "test-token"]}, "description": "Get a temporary JWT token using your API key"}, "response": []}], "description": "Authentication and token generation endpoints"}, {"name": "Test API Keys", "item": [{"name": "Create Test API Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}, {"key": "company-id", "value": "{{companyId}}"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"{{userId}}\",\n    \"role\": \"admin\",\n    \"description\": \"Postman testing key\",\n    \"expiryDays\": 30\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/test-api-keys", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "test-api-keys"]}, "description": "Create a new API key for testing (requires admin cookie auth)"}, "response": []}, {"name": "List Test API Keys", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}, {"key": "company-id", "value": "{{companyId}}"}], "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/test-api-keys", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "test-api-keys"]}, "description": "List all API keys for this company (requires admin cookie auth)"}, "response": []}], "description": "Manage API keys for testing"}, {"name": "Inventory", "item": [{"name": "Inventory Movement - Purchase", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"PURCHASE\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Weekly ingredient order\",\n    \"supplierName\": \"Farm Fresh Foods\",\n    \"invoiceNumber\": \"INV-12345\",\n    \"purchaseOrderRef\": \"PO-6789\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 10,\n            \"unitId\": \"{{unitId}}\",\n            \"cost\": 5.99,\n            \"lotNumber\": \"LOT-123\",\n            \"expiryDate\": \"2025-06-30\"\n        },\n        {\n            \"itemId\": \"{{ingredientId2}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 5,\n            \"unitId\": \"{{unitId}}\",\n            \"cost\": 12.49,\n            \"lotNumber\": \"LOT-456\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record a purchase of inventory items"}, "response": []}, {"name": "Inventory Movement - Wastage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"WASTAGE\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Spoiled inventory\",\n    \"disposalMethod\": \"DISCARDED\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 2.5,\n            \"unitId\": \"{{unitId}}\",\n            \"reason\": \"Expired\"\n        },\n        {\n            \"itemId\": \"{{ingredientId2}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 1,\n            \"unitId\": \"{{unitId}}\",\n            \"reason\": \"Quality issue\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record wastage of inventory items"}, "response": []}, {"name": "Inventory Movement - Production", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"PRODUCTION_INPUT\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Batch production #123\",\n    \"recipeId\": \"{{recipeId}}\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 5,\n            \"unitId\": \"{{unitId}}\",\n            \"expectedQuantity\": 5\n        },\n        {\n            \"itemId\": \"{{ingredientId2}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 3,\n            \"unitId\": \"{{unitId}}\",\n            \"expectedQuantity\": 2.5\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record ingredients used in production"}, "response": []}, {"name": "Inventory Movement - Production Output", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"PRODUCTION_OUTPUT\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Batch production #123 output\",\n    \"recipeId\": \"{{recipeId}}\",\n    \"items\": [\n        {\n            \"itemId\": \"{{recipeId}}\",\n            \"itemType\": \"RECIPE\",\n            \"quantity\": 10,\n            \"unitId\": \"{{unitId}}\",\n            \"expectedQuantity\": 12\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record produced recipe quantities"}, "response": []}, {"name": "Inventory Movement - Adjustment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"ADJUSTMENT\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Correcting inventory discrepancy\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 3,\n            \"unitId\": \"{{unitId}}\"\n        },\n        {\n            \"itemId\": \"{{ingredientId2}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": -1.5,\n            \"unitId\": \"{{unitId}}\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Make manual adjustments to inventory quantities (positive/negative)"}, "response": []}, {"name": "Inventory Movement - Count", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"COUNT\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Monthly inventory count\",\n    \"countBatch\": \"COUNT-{{$timestamp}}\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 45.5,\n            \"unitId\": \"{{unitId}}\"\n        },\n        {\n            \"itemId\": \"{{ingredientId2}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 23,\n            \"unitId\": \"{{unitId}}\"\n        },\n        {\n            \"itemId\": \"{{recipeId}}\",\n            \"itemType\": \"RECIPE\",\n            \"quantity\": 18,\n            \"unitId\": \"{{unitId}}\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record a physical inventory count (overrides current stock)"}, "response": []}, {"name": "Inventory Movement - Transfer Out", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"TRANSFER_OUT\",\n    \"locationId\": \"{{locationId}}\",\n    \"notes\": \"Transfer to branch location\",\n    \"destinationLocationId\": \"{{destinationLocationId}}\",\n    \"transferReference\": \"TR-{{$timestamp}}\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 10,\n            \"unitId\": \"{{unitId}}\"\n        },\n        {\n            \"itemId\": \"{{recipeId}}\",\n            \"itemType\": \"RECIPE\",\n            \"quantity\": 5,\n            \"unitId\": \"{{unitId}}\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record inventory being sent to another location"}, "response": []}, {"name": "Inventory Movement - Transfer In", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "company-id", "value": "{{companyId}}"}, {"key": "<PERSON><PERSON>", "value": "auth-token={{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"transactionType\": \"TRANSFER_IN\",\n    \"locationId\": \"{{destinationLocationId}}\",\n    \"notes\": \"Received from central kitchen\",\n    \"sourceLocationId\": \"{{locationId}}\",\n    \"transferReference\": \"TR-{{$timestamp}}\",\n    \"items\": [\n        {\n            \"itemId\": \"{{ingredientId1}}\",\n            \"itemType\": \"INGREDIENT\",\n            \"quantity\": 10,\n            \"unitId\": \"{{unitId}}\"\n        },\n        {\n            \"itemId\": \"{{recipeId}}\",\n            \"itemType\": \"RECIPE\",\n            \"quantity\": 5,\n            \"unitId\": \"{{unitId}}\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/company/{{companyId}}/inventory/movement", "host": ["{{baseUrl}}"], "path": ["api", "company", "{{companyId}}", "inventory", "movement"]}, "description": "Record inventory received from another location"}, "response": []}], "description": "Inventory management endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Only run this script if we have a test API key and we don't have a valid token yet", "if (pm.variables.get(\"testApiKey\") && (!pm.variables.get(\"tempToken\") || pm.variables.get(\"tokenExpiry\") < new Date().getTime())) {", "  pm.sendRequest({", "      url: pm.variables.get(\"baseUrl\") + \"/api/auth/test-token\",", "      method: 'POST',", "      header: {", "          'Content-Type': 'application/json',", "          'x-test-api-key': pm.variables.get(\"testApiKey\")", "      },", "      body: {", "          mode: 'raw',", "          raw: JSON.stringify({", "              companyId: pm.variables.get(\"companyId\")", "          })", "      }", "  }, function (err, res) {", "      if (!err && res.code === 200) {", "          const responseData = res.json();", "          // Set token with 55 minute expiry (token lasts 1 hour)", "          pm.variables.set(\"tempToken\", responseData.token);", "          pm.variables.set(\"tokenExpiry\", new Date().getTime() + (55 * 60 * 1000));", "          console.log(\"Generated new test token\");", "      }", "  });", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000"}]}