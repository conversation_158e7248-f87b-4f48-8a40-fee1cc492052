# User Synchronization Architecture

This document describes the user synchronization architecture between the main FoodPrepAI platform and IonicPOS.

## Overview

The user synchronization system enables seamless user data sharing between the main web application and the IonicPOS system, allowing consistent authentication and permissions across platforms while supporting offline capabilities.

### Key Design Principles

1. **Source of Truth**: The main web application serves as the central source of truth for user data
2. **Timestamp-Based Synchronization**: Efficient sync using lastModified timestamps
3. **Security First**: Comprehensive security measures including secure token authentication and hashed PINs
4. **Offline Support**: IonicPOS can function offline with user data

## Data Model

The User model has been enhanced with the following sync-related fields:

```typescript
interface IUser extends Document {
  // Existing fields...
  lastModified: Date;        // Timestamp of last modification
  syncStatus: 'pending' | 'synced';  // Current sync status
  modifiedFields?: string[]; // Tracks which fields were changed
  pin?: string;              // Hashed PIN for POS access
  isDeleted?: boolean;       // Soft delete flag for sync
  locationId?: mongoose.Types.ObjectId; // Associated location
}
```

## API Endpoints

### GET /api/company/[companyId]/users/sync

Retrieves users that have been modified since a specified timestamp.

**Query Parameters:**
- `since`: ISO timestamp to fetch changes after (optional)
- `locationId`: Filter by location (optional)
- `limit`: Number of records to return (default: 100)
- `page`: Page number for pagination (default: 1)
- `fields`: Comma-separated list of fields to include (optional)

**Response:**
```json
{
  "users": [
    {
      "_id": "userId",
      "email": "<EMAIL>",
      "displayName": "User Name",
      "role": "manager",
      "permissions": ["inventory:read"],
      "lastModified": "2023-04-01T12:00:00Z",
      "syncStatus": "synced"
    }
  ],
  "deletedUserIds": ["deletedUserId1"],
  "syncTimestamp": "2023-04-01T12:30:00Z",
  "pagination": {
    "totalCount": 150,
    "page": 1,
    "totalPages": 2,
    "hasMore": true
  }
}
```

### POST /api/company/[companyId]/users/sync

Updates user data received from the IonicPOS system.

**Request Body:**
```json
{
  "syncId": "sync-uuid-123",
  "userUpdates": [
    {
      "_id": "userId",
      "displayName": "Updated Name",
      "email": "<EMAIL>"
    }
  ]
}
```

**Response:**
```json
{
  "syncId": "sync-uuid-123",
  "status": "success", // or "partial"
  "updatedUsers": [
    {
      "userId": "userId",
      "status": "success"
    }
  ],
  "timestamp": "2023-04-01T12:30:00Z"
}
```

## Authentication

The system supports multiple authentication methods:

1. **JWT Token Authentication**: Standard web authentication via session cookie
2. **Device Token Authentication**: For IonicPOS device authentication
3. **PIN-based Authentication**: For cashier/staff quick access on POS

### Device Authentication

```typescript
// Request with device token
headers: {
  "Authorization": "Bearer <token>",
  "X-Device-Token": "<device-token>",
  "company-id": "companyId"
}
```

## User Change Tracking

Changes to user data are automatically tracked using the `trackUserChanges` utility:

```typescript
await trackUserChanges(userId, {
  displayName: "New Name",
  email: "<EMAIL>"
});
```

This updates the user and sets `lastModified`, `syncStatus`, and `modifiedFields` appropriately.

## Security Considerations

1. **Token Security**: Short-lived JWT tokens with refresh mechanism
2. **Sensitive Data**: Passwords and PINs are hashed using bcrypt
3. **Access Control**: Company-specific access control
4. **Rate Limiting**: Sync endpoints are protected with rate limiting
5. **Audit Logging**: All sync operations are logged

## Implementation Notes

### Add User Change Tracking to Existing Endpoints

All user modification endpoints should use the tracking functions:

```typescript
// Example: updating a user profile
app.patch('/api/users/:id', async (req, res) => {
  const updates = req.body;
  await trackUserChanges(req.params.id, updates);
  // ...
});
```

### Handling User Deletion

User deletion is implemented as a soft delete to ensure proper synchronization:

```typescript
await markUserDeleted(userId, companyId);
```

## Sync Process Flow

1. **Initial Sync**: IonicPOS requests all users with `since=0`
2. **Regular Sync**: IonicPOS periodically checks for changes with `since=lastSyncTimestamp`
3. **Conflict Resolution**: Server-side changes take precedence over client changes
4. **Offline Changes**: IonicPOS tracks local changes and syncs when connection is restored

## Testing Checklist

- [ ] User created on main platform syncs to IonicPOS
- [ ] User updated on main platform syncs to IonicPOS
- [ ] User deleted on main platform is removed from IonicPOS
- [ ] PIN changes sync correctly
- [ ] Permission changes sync correctly
- [ ] Authentication works consistently across platforms
- [ ] Offline access continues to work on IonicPOS

## Future Enhancements

1. **Change Versioning**: Add version numbers for stronger conflict detection
2. **Selective Sync**: Only sync users relevant to specific locations
3. **Sync Status Dashboard**: Admin interface to monitor sync status
4. **Conflict Resolution UI**: Interface for manually resolving conflicts