# JWT Authentication System Documentation

## JWT Token Structure Changes

The authentication system has been standardized across the web application and Ionic POS app with the following key changes:

### 1. Standardized Token Payload

All JWT tokens now contain the following consistent fields:

| Field | Description | Previous Names | Current Name |
|-------|-------------|----------------|--------------|
| `id` | MongoDB ObjectId of the user | `userId` | `id` |
| `email` | User's email address | `email` | `email` |
| `role` | User's role (admin, owner, etc.) | `userRole` | `role` |
| `companyId` | MongoDB ObjectId of the company | Missing in some tokens | `companyId` |
| `userType` | Type of user (company_user, etc.) | Missing in some tokens | `userType` |

### 2. Unified Cookie Naming

* Previous: Inconsistent naming (`session` in some places, `auth-token` in others)
* Current: Standardized to `auth-token` across all endpoints and middleware

### 3. Modified Endpoints

The following endpoints have been updated:

* `/api/auth/signin/route.ts` - Updated token structure and cookie name
* `/api/auth/session/route.ts` - Updated token structure and cookie name
* `/api/auth/signout/route.ts` - Updated cookie name for deletion
* `/api/auth/test-token/route.ts` - Updated token structure
* `/api/auth/verify/route.ts` - Updated cookie name for verification
* `/api/auth/admin-token/route.ts` - New temporary endpoint for token generation

### 4. Modified Middleware and Auth Helpers

* `src/middleware.ts` - Updated to check for `auth-token` cookie
* `src/lib/auth-helpers.ts` - Updated to check for `auth-token` cookie

## API Key Testing System

A new API key testing system has been implemented to facilitate Postman testing:

### 1. API Key Generation

* Endpoint: `POST /api/company/{companyId}/test-api-keys`
* Requirements: Admin or owner role
* Key Properties:
  - 32-byte hex string
  - Associated with a specific user role
  - Configurable expiry date

### 2. Using API Keys

API Keys can be used in two ways:

#### Direct Usage
```
Header: x-test-api-key: [api-key]
Header: company-id: [companyId]
```

#### Token Generation
```
POST /api/auth/test-token
```
With API key header and JSON body containing companyId, returns a fully formed JWT token

## For the Ionic App Development Team

### Important Changes

1. **JWT Token Structure**: 
   - The JWT token now consistently includes `id`, `email`, `role`, `companyId`, and `userType`
   - The field for user ID is now consistently named `id` (was previously `userId` in some places)
   - The field for user role is now consistently named `role` (was previously `userRole` in some places)

2. **Token Verification**:
   - Token verification will check for all required fields
   - Missing fields will result in authentication failures

### Implementation Notes

1. When decoding JWT tokens in the Ionic app, ensure your code expects:
   ```typescript
   interface DecodedToken {
     id: string;           // User ID (previously userId)
     email: string;        // User email
     role: string;         // User role (previously userRole)
     companyId: string;    // Company ID
     userType: string;     // User type
     iat: number;          // Issued at timestamp
     exp: number;          // Expiry timestamp
   }
   ```

2. When making API requests to the central system, ensure:
   - Authentication headers include a JWT token with all required fields
   - The company-id header matches the companyId in the token

## Security Considerations

1. **Token Expiry**: All tokens have a configurable expiry time (default: 5 days)
2. **HttpOnly Cookies**: Auth cookies are HttpOnly to prevent XSS attacks
3. **Cookie Security**: In production, cookies are set with secure flag
4. **API Key Limitations**: API keys are role-restricted and have expiry dates
