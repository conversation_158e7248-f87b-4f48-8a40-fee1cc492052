# User Sync Quick Test Guide

This guide provides a streamlined way to test the user synchronization between the main app and the Ionic app.

## Prerequisites

1. Main app running at http://localhost:3000
2. Ionic app running at http://localhost:8101
3. MongoDB connection properly configured
4. `ts-node` installed (`npm install -g ts-node typescript`)

## Step 1: Set Up Test Data

Run the test script to create test users that will be synchronized:

```bash
# Navigate to your project directory
cd /path/to/foodprepai

# Run the test script
npx ts-node scripts/test-user-sync.ts
```

This will create the following test users in your database:
- **Test Manager**: A user with manager role (email: <EMAIL>, PIN: 1234)
- **Test Staff**: A regular user (email: <EMAIL>, PIN: 5678)

## Step 2: View Users in Main App

1. Go to http://localhost:3000/company/67682466d436c5f697693330/users
2. You should see the test users in the list
3. If you don't see the users, check the browser console for errors

## Step 3: Test Sync in Ionic App

1. Go to the Ionic app at http://localhost:8101
2. Log in with your administrator credentials
3. Navigate to the user management or sync section
4. Trigger a user sync
5. Verify that the test users appear in the user list
6. Check that the roles and permissions are correctly synchronized

## Step 4: Test PIN Login

1. In the Ionic app, log out of the admin account
2. On the login screen, look for a PIN login option
3. Select one of the test users
4. Enter the PIN (1234 for Test Manager, 5678 for Test Staff)
5. Verify that login is successful and the correct permissions are applied

## Step 5: Test User Updates

1. Go back to the test script and modify it to update a user attribute
2. Run the script again to update the users
3. Trigger another sync in the Ionic app
4. Verify that the user attributes are updated correctly

## Step 6: Test User Deletion

1. Run the cleanup script to mark the test users as deleted:
   ```bash
   npx ts-node scripts/test-user-sync.ts --cleanup
   ```
2. Trigger another sync in the Ionic app
3. Verify that the deleted users are no longer visible or are marked as inactive

## Step 7: Test Manual API

If you need to test the API endpoints directly, you can use these curl commands:

```bash
# Get all users for a company
curl -X GET "http://localhost:3000/api/debug/users/67682466d436c5f697693330"

# Get user sync data (change the timestamp to get updates since that time)
curl -X GET "http://localhost:3000/api/company/67682466d436c5f697693330/users/sync?since=2025-03-01T00:00:00Z"
```

## Troubleshooting

If you encounter issues:

1. Check browser console for errors
2. Check server logs for error messages
3. Verify database connection and data
4. Make sure both applications are running
5. Clear browser cookies and local storage if needed
6. Check that your company ID is correct in the test script

## Further Testing

For more comprehensive testing, refer to the full [User Sync Testing Guide](./user-sync-testing.md).