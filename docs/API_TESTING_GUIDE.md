# API Testing Guide for FoodPrepAI

## Authentication Requirements

The FoodPrepAI API uses JWT token-based authentication with the following requirements:

### JWT Token Requirements

All JWT tokens must include:
- `id` or `userId` - The user's MongoDB ID
- `userType` - Either "company_user" or "superuser"
- `companyId` - The MongoDB ID of the company (for company_user type)
- `role` - User role such as "owner", "admin", "manager", etc.

### How to Obtain a Valid Token

#### Method 1: Web App Sign-In (Recommended)

1. Sign in to the web application using a valid user account
2. Use browser DevTools to extract the JWT token:
   - Open DevTools (F12 or Right-click → Inspect)
   - Go to Application → Cookies → Select your domain
   - Find the `auth-token` cookie and copy its value

#### Method 2: Generate a Token (Development Only)

For development environments, you can generate a token with the proper payload structure:

```javascript
// Example token generation (Node.js)
const jwt = require('jsonwebtoken');
const payload = {
  id: "USER_ID_HERE",
  userType: "company_user",
  companyId: "COMPANY_ID_HERE",
  role: "owner",
  // Add standard JWT fields
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 86400 // 24 hours
};
const token = jwt.sign(payload, process.env.JWT_SECRET);
console.log(token);
```

## Testing APIs with cURL

### Required Headers

```
company-id: YOUR_COMPANY_ID
Cookie: auth-token=YOUR_JWT_TOKEN
```

### Inventory API Example

```bash
curl -X GET 'http://localhost:3000/api/company/YOUR_COMPANY_ID/location/YOUR_LOCATION_ID/inventory' \
-H 'company-id: YOUR_COMPANY_ID' \
-H 'Cookie: auth-token=YOUR_JWT_TOKEN'
```

### Inventory Sync API Example

```bash
curl -X POST 'http://localhost:3000/api/company/YOUR_COMPANY_ID/location/YOUR_LOCATION_ID/inventory/sync' \
-H 'company-id: YOUR_COMPANY_ID' \
-H 'Cookie: auth-token=YOUR_JWT_TOKEN' \
-H 'Content-Type: application/json' \
-d '{
  "updates": [
    {
      "itemId": "INVENTORY_ITEM_ID",
      "currentStock": 50,
      "transactions": [
        {
          "type": "sale",
          "quantity": 5,
          "menuItemId": "MENU_ITEM_ID",
          "timestamp": "2025-03-29T11:22:42+02:00"
        }
      ]
    }
  ],
  "syncTimestamp": "2025-03-29T11:22:42+02:00"
}'
```

## Troubleshooting

### Common Error Messages

- **"User not found"**: The user ID in the token doesn't exist in the database
- **"No authentication token found"**: Missing or invalid auth-token cookie
- **"Invalid user type"**: The userType field is missing or invalid
- **"User does not have access to this company"**: The companyId in the token doesn't match the URL parameter
