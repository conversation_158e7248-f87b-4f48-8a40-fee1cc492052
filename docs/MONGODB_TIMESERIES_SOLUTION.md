# MongoDB Time-Series Collection Issue & Solution

## The Problem

When testing the inventory management APIs, we encountered the following error:

```
"error": "time-series insert failed: test.inventoryTransactions :: caused by :: Cannot insert into a time-series collection in a multi-document transaction: test.inventoryTransactions"
```

### Technical Explanation

MongoDB has a specific limitation: **You cannot insert data into time-series collections within a multi-document transaction**.

In our application:
1. `InventoryTransaction` is implemented as a time-series collection (as indicated in the model file)
2. The inventory movement endpoint was using a MongoDB transaction for the entire process, including:
   - Updating inventory quantities
   - Creating transaction records in the time-series collection
3. This approach conflicts with MongoDB's technical constraints for time-series collections

### Layman's Explanation

Think of MongoDB transactions like banking transactions - they ensure that either all operations succeed or none do. Time-series collections are special containers optimized for time-ordered data (like our inventory history).

The issue is similar to trying to write in a special historical ledger while also updating your current account balance, all in one atomic operation. MongoDB doesn't allow this - you need to handle the historical record separately from the current balance update.

## The Solution

We modified the inventory movement endpoint to use a two-phase approach:

### Phase 1: Inventory Update (Within Transaction)
- Start a MongoDB transaction
- Validate inventory items exist
- Calculate new quantities
- Update inventory quantities
- Commit the transaction for inventory changes

### Phase 2: Record History (Outside Transaction)
- After the transaction is committed successfully
- Insert records into the time-series collection
- Link the transaction records back to the affected inventory items

### Code Implementation

```typescript
// Phase 1: Inventory update in transaction
const session = await mongoose.startSession();
session.startTransaction();

try {
  // Process inventory updates within transaction
  // Update quantities, validate constraints, etc.
  
  // Commit inventory changes
  await session.commitTransaction();
  session.endSession();
  
  // Phase 2: Time-series operations outside transaction
  // Create time-series records
  const createdTransactions = [];
  for (const transactionData of transactionRecords) {
    const transaction = await InventoryTransaction.create([transactionData]);
    createdTransactions.push(transaction[0]);
  }
  
  // Update response with created transaction IDs
  // ...
} catch (error) {
  await session.abortTransaction();
  session.endSession();
  throw error;
}
```

## Benefits of This Approach

1. **Works Within MongoDB Limitations**: Complies with technical constraints of time-series collections
2. **Maintains Data Integrity**: Still uses transactions for critical inventory updates
3. **Preserves Audit Trail**: All inventory movements are still recorded in time-series collection
4. **Minimal Risk**: Even if time-series insert fails, the inventory quantities would still be correct
5. **Supports Sync Pattern**: Aligns with the application's inventory sync architecture

## Additional Information

- Time-series collections in MongoDB are optimized for:
  - Efficient storage of time-ordered data
  - Fast time-range queries
  - Automatic data expiration policies
- Our application uses time-series collections to maintain a complete history of inventory changes
- The implementation supports the inventory sync pattern where Ionic POS app periodically syncs with central system
