# Shared Component Library Architecture

## Overview

The FoodPrepAI monorepo contains a sophisticated shared component library that works seamlessly across both the Next.js web application and the Ionic React mobile application. The library automatically adapts components based on the platform, providing optimal user experiences on both web and mobile.

## Architecture

### Package Structure

```
packages/
├── design-tokens/          # Unified design system tokens
├── ui-core/               # Platform-agnostic component APIs
├── ui-components/         # Concrete component implementations
├── shared-types/          # TypeScript type definitions
├── shared-utils/          # Common utilities
└── api-client/           # Shared API client
```

### Core Principles

1. **Platform Adaptability**: Components automatically render differently on web vs mobile
2. **Unified API**: Same component interface works across both platforms
3. **Design Consistency**: Shared design tokens ensure visual consistency
4. **Performance**: Platform-specific optimizations where needed
5. **Developer Experience**: Simple, intuitive APIs with excellent TypeScript support

## Package Details

### 1. Design Tokens (`@foodprepai/design-tokens`)

Centralized design system values that work across platforms:

- **Colors**: Primary, secondary, semantic, and neutral color palettes
- **Typography**: Font families, sizes, weights, and line heights
- **Spacing**: Consistent spacing scale
- **Shadows**: Box shadow definitions
- **Border Radius**: Consistent corner radius values
- **Animation**: Duration and timing function tokens

**Platform Mappings:**
- Web: Maps to CSS custom properties compatible with shadcn/ui
- Ionic: Maps to Ionic CSS variables for native styling

**Usage:**
```typescript
import { colors, typography, spacing } from '@foodprepai/design-tokens';

// Use in components
const primaryColor = colors.primary[500];
const bodyFont = typography.fontFamily.sans;
```

### 2. UI Core (`@foodprepai/ui-core`)

Platform-agnostic foundation providing:

**Platform Detection:**
```typescript
import { getPlatformConfig, platformSelect } from '@foodprepai/ui-core';

const config = getPlatformConfig();
// { platform: 'web' | 'ionic', isMobile: boolean, isWeb: boolean }

const buttonText = platformSelect({
  web: 'Click Me',
  ionic: 'Tap Me',
  default: 'Press Me'
});
```

**Component Factory:**
```typescript
import { createPlatformComponent } from '@foodprepai/ui-core';

const MyComponent = createPlatformComponent(
  WebImplementation,
  IonicImplementation,
  'MyComponent'
);
```

**Type Definitions:**
- `BaseComponentProps`: Common props for all components
- `ButtonProps`, `TableProps`, `ModalProps`: Unified component interfaces
- Platform-specific type utilities

### 3. UI Components (`@foodprepai/ui-components`)

Concrete implementations of platform-adaptive components:

#### Button Component

```typescript
import { Button } from '@foodprepai/ui-components';

// Works on both platforms
<Button 
  variant="primary" 
  size="lg" 
  loading={isLoading}
  icon="checkmark"
  onClick={handleClick}
>
  Save Changes
</Button>
```

**Platform Behavior:**
- **Web**: Renders as shadcn/ui Button with Radix primitives
- **Mobile**: Renders as IonButton with native touch feedback

#### Table Component

```typescript
import { Table } from '@foodprepai/ui-components';

const columns = [
  { key: 'name', header: 'Name', sortable: true },
  { key: 'email', header: 'Email' },
  { key: 'status', header: 'Status', render: (value) => <Badge>{value}</Badge> }
];

<Table
  columns={columns}
  data={users}
  loading={isLoading}
  onRowClick={handleRowClick}
  selectable
  selectedRows={selectedIds}
  onSelectionChange={setSelectedIds}
/>
```

**Platform Behavior:**
- **Web**: HTML table with sorting, selection, and hover states
- **Mobile**: IonList with IonItems, optimized for touch with card-like layout

#### Modal Component

```typescript
import { Modal } from '@foodprepai/ui-components';

<Modal
  isOpen={isOpen}
  onClose={handleClose}
  title="Edit User"
  size="lg"
  footer={
    <div className="flex space-x-2">
      <Button variant="outline" onClick={handleClose}>Cancel</Button>
      <Button onClick={handleSave}>Save</Button>
    </div>
  }
>
  <UserForm user={selectedUser} />
</Modal>
```

**Platform Behavior:**
- **Web**: Radix Dialog with backdrop blur and animations
- **Mobile**: IonModal with native sheet behavior and breakpoints

## Platform Detection

The library automatically detects the platform based on:

1. **Environment Detection**: Checks for `window.Ionic` or `window.Capacitor`
2. **User Agent**: Mobile device detection
3. **Manual Override**: Can be explicitly set via `setPlatformConfig()`

```typescript
import { setPlatformConfig } from '@foodprepai/ui-components';

// Force platform (useful for testing)
setPlatformConfig('ionic');

// Or configure with options
setPlatformConfig({ 
  platform: 'web', 
  preferIonic: false 
});
```

## Component Development Guidelines

### Creating New Components

1. **Define Types**: Start with TypeScript interfaces in `ui-core/types`
2. **Create Platform Implementations**: Separate files for web and mobile
3. **Use Factory Pattern**: Combine implementations with `createPlatformComponent`
4. **Add Tests**: Test both platform implementations
5. **Document Usage**: Include examples and platform differences

### Example Component Structure

```
packages/ui-components/src/NewComponent/
├── NewComponent.tsx        # Main component factory
├── WebNewComponent.tsx     # Web-specific implementation
├── IonicNewComponent.tsx   # Ionic-specific implementation
└── index.ts               # Exports
```

### Best Practices

1. **Consistent APIs**: Keep prop interfaces identical across platforms
2. **Platform Optimization**: Leverage platform-specific features where appropriate
3. **Accessibility**: Ensure WCAG compliance on both platforms
4. **Performance**: Use platform-specific optimizations (e.g., virtualization)
5. **Testing**: Test components on both platforms

## Styling Strategy

### Design Token Usage

```typescript
// Use design tokens in implementations
import { colors, spacing } from '@foodprepai/design-tokens';

const styles = {
  backgroundColor: colors.primary[500],
  padding: spacing[4],
  borderRadius: borderRadius.md
};
```

### Platform-Specific Styling

**Web (Tailwind CSS):**
```typescript
className={cn(
  'bg-primary text-primary-foreground',
  'hover:bg-primary/90',
  'transition-colors duration-200'
)}
```

**Ionic (CSS Variables):**
```typescript
// Ionic components use design tokens via CSS variables
<IonButton color="primary" fill="solid">
  {children}
</IonButton>
```

## Migration Guide

### From Existing Components

1. **Audit Current Components**: Identify reusable patterns
2. **Extract Common Logic**: Move business logic to shared utilities
3. **Create Unified Interfaces**: Define props that work for both platforms
4. **Implement Platform Versions**: Create web and mobile implementations
5. **Update Imports**: Replace direct imports with unified components

### Example Migration

**Before:**
```typescript
// In web app
import { Button } from '@/components/ui/button';

// In mobile app  
import { IonButton } from '@ionic/react';
```

**After:**
```typescript
// In both apps
import { Button } from '@foodprepai/ui-components';
```

## Build Configuration

The monorepo uses Turborepo for efficient building:

```json
{
  "build": {
    "dependsOn": ["^build"],
    "outputs": ["dist/**"]
  }
}
```

### Package Build Scripts

Each package includes standard scripts:
- `build`: Compile TypeScript to JavaScript
- `dev`: Watch mode for development
- `typecheck`: Type checking without emitting
- `test`: Run unit tests

## Usage in Applications

### Next.js Web App

```typescript
// app/layout.tsx
import { initializePlatform } from '@foodprepai/ui-components';
import '@foodprepai/design-tokens/css/tokens.css';

initializePlatform('web');

export default function RootLayout({ children }) {
  return (
    <html>
      <body>{children}</body>
    </html>
  );
}
```

### Ionic React App

```typescript
// main.tsx
import { initializePlatform } from '@foodprepai/ui-components';
import '@foodprepai/design-tokens/css/ionic-tokens.css';

initializePlatform('ionic');

// App component renders normally
```

## Testing Strategy

### Unit Tests

Each component includes tests for both platform implementations:

```typescript
describe('Button Component', () => {
  it('renders web version correctly', () => {
    setPlatformConfig('web');
    render(<Button>Click me</Button>);
    // Test web-specific behavior
  });

  it('renders ionic version correctly', () => {
    setPlatformConfig('ionic');
    render(<Button>Click me</Button>);
    // Test ionic-specific behavior
  });
});
```

### Integration Tests

Test component interactions within each platform context.

## Performance Considerations

### Code Splitting

Components are designed for optimal code splitting:
- Platform-specific code only loads when needed
- Shared utilities are extracted to minimize bundle size

### Bundle Analysis

Monitor bundle sizes to ensure efficient loading:
- Web bundles exclude Ionic-specific code
- Mobile bundles exclude web-specific dependencies

## Troubleshooting

### Common Issues

1. **Platform Detection**: If components render incorrectly, check platform configuration
2. **CSS Variables**: Ensure correct token CSS files are imported
3. **Type Errors**: Make sure peer dependencies are installed correctly

### Debug Mode

Enable debug logging to troubleshoot platform detection:

```typescript
import { env } from '@foodprepai/ui-components';

console.log('Platform environment:', env);
```

## Future Roadmap

1. **Additional Components**: Form controls, data visualization, navigation
2. **Theme System**: Runtime theme switching
3. **Animation Library**: Shared motion primitives
4. **Storybook Integration**: Visual component documentation
5. **Design Token Studio**: Visual token management

## Contributing

1. Follow existing patterns and conventions
2. Include both platform implementations for new components
3. Add comprehensive tests and documentation
4. Ensure TypeScript types are properly exported
5. Test on both web and mobile environments before submitting

This architecture provides a solid foundation for sharing components across platforms while maintaining optimal user experiences on each platform.