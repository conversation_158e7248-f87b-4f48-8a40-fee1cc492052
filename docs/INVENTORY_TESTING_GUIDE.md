# Inventory Testing Guide

This guide explains how to test the inventory movement system, specifically the new purchase-to-receipt workflow using Postman.

## Prerequisites

1. Ensure you have the latest Postman collection imported
2. Set up the environment variables:
   - `companyId`
   - `locationId`
   - `authToken`
   - `ingredientId1` (a valid inventory item ID)
   - `unitId` (a valid unit ID)

## Testing PURCHASE and RECEIVED Workflows

### Step 1: Create a PURCHASE Transaction

1. Open the "Inventory Movement - Purchase" request in Postman
2. Ensure the request body looks like this:

```json
{
  "transactionType": "PURCHASE",
  "locationId": "{{locationId}}",
  "notes": "Test purchase order",
  "supplierName": "Test Supplier Ltd",
  "invoiceNumber": "INV-12345",
  "purchaseOrderRef": "PO-12345",
  "items": [
    {
      "itemId": "{{ingredientId1}}",
      "itemType": "INGREDIENT",
      "quantity": 10,
      "unitId": "{{unitId}}",
      "cost": 15.99,
      "lotNumber": "LOT-001",
      "expiryDate": "2025-06-30"
    }
  ]
}
```

3. Send the request
4. Note the transaction ID from the response for later reference

### Step 2: Verify Pending Stock Increased

1. Open a new GET request to:
   ```
   {{baseUrl}}/api/company/{{companyId}}/inventory/item/{{ingredientId1}}?locationId={{locationId}}
   ```

2. Add the same headers:
   - Content-Type: application/json
   - company-id: {{companyId}}
   - Cookie: auth-token={{authToken}}

3. Send the request

4. Verify in the response that:
   - `pendingStock` has increased by 10
   - `currentStock` remains unchanged

### Step 3: Create a RECEIVED Transaction

1. Open the "Inventory Movement - Received" request (or duplicate the Purchase request)
2. Modify the request body to:

```json
{
  "transactionType": "RECEIVED",
  "locationId": "{{locationId}}",
  "notes": "Received partial order",
  "reference": {
    "id": "[Transaction ID from Step 1]",
    "type": "PURCHASE_ORDER"
  },
  "items": [
    {
      "itemId": "{{ingredientId1}}",
      "itemType": "INGREDIENT",
      "quantity": 5,
      "unitId": "{{unitId}}"
    }
  ]
}
```

3. Send the request

### Step 4: Verify Stock Movement

1. Send the GET request from Step 2 again
2. Verify in the response that:
   - `currentStock` has increased by 5
   - `pendingStock` has decreased by 5

### Step 5: Complete Receipt (Optional)

1. Create another RECEIVED transaction for the remaining 5 units
2. Verify that `currentStock` increases by 5 more and `pendingStock` becomes 0

## Error Testing

You should also test these error scenarios:

### Receiving More Than Pending

Try to receive more units than are pending:

```json
{
  "transactionType": "RECEIVED",
  "locationId": "{{locationId}}",
  "notes": "Attempt to receive too many",
  "items": [
    {
      "itemId": "{{ingredientId1}}",
      "itemType": "INGREDIENT",
      "quantity": 100,
      "unitId": "{{unitId}}"
    }
  ]
}
```

Expected: Error message stating "Cannot receive 100 units of item [ID] as only [X] units are pending"

## Common Postman Errors

If you encounter errors:

1. **Authentication errors**: Ensure your auth-token is current
2. **Invalid ID errors**: Confirm all IDs (companyId, locationId, itemId, unitId) are valid MongoDB ObjectIds
3. **Missing headers**: Verify you have all required headers (Content-Type, company-id, Cookie)

## API Reference

### Inventory Movement Endpoint

**URL**: `{{baseUrl}}/api/company/{{companyId}}/inventory/movement`

**Method**: POST

**Headers**:
- Content-Type: application/json
- company-id: {{companyId}}
- Cookie: auth-token={{authToken}}

### Inventory Item Details Endpoint

**URL**: `{{baseUrl}}/api/company/{{companyId}}/inventory/item/{{itemId}}?locationId={{locationId}}`

**Method**: GET

**Headers**:
- Content-Type: application/json
- company-id: {{companyId}}
- Cookie: auth-token={{authToken}}
