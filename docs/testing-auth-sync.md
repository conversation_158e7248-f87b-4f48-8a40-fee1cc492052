# Testing User Synchronization with Full Authentication

This guide walks you through testing the user synchronization between the main app and Ionic app with proper authentication.

## Prerequisites

1. Main app running at http://localhost:3000
2. Ionic app running at http://localhost:8101
3. Test users created (you can run `npm run test:user-sync` to create them)

## Step 1: Test Authentication and User Sync

### 1.1. Log in and get authentication token

1. Go to http://localhost:3000/test/auth
2. Enter the credentials for a test user:
   - Email: `<EMAIL>` (or another valid user)
   - Password: `Test1234!`
   - Company ID: `67682466d436c5f697693330` (or your specific company ID)
3. Click "Login & Sync"
4. Verify that you receive a JWT token and see the user data

### 1.2. View users with authentication

1. Click "View Users Page" or go to http://localhost:3000/test/users
2. The page should now show users without authentication errors
3. Verify that the user list loads correctly

## Step 2: Integrate with Ionic App

### 2.1. Configure Ionic app to use the authentication

In the Ionic app, you need to modify the authentication service to use the JWT token:

```typescript
// Example authentication code for Ionic app
async login(email: string, password: string): Promise<boolean> {
  try {
    const response = await this.http.post('http://localhost:3000/api/auth/signin', {
      email,
      password
    }).toPromise();
    
    if (response && response.token) {
      // Store the token in secure storage
      await this.storage.set('auth_token', response.token);
      await this.storage.set('user_data', response.user);
      
      // Set current user and company
      this.currentUser = response.user;
      if (response.user.companyId) {
        await this.storage.set('company_id', response.user.companyId);
      }
      
      return true;
    }
    return false;
  } catch (error) {
    console.error('Login error:', error);
    return false;
  }
}
```

### 2.2. Configure Ionic app for user sync

Update your user sync service to include the token in requests:

```typescript
// Example sync code for Ionic app
async syncUsers(): Promise<any> {
  try {
    // Get authentication token
    const token = await this.storage.get('auth_token');
    const companyId = await this.storage.get('company_id');
    
    if (!token || !companyId) {
      throw new Error('Authentication required');
    }
    
    // Get timestamp of last sync
    const lastSync = await this.storage.get('last_user_sync') || '1970-01-01T00:00:00Z';
    
    // Make authenticated request to sync endpoint
    const response = await this.http.get(
      `http://localhost:3000/api/company/${companyId}/users/sync?since=${lastSync}`, 
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'company-id': companyId
        }
      }
    ).toPromise();
    
    if (response && response.users) {
      // Store users in local database
      await this.storeUsers(response.users);
      
      // Handle deleted users
      if (response.deletedUserIds && response.deletedUserIds.length > 0) {
        await this.handleDeletedUsers(response.deletedUserIds);
      }
      
      // Store sync timestamp
      await this.storage.set('last_user_sync', response.syncTimestamp);
      
      return {
        success: true,
        syncedUsers: response.users.length,
        timestamp: response.syncTimestamp
      };
    }
    
    return { success: false, error: 'Invalid response' };
  } catch (error) {
    console.error('User sync error:', error);
    return { success: false, error: error.message || 'Sync failed' };
  }
}
```

## Step 3: Testing Authentication Edge Cases

### 3.1. Test with expired token

1. Get a valid token from the authentication endpoint
2. Wait for the token to expire (you can manually modify the token expiry for testing)
3. Try to access the sync endpoint with the expired token
4. Verify that you receive a 401 Unauthorized response

### 3.2. Test with invalid permissions

1. Create a user with insufficient permissions
2. Try to access the sync endpoint with this user's token
3. Verify that you receive a 403 Forbidden response

### 3.3. Test with incorrect company ID

1. Get a valid token for a user in Company A
2. Try to access the sync endpoint for Company B
3. Verify that you receive a 403 Forbidden response

## Step 4: Testing PIN Authentication

1. Go to http://localhost:3000/test/auth
2. Use a user with a PIN: `<EMAIL>` (created by the test script)
3. Get the authentication token
4. Use this token to access the sync endpoint
5. Verify that the user can sync data

## Testing Checklist

Use this checklist to ensure your testing is complete:

- [ ] Login authentication works
- [ ] JWT token is returned and valid
- [ ] Session cookie is set correctly
- [ ] User sync endpoint requires authentication
- [ ] Authenticated requests to sync endpoint succeed
- [ ] Sync returns correct user data
- [ ] Updated users are properly synced
- [ ] Deleted users are properly handled
- [ ] Token expiration works as expected
- [ ] Permission validation works as expected
- [ ] Company ID validation works as expected
- [ ] Ionic app integration works correctly

## Troubleshooting Common Issues

### Authentication Failures

- **Issue**: Token validation fails
  - **Solution**: Verify JWT_SECRET is consistent between signing and verification

- **Issue**: No permission to access sync endpoint
  - **Solution**: Ensure user has the required permissions or role

### Sync Issues

- **Issue**: No users returned from sync
  - **Solution**: Check the company ID and timestamp parameters

- **Issue**: Only partial user data returned
  - **Solution**: Verify the field projection in the sync endpoint

### Ionic Integration Issues

- **Issue**: CORS errors
  - **Solution**: Ensure CORS middleware is properly configured

- **Issue**: Token not included in requests
  - **Solution**: Check authentication header formatting

## Next Steps

After successfully testing the authentication and sync, you can:

1. Implement bidirectional synchronization
2. Add conflict resolution logic
3. Implement offline capabilities
4. Add user PIN authentication for POS users