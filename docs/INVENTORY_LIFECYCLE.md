# FoodPrepAI Inventory Lifecycle Documentation

This document provides a comprehensive guide to the inventory movement lifecycle in the FoodPrepAI system, focusing on the central kitchen inventory management.

## Table of Contents

1. [Inventory System Overview](#inventory-system-overview)
2. [Collection Structure](#collection-structure)
3. [Complete Inventory Lifecycle](#complete-inventory-lifecycle)
   - [Ingredient Purchasing Flow](#ingredient-purchasing-flow)
   - [Recipe Production Flow](#recipe-production-flow)
   - [Inventory Adjustment Flow](#inventory-adjustment-flow)
   - [Location Transfer Flow](#location-transfer-flow)
4. [Transaction Types Reference](#transaction-types-reference)
5. [API Documentation](#api-documentation)
6. [Examples and Payloads](#examples-and-payloads)

## Inventory System Overview

The inventory system consists of three primary components:

1. **Central System**: Maintains master records of ingredients and recipes
2. **Branch Inventories**: Tracks actual stock levels at different branch locations
3. **Ionic POS App**: Handles real-time updates to branch inventory at each location

## Collection Structure

### Ingredients Collection
- Raw materials purchased from suppliers
- Contains core product information (name, description, base unit, etc.)
- Tracks central stock levels with `currentStock` field
- Uses `pendingStock` field to track purchase orders
- When ingredients are received, they move from `pendingStock` to `currentStock`

### Recipes Collection
- Composed of ingredients and/or sub-recipes
- Contains recipe information (name, yield, components, etc.)
- Tracks central stock levels with `currentStock` field
- Uses `pendingStock` field for tracking production in progress
- When production completes, items move from `pendingStock` to `currentStock`

### BranchInventories Collection
- Tracks inventory of ingredients and recipes AT specific branch locations
- Each record is linked to a specific branch via `locationId`
- Updates based on sales, transfers, etc. at the branch level
- Synced with the Ionic POS app at branches
- Uses a one-to-one relationship between items and locations

## Complete Inventory Lifecycle

### Ingredient Purchasing Flow

The purchasing flow involves two key transactions: PURCHASE and RECEIVED.

#### Step 1: PURCHASE Transaction
When ingredients are ordered from a supplier, a PURCHASE transaction is created.

1. **Effect on Inventory**:
   - Increases `pendingStock` for the ingredient
   - Does not affect `currentStock`

2. **Example Payload**:
```json
{
  "transactionType": "PURCHASE",
  "locationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "quantity": 20,
      "unitId": "676a982bd253bdfa34d2050c",
      "cost": 12.99
    }
  ],
  "supplierName": "Main Supplier Co.",
  "invoiceNumber": "INV-12346",
  "notes": "Regular order"
}
```

3. **Expected Response**:
```json
{
  "success": true,
  "message": "Successfully processed PURCHASE transaction",
  "transactionId": "67ea62694d3103678bb2aa94",
  "transactionType": "PURCHASE",
  "affectedItems": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "itemName": "Betteraves (kg)",
      "previousStock": 0,
      "newStock": 20,
      "difference": 20
    }
  ]
}
```

4. **Database Effect**:
```json
{
  "_id": "676aca53d253bdfa34d20544",
  "name": "Betteraves (kg)",
  "currentStock": 0,        // Remains unchanged
  "pendingStock": 20        // Increased by 20
}
```

#### Step 2: RECEIVED Transaction
When ingredients arrive from a supplier, a RECEIVED transaction is created.

1. **Effect on Inventory**:
   - Decreases `pendingStock` by the received quantity
   - Increases `currentStock` by the received quantity
   - Must have sufficient `pendingStock` to receive

2. **Example Payload**:
```json
{
  "transactionType": "RECEIVED",
  "locationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "quantity": 20,
      "unitId": "676a982bd253bdfa34d2050c"
    }
  ],
  "notes": "Received complete order"
}
```

3. **Expected Response**:
```json
{
  "success": true,
  "message": "Successfully processed RECEIVED transaction",
  "transactionId": "67ea62994d3103678bb2aa96",
  "transactionType": "RECEIVED",
  "affectedItems": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "itemName": "Betteraves (kg)",
      "previousStock": 0,
      "newStock": 20,
      "difference": 20
    }
  ]
}
```

4. **Database Effect**:
```json
{
  "_id": "676aca53d253bdfa34d20544",
  "name": "Betteraves (kg)",
  "currentStock": 20,       // Increased by 20
  "pendingStock": 0         // Decreased by 20
}
```

### Recipe Production Flow

Recipe production involves three transactions: PRODUCTION_INPUT, PRODUCTION_OUTPUT, and RECEIVED.

#### Step 1: PRODUCTION_INPUT Transaction
When ingredients are used to produce a recipe, a PRODUCTION_INPUT transaction is created.

1. **Effect on Inventory**:
   - Decreases `currentStock` of the ingredients used
   - Must have sufficient `currentStock` for each ingredient

2. **Example Payload**:
```json
{
  "transactionType": "PRODUCTION_INPUT",
  "locationId": "67697ac879a92509b1f9f245",
  "recipeId": "67ea6e5a54a3a2d7aab3c1a3",
  "items": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "quantity": 10,
      "unitId": "676a982bd253bdfa34d2050c"
    },
    {
      "itemId": "676aca53d253bdfa34d2052c",
      "itemType": "INGREDIENT",
      "quantity": 2,
      "unitId": "676a982bd253bdfa34d20515"
    }
  ],
  "notes": "Using ingredients for recipe production"
}
```

3. **Expected Response**:
```json
{
  "success": true,
  "message": "Successfully processed PRODUCTION_INPUT transaction",
  "transactionId": "67ea71674d3103678bb2aad2",
  "transactionType": "PRODUCTION_INPUT",
  "affectedItems": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "itemName": "Betteraves (kg)",
      "previousStock": 20,
      "newStock": 10,
      "difference": -10
    },
    {
      "itemId": "676aca53d253bdfa34d2052c",
      "itemType": "INGREDIENT",
      "itemName": "Aluminium Foil",
      "previousStock": 10,
      "newStock": 8,
      "difference": -2
    }
  ]
}
```

4. **Database Effect**:
```json
// Betteraves ingredient
{
  "_id": "676aca53d253bdfa34d20544",
  "name": "Betteraves (kg)",
  "currentStock": 10,       // Decreased by 10
  "pendingStock": 0         // Unchanged
}

// Aluminium Foil ingredient
{
  "_id": "676aca53d253bdfa34d2052c",
  "name": "Aluminium Foil",
  "currentStock": 8,        // Decreased by 2
  "pendingStock": 0         // Unchanged
}
```

#### Step 2: PRODUCTION_OUTPUT Transaction
When a recipe is produced, a PRODUCTION_OUTPUT transaction is created.

1. **Effect on Inventory**:
   - Increases `pendingStock` of the recipe
   - Does not affect `currentStock` of the recipe

2. **Example Payload**:
```json
{
  "transactionType": "PRODUCTION_OUTPUT",
  "locationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "quantity": 5,
      "unitId": "676a982bd253bdfa34d2050c"
    }
  ],
  "notes": "Completed production of Test Recipe"
}
```

3. **Expected Response**:
```json
{
  "success": true,
  "message": "Successfully processed PRODUCTION_OUTPUT transaction",
  "transactionId": "67ea718c4d3103678bb2aadc",
  "transactionType": "PRODUCTION_OUTPUT",
  "affectedItems": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "itemName": "Test Recipe with Betteraves & Aluminium",
      "previousStock": 0,
      "newStock": 0,
      "difference": 0,
      "previousPendingStock": 0,
      "newPendingStock": 5,
      "pendingDifference": 5
    }
  ]
}
```

4. **Database Effect**:
```json
{
  "_id": "67ea6e5a54a3a2d7aab3c1a3",
  "name": "Test Recipe with Betteraves & Aluminium",
  "currentStock": 0,        // Unchanged
  "pendingStock": 5         // Increased by 5
}
```

#### Step 3: RECEIVED Transaction for Recipe
When a recipe production is finalized, a RECEIVED transaction is created to make the recipe available for use.

1. **Effect on Inventory**:
   - Decreases `pendingStock` of the recipe by the received quantity
   - Increases `currentStock` of the recipe by the received quantity

2. **Example Payload**:
```json
{
  "transactionType": "RECEIVED",
  "locationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "quantity": 5,
      "unitId": "676a982bd253bdfa34d2050c"
    }
  ],
  "notes": "Finalizing production of Test Recipe"
}
```

3. **Expected Response**:
```json
{
  "success": true,
  "message": "Successfully processed RECEIVED transaction",
  "transactionId": "67ea71ab4d3103678bb2aae1",
  "transactionType": "RECEIVED",
  "affectedItems": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "itemName": "Test Recipe with Betteraves & Aluminium",
      "previousStock": 0,
      "newStock": 5,
      "difference": 5,
      "previousPendingStock": 5,
      "newPendingStock": 0,
      "pendingDifference": -5
    }
  ]
}
```

4. **Database Effect**:
```json
{
  "_id": "67ea6e5a54a3a2d7aab3c1a3",
  "name": "Test Recipe with Betteraves & Aluminium",
  "currentStock": 5,        // Increased by 5
  "pendingStock": 0         // Decreased by 5
}
```

### Inventory Adjustment Flow

#### WASTAGE Transaction
For recording damaged, expired, or otherwise unusable inventory.

1. **Effect on Inventory**:
   - Decreases `currentStock` of the item
   - Must have sufficient `currentStock`

2. **Example Payload**:
```json
{
  "transactionType": "WASTAGE",
  "locationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "quantity": 2,
      "unitId": "676a982bd253bdfa34d2050c",
      "reason": "Spoiled"
    }
  ],
  "notes": "Weekly wastage check"
}
```

#### ADJUSTMENT Transaction
For manually correcting inventory discrepancies.

1. **Effect on Inventory**:
   - Sets `currentStock` directly to the expected value
   - Can increase or decrease stock based on the difference

2. **Example Payload**:
```json
{
  "transactionType": "ADJUSTMENT",
  "locationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "676aca53d253bdfa34d20544",
      "itemType": "INGREDIENT",
      "expectedQuantity": 9,
      "unitId": "676a982bd253bdfa34d2050c",
      "reason": "Physical count discrepancy"
    }
  ],
  "notes": "Adjusting after monthly inventory count"
}
```

### Location Transfer Flow

#### Step 1: TRANSFER_OUT Transaction
When inventory is transferred from one location to another, a TRANSFER_OUT transaction is created at the source location.

1. **Effect on Inventory**:
   - Decreases `currentStock` at the source location
   - Increases `pendingStock` at the destination location
   - Must have sufficient `currentStock` at the source

2. **Example Payload**:
```json
{
  "transactionType": "TRANSFER_OUT",
  "locationId": "67697ac879a92509b1f9f245",
  "destinationLocationId": "67697ac879a92509b1f9f246",
  "items": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "quantity": 2,
      "unitId": "676a982bd253bdfa34d2050c"
    }
  ],
  "notes": "Transfer to branch location"
}
```

#### Step 2: TRANSFER_IN Transaction
When the transferred inventory is received at the destination, a TRANSFER_IN transaction is created.

1. **Effect on Inventory**:
   - Decreases `pendingStock` at the destination location
   - Increases `currentStock` at the destination location
   - Must have sufficient `pendingStock` at the destination

2. **Example Payload**:
```json
{
  "transactionType": "TRANSFER_IN",
  "locationId": "67697ac879a92509b1f9f246",
  "sourceLocationId": "67697ac879a92509b1f9f245",
  "items": [
    {
      "itemId": "67ea6e5a54a3a2d7aab3c1a3",
      "itemType": "RECIPE",
      "quantity": 2,
      "unitId": "676a982bd253bdfa34d2050c"
    }
  ],
  "notes": "Received transfer from central kitchen"
}
```

## Transaction Types Reference

| Transaction Type | Effect on `currentStock` | Effect on `pendingStock` | Validation Requirements |
|------------------|--------------------------|--------------------------|-------------------------|
| PURCHASE | No effect | Increases | Valid item ID and quantity |
| RECEIVED | Increases | Decreases | Sufficient pendingStock |
| PRODUCTION_INPUT | Decreases | No effect | Sufficient currentStock |
| PRODUCTION_OUTPUT | No effect | Increases | Valid recipe ID |
| WASTAGE | Decreases | No effect | Sufficient currentStock |
| ADJUSTMENT | Sets directly | No effect | Valid item ID |
| TRANSFER_OUT | Decreases at source | Increases at destination | Sufficient currentStock at source |
| TRANSFER_IN | Increases at destination | Decreases at destination | Sufficient pendingStock at destination |
| COUNT | Sets directly | No effect | Valid item ID |
| SALE | Decreases | No effect | Sufficient currentStock |

## API Documentation

### Inventory Movement API Endpoint

```
POST /api/company/{companyId}/inventory/movement/v2
```

### Request Body Structure

The API expects a JSON payload with the following structure:

```typescript
interface InventoryMovementRequest {
  transactionType: 'PURCHASE' | 'WASTAGE' | 'ADJUSTMENT' | 'PRODUCTION_INPUT' | 
                  'PRODUCTION_OUTPUT' | 'TRANSFER_OUT' | 'TRANSFER_IN' | 'COUNT' |
                  'SALE' | 'RECEIVED' | 'DISPATCHED' | 'THEFT';
  locationId: string;
  destinationLocationId?: string;  // Required for TRANSFER_OUT
  sourceLocationId?: string;       // Required for TRANSFER_IN
  recipeId?: string;               // Optional for recipe-related operations
  items: InventoryMovementItem[];
  notes?: string;
  invoiceNumber?: string;          // For PURCHASE
  supplierName?: string;           // For PURCHASE
  countBatch?: string;             // For COUNT
  reference?: {                    // Optional reference to other documents
    type: string;
    id: string;
  };
}

interface InventoryMovementItem {
  itemId: string;
  itemType: 'INGREDIENT' | 'RECIPE';
  quantity?: number;               // Required for most transactions
  expectedQuantity?: number;       // Required for ADJUSTMENT
  unitId: string;
  cost?: number;                   // For PURCHASE
  lotNumber?: string;              // Optional tracking information
  expiryDate?: string;             // Optional tracking information
  reason?: string;                 // For WASTAGE, ADJUSTMENT
}
```

### Response Structure

```typescript
interface InventoryMovementResponse {
  success: boolean;
  message: string;
  transactionId: string;
  movementDate: Date;
  transactionType: string;
  affectedItems: AffectedItem[];
}

interface AffectedItem {
  itemId: string;
  itemType: 'INGREDIENT' | 'RECIPE';
  itemName: string;
  previousStock: number;
  newStock: number;
  difference: number;
  previousPendingStock?: number;   // Included for PURCHASE, RECEIVED
  newPendingStock?: number;        // Included for PURCHASE, RECEIVED
  pendingDifference?: number;      // Included for PURCHASE, RECEIVED
  transactionId: string;
}
```

## Examples and Payloads

See the transaction examples provided in each section of the inventory lifecycle above.

For additional examples and testing scenarios, refer to the [INVENTORY_MOVEMENT_V2_TESTING.md](./INVENTORY_MOVEMENT_V2_TESTING.md) document.
