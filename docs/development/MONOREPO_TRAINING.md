# Monorepo Architecture Training Guide

Comprehensive training material for understanding and working effectively with the FoodPrepAI monorepo architecture.

## 🎯 Learning Objectives

After completing this training, you will be able to:
- Understand monorepo concepts and benefits
- Navigate the FoodPrepAI monorepo structure efficiently
- Use shared packages effectively
- Debug monorepo-specific issues
- Follow best practices for cross-package development
- Contribute to the monorepo following established patterns

## 📚 Module 1: Monorepo Fundamentals

### What is a Monorepo?

A monorepo (monolithic repository) is a software development strategy where code for many projects is stored in the same repository.

**Traditional Multi-Repo Structure:**
```
├── foodprepai-web/          (separate repo)
├── foodprepai-mobile/       (separate repo)
├── foodprepai-shared/       (separate repo)
└── foodprepai-api-client/   (separate repo)
```

**Our Monorepo Structure:**
```
foodprepai/
├── apps/
│   ├── web/           # Next.js web app
│   └── mobile/        # Ionic mobile app
├── packages/
│   ├── shared-types/     # Common TypeScript types
│   ├── shared-utils/     # Utility functions
│   ├── api-client/       # HTTP client
│   ├── ui-components/    # React components
│   └── database-models/  # MongoDB schemas
└── tools/              # Development tools
```

### Benefits of Our Monorepo

1. **Code Sharing**: Common types, utilities, and components
2. **Atomic Changes**: Update multiple packages in one commit
3. **Simplified Dependencies**: Single source of truth for versions
4. **Better Collaboration**: All code in one place
5. **Unified Tooling**: Same linting, testing, and build tools

### Challenges and Solutions

| Challenge | Our Solution |
|-----------|--------------|
| Build complexity | Turborepo for orchestration |
| Dependency management | npm workspaces |
| Code ownership | Clear package boundaries |
| Testing strategy | Shared testing configuration |

## 📦 Module 2: Package Architecture

### Package Types

#### 1. Applications (`apps/`)
- **Purpose**: Deployable applications
- **Dependencies**: Can use all shared packages
- **Examples**: Web app, mobile app

#### 2. Shared Libraries (`packages/`)
- **Purpose**: Reusable code across applications
- **Dependencies**: Limited, well-defined dependencies
- **Examples**: Types, utilities, components

#### 3. Tools (`tools/`)
- **Purpose**: Development and build tools
- **Dependencies**: Development-only
- **Examples**: ESLint configs, TypeScript configs

### Dependency Graph Deep Dive

```mermaid
graph TD
    A[apps/web] --> B[shared-types]
    A --> C[shared-utils]
    A --> D[api-client]
    A --> E[ui-components]
    A --> F[database-models]
    
    G[apps/mobile] --> B
    G --> C
    G --> D
    G --> E
    
    C --> B
    D --> B
    D --> C
    E --> B
    E --> C
    F --> B
    F --> C
    
    H[tools/eslint-config] --> I[tools/tsconfig]
```

### Package Naming Convention

- **Scope**: `@foodprepai/`
- **Format**: `@foodprepai/package-name`
- **Examples**:
  - `@foodprepai/shared-types`
  - `@foodprepai/api-client`
  - `@foodprepai/ui-components`

## 🔧 Module 3: Development Workflow

### Daily Development Cycle

```bash
# 1. Start your day
git pull origin main
npm run health-check

# 2. Start development
npm run dev

# 3. Make changes to shared packages
cd packages/shared-types
# Edit files
npm run build --workspace=packages/shared-types

# 4. Test changes in apps
# Apps will automatically pick up rebuilt packages

# 5. Run tests
npm run test

# 6. Commit changes
git add .
git commit -m "feat(shared-types): add new user interface"
```

### Cross-Package Development

**Scenario**: Adding a new feature that requires changes to multiple packages

1. **Start with types** (foundation first):
   ```typescript
   // packages/shared-types/src/notifications.ts
   export interface Notification {
     id: string;
     type: 'info' | 'warning' | 'error' | 'success';
     message: string;
     timestamp: Date;
   }
   ```

2. **Add utilities**:
   ```typescript
   // packages/shared-utils/src/notifications.ts
   import { Notification } from '@foodprepai/shared-types';
   
   export const createNotification = (
     type: Notification['type'], 
     message: string
   ): Notification => ({
     id: crypto.randomUUID(),
     type,
     message,
     timestamp: new Date()
   });
   ```

3. **Create UI components**:
   ```typescript
   // packages/ui-components/src/NotificationToast.tsx
   import { Notification } from '@foodprepai/shared-types';
   
   export const NotificationToast: React.FC<{notification: Notification}> = ...
   ```

4. **Use in applications**:
   ```typescript
   // apps/web/src/components/NotificationCenter.tsx
   import { NotificationToast } from '@foodprepai/ui-components';
   import { createNotification } from '@foodprepai/shared-utils';
   ```

### Build Order Understanding

Packages must be built in dependency order:

```bash
# Correct order (automated by Turborepo)
1. shared-types     (no dependencies)
2. shared-utils     (depends on shared-types)
3. api-client       (depends on shared-types, shared-utils)
4. ui-components    (depends on shared-types, shared-utils)
5. database-models  (depends on shared-types, shared-utils)
6. apps/web         (depends on all packages)
7. apps/mobile      (depends on most packages)
```

## 🧪 Module 4: Testing Strategy

### Testing Pyramid in Monorepo

```
    🔺 E2E Tests
   🔺🔺 Integration Tests
  🔺🔺🔺 Unit Tests
```

#### Unit Tests
- **Location**: Each package has its own tests
- **Scope**: Individual functions and components
- **Tools**: Jest, React Testing Library

```typescript
// packages/shared-utils/src/__tests__/date.test.ts
import { formatDate } from '../date';

describe('formatDate', () => {
  it('formats date correctly', () => {
    const date = new Date('2024-01-15');
    expect(formatDate(date, 'MM/dd/yyyy')).toBe('01/15/2024');
  });
});
```

#### Integration Tests
- **Location**: Test package interactions
- **Scope**: Multiple packages working together
- **Tools**: Jest with mocked dependencies

```typescript
// apps/web/src/__tests__/integration/api-client.test.ts
import { authClient } from '@foodprepai/api-client';
import { User } from '@foodprepai/shared-types';

describe('Auth Integration', () => {
  it('login returns proper user type', async () => {
    const user = await authClient.login('<EMAIL>', 'password');
    expect(user).toMatchObject<User>({
      id: expect.any(String),
      email: '<EMAIL>',
      // ... other User properties
    });
  });
});
```

#### E2E Tests
- **Location**: `apps/web/cypress/` or `apps/web/playwright/`
- **Scope**: Full user workflows
- **Tools**: Cypress or Playwright

### Running Tests

```bash
# Run all tests
npm run test

# Run tests for specific package
npm run test --workspace=packages/shared-utils

# Run tests in watch mode during development
npm run test:watch --workspace=packages/api-client

# Run E2E tests
npm run test:e2e --workspace=apps/web
```

## 🔍 Module 5: Debugging & Troubleshooting

### Common Scenarios and Solutions

#### 1. "Cannot find module" Errors

**Problem**: Import errors for shared packages
```
Cannot find module '@foodprepai/shared-types'
```

**Diagnosis**:
```bash
# Check if package is built
ls packages/shared-types/dist

# Check if it's properly referenced
npm ls @foodprepai/shared-types
```

**Solution**:
```bash
npm run build --workspace=packages/shared-types
```

#### 2. Stale Types

**Problem**: TypeScript not picking up type changes

**Solution**:
```bash
# Rebuild types package
npm run build --workspace=packages/shared-types

# Restart TypeScript in VS Code
# Cmd/Ctrl + Shift + P -> "TypeScript: Restart TS Server"
```

#### 3. Hot Reload Not Working

**Problem**: Changes in shared packages don't trigger app reload

**Solution**: Use Turborepo dev mode:
```bash
npm run dev  # Uses Turborepo to watch all packages
```

### Debugging Tools

#### 1. VS Code Debugging
- Use provided launch configurations
- Debug API routes with Node.js debugger
- Debug React components with React DevTools

#### 2. Network Debugging
```typescript
// Enable API client logging
import { apiClient } from '@foodprepai/api-client/base';

apiClient.interceptors.request.use(request => {
  console.log('API Request:', request);
  return request;
});
```

#### 3. Build Debugging
```bash
# Verbose build output
npm run build -- --verbose

# Check dependency graph
npx madge --extensions ts,tsx src/ --image deps.svg
```

## 🎨 Module 6: Best Practices

### Code Organization

#### 1. Feature-First Structure
```
apps/web/src/features/
├── inventory/
│   ├── components/
│   ├── hooks/
│   ├── api/
│   └── types/
└── orders/
    ├── components/
    ├── hooks/
    ├── api/
    └── types/
```

#### 2. Shared Package Structure
```
packages/shared-types/src/
├── index.ts           # Main exports
├── api.ts            # API-related types
├── user.ts           # User-related types
├── inventory.ts      # Inventory types
└── common.ts         # Common utility types
```

### Import Patterns

#### ✅ Good Import Patterns
```typescript
// Use specific imports
import { User, Order } from '@foodprepai/shared-types';
import { formatDate } from '@foodprepai/shared-utils/date';
import { Button } from '@foodprepai/ui-components';

// Use subpath exports
import { ApiResponse } from '@foodprepai/shared-types/api';
```

#### ❌ Avoid These Patterns
```typescript
// Don't import entire packages
import * as SharedTypes from '@foodprepai/shared-types';

// Don't use relative imports across packages
import { User } from '../../../packages/shared-types/src/user';

// Don't create circular dependencies
// Package A imports from Package B, which imports from Package A
```

### Type Safety

#### Strong Typing
```typescript
// ✅ Good: Specific types
interface CreateUserRequest {
  name: string;
  email: string;
  role: 'admin' | 'user';
}

// ❌ Bad: Weak typing
interface CreateUserRequest {
  [key: string]: any;
}
```

#### Generic Utilities
```typescript
// ✅ Good: Reusable generic types
type ApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

// Usage
const userResponse: ApiResponse<User> = await api.getUser(id);
```

### Performance Considerations

#### Bundle Size
- Use tree-shaking friendly exports
- Avoid importing large libraries in shared packages
- Use dynamic imports for heavy components

```typescript
// ✅ Good: Dynamic import
const HeavyChart = lazy(() => import('@foodprepai/ui-components/HeavyChart'));

// ❌ Bad: Static import of large library
import { entireHeavyLibrary } from 'heavy-library';
```

#### Build Performance
- Use TypeScript project references
- Leverage Turborepo caching
- Minimize dependencies between packages

## 🏆 Module 7: Advanced Topics

### Custom Hooks Pattern
```typescript
// packages/shared-utils/src/hooks/useApi.ts
export const useApi = <T>(endpoint: string) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    apiClient.get<T>(endpoint)
      .then(response => setData(response.data))
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, [endpoint]);
  
  return { data, loading, error };
};
```

### Code Generation
Use our generators for consistency:

```bash
# Generate new component
npm run generate:component

# Generate new API endpoint
npm run generate:api
```

### Deployment Considerations

#### Environment Variables
```bash
# Root level (for build tools)
TURBO_TOKEN=your-token

# App level (for runtime)
NEXT_PUBLIC_API_URL=https://api.example.com
DATABASE_URL=mongodb://localhost:27017/foodprepai
```

#### Build Optimization
```bash
# Use Turborepo remote caching
export TURBO_TOKEN="your-token"
export TURBO_TEAM="your-team"

# Build with caching
npm run build
```

## 📋 Module 8: Practical Exercises

### Exercise 1: Create a New Feature (30 minutes)

**Goal**: Add a "themes" feature across the monorepo

1. **Add types** in `packages/shared-types/src/theme.ts`:
   ```typescript
   export interface Theme {
     id: string;
     name: string;
     colors: {
       primary: string;
       secondary: string;
       background: string;
     };
   }
   ```

2. **Add utilities** in `packages/shared-utils/src/theme.ts`:
   ```typescript
   export const applyTheme = (theme: Theme) => { /* implementation */ };
   ```

3. **Create component** in `packages/ui-components/src/ThemeProvider.tsx`

4. **Use in web app** in `apps/web/src/components/Layout.tsx`

### Exercise 2: Debug a Cross-Package Issue (15 minutes)

**Scenario**: Type imports not working

1. Identify the problem
2. Check build status
3. Rebuild dependencies
4. Verify imports

### Exercise 3: Add API Endpoint (45 minutes)

Use the API generator to create a complete CRUD endpoint for "categories"

```bash
npm run generate:api
# Follow prompts to create categories API
```

## 📖 Further Reading

### Documentation Links
- [Turborepo Documentation](https://turbo.build/repo/docs)
- [npm Workspaces](https://docs.npmjs.com/cli/v7/using-npm/workspaces)
- [TypeScript Project References](https://www.typescriptlang.org/docs/handbook/project-references.html)

### Internal Documentation
- [Developer Onboarding Guide](./DEVELOPER_ONBOARDING.md)
- [Debugging Guide](./DEBUGGING_GUIDE.md)
- [Shared Packages Guide](./SHARED_PACKAGES_GUIDE.md)
- [Cross-Package Workflows](./CROSS_PACKAGE_WORKFLOWS.md)

## 🎓 Assessment

### Knowledge Check

1. **What are the main benefits of our monorepo structure?**
2. **In what order should packages be built and why?**
3. **How do you debug import issues with shared packages?**
4. **What's the difference between apps and packages?**
5. **How do you add a new shared component?**

### Practical Assessment

Complete a mini-project:
1. Add a new shared type
2. Create a utility function using that type
3. Build a UI component
4. Use the component in both web and mobile apps
5. Write tests for each piece
6. Create proper documentation

## 🤝 Getting Help

1. **Documentation**: Check `/docs` folder first
2. **Health Check**: Run `npm run health-check`
3. **Team Resources**: Ask team members
4. **Issues**: Create GitHub issue for bugs
5. **Pair Programming**: Schedule time with experienced developers

Congratulations! You now have a solid understanding of the FoodPrepAI monorepo architecture. Keep practicing these concepts and refer back to this guide as needed. 🎉