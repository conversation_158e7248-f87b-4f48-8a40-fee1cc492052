# Developer Onboarding Guide

Welcome to the FoodPrepAI Monorepo! This guide will help you get up and running quickly with our development environment.

## 🎯 Overview

The FoodPrepAI monorepo contains two main applications:
- **Web App** (`apps/web/`): Next.js application for restaurant management
- **Mobile App** (`apps/mobile/`): Ionic React POS application

Both apps share common code through our shared packages system.

## 🚀 Quick Start (5 minutes)

### 1. Prerequisites Check

Ensure you have the correct versions installed:

```bash
# Check Node.js version (required: 18+)
node --version

# Check npm version (required: 8+)
npm --version

# Install nvm if needed (macOS/Linux)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

### 2. Repository Setup

```bash
# Clone the repository
git clone https://github.com/Jpkay/foodprepai.git
cd foodprepai

# Install all dependencies (this may take 2-3 minutes)
npm install --legacy-peer-deps

# Set up git hooks
npm run prepare

# Verify installation
npm run typecheck
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit with your local settings
# Ask a team member for the required environment variables
```

### 4. Start Development

```bash
# Start all applications
npm run dev

# Or start specific apps
npm run dev:web    # Web app on http://localhost:3000
npm run dev:mobile # Mobile app on http://localhost:8100
```

## 📚 Deep Dive Setup

### Repository Structure Understanding

```
foodprepai/
├── apps/
│   ├── web/           # Next.js web application
│   └── mobile/        # Ionic React mobile app
├── packages/
│   ├── shared-types/     # Common TypeScript interfaces
│   ├── shared-utils/     # Utility functions
│   ├── api-client/       # API communication layer
│   ├── ui-components/    # Shared UI components
│   └── database-models/  # Database schemas
├── tools/
│   ├── eslint-config/    # Linting rules
│   ├── tsconfig/         # TypeScript configs
│   └── prettier-config/  # Code formatting
└── docs/                 # Documentation
```

### Development Environment Setup

#### Required VS Code Extensions

Install these extensions for optimal development experience:

```bash
# Install recommended extensions (will be automated in VS Code workspace)
code --install-extension ms-vscode.typescript-hero
code --install-extension esbenp.prettier-vscode
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension bradlc.vscode-tailwindcss
code --install-extension ms-vscode.vscode-jest
```

#### Git Configuration

```bash
# Set up conventional commits
npm install -g @commitlint/cli @commitlint/config-conventional

# Configure git hooks (already done in setup)
# This enables automatic linting and formatting on commit
```

### First Development Task

To verify everything works, try this simple task:

1. **Make a small change**:
   ```bash
   # Edit a file in apps/web/src/app/page.tsx
   # Change the main heading text
   ```

2. **Test the change**:
   ```bash
   npm run dev:web
   # Visit http://localhost:3000 to see your change
   ```

3. **Run quality checks**:
   ```bash
   npm run lint      # Check for linting issues
   npm run typecheck # Verify TypeScript types
   npm run test      # Run unit tests
   ```

4. **Commit your change**:
   ```bash
   git add .
   git commit -m "feat(web): update main page heading"
   # This will trigger automatic formatting and linting
   ```

## 🛠️ Development Workflows

### Working with Shared Packages

When you need to modify shared packages:

1. **Make changes in the package**:
   ```bash
   # Example: Adding a new type to shared-types
   cd packages/shared-types/src
   # Edit your files
   ```

2. **Build the package**:
   ```bash
   npm run build --workspace=packages/shared-types
   ```

3. **Test in consuming apps**:
   ```bash
   npm run dev:web    # See changes in web app
   npm run dev:mobile # See changes in mobile app
   ```

### Creating New Components

Use our generators for consistency:

```bash
# Generate a new shared component
npm run generate:component

# Generate a new API endpoint
npm run generate:api

# Generate a new page
npm run generate:page
```

### Running Tests

```bash
# Run all tests
npm run test

# Run tests for specific package
npm run test --workspace=packages/shared-utils

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

### Database Operations

```bash
# Start local MongoDB (if using Docker)
docker-compose up -d mongodb

# Run database migrations
npm run db:migrate

# Seed test data
npm run db:seed

# Reset database
npm run db:reset
```

## 🐛 Troubleshooting

### Common Issues

#### "Cannot find module" errors
```bash
# Clear all node_modules and reinstall
npm run clean:deps
npm install --legacy-peer-deps
```

#### TypeScript errors after pulling changes
```bash
# Rebuild all packages
npm run build
npm run typecheck
```

#### Turborepo cache issues
```bash
# Clear Turborepo cache
npx turbo clean
npm run build
```

#### Port conflicts
```bash
# Web app (3000), Mobile app (8100)
# Kill processes on these ports:
lsof -ti:3000 | xargs kill -9
lsof -ti:8100 | xargs kill -9
```

### Getting Help

1. **Check documentation**: Browse `/docs` folder
2. **Search existing issues**: Check GitHub issues
3. **Ask the team**: Use your team communication channel
4. **Debug logs**: Enable debug mode with `DEBUG=* npm run dev`

## 🎯 Your First Week Goals

### Day 1: Environment Setup
- [ ] Complete quick start setup
- [ ] Successfully run both applications
- [ ] Make a small test commit

### Day 2-3: Codebase Exploration
- [ ] Read architecture documentation
- [ ] Explore shared packages
- [ ] Review testing setup
- [ ] Understand deployment process

### Day 4-5: First Contribution
- [ ] Pick a small bug fix or feature
- [ ] Create a PR with proper formatting
- [ ] Address code review feedback
- [ ] Merge your first contribution

## 📖 Next Steps

After completing onboarding:

1. **Read specialized guides**:
   - [API Development Guide](./API_DEVELOPMENT.md)
   - [Component Development Guide](./COMPONENT_DEVELOPMENT.md)
   - [Mobile Development Guide](./MOBILE_DEVELOPMENT.md)

2. **Join team practices**:
   - Daily standups
   - Code review processes
   - Sprint planning

3. **Set up advanced tools**:
   - [Debugging setup](./DEBUGGING_GUIDE.md)
   - [Performance monitoring](./PERFORMANCE_GUIDE.md)
   - [Testing workflows](./TESTING_GUIDE.md)

## 🏆 Success Metrics

You're successfully onboarded when you can:
- [ ] Start both applications without errors
- [ ] Make changes to shared packages and see them in apps
- [ ] Run the full test suite
- [ ] Create a pull request that passes all checks
- [ ] Navigate the codebase confidently

## 🤝 Contributing

Now that you're set up, check out our [Contributing Guide](../CONTRIBUTING.md) for:
- Code style guidelines
- Review process
- Release procedures
- Team conventions

Welcome to the team! 🎉