# Shared Packages Usage Guide

This guide demonstrates how to effectively use and contribute to our shared packages in the FoodPrepAI monorepo.

## 📦 Package Overview

### Core Packages

| Package | Purpose | Used By |
|---------|---------|---------|
| `@foodprepai/shared-types` | TypeScript interfaces and types | All apps and packages |
| `@foodprepai/shared-utils` | Common utility functions | All apps and packages |
| `@foodprepai/api-client` | HTTP client and API calls | Web and mobile apps |
| `@foodprepai/ui-components` | Shared React components | Web and mobile apps |
| `@foodprepai/database-models` | MongoDB schemas and models | Backend and scripts |

### Tool Packages

| Package | Purpose | Used By |
|---------|---------|---------|
| `@foodprepai/eslint-config` | Linting rules | All TypeScript packages |
| `@foodprepai/tsconfig` | TypeScript configurations | All TypeScript packages |
| `@foodprepai/prettier-config` | Code formatting rules | All packages |

## 🎯 Usage Examples

### 1. Shared Types (`@foodprepai/shared-types`)

**Basic Usage**:
```typescript
// In web app or mobile app
import { User, Order, InventoryItem } from '@foodprepai/shared-types';
import { ApiResponse } from '@foodprepai/shared-types/api';
import { AuthTokens } from '@foodprepai/shared-types/auth';

// Type-safe component props
interface UserProfileProps {
  user: User;
  onUpdate: (user: Partial<User>) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onUpdate }) => {
  // TypeScript will provide full type safety and autocomplete
  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
      <button onClick={() => onUpdate({ isActive: !user.isActive })}>
        Toggle Status
      </button>
    </div>
  );
};
```

**Advanced Usage with Generics**:
```typescript
// Using generic API response types
import { ApiResponse, PaginatedResponse } from '@foodprepai/shared-types/api';

// Type-safe API response handling
const fetchUsers = async (): Promise<PaginatedResponse<User>> => {
  const response = await apiClient.get<PaginatedResponse<User>>('/users');
  return response.data;
};

// Using union types for status handling
import { OrderStatus } from '@foodprepai/shared-types/orders';

const getStatusColor = (status: OrderStatus): string => {
  switch (status) {
    case 'pending': return 'yellow';
    case 'confirmed': return 'blue';
    case 'completed': return 'green';
    case 'cancelled': return 'red';
    default:
      // TypeScript will catch if we miss any status
      const exhaustiveCheck: never = status;
      return 'gray';
  }
};
```

### 2. Shared Utils (`@foodprepai/shared-utils`)

**Date and Time Utilities**:
```typescript
import { 
  formatDate, 
  parseDate, 
  getBusinessDays,
  isBusinessHour 
} from '@foodprepai/shared-utils/date';

// Consistent date formatting across apps
const orderDate = formatDate(order.createdAt, 'MM/dd/yyyy');
const businessDays = getBusinessDays(startDate, endDate);

// Business logic utilities
if (isBusinessHour(new Date())) {
  // Show "Order now" button
} else {
  // Show "Order for tomorrow" message
}
```

**Validation Utilities**:
```typescript
import { 
  validateEmail, 
  validatePhone, 
  sanitizeInput,
  isValidPrice
} from '@foodprepai/shared-utils/validation';

// Form validation
const validateUserInput = (data: any) => {
  const errors: string[] = [];
  
  if (!validateEmail(data.email)) {
    errors.push('Invalid email address');
  }
  
  if (!validatePhone(data.phone)) {
    errors.push('Invalid phone number');
  }
  
  if (!isValidPrice(data.budget)) {
    errors.push('Invalid budget amount');
  }
  
  return errors;
};

// Input sanitization
const sanitizedDescription = sanitizeInput(userInput);
```

**Inventory Calculations**:
```typescript
import { 
  calculateTotalValue, 
  convertUnits, 
  isLowStock,
  calculateReorderPoint
} from '@foodprepai/shared-utils/inventory';

// Inventory management
const totalInventoryValue = calculateTotalValue(inventoryItems);
const kgAmount = convertUnits(5, 'lbs', 'kg');
const needsReorder = isLowStock(item.currentStock, item.minStock);
const reorderPoint = calculateReorderPoint(item.averageUsage, item.leadTime);
```

### 3. API Client (`@foodprepai/api-client`)

**Basic API Calls**:
```typescript
import { apiClient } from '@foodprepai/api-client';
import { authClient } from '@foodprepai/api-client/auth';
import { inventoryClient } from '@foodprepai/api-client/inventory';

// Authentication
const login = async (email: string, password: string) => {
  try {
    const response = await authClient.login(email, password);
    return response.data;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};

// Inventory operations
const fetchInventory = async (locationId: string) => {
  const inventory = await inventoryClient.getByLocation(locationId);
  return inventory;
};

// Generic API calls with full type safety
const createOrder = async (orderData: CreateOrderRequest) => {
  const response = await apiClient.post<Order>('/orders', orderData);
  return response.data;
};
```

**Advanced Usage with Interceptors**:
```typescript
import { apiClient } from '@foodprepai/api-client/base';

// Add authentication token automatically
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle errors globally
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 4. UI Components (`@foodprepai/ui-components`)

**Basic Component Usage**:
```typescript
import { Button, Modal, Table } from '@foodprepai/ui-components';
import { DataTable } from '@foodprepai/ui-components/Table';

// Using shared components
const UserList: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  
  return (
    <div>
      <Button variant="primary" onClick={() => setShowModal(true)}>
        Add User
      </Button>
      
      <DataTable
        data={users}
        columns={userColumns}
        onRowClick={(user) => handleUserClick(user)}
      />
      
      <Modal 
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Add New User"
      >
        <UserForm onSubmit={handleAddUser} />
      </Modal>
    </div>
  );
};
```

**Form Components**:
```typescript
import { FormField, Select, DatePicker } from '@foodprepai/ui-components/forms';
import { useForm } from 'react-hook-form';

const OrderForm: React.FC = () => {
  const { control, handleSubmit } = useForm();
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormField
        name="customerName"
        label="Customer Name"
        control={control}
        rules={{ required: 'Customer name is required' }}
      />
      
      <Select
        name="location"
        label="Location"
        options={locationOptions}
        control={control}
      />
      
      <DatePicker
        name="deliveryDate"
        label="Delivery Date"
        control={control}
        minDate={new Date()}
      />
    </form>
  );
};
```

### 5. Database Models (`@foodprepai/database-models`)

**Using Mongoose Models**:
```typescript
import { UserModel, OrderModel, InventoryModel } from '@foodprepai/database-models';
import { User, Order } from '@foodprepai/shared-types';

// API route handler
export async function POST(request: Request) {
  try {
    const userData: Partial<User> = await request.json();
    
    // Create user with validation
    const user = new UserModel(userData);
    await user.save();
    
    return Response.json({ success: true, user });
  } catch (error) {
    return Response.json(
      { error: 'Failed to create user' }, 
      { status: 400 }
    );
  }
}

// Complex queries
const getActiveOrdersWithInventory = async (locationId: string) => {
  const orders = await OrderModel
    .find({ 
      locationId, 
      status: { $in: ['pending', 'confirmed'] } 
    })
    .populate('items.inventoryItem')
    .sort({ createdAt: -1 });
    
  return orders;
};
```

## 🔄 Development Workflow

### Making Changes to Shared Packages

1. **Make your changes**:
   ```bash
   # Edit files in packages/shared-types/src/
   # Add new types, update existing ones
   ```

2. **Build the package**:
   ```bash
   npm run build --workspace=packages/shared-types
   ```

3. **Test in consuming apps**:
   ```bash
   # Start the apps to see your changes
   npm run dev:web
   npm run dev:mobile
   ```

4. **Run tests**:
   ```bash
   npm run test --workspace=packages/shared-types
   npm run test  # Run all tests
   ```

### Adding New Shared Functionality

**Example: Adding a new utility function**

1. **Create the function**:
   ```typescript
   // packages/shared-utils/src/pricing.ts
   export const calculateDiscount = (
     originalPrice: number, 
     discountPercent: number
   ): number => {
     return originalPrice * (1 - discountPercent / 100);
   };
   
   export const formatCurrency = (
     amount: number, 
     currency: string = 'USD'
   ): string => {
     return new Intl.NumberFormat('en-US', {
       style: 'currency',
       currency: currency,
     }).format(amount);
   };
   ```

2. **Export from index**:
   ```typescript
   // packages/shared-utils/src/index.ts
   export * from './pricing';
   export * from './date';
   export * from './validation';
   // ... other exports
   ```

3. **Add tests**:
   ```typescript
   // packages/shared-utils/src/__tests__/pricing.test.ts
   import { calculateDiscount, formatCurrency } from '../pricing';
   
   describe('pricing utilities', () => {
     test('calculateDiscount works correctly', () => {
       expect(calculateDiscount(100, 10)).toBe(90);
       expect(calculateDiscount(50, 25)).toBe(37.5);
     });
     
     test('formatCurrency formats correctly', () => {
       expect(formatCurrency(100)).toBe('$100.00');
       expect(formatCurrency(99.99)).toBe('$99.99');
     });
   });
   ```

4. **Build and test**:
   ```bash
   npm run build --workspace=packages/shared-utils
   npm run test --workspace=packages/shared-utils
   ```

5. **Use in apps**:
   ```typescript
   // In web or mobile app
   import { calculateDiscount, formatCurrency } from '@foodprepai/shared-utils';
   
   const discountedPrice = calculateDiscount(order.total, coupon.discount);
   const formattedPrice = formatCurrency(discountedPrice);
   ```

## 🎨 Best Practices

### 1. Type Safety First
```typescript
// ✅ Good: Strong typing
export interface CreateUserRequest {
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'staff';
}

// ❌ Bad: Weak typing
export interface CreateUserRequest {
  [key: string]: any;
}
```

### 2. Consistent API Patterns
```typescript
// ✅ Good: Consistent error handling
export const apiClient = {
  async get<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await axios.get(url);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
};

// ❌ Bad: Inconsistent patterns
export const getUsers = () => axios.get('/users');
export const getOrders = async () => {
  try {
    return await axios.get('/orders');
  } catch (e) {
    throw e;
  }
};
```

### 3. Tree-shaking Friendly Exports
```typescript
// ✅ Good: Named exports for tree-shaking
export const formatDate = (date: Date) => { /* ... */ };
export const parseDate = (dateString: string) => { /* ... */ };

// ❌ Bad: Default exports with objects
export default {
  formatDate: (date: Date) => { /* ... */ },
  parseDate: (dateString: string) => { /* ... */ }
};
```

### 4. Documentation and Examples
```typescript
/**
 * Calculates the reorder point for inventory items
 * @param averageUsage - Average daily usage
 * @param leadTime - Lead time in days
 * @param safetyStock - Safety stock percentage (default: 0.2)
 * @returns Recommended reorder point quantity
 * 
 * @example
 * ```typescript
 * const reorderPoint = calculateReorderPoint(10, 5, 0.3);
 * // Returns 65 (10 * 5 * 1.3)
 * ```
 */
export const calculateReorderPoint = (
  averageUsage: number,
  leadTime: number,
  safetyStock: number = 0.2
): number => {
  return Math.ceil(averageUsage * leadTime * (1 + safetyStock));
};
```

## 🚀 Contributing Guidelines

### Before Adding New Functionality

1. **Check if it already exists** - Search existing packages
2. **Consider the scope** - Is it truly shared across apps?
3. **Think about API design** - Will other developers find it intuitive?
4. **Plan for testing** - How will you test the functionality?

### Code Review Checklist

- [ ] Types are properly defined and exported
- [ ] Functions are pure and side-effect free when possible
- [ ] Good TypeScript practices (no `any`, proper generics)
- [ ] Tests cover main use cases and edge cases
- [ ] Documentation includes examples
- [ ] Changes are backwards compatible
- [ ] Package builds successfully
- [ ] Used in at least one consuming app

### Performance Considerations

- Keep bundle size small
- Avoid heavy dependencies in shared packages
- Use dynamic imports for optional functionality
- Consider tree-shaking implications

This guide should help you effectively use and contribute to our shared packages. For questions or suggestions, check our [Contributing Guide](../CONTRIBUTING.md) or reach out to the team!