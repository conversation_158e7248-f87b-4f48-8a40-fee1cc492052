# Cross-Package Development Workflows

Guide for efficiently working with changes that span multiple packages in the FoodPrepAI monorepo.

## 🔄 Understanding Dependencies

### Package Dependency Graph

```
📦 Root Package
├── 🌐 apps/web
│   ├── depends on: shared-types, shared-utils, api-client, ui-components, database-models
│   └── affects: none (consumer only)
├── 📱 apps/mobile  
│   ├── depends on: shared-types, shared-utils, api-client, ui-components
│   └── affects: none (consumer only)
├── 🎯 packages/shared-types
│   ├── depends on: none
│   └── affects: ALL other packages
├── 🛠️ packages/shared-utils
│   ├── depends on: shared-types
│   └── affects: api-client, ui-components, database-models, apps
├── 🌐 packages/api-client
│   ├── depends on: shared-types, shared-utils
│   └── affects: apps only
├── 🎨 packages/ui-components
│   ├── depends on: shared-types, shared-utils
│   └── affects: apps only
└── 🗄️ packages/database-models
    ├── depends on: shared-types, shared-utils
    └── affects: web app only (backend)
```

## 🚀 Common Cross-Package Scenarios

### 1. Adding a New API Endpoint

**Scenario**: Adding user preferences API

**Step-by-step workflow**:

```bash
# 1. Start with types (foundation first)
cd packages/shared-types/src

# Add new types
echo "export interface UserPreferences {
  id: string;
  userId: string;
  theme: 'light' | 'dark';
  notifications: boolean;
  language: string;
}" >> user.ts

# Build types package
npm run build --workspace=packages/shared-types
```

```bash
# 2. Add database model
cd packages/database-models/src

# Create UserPreferences model
echo "import { Schema, model } from 'mongoose';
import { UserPreferences } from '@foodprepai/shared-types';

const userPreferencesSchema = new Schema<UserPreferences>({
  userId: { type: String, required: true, unique: true },
  theme: { type: String, enum: ['light', 'dark'], default: 'light' },
  notifications: { type: Boolean, default: true },
  language: { type: String, default: 'en' },
});

export const UserPreferencesModel = model<UserPreferences>('UserPreferences', userPreferencesSchema);" > UserPreferences.ts

# Update index.ts
echo "export { UserPreferencesModel } from './UserPreferences';" >> index.ts

# Build database models
npm run build --workspace=packages/database-models
```

```bash
# 3. Add API client methods
cd packages/api-client/src

# Create preferences client
echo "import { UserPreferences } from '@foodprepai/shared-types';
import { apiClient } from './base';

export const preferencesClient = {
  get: (userId: string) => 
    apiClient.get<UserPreferences>(\`/users/\${userId}/preferences\`),
  
  update: (userId: string, preferences: Partial<UserPreferences>) =>
    apiClient.put<UserPreferences>(\`/users/\${userId}/preferences\`, preferences),
};" > preferences.ts

# Update index.ts
echo "export { preferencesClient } from './preferences';" >> index.ts

# Build API client
npm run build --workspace=packages/api-client
```

```bash
# 4. Create API route in web app
cd apps/web/src/app/api/users/[userId]/preferences

# Create API route
mkdir -p route.ts
echo "import { NextRequest } from 'next/server';
import { UserPreferencesModel } from '@foodprepai/database-models';
import { validateSession } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  const session = await validateSession(request);
  if (!session || session.user.id !== params.userId) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const preferences = await UserPreferencesModel.findOne({ userId: params.userId });
  return Response.json({ success: true, data: preferences });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  const session = await validateSession(request);
  if (!session || session.user.id !== params.userId) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const updates = await request.json();
  const preferences = await UserPreferencesModel.findOneAndUpdate(
    { userId: params.userId },
    updates,
    { new: true, upsert: true }
  );
  
  return Response.json({ success: true, data: preferences });
}" > route.ts
```

```bash
# 5. Test everything works
npm run typecheck  # Check for type errors
npm run build     # Build all packages
npm run dev:web   # Start web app to test
```

### 2. Updating Shared Types (Breaking Changes)

**Scenario**: Adding required field to existing interface

**Strategy**: Use deprecation and gradual migration

```typescript
// packages/shared-types/src/user.ts

// Step 1: Add optional field first
export interface User {
  id: string;
  name: string;
  email: string;
  // Add as optional initially
  companyId?: string;
  
  // Mark old field as deprecated
  /** @deprecated Use companyId instead */
  company?: string;
}

// Step 2: Create migration helper
export const migrateUser = (oldUser: any): User => {
  return {
    ...oldUser,
    companyId: oldUser.companyId || oldUser.company,
  };
};
```

**Migration workflow**:

```bash
# 1. Update types with backward compatibility
cd packages/shared-types
# Edit files to add optional fields
npm run build --workspace=packages/shared-types

# 2. Update consuming packages gradually
# Start with database models
cd packages/database-models
# Update schemas to handle both old and new fields
npm run build --workspace=packages/database-models

# 3. Update API client with migration logic
cd packages/api-client
# Add response transformation
npm run build --workspace=packages/api-client

# 4. Update apps one by one
cd apps/web
# Update components to use new field
npm run typecheck
npm run build:web

cd apps/mobile
# Update components to use new field  
npm run typecheck
npm run build:mobile

# 5. Remove deprecated fields after all consumers updated
cd packages/shared-types
# Remove optional marker and deprecated fields
npm run build --workspace=packages/shared-types

# 6. Final verification
npm run typecheck  # Should have no errors
npm run build     # All packages should build
```

### 3. Adding New Shared Component

**Scenario**: Creating a reusable DateRangePicker component

```bash
# 1. Create component in ui-components
cd packages/ui-components/src

mkdir DateRangePicker
cd DateRangePicker

# Create the component
echo "import React from 'react';
import { DateRange } from '@foodprepai/shared-types';

export interface DateRangePickerProps {
  value?: DateRange;
  onChange: (range: DateRange) => void;
  minDate?: Date;
  maxDate?: Date;
  placeholder?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  minDate,
  maxDate,
  placeholder = 'Select date range'
}) => {
  // Implementation here
  return (
    <div className=\"date-range-picker\">
      {/* Component implementation */}
    </div>
  );
};

export default DateRangePicker;" > DateRangePicker.tsx

# Create index file
echo "export { DateRangePicker, type DateRangePickerProps } from './DateRangePicker';" > index.ts

# Update main index
cd ..
echo "export * from './DateRangePicker';" >> index.ts

# Build the package
npm run build --workspace=packages/ui-components
```

```bash
# 2. Add the DateRange type to shared-types
cd packages/shared-types/src

echo "export interface DateRange {
  startDate: Date;
  endDate: Date;
}" >> index.ts

npm run build --workspace=packages/shared-types
```

```bash
# 3. Use in web app
cd apps/web/src/features/reports/components

echo "import React, { useState } from 'react';
import { DateRangePicker } from '@foodprepai/ui-components';
import { DateRange } from '@foodprepai/shared-types';

export const ReportsFilter: React.FC = () => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  
  return (
    <div>
      <DateRangePicker
        value={dateRange}
        onChange={setDateRange}
        minDate={new Date('2024-01-01')}
        placeholder=\"Select report period\"
      />
    </div>
  );
};" > ReportsFilter.tsx
```

## 🔧 Development Tools and Scripts

### Package Build Order Script

```bash
#!/bin/bash
# scripts/build-order.sh - Build packages in dependency order

echo "🏗️  Building packages in dependency order..."

# Build foundation packages first
echo "Building shared-types..."
npm run build --workspace=packages/shared-types

echo "Building shared-utils..."
npm run build --workspace=packages/shared-utils

# Build dependent packages
echo "Building api-client..."
npm run build --workspace=packages/api-client

echo "Building ui-components..."
npm run build --workspace=packages/ui-components

echo "Building database-models..."
npm run build --workspace=packages/database-models

# Build applications last
echo "Building web app..."
npm run build --workspace=apps/web

echo "Building mobile app..."
npm run build --workspace=apps/mobile

echo "✅ All packages built successfully!"
```

### Cross-Package Testing Script

```bash
#!/bin/bash
# scripts/test-cross-package.sh - Test cross-package integration

echo "🧪 Running cross-package integration tests..."

# Test shared packages first
npm run test --workspace=packages/shared-types
npm run test --workspace=packages/shared-utils
npm run test --workspace=packages/api-client

# Test integration points
echo "Testing type compatibility..."
npm run typecheck

echo "Testing API client with shared types..."
cd packages/api-client
npm run test

echo "Testing UI components with shared types..."
cd ../ui-components
npm run test

echo "Testing apps with all dependencies..."
cd ../../apps/web
npm run test

cd ../mobile
npm run test

echo "✅ All cross-package tests passed!"
```

### Dependency Impact Analysis

```bash
#!/bin/bash
# scripts/impact-analysis.sh - Analyze impact of changes

CHANGED_PACKAGE=$1

if [ -z "$CHANGED_PACKAGE" ]; then
  echo "Usage: ./impact-analysis.sh <package-name>"
  exit 1
fi

echo "📊 Analyzing impact of changes to $CHANGED_PACKAGE..."

case $CHANGED_PACKAGE in
  "shared-types")
    echo "⚠️  HIGH IMPACT: All packages depend on shared-types"
    echo "Packages to rebuild: ALL"
    echo "Packages to test: ALL"
    ;;
  "shared-utils")
    echo "🔶 MEDIUM IMPACT: Most packages depend on shared-utils"
    echo "Packages to rebuild: api-client, ui-components, database-models, apps"
    echo "Packages to test: api-client, ui-components, database-models, apps"
    ;;
  "api-client"|"ui-components"|"database-models")
    echo "🔸 LOW IMPACT: Only apps depend on this package"
    echo "Packages to rebuild: apps only"
    echo "Packages to test: apps only"
    ;;
  *)
    echo "❓ Unknown package: $CHANGED_PACKAGE"
    ;;
esac

echo ""
echo "Recommended workflow:"
echo "1. Build dependencies: npm run build"
echo "2. Run type check: npm run typecheck"
echo "3. Run tests: npm run test"
echo "4. Test affected apps manually"
```

## 🎯 Best Practices

### 1. Always Build in Dependency Order

```bash
# ✅ Good: Build dependencies first
npm run build --workspace=packages/shared-types
npm run build --workspace=packages/shared-utils
npm run build --workspace=packages/api-client

# ❌ Bad: Build in random order (may fail)
npm run build --workspace=packages/api-client  # Fails - needs shared-types
```

### 2. Use Watch Mode for Active Development

```bash
# Terminal 1: Watch shared packages
npm run dev --workspace=packages/shared-types &
npm run dev --workspace=packages/shared-utils &
npm run dev --workspace=packages/api-client &

# Terminal 2: Run apps
npm run dev:web

# This ensures changes in shared packages are automatically rebuilt
```

### 3. Version Compatibility

```bash
# Check for version mismatches
npm ls | grep -E "UNMET|invalid"

# Update package references after major changes
npm run workspace:info
```

### 4. Testing Strategy

```bash
# Test in this order:
# 1. Unit tests for changed package
npm run test --workspace=packages/shared-types

# 2. Integration tests for dependent packages
npm run test --workspace=packages/api-client

# 3. E2E tests for apps
npm run test:e2e --workspace=apps/web
```

## 🚨 Common Pitfalls and Solutions

### 1. Stale Build Artifacts

**Problem**: Changes not reflected in consuming packages

**Solution**:
```bash
# Clear all build outputs
npm run clean
# Rebuild everything
npm run build
```

### 2. Circular Dependencies

**Problem**: Package A imports from Package B, which imports from Package A

**Detection**:
```bash
npx madge --circular --extensions ts,tsx packages/
```

**Solution**: Extract shared interfaces to a common package

### 3. TypeScript Path Mapping Issues

**Problem**: IDE can't resolve package imports

**Solution**: Ensure proper TypeScript project references:

```json
// tsconfig.json
{
  "references": [
    { "path": "./packages/shared-types" },
    { "path": "./packages/shared-utils" },
    { "path": "./packages/api-client" },
    { "path": "./apps/web" }
  ]
}
```

### 4. Hot Reload Not Working

**Problem**: Changes in shared packages don't trigger app reload

**Solution**: Use Turborepo dev mode:
```bash
npm run dev  # Uses Turborepo to watch all packages
```

## 📈 Performance Optimization

### 1. Selective Building

```bash
# Only build changed packages and their dependents
npx turbo run build --filter=...packages/shared-types

# Build specific app and its dependencies
npx turbo run build --filter=web...
```

### 2. Parallel Development

```bash
# Use multiple terminals for parallel development
# Terminal 1: Core packages
npm run dev --workspace=packages/shared-types --workspace=packages/shared-utils

# Terminal 2: Application packages  
npm run dev --workspace=packages/api-client --workspace=packages/ui-components

# Terminal 3: Apps
npm run dev:web
```

### 3. Smart Caching

```bash
# Enable remote caching for team
export TURBO_TOKEN="your-token"
export TURBO_TEAM="your-team"

# Use local cache for builds
npx turbo run build --cache-dir=.turbo
```

This workflow guide should help you navigate complex cross-package changes efficiently. Remember: always start with the most foundational packages (types) and work your way up to consuming applications!