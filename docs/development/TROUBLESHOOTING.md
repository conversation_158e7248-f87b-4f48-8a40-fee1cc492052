# Troubleshooting Guide

Quick solutions for common problems in the FoodPrepAI monorepo.

## 🚀 Quick Fixes

### I Can't Start the Development Server

```bash
# First, try the health check
npm run health-check

# If that fails, nuclear option:
npm run clean:deps
npm install --legacy-peer-deps
npm run build
npm run dev
```

### My Changes Aren't Showing Up

```bash
# Clear caches and rebuild
npx turbo clean
npm run build
# Restart dev server
```

### TypeScript Errors After Git Pull

```bash
# Rebuild all packages to update types
npm run build
npm run typecheck
```

### "Cannot find module" Errors

```bash
# Build the specific package
npm run build --workspace=packages/[package-name]

# Or build everything
npm run build
```

## 📱 Mobile App Issues

### Capacitor/Ionic Issues

```bash
# Check Capacitor health
npx cap doctor

# Sync native platforms
npx cap sync

# Clear and rebuild
npm run clean
npm run build:mobile
npx cap sync
```

### iOS Build Issues

```bash
# Clear iOS build cache
rm -rf apps/mobile/ios/App/App.xcworkspace/xcuserdata
rm -rf apps/mobile/ios/App/build

# Reinstall iOS dependencies
cd apps/mobile/ios/App && pod install
```

### Android Build Issues

```bash
# Clear Android build cache
cd apps/mobile/android
./gradlew clean

# Reset Android project
npx cap sync android
```

## 🌐 Web App Issues

### Next.js Build Errors

```bash
# Clear Next.js cache
rm -rf apps/web/.next

# Check for duplicate dependencies
npm ls | grep -i duplicate

# Rebuild
npm run build:web
```

### SSR/Hydration Issues

```bash
# Enable Next.js debugging
export DEBUG=next:*
npm run dev:web
```

## 🔧 Environment Issues

### Environment Variables Not Loading

1. Check `.env.local` exists and has correct values
2. Restart development server
3. Verify environment variable names start with `NEXT_PUBLIC_` for client-side access

### Database Connection Issues

```bash
# Check MongoDB connection
mongosh "your-connection-string"

# Check environment variables
echo $DATABASE_URL
```

## 🧪 Testing Issues

### Tests Failing After Changes

```bash
# Clear Jest cache
npm run test -- --clearCache

# Run tests with verbose output
npm run test -- --verbose

# Update snapshots if needed
npm run test -- --updateSnapshot
```

### Test Database Issues

```bash
# Reset test database
npm run db:reset:test

# Check test environment
NODE_ENV=test npm run test
```

## 🔄 Git and Version Control

### Merge Conflicts in package-lock.json

```bash
# Delete lock files and reinstall
rm package-lock.json
rm apps/*/package-lock.json
rm packages/*/package-lock.json
npm install --legacy-peer-deps
```

### Husky Hooks Failing

```bash
# Reinstall git hooks
rm -rf .husky
npm run prepare

# Skip hooks temporarily (emergency only)
git commit --no-verify
```

## 💾 Performance Issues

### Slow Build Times

```bash
# Check what's taking time
npm run build -- --verbose

# Clear all caches
npx turbo clean
rm -rf node_modules/.cache
npm run build
```

### Development Server Slow

```bash
# Increase Node.js memory
export NODE_OPTIONS="--max-old-space-size=4096"
npm run dev

# Check for memory leaks
npm install --save-dev clinic
clinic doctor -- npm run dev
```

## 🔒 Dependency Issues

### Peer Dependency Warnings

```bash
# Install with legacy peer deps flag
npm install --legacy-peer-deps

# Check for conflicts
npm ls
```

### Version Conflicts

```bash
# Check for duplicate packages
npm ls | grep -E "deduped|UNMET"

# Update all packages
npx npm-check-updates -u
npm install --legacy-peer-deps
```

## 🚨 Emergency Commands

### Complete Reset (Nuclear Option)

⚠️ **Use only when everything else fails**

```bash
# Save your work first!
git add . && git commit -m "WIP: before nuclear reset"

# Nuclear reset
rm -rf node_modules apps/*/node_modules packages/*/node_modules tools/*/node_modules
rm -rf .next .turbo dist build coverage
rm -f package-lock.json apps/*/package-lock.json packages/*/package-lock.json tools/*/package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
npm run build
```

### Rollback to Last Working State

```bash
# Stash current changes
git stash

# Go back to main
git checkout main
git pull origin main

# Clean install
npm run clean:deps
npm install --legacy-peer-deps
npm run build
npm run dev
```

## 🆘 When to Ask for Help

Ask for help if you've tried:
1. ✅ Health check (`npm run health-check`)
2. ✅ Clean reinstall (`npm run clean:deps && npm install --legacy-peer-deps`)
3. ✅ Full rebuild (`npm run build`)
4. ✅ Checked this troubleshooting guide
5. ✅ Searched existing GitHub issues

When asking for help, include:
- Your operating system
- Node.js and npm versions
- Full error message
- Steps to reproduce
- What you've already tried

## 📞 Support Channels

1. **Documentation**: Check `/docs` folder first
2. **GitHub Issues**: Search existing issues
3. **Team Chat**: For quick questions
4. **Pair Programming**: For complex debugging

Remember: Every error is a learning opportunity! 🎓