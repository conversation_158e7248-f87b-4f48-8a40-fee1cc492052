# Developer Productivity Tools & Shortcuts

Maximize your productivity in the FoodPrepAI monorepo with these tools, shortcuts, and workflows.

## 🚀 Quick Commands

### Essential Daily Commands

```bash
# Quick health check
npm run health-check

# Start everything
npm run dev

# Clean slate restart (when things break)
npm run clean:deps && npm install --legacy-peer-deps && npm run build

# Generate new components/APIs
npm run generate:component
npm run generate:api

# Interactive dev tools
npm run dev-tools
```

### Package-Specific Commands

```bash
# Build specific package
npm run build --workspace=packages/shared-types

# Test specific package
npm run test --workspace=packages/api-client

# Start specific app
npm run dev:web     # Web app only
npm run dev:mobile  # Mobile app only

# Type check everything
npm run typecheck
```

## ⌨️ VS Code Shortcuts & Extensions

### Custom Keyboard Shortcuts

Add these to your VS Code `keybindings.json`:

```json
[
  {
    "key": "ctrl+alt+b",
    "command": "workbench.action.tasks.runTask",
    "args": "Build All"
  },
  {
    "key": "ctrl+alt+t",
    "command": "workbench.action.tasks.runTask", 
    "args": "Run Tests"
  },
  {
    "key": "ctrl+alt+d",
    "command": "workbench.action.tasks.runTask",
    "args": "Start All Apps"
  },
  {
    "key": "ctrl+alt+c",
    "command": "workbench.action.tasks.runTask",
    "args": "Clean Cache"
  },
  {
    "key": "ctrl+alt+n",
    "command": "workbench.action.terminal.new"
  }
]
```

### Essential Extensions (Auto-installed via workspace)

- **TypeScript Hero**: Auto-import management
- **Prettier**: Code formatting
- **ESLint**: Linting
- **Tailwind CSS IntelliSense**: CSS autocomplete
- **Jest Runner**: Run tests in editor
- **Auto Rename Tag**: Rename paired HTML/JSX tags
- **Path Intellisense**: File path autocomplete
- **Error Lens**: Inline error display
- **Todo Tree**: Track TODO comments

### Extension Settings

Add to your VS Code `settings.json`:

```json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

## 🛠️ Terminal Productivity

### Bash Aliases

Add to your `~/.bashrc` or `~/.zshrc`:

```bash
# FoodPrepAI aliases
alias fpai="cd ~/path/to/foodprepai"
alias fpdev="npm run dev"
alias fpbuild="npm run build"
alias fptest="npm run test"
alias fpclean="npm run clean:deps"
alias fphealth="npm run health-check"
alias fptools="npm run dev-tools"

# Git aliases for monorepo
alias gpull="git pull origin main"
alias gstatus="git status"
alias gadd="git add ."
alias gcommit="git commit -m"
alias gpush="git push origin"

# Package-specific shortcuts
alias buildt="npm run build --workspace=packages/shared-types"
alias buildu="npm run build --workspace=packages/shared-utils"
alias buildapi="npm run build --workspace=packages/api-client"
alias buildui="npm run build --workspace=packages/ui-components"
```

### Fish Shell Functions (if using Fish)

```fish
# ~/.config/fish/functions/fpai.fish
function fpai
    cd ~/path/to/foodprepai
end

# ~/.config/fish/functions/fpdev.fish  
function fpdev
    npm run dev
end

# ~/.config/fish/functions/fpgen.fish
function fpgen
    switch $argv[1]
        case component comp c
            npm run generate:component
        case api a
            npm run generate:api
        case '*'
            echo "Usage: fpgen [component|api]"
    end
end
```

### Directory Bookmarks (using `z` or `fasd`)

```bash
# After using z for a while, you can jump quickly:
z foodprepai  # Jump to monorepo root
z web         # Jump to apps/web
z shared      # Jump to packages/shared-types
z ui          # Jump to packages/ui-components
```

## 🔄 Workflow Automation

### Git Hooks (Already Configured)

The monorepo includes pre-configured hooks:

- **pre-commit**: Lints and formats staged files
- **commit-msg**: Validates commit message format
- **pre-push**: Runs tests before pushing

### Custom Git Commands

Add to `~/.gitconfig`:

```ini
[alias]
    # Monorepo specific commands
    sync-main = !git checkout main && git pull origin main
    feature = !git checkout -b feature/
    hotfix = !git checkout -b hotfix/
    
    # Better logs
    lg = log --oneline --graph --decorate --all
    last = log -1 HEAD --stat
    
    # Quick status
    s = status -s
    
    # Undo last commit (keep changes)
    undo = reset HEAD~1 --mixed
```

### Package Scripts Integration

Create `scripts/quick.sh` for rapid development:

```bash
#!/bin/bash
# Quick development script

case $1 in
  "start"|"dev")
    echo "🚀 Starting development servers..."
    npm run dev
    ;;
  "build")
    echo "🏗️  Building all packages..."
    npm run build
    ;;
  "test")
    echo "🧪 Running all tests..."
    npm run test
    ;;
  "fix")
    echo "🔧 Running full cleanup and rebuild..."
    npm run clean:deps
    npm install --legacy-peer-deps
    npm run build
    ;;
  "new")
    echo "📝 What would you like to generate?"
    echo "1) Component"
    echo "2) API"
    read -p "Choice (1-2): " choice
    case $choice in
      1) npm run generate:component ;;
      2) npm run generate:api ;;
      *) echo "Invalid choice" ;;
    esac
    ;;
  *)
    echo "Usage: ./quick.sh [start|build|test|fix|new]"
    ;;
esac
```

## 📱 Mobile Development Tools

### Ionic CLI Shortcuts

```bash
# Add to your shell aliases
alias icap="npx cap"
alias isync="npx cap sync"
alias irun="npx cap run"
alias iopen="npx cap open"
alias idoctor="npx cap doctor"

# Quick mobile commands
alias ios-dev="npx cap run ios --livereload --external"
alias android-dev="npx cap run android --livereload --external"
alias mobile-sync="npx cap sync && echo 'Platforms synced!'"
```

### Capacitor Development

```bash
# Quick platform setup
setup-ios() {
  cd apps/mobile
  npx cap add ios
  npx cap sync ios
  npx cap open ios
}

setup-android() {
  cd apps/mobile
  npx cap add android
  npx cap sync android
  npx cap open android
}
```

## 🎯 Debugging Tools

### VS Code Launch Configurations

The workspace includes pre-configured debug setups:

- **Debug Web App**: Attach to Next.js dev server
- **Debug API Routes**: Debug backend API endpoints  
- **Debug Jest Tests**: Debug unit tests
- **Debug Current Jest Test**: Debug specific test file

### Browser DevTools Integration

```javascript
// Add these to your browser console for quick debugging
// Inspect React Query cache
window.queryClient = require('@tanstack/react-query').useQueryClient();

// Inspect component props
window.inspect = (element) => {
  console.log(element._owner.memoizedProps);
};

// Quick performance check
window.perf = {
  start: () => performance.mark('start'),
  end: () => {
    performance.mark('end');
    performance.measure('duration', 'start', 'end');
    console.log(performance.getEntriesByType('measure'));
  }
};
```

## 📊 Monitoring & Analytics

### Bundle Analysis

```bash
# Add to package.json scripts
"analyze:web": "cd apps/web && npx @next/bundle-analyzer",
"analyze:mobile": "cd apps/mobile && npx webpack-bundle-analyzer build/static/js/*.js"

# Usage
npm run analyze:web
npm run analyze:mobile
```

### Performance Monitoring

```bash
# Create performance test script
#!/bin/bash
echo "🔍 Performance Analysis"

echo "📦 Checking bundle sizes..."
npm run build
du -sh apps/*/build apps/*/.next

echo "⚡ Running Lighthouse..."
npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html

echo "🧪 Running performance tests..."
npm run test -- --testNamePattern="performance"
```

## 🎨 Code Quality Tools

### Automated Code Review

```bash
# Create pre-PR script
#!/bin/bash
echo "🔍 Pre-PR Quality Check"

echo "📝 Checking TypeScript..."
npm run typecheck

echo "🧹 Checking linting..."
npm run lint

echo "🧪 Running tests..."
npm run test

echo "📊 Checking test coverage..."
npm run test -- --coverage

echo "🔒 Checking for security issues..."
npm audit

echo "✅ Quality check complete!"
```

### Code Metrics

```bash
# Install additional tools
npm install -g cloc madge complexity-report

# Create metrics script
check-metrics() {
  echo "📊 Code Metrics Report"
  
  echo "📈 Lines of code:"
  cloc src/ --exclude-dir=node_modules
  
  echo "🔄 Circular dependencies:"
  madge --circular --extensions ts,tsx src/
  
  echo "🧠 Code complexity:"
  complexity-report -o complexity-report.html src/
}
```

## 🔧 Environment Management

### Multiple Environment Setup

```bash
# Create environment switcher
switch-env() {
  case $1 in
    "dev"|"development")
      cp .env.development .env.local
      echo "🔄 Switched to development environment"
      ;;
    "staging")
      cp .env.staging .env.local
      echo "🔄 Switched to staging environment"
      ;;
    "prod"|"production")
      cp .env.production .env.local
      echo "🔄 Switched to production environment"
      ;;
    *)
      echo "Usage: switch-env [dev|staging|prod]"
      ;;
  esac
}
```

### Docker Integration

```bash
# Quick Docker commands for local development
alias docker-dev="docker-compose -f docker-compose.dev.yml up -d"
alias docker-prod="docker-compose -f docker-compose.prod.yml up -d"
alias docker-stop="docker-compose down"
alias docker-logs="docker-compose logs -f"
alias docker-clean="docker system prune -a"
```

## 📈 Productivity Metrics

### Track Your Progress

```bash
# Create productivity tracker
#!/bin/bash
# productivity-tracker.sh

DATE=$(date +%Y-%m-%d)
LOG_FILE="productivity-log.txt"

track() {
  case $1 in
    "commits")
      COMMITS=$(git log --since="$DATE 00:00:00" --oneline | wc -l)
      echo "$DATE: $COMMITS commits" >> $LOG_FILE
      ;;
    "tests")
      TESTS=$(npm run test -- --passWithNoTests --silent | grep -o '[0-9]* passed' | cut -d' ' -f1)
      echo "$DATE: $TESTS tests passed" >> $LOG_FILE
      ;;
    "build-time")
      START_TIME=$(date +%s)
      npm run build > /dev/null 2>&1
      END_TIME=$(date +%s)
      BUILD_TIME=$((END_TIME - START_TIME))
      echo "$DATE: Build took ${BUILD_TIME}s" >> $LOG_FILE
      ;;
  esac
}

# Usage: track commits|tests|build-time
```

## 🎓 Learning & Documentation

### Quick Reference Commands

```bash
# Create quick reference
ref() {
  case $1 in
    "shortcuts")
      echo "🔑 VS Code Shortcuts:"
      echo "Ctrl+Alt+B - Build All"
      echo "Ctrl+Alt+T - Run Tests"
      echo "Ctrl+Alt+D - Start Dev Servers"
      ;;
    "commands")
      echo "📝 Common Commands:"
      echo "npm run dev - Start development"
      echo "npm run build - Build all packages"
      echo "npm run generate:component - Generate component"
      ;;
    "git")
      echo "🔧 Git Workflow:"
      echo "git checkout -b feature/name - Create feature branch"
      echo "git add . && git commit -m 'message' - Commit changes"
      echo "git push origin feature/name - Push branch"
      ;;
  esac
}
```

These productivity tools should significantly speed up your development workflow. Customize them based on your preferences and add your own shortcuts as you discover new patterns!