# Debugging Guide for FoodPrepAI Monorepo

This guide covers common debugging scenarios and troubleshooting techniques specific to our monorepo setup.

## 🔧 Quick Troubleshooting

### Common Issues Checklist

When things break, start here:

```bash
# 1. Health check
npm run health-check

# 2. Clear caches and reinstall
npm run clean:deps
npm install --legacy-peer-deps

# 3. Rebuild all packages
npm run build

# 4. Type check
npm run typecheck
```

## 🚨 Common Monorepo Issues

### 1. "Cannot find module" Errors

**Symptoms**: Import errors for shared packages
```
Cannot find module '@foodprepai/shared-types'
Module not found: Can't resolve '@foodprepai/api-client'
```

**Solutions**:
```bash
# Build the missing package
npm run build --workspace=packages/shared-types

# Or build all packages
npm run build

# Check if package is properly referenced
npm ls @foodprepai/shared-types
```

**Root Causes**:
- Shared package not built
- Package name mismatch in package.json
- Workspace reference not using `*` version

### 2. TypeScript "Cannot find declaration file" Errors

**Symptoms**:
```
Could not find a declaration file for module '@foodprepai/shared-utils'
```

**Solutions**:
```bash
# Build the package to generate .d.ts files
npm run build --workspace=packages/shared-utils

# Check tsconfig references
cat packages/shared-utils/tsconfig.json

# Verify exports in package.json
cat packages/shared-utils/package.json
```

**Prevention**:
- Always build packages after changes
- Use TypeScript project references properly
- Ensure `types` field in package.json points to correct .d.ts files

### 3. Circular Dependency Issues

**Symptoms**:
```
WARNING in Circular dependency detected
ReferenceError: Cannot access before initialization
```

**Debugging**:
```bash
# Use dependency graph tools
npx madge --circular --extensions ts,tsx apps/web/src

# Check import patterns
grep -r "from.*shared" apps/web/src
```

**Solutions**:
- Move shared types to a separate barrel export
- Use dynamic imports for heavy dependencies
- Restructure imports to break cycles

### 4. Build Cache Issues

**Symptoms**:
- Changes not reflected in apps
- Inconsistent build results
- Stale type definitions

**Solutions**:
```bash
# Clear Turborepo cache
npx turbo clean

# Clear Node.js require cache (for hot reload issues)
# Add to your dev script:
# delete require.cache[require.resolve('./path')]

# Full nuclear option
npm run clean:deps
rm -rf .next .turbo dist build
npm install --legacy-peer-deps
npm run build
```

## 🐛 Debugging Techniques

### 1. VS Code Debugging Setup

**Debug Next.js API Routes**:
```json
{
  "name": "Debug API Routes",
  "type": "node", 
  "request": "attach",
  "port": 9229,
  "restart": true,
  "localRoot": "${workspaceFolder}/apps/web",
  "skipFiles": ["<node_internals>/**"]
}
```

**Debug Jest Tests**:
```json
{
  "name": "Debug Jest Tests",
  "type": "node",
  "request": "launch",
  "program": "${workspaceFolder}/node_modules/.bin/jest",
  "args": ["--runInBand", "${relativeFile}"],
  "console": "integratedTerminal",
  "skipFiles": ["<node_internals>/**"]
}
```

### 2. Package-Level Debugging

**Debug shared package in isolation**:
```bash
cd packages/api-client
npm run dev  # Watch mode
npm run test -- --watch  # Test watch mode
```

**Test package integration**:
```bash
# Build and link locally
cd packages/shared-types
npm run build
npm link

cd ../../apps/web  
npm link @foodprepai/shared-types
```

### 3. Network and API Debugging

**Enable request logging**:
```typescript
// In api-client base configuration
const client = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
});

// Add request interceptor for debugging
client.interceptors.request.use(request => {
  console.log('Starting Request:', request);
  return request;
});

client.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);
```

**Database debugging**:
```bash
# Enable MongoDB debug logging
DEBUG=mongoose:* npm run dev

# Or set in environment
export DEBUG=mongoose:*
```

## 🔍 Debugging by App Type

### Web App (Next.js) Debugging

**Common Issues**:
```bash
# SSR/Hydration issues
export DEBUG=next:*
npm run dev:web

# Build issues
npm run build:web -- --debug

# Route debugging
# Add to next.config.ts:
module.exports = {
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
}
```

**Performance debugging**:
```bash
# Bundle analysis
npm install --save-dev @next/bundle-analyzer
# Add to next.config.ts for bundle analysis
```

### Mobile App (Ionic) Debugging

**Common Issues**:
```bash
# Capacitor debugging
npx cap doctor

# iOS debugging
npx cap open ios
# Then use Xcode console

# Android debugging  
npx cap open android
# Then use Android Studio logcat

# Ionic DevApp
ionic serve --lab
```

**Capacitor plugin issues**:
```bash
# Check plugin installation
npx cap ls

# Sync native platforms
npx cap sync

# Check native logs
npx cap run ios --livereload --external
npx cap run android --livereload --external
```

## 🧪 Testing and Debugging

### Unit Test Debugging

**Debug failing tests**:
```bash
# Run single test with debugging
npm run test -- --testNamePattern="specific test" --verbose

# Debug with VS Code
# Use launch.json configuration for Jest debugging

# Enable test debugging output
DEBUG=* npm run test
```

**Mock debugging**:
```typescript
// Add debugging to mocks
jest.mock('@foodprepai/api-client', () => ({
  ...jest.requireActual('@foodprepai/api-client'),
  apiClient: {
    get: jest.fn().mockImplementation((url) => {
      console.log('Mock API call:', url);
      return Promise.resolve({ data: {} });
    })
  }
}));
```

### Integration Test Debugging

**Database test issues**:
```bash
# Use in-memory MongoDB for tests
npm install --save-dev mongodb-memory-server

# Debug test database
export DEBUG=mongodb-memory-server:*
npm run test
```

**API test debugging**:
```bash
# Start test server with logging
export NODE_ENV=test
export DEBUG=express:*
npm run test:api
```

## 🛠️ Advanced Debugging Tools

### 1. Dependency Analysis

```bash
# Analyze bundle size
npm install --save-dev webpack-bundle-analyzer
npx webpack-bundle-analyzer apps/web/.next/static/chunks/*.js

# Check duplicate dependencies
npm ls --all | grep -i duplicate
npx npm-check-updates

# Workspace dependency graph
npm install --save-dev @nx/workspace
npx nx graph
```

### 2. Performance Profiling

```bash
# Node.js profiling
node --prof apps/web/server.js
node --prof-process isolate-*.log > processed.txt

# React profiling
# Add to development:
import { Profiler } from 'react';
// Wrap components with Profiler
```

### 3. Memory Leak Detection

```bash
# Heap snapshots
node --inspect apps/web/server.js
# Open Chrome DevTools -> Memory tab

# Process monitoring
npm install --save-dev clinic
clinic doctor -- node apps/web/server.js
```

## 🚨 Emergency Procedures

### Complete Reset

When everything is broken:
```bash
#!/bin/bash
# Nuclear reset script

echo "🚨 NUCLEAR RESET - This will take 5-10 minutes"

# Stop all processes
pkill -f "node"
pkill -f "next"

# Remove all generated files
rm -rf node_modules
rm -rf apps/*/node_modules  
rm -rf packages/*/node_modules
rm -rf tools/*/node_modules
rm -rf .next
rm -rf .turbo
rm -rf */dist
rm -rf */build
rm -rf coverage

# Remove lock files
rm -f package-lock.json
rm -f apps/*/package-lock.json
rm -f packages/*/package-lock.json
rm -f tools/*/package-lock.json

# Clear npm cache
npm cache clean --force

# Reinstall everything
npm install --legacy-peer-deps

# Rebuild all packages
npm run build

# Verify health
npm run health-check

echo "✅ Reset complete!"
```

### Rollback Strategy

```bash
# Quick rollback to last working state
git stash
git checkout main
git pull origin main
npm run clean:deps
npm install --legacy-peer-deps
npm run build
npm run dev
```

## 📋 Debugging Checklist

Before asking for help, verify:

- [ ] Latest code from main branch
- [ ] All dependencies installed (`npm install --legacy-peer-deps`)
- [ ] All packages built (`npm run build`)  
- [ ] No TypeScript errors (`npm run typecheck`)
- [ ] Environment variables set correctly
- [ ] Correct Node.js version (18+)
- [ ] VS Code workspace loaded with recommended extensions

## 🤝 Getting Help

1. **Self-service**:
   - Check this debugging guide
   - Search existing GitHub issues
   - Run health check: `npm run health-check`

2. **Team help**:
   - Share specific error messages
   - Include steps to reproduce
   - Mention what you've already tried
   - Share relevant logs/screenshots

3. **Create issue**:
   - Include system info (`node --version`, `npm --version`)
   - Include full error stack trace
   - Mention which debugging steps you've tried

## 📖 Additional Resources

- [Next.js Debugging Guide](https://nextjs.org/docs/advanced-features/debugging)
- [Jest Debugging Guide](https://jestjs.io/docs/troubleshooting)
- [Ionic Debugging Guide](https://ionicframework.com/docs/troubleshooting/debugging)
- [TypeScript Debugging](https://www.typescriptlang.org/docs/handbook/project-references.html)