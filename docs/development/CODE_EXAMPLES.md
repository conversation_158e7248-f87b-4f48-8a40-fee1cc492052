# Code Examples and Patterns

Real-world examples of common development patterns in the FoodPrepAI monorepo.

## 🏗️ Architecture Patterns

### 1. Feature-based Component Organization

```typescript
// apps/web/src/features/inventory/
├── components/
│   ├── InventoryList.tsx
│   ├── InventoryItem.tsx
│   └── InventoryForm.tsx
├── hooks/
│   ├── useInventory.ts
│   └── useInventoryMutations.ts
├── api/
│   └── inventoryApi.ts
└── index.ts

// apps/web/src/features/inventory/index.ts
export { InventoryList } from './components/InventoryList';
export { useInventory } from './hooks/useInventory';
export * from './api/inventoryApi';
```

### 2. Custom Hook Pattern

```typescript
// apps/web/src/features/inventory/hooks/useInventory.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { inventoryClient } from '@foodprepai/api-client/inventory';
import { InventoryItem } from '@foodprepai/shared-types';

export const useInventory = (locationId: string) => {
  return useQuery({
    queryKey: ['inventory', locationId],
    queryFn: () => inventoryClient.getByLocation(locationId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useInventoryMutations = () => {
  const queryClient = useQueryClient();
  
  const updateItem = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<InventoryItem> }) =>
      inventoryClient.update(id, data),
    onSuccess: (data) => {
      // Update cache optimistically
      queryClient.setQueryData(
        ['inventory', data.locationId],
        (old: InventoryItem[] = []) =>
          old.map(item => item.id === data.id ? data : item)
      );
    },
  });
  
  const deleteItem = useMutation({
    mutationFn: (id: string) => inventoryClient.delete(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
    },
  });
  
  return { updateItem, deleteItem };
};
```

### 3. API Route Pattern (Next.js)

```typescript
// apps/web/src/app/api/inventory/[id]/route.ts
import { NextRequest } from 'next/server';
import { InventoryModel } from '@foodprepai/database-models';
import { InventoryItem } from '@foodprepai/shared-types';
import { validateSession } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authentication
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Authorization - check if user can access this inventory
    const item = await InventoryModel.findById(params.id);
    if (!item) {
      return Response.json({ error: 'Not found' }, { status: 404 });
    }
    
    if (!session.user.locations.includes(item.locationId)) {
      return Response.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    return Response.json({ success: true, data: item });
  } catch (error) {
    console.error('API Error:', error);
    return Response.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await validateSession(request);
    if (!session) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const updates: Partial<InventoryItem> = await request.json();
    
    // Validate input
    const validatedData = inventoryUpdateSchema.parse(updates);
    
    const item = await InventoryModel.findByIdAndUpdate(
      params.id,
      { ...validatedData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );
    
    if (!item) {
      return Response.json({ error: 'Not found' }, { status: 404 });
    }
    
    return Response.json({ success: true, data: item });
  } catch (error) {
    if (error.name === 'ValidationError') {
      return Response.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }
    
    console.error('API Error:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## 🎨 Component Patterns

### 1. Compound Component Pattern

```typescript
// packages/ui-components/src/DataTable/DataTable.tsx
import React, { createContext, useContext } from 'react';

interface DataTableContextType<T = any> {
  data: T[];
  selectedRows: T[];
  onRowSelect: (row: T) => void;
}

const DataTableContext = createContext<DataTableContextType | null>(null);

const useDataTable = () => {
  const context = useContext(DataTableContext);
  if (!context) {
    throw new Error('DataTable compound components must be used within DataTable');
  }
  return context;
};

// Main component
export const DataTable = <T extends Record<string, any>>({
  data,
  children,
  onSelectionChange,
}: {
  data: T[];
  children: React.ReactNode;
  onSelectionChange?: (selected: T[]) => void;
}) => {
  const [selectedRows, setSelectedRows] = useState<T[]>([]);
  
  const handleRowSelect = (row: T) => {
    const newSelection = selectedRows.includes(row)
      ? selectedRows.filter(r => r !== row)
      : [...selectedRows, row];
    
    setSelectedRows(newSelection);
    onSelectionChange?.(newSelection);
  };
  
  return (
    <DataTableContext.Provider value={{ data, selectedRows, onRowSelect: handleRowSelect }}>
      <div className="data-table">
        {children}
      </div>
    </DataTableContext.Provider>
  );
};

// Sub-components
DataTable.Header = ({ children }: { children: React.ReactNode }) => (
  <thead className="data-table-header">{children}</thead>
);

DataTable.Body = ({ children }: { children: React.ReactNode }) => (
  <tbody className="data-table-body">{children}</tbody>
);

DataTable.Row = ({ item, children }: { item: any; children: React.ReactNode }) => {
  const { selectedRows, onRowSelect } = useDataTable();
  const isSelected = selectedRows.includes(item);
  
  return (
    <tr 
      className={`data-table-row ${isSelected ? 'selected' : ''}`}
      onClick={() => onRowSelect(item)}
    >
      {children}
    </tr>
  );
};

DataTable.Cell = ({ children }: { children: React.ReactNode }) => (
  <td className="data-table-cell">{children}</td>
);

// Usage
const InventoryTable = ({ inventory }: { inventory: InventoryItem[] }) => (
  <DataTable data={inventory} onSelectionChange={handleSelectionChange}>
    <DataTable.Header>
      <tr>
        <th>Name</th>
        <th>Quantity</th>
        <th>Status</th>
      </tr>
    </DataTable.Header>
    
    <DataTable.Body>
      {inventory.map(item => (
        <DataTable.Row key={item.id} item={item}>
          <DataTable.Cell>{item.name}</DataTable.Cell>
          <DataTable.Cell>{item.quantity}</DataTable.Cell>
          <DataTable.Cell>
            <StatusBadge status={item.status} />
          </DataTable.Cell>
        </DataTable.Row>
      ))}
    </DataTable.Body>
  </DataTable>
);
```

### 2. Form with Validation Pattern

```typescript
// apps/web/src/features/orders/components/OrderForm.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormField, Select, DatePicker } from '@foodprepai/ui-components/forms';
import { CreateOrderRequest } from '@foodprepai/shared-types';

const orderSchema = z.object({
  customerName: z.string().min(1, 'Customer name is required'),
  items: z.array(z.object({
    inventoryItemId: z.string(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
  })).min(1, 'At least one item is required'),
  deliveryDate: z.date().min(new Date(), 'Delivery date must be in the future'),
  locationId: z.string().min(1, 'Location is required'),
  notes: z.string().optional(),
});

type OrderFormData = z.infer<typeof orderSchema>;

interface OrderFormProps {
  onSubmit: (data: CreateOrderRequest) => Promise<void>;
  initialData?: Partial<OrderFormData>;
  locations: Array<{ id: string; name: string }>;
  inventoryItems: Array<{ id: string; name: string; availableQuantity: number }>;
}

export const OrderForm: React.FC<OrderFormProps> = ({
  onSubmit,
  initialData,
  locations,
  inventoryItems,
}) => {
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<OrderFormData>({
    resolver: zodResolver(orderSchema),
    defaultValues: {
      customerName: '',
      items: [{ inventoryItemId: '', quantity: 1 }],
      deliveryDate: new Date(),
      locationId: '',
      notes: '',
      ...initialData,
    },
  });
  
  const watchedItems = watch('items');
  
  const addItem = () => {
    // This would typically use useFieldArray from react-hook-form
  };
  
  const removeItem = (index: number) => {
    // Implementation for removing items
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <FormField
        name="customerName"
        label="Customer Name"
        control={control}
        error={errors.customerName?.message}
      />
      
      <Select
        name="locationId"
        label="Location"
        control={control}
        options={locations.map(loc => ({ value: loc.id, label: loc.name }))}
        error={errors.locationId?.message}
      />
      
      <div className="space-y-4">
        <h3>Order Items</h3>
        {watchedItems.map((item, index) => (
          <div key={index} className="flex gap-4 items-end">
            <Select
              name={`items.${index}.inventoryItemId`}
              label="Item"
              control={control}
              options={inventoryItems.map(item => ({
                value: item.id,
                label: `${item.name} (${item.availableQuantity} available)`,
              }))}
              error={errors.items?.[index]?.inventoryItemId?.message}
            />
            
            <FormField
              name={`items.${index}.quantity`}
              label="Quantity"
              type="number"
              control={control}
              error={errors.items?.[index]?.quantity?.message}
            />
            
            {watchedItems.length > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={() => removeItem(index)}
              >
                Remove
              </Button>
            )}
          </div>
        ))}
        
        <Button type="button" onClick={addItem}>
          Add Item
        </Button>
      </div>
      
      <DatePicker
        name="deliveryDate"
        label="Delivery Date"
        control={control}
        minDate={new Date()}
        error={errors.deliveryDate?.message}
      />
      
      <FormField
        name="notes"
        label="Notes"
        as="textarea"
        control={control}
        error={errors.notes?.message}
      />
      
      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline">
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Creating...' : 'Create Order'}
        </Button>
      </div>
    </form>
  );
};
```

### 3. Error Boundary Pattern

```typescript
// packages/ui-components/src/ErrorBoundary/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ error, errorInfo });
    this.props.onError?.(error, errorInfo);
    
    // Log to error reporting service
    console.error('Error Boundary caught an error:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      if (this.props.fallback && this.state.error && this.state.errorInfo) {
        return this.props.fallback(this.state.error, this.state.errorInfo);
      }
      
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <details style={{ whiteSpace: 'pre-wrap' }}>
            <summary>Error details</summary>
            {this.state.error && this.state.error.toString()}
            <br />
            {this.state.errorInfo.componentStack}
          </details>
        </div>
      );
    }
    
    return this.props.children;
  }
}

// Hook version for functional components
export const useErrorHandler = () => {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by hook:', error, errorInfo);
    // Handle error (show toast, redirect, etc.)
  };
};
```

## 🔄 State Management Patterns

### 1. Context + Reducer Pattern

```typescript
// apps/web/src/contexts/OrderContext.tsx
import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Order, OrderStatus } from '@foodprepai/shared-types';

interface OrderState {
  orders: Order[];
  loading: boolean;
  error: string | null;
  selectedOrder: Order | null;
}

type OrderAction =
  | { type: 'ORDERS_LOADING' }
  | { type: 'ORDERS_SUCCESS'; payload: Order[] }
  | { type: 'ORDERS_ERROR'; payload: string }
  | { type: 'ORDER_SELECTED'; payload: Order }
  | { type: 'ORDER_UPDATED'; payload: Order }
  | { type: 'ORDER_STATUS_CHANGED'; payload: { id: string; status: OrderStatus } };

const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'ORDERS_LOADING':
      return { ...state, loading: true, error: null };
      
    case 'ORDERS_SUCCESS':
      return { ...state, loading: false, orders: action.payload };
      
    case 'ORDERS_ERROR':
      return { ...state, loading: false, error: action.payload };
      
    case 'ORDER_SELECTED':
      return { ...state, selectedOrder: action.payload };
      
    case 'ORDER_UPDATED':
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? action.payload : order
        ),
        selectedOrder: state.selectedOrder?.id === action.payload.id
          ? action.payload
          : state.selectedOrder,
      };
      
    case 'ORDER_STATUS_CHANGED':
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id
            ? { ...order, status: action.payload.status }
            : order
        ),
      };
      
    default:
      return state;
  }
};

const OrderContext = createContext<{
  state: OrderState;
  dispatch: React.Dispatch<OrderAction>;
} | null>(null);

export const OrderProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(orderReducer, {
    orders: [],
    loading: false,
    error: null,
    selectedOrder: null,
  });
  
  return (
    <OrderContext.Provider value={{ state, dispatch }}>
      {children}
    </OrderContext.Provider>
  );
};

export const useOrderContext = () => {
  const context = useContext(OrderContext);
  if (!context) {
    throw new Error('useOrderContext must be used within OrderProvider');
  }
  return context;
};

// Custom hooks for specific actions
export const useOrderActions = () => {
  const { dispatch } = useOrderContext();
  
  const loadOrders = async (locationId: string) => {
    dispatch({ type: 'ORDERS_LOADING' });
    try {
      const orders = await orderClient.getByLocation(locationId);
      dispatch({ type: 'ORDERS_SUCCESS', payload: orders });
    } catch (error) {
      dispatch({ type: 'ORDERS_ERROR', payload: error.message });
    }
  };
  
  const updateOrderStatus = async (id: string, status: OrderStatus) => {
    try {
      await orderClient.updateStatus(id, status);
      dispatch({ type: 'ORDER_STATUS_CHANGED', payload: { id, status } });
    } catch (error) {
      dispatch({ type: 'ORDERS_ERROR', payload: error.message });
    }
  };
  
  return { loadOrders, updateOrderStatus };
};
```

## 🧪 Testing Patterns

### 1. Component Testing with React Testing Library

```typescript
// apps/web/src/features/inventory/__tests__/InventoryList.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { InventoryList } from '../components/InventoryList';
import { inventoryClient } from '@foodprepai/api-client/inventory';

// Mock the API client
jest.mock('@foodprepai/api-client/inventory');
const mockInventoryClient = inventoryClient as jest.Mocked<typeof inventoryClient>;

const mockInventoryData = [
  { id: '1', name: 'Tomatoes', quantity: 50, unit: 'kg', status: 'in-stock' },
  { id: '2', name: 'Chicken', quantity: 5, unit: 'kg', status: 'low-stock' },
];

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('InventoryList', () => {
  beforeEach(() => {
    mockInventoryClient.getByLocation.mockResolvedValue(mockInventoryData);
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders inventory items', async () => {
    renderWithQueryClient(<InventoryList locationId="location-1" />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Tomatoes')).toBeInTheDocument();
      expect(screen.getByText('Chicken')).toBeInTheDocument();
    });
    
    expect(mockInventoryClient.getByLocation).toHaveBeenCalledWith('location-1');
  });
  
  it('handles search functionality', async () => {
    renderWithQueryClient(<InventoryList locationId="location-1" />);
    
    await waitFor(() => {
      expect(screen.getByText('Tomatoes')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search inventory...');
    fireEvent.change(searchInput, { target: { value: 'chicken' } });
    
    expect(screen.getByText('Chicken')).toBeInTheDocument();
    expect(screen.queryByText('Tomatoes')).not.toBeInTheDocument();
  });
  
  it('displays low stock warning', async () => {
    renderWithQueryClient(<InventoryList locationId="location-1" />);
    
    await waitFor(() => {
      const lowStockItem = screen.getByText('Chicken').closest('[data-testid="inventory-item"]');
      expect(lowStockItem).toHaveClass('low-stock');
    });
  });
});
```

### 2. API Route Testing

```typescript
// apps/web/src/app/api/inventory/__tests__/route.test.ts
import { GET, POST } from '../route';
import { NextRequest } from 'next/server';
import { InventoryModel } from '@foodprepai/database-models';

// Mock the database model
jest.mock('@foodprepai/database-models');
const mockInventoryModel = InventoryModel as jest.Mocked<typeof InventoryModel>;

// Mock authentication
jest.mock('@/lib/auth', () => ({
  validateSession: jest.fn(),
}));

import { validateSession } from '@/lib/auth';
const mockValidateSession = validateSession as jest.MockedFunction<typeof validateSession>;

describe('/api/inventory', () => {
  const mockSession = {
    user: {
      id: 'user-1',
      locations: ['location-1', 'location-2'],
    },
  };
  
  beforeEach(() => {
    mockValidateSession.mockResolvedValue(mockSession);
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  describe('GET', () => {
    it('returns inventory for authenticated user', async () => {
      const mockInventory = [
        { id: '1', name: 'Item 1', locationId: 'location-1' },
        { id: '2', name: 'Item 2', locationId: 'location-1' },
      ];
      
      mockInventoryModel.find.mockResolvedValue(mockInventory);
      
      const request = new NextRequest('http://localhost:3000/api/inventory?locationId=location-1');
      const response = await GET(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockInventory);
      expect(mockInventoryModel.find).toHaveBeenCalledWith({ locationId: 'location-1' });
    });
    
    it('returns 401 for unauthenticated requests', async () => {
      mockValidateSession.mockResolvedValue(null);
      
      const request = new NextRequest('http://localhost:3000/api/inventory');
      const response = await GET(request);
      const data = await response.json();
      
      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });
    
    it('returns 403 for unauthorized location access', async () => {
      const request = new NextRequest('http://localhost:3000/api/inventory?locationId=location-3');
      const response = await GET(request);
      const data = await response.json();
      
      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });
  });
});
```

## 🚀 Performance Patterns

### 1. Optimistic Updates

```typescript
// Custom hook for optimistic inventory updates
export const useOptimisticInventory = (locationId: string) => {
  const queryClient = useQueryClient();
  const queryKey = ['inventory', locationId];
  
  const updateQuantity = useMutation({
    mutationFn: ({ id, newQuantity }: { id: string; newQuantity: number }) =>
      inventoryClient.updateQuantity(id, newQuantity),
    
    onMutate: async ({ id, newQuantity }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey });
      
      // Snapshot previous value
      const previousInventory = queryClient.getQueryData<InventoryItem[]>(queryKey);
      
      // Optimistically update
      queryClient.setQueryData<InventoryItem[]>(queryKey, (old = []) =>
        old.map(item =>
          item.id === id ? { ...item, quantity: newQuantity } : item
        )
      );
      
      return { previousInventory };
    },
    
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousInventory) {
        queryClient.setQueryData(queryKey, context.previousInventory);
      }
    },
    
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey });
    },
  });
  
  return { updateQuantity };
};
```

### 2. Virtual Scrolling for Large Lists

```typescript
// Using react-window for performance
import { FixedSizeList as List } from 'react-window';

interface VirtualizedInventoryListProps {
  items: InventoryItem[];
  onItemClick: (item: InventoryItem) => void;
}

const InventoryRow = ({ index, style, data }: {
  index: number;
  style: React.CSSProperties;
  data: { items: InventoryItem[]; onItemClick: (item: InventoryItem) => void };
}) => {
  const item = data.items[index];
  
  return (
    <div style={style} className="inventory-row" onClick={() => data.onItemClick(item)}>
      <span>{item.name}</span>
      <span>{item.quantity} {item.unit}</span>
      <StatusBadge status={item.status} />
    </div>
  );
};

export const VirtualizedInventoryList: React.FC<VirtualizedInventoryListProps> = ({
  items,
  onItemClick,
}) => {
  return (
    <List
      height={600}
      itemCount={items.length}
      itemSize={60}
      itemData={{ items, onItemClick }}
    >
      {InventoryRow}
    </List>
  );
};
```

These patterns provide a solid foundation for building consistent, maintainable, and performant features in the FoodPrepAI monorepo. Use them as templates and adapt them to your specific needs!