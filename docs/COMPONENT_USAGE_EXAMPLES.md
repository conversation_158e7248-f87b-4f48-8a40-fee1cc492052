# Component Usage Examples

This document provides practical examples of how to use the unified component library in both Next.js and Ionic React applications.

## Setup

### Next.js App Setup

```typescript
// app/layout.tsx
import { initializePlatform } from '@foodprepai/ui-components';
import '@foodprepai/design-tokens/css/tokens.css';

// Initialize for web platform
initializePlatform('web');

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
```

### Ionic React App Setup

```typescript
// main.tsx
import React from 'react';
import { createRoot } from 'react-dom/client';
import { initializePlatform } from '@foodprepai/ui-components';
import '@foodprepai/design-tokens/css/ionic-tokens.css';
import './theme/variables.css';

// Initialize for Ionic platform
initializePlatform('ionic');

import App from './App';

const container = document.getElementById('root');
const root = createRoot(container!);
root.render(<App />);
```

## Component Examples

### Button Component

```typescript
import React from 'react';
import { Button } from '@foodprepai/ui-components';
import { Plus, Save, Trash2 } from 'lucide-react';

export function ButtonExamples() {
  return (
    <div className="space-y-4 p-4">
      {/* Basic buttons */}
      <div className="flex space-x-2">
        <Button variant="primary">Primary</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
        <Button variant="destructive">Delete</Button>
      </div>

      {/* Different sizes */}
      <div className="flex items-center space-x-2">
        <Button size="xs">Extra Small</Button>
        <Button size="sm">Small</Button>
        <Button size="md">Medium</Button>
        <Button size="lg">Large</Button>
        <Button size="xl">Extra Large</Button>
      </div>

      {/* With icons */}
      <div className="flex space-x-2">
        <Button icon={<Plus />} iconPosition="start">
          Add Item
        </Button>
        <Button icon={<Save />} iconPosition="end" variant="secondary">
          Save Changes
        </Button>
        <Button icon={<Trash2 />} variant="destructive" size="sm">
          Delete
        </Button>
      </div>

      {/* Loading states */}
      <div className="flex space-x-2">
        <Button loading>Saving...</Button>
        <Button loading variant="outline">
          Loading
        </Button>
      </div>

      {/* Full width */}
      <Button fullWidth variant="primary">
        Full Width Button
      </Button>

      {/* Disabled */}
      <Button disabled>Disabled Button</Button>
    </div>
  );
}
```

### Table Component

```typescript
import React, { useState } from 'react';
import { Table, Button } from '@foodprepai/ui-components';
import { Badge } from '@/components/ui/badge'; // Your existing badge component

interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive' | 'pending';
  role: string;
  lastLogin: Date;
}

export function UserTable() {
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      status: 'active',
      role: 'Admin',
      lastLogin: new Date(),
    },
    // ... more users
  ]);

  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const columns = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (value: string, user: User) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{user.email}</div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (value: string) => (
        <Badge 
          variant={value === 'active' ? 'success' : value === 'inactive' ? 'destructive' : 'warning'}
        >
          {value}
        </Badge>
      ),
    },
    {
      key: 'role',
      header: 'Role',
      sortable: true,
    },
    {
      key: 'lastLogin',
      header: 'Last Login',
      sortable: true,
      render: (value: Date) => value.toLocaleDateString(),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, user: User) => (
        <div className="flex space-x-1">
          <Button size="xs" variant="outline" onClick={() => handleEdit(user.id)}>
            Edit
          </Button>
          <Button size="xs" variant="destructive" onClick={() => handleDelete(user.id)}>
            Delete
          </Button>
        </div>
      ),
    },
  ];

  const handleRowClick = (user: User) => {
    console.log('Row clicked:', user);
  };

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
    // Implement sorting logic
  };

  const handleEdit = (userId: string) => {
    console.log('Edit user:', userId);
  };

  const handleDelete = (userId: string) => {
    console.log('Delete user:', userId);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Users</h2>
        <Button icon={<Plus />}>Add User</Button>
      </div>

      <Table
        columns={columns}
        data={users}
        loading={loading}
        loadingRows={5}
        emptyMessage="No users found"
        onRowClick={handleRowClick}
        selectable
        selectedRows={selectedRows}
        onSelectionChange={setSelectedRows}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
      />

      {selectedRows.length > 0 && (
        <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
          <span className="text-sm font-medium">
            {selectedRows.length} user(s) selected
          </span>
          <Button size="sm" variant="outline">
            Bulk Edit
          </Button>
          <Button size="sm" variant="destructive">
            Bulk Delete
          </Button>
        </div>
      )}
    </div>
  );
}
```

### Modal Component

```typescript
import React, { useState } from 'react';
import { Modal, Button } from '@foodprepai/ui-components';
import { X } from 'lucide-react';

export function ModalExamples() {
  const [basicModal, setBasicModal] = useState(false);
  const [formModal, setFormModal] = useState(false);
  const [confirmModal, setConfirmModal] = useState(false);
  const [fullScreenModal, setFullScreenModal] = useState(false);

  return (
    <div className="space-y-4 p-4">
      {/* Trigger buttons */}
      <div className="flex space-x-2">
        <Button onClick={() => setBasicModal(true)}>
          Basic Modal
        </Button>
        <Button onClick={() => setFormModal(true)}>
          Form Modal
        </Button>
        <Button onClick={() => setConfirmModal(true)} variant="destructive">
          Confirm Modal
        </Button>
        <Button onClick={() => setFullScreenModal(true)} variant="outline">
          Full Screen
        </Button>
      </div>

      {/* Basic Modal */}
      <Modal
        isOpen={basicModal}
        onClose={() => setBasicModal(false)}
        title="Basic Modal"
        size="md"
      >
        <p className="text-gray-600">
          This is a basic modal with some content. It adapts automatically
          to the platform - showing as a dialog on web and a sheet on mobile.
        </p>
      </Modal>

      {/* Form Modal */}
      <Modal
        isOpen={formModal}
        onClose={() => setFormModal(false)}
        title="Create New User"
        size="lg"
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setFormModal(false)}>
              Cancel
            </Button>
            <Button onClick={() => setFormModal(false)}>
              Save User
            </Button>
          </div>
        }
      >
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              type="text"
              className="w-full p-2 border rounded-md"
              placeholder="Enter name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input
              type="email"
              className="w-full p-2 border rounded-md"
              placeholder="Enter email"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Role</label>
            <select className="w-full p-2 border rounded-md">
              <option>Admin</option>
              <option>Manager</option>
              <option>User</option>
            </select>
          </div>
        </form>
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        isOpen={confirmModal}
        onClose={() => setConfirmModal(false)}
        title="Confirm Deletion"
        size="sm"
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setConfirmModal(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => setConfirmModal(false)}>
              Delete
            </Button>
          </div>
        }
      >
        <p className="text-gray-600">
          Are you sure you want to delete this item? This action cannot be undone.
        </p>
      </Modal>

      {/* Full Screen Modal */}
      <Modal
        isOpen={fullScreenModal}
        onClose={() => setFullScreenModal(false)}
        title="Full Screen Content"
        size="full"
        header={
          <div className="flex items-center justify-between w-full">
            <h2 className="text-lg font-semibold">Custom Header</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFullScreenModal(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        }
      >
        <div className="space-y-6">
          <p>This modal takes up the full screen on mobile and a large portion on desktop.</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Section 1</h3>
              <p className="text-sm text-gray-600">Content for section 1</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Section 2</h3>
              <p className="text-sm text-gray-600">Content for section 2</p>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}
```

## Advanced Usage Patterns

### Platform-Specific Behavior

```typescript
import React from 'react';
import { Button, platformSelect, getPlatformConfig } from '@foodprepai/ui-components';

export function PlatformAwareComponent() {
  const platformConfig = getPlatformConfig();

  const buttonText = platformSelect({
    web: 'Click to Continue',
    ionic: 'Tap to Continue',
    default: 'Continue',
  });

  const iconName = platformSelect({
    web: 'arrow-right', // Web uses different icon set
    ionic: 'chevron-forward', // Ionic uses ionicons
    default: 'arrow-right',
  });

  return (
    <div>
      <p>Platform: {platformConfig.platform}</p>
      <p>Is Mobile: {platformConfig.isMobile ? 'Yes' : 'No'}</p>
      
      <Button icon={iconName}>
        {buttonText}
      </Button>
    </div>
  );
}
```

### Custom Platform Override

```typescript
import React from 'react';
import { Button } from '@foodprepai/ui-components';

export function PlatformOverrideExample() {
  return (
    <div className="space-y-4">
      {/* Force web rendering even on mobile */}
      <Button platform="web" variant="primary">
        Always Web Button
      </Button>

      {/* Force ionic rendering even on web */}
      <Button platform="ionic" variant="primary">
        Always Ionic Button
      </Button>

      {/* Auto-detect (default) */}
      <Button platform="auto" variant="primary">
        Auto-detect Button
      </Button>
    </div>
  );
}
```

### Form Integration

```typescript
import React from 'react';
import { useForm } from 'react-hook-form';
import { Button, Modal } from '@foodprepai/ui-components';

interface FormData {
  name: string;
  email: string;
  role: string;
}

export function FormIntegrationExample() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { register, handleSubmit, formState: { isSubmitting, errors } } = useForm<FormData>();

  const onSubmit = async (data: FormData) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('Form submitted:', data);
    setIsModalOpen(false);
  };

  return (
    <div>
      <Button onClick={() => setIsModalOpen(true)}>
        Open Form
      </Button>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="User Information"
        size="md"
      >
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              {...register('name', { required: 'Name is required' })}
              type="text"
              className="w-full p-2 border rounded-md"
              placeholder="Enter name"
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input
              {...register('email', { 
                required: 'Email is required',
                pattern: {
                  value: /\S+@\S+\.\S+/,
                  message: 'Invalid email address'
                }
              })}
              type="email"
              className="w-full p-2 border rounded-md"
              placeholder="Enter email"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Role</label>
            <select
              {...register('role', { required: 'Role is required' })}
              className="w-full p-2 border rounded-md"
            >
              <option value="">Select role</option>
              <option value="admin">Admin</option>
              <option value="manager">Manager</option>
              <option value="user">User</option>
            </select>
            {errors.role && (
              <p className="text-red-500 text-sm mt-1">{errors.role.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsModalOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={isSubmitting}
            >
              Save User
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
}
```

### Data Fetching with Tables

```typescript
import React, { useState, useEffect } from 'react';
import { Table, Button } from '@foodprepai/ui-components';
import { useQuery } from '@tanstack/react-query';

interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  stock: number;
  status: 'active' | 'inactive';
}

export function ProductTable() {
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const { data, isLoading, error } = useQuery({
    queryKey: ['products', page, sortBy, sortDirection],
    queryFn: () => fetchProducts({ page, sortBy, sortDirection }),
  });

  const columns = [
    {
      key: 'name',
      header: 'Product Name',
      sortable: true,
    },
    {
      key: 'category',
      header: 'Category',
      sortable: true,
    },
    {
      key: 'price',
      header: 'Price',
      sortable: true,
      render: (value: number) => `$${value.toFixed(2)}`,
      align: 'right' as const,
    },
    {
      key: 'stock',
      header: 'Stock',
      sortable: true,
      render: (value: number) => (
        <span className={value < 10 ? 'text-red-600 font-medium' : ''}>
          {value}
        </span>
      ),
      align: 'right' as const,
    },
    {
      key: 'status',
      header: 'Status',
      render: (value: string) => (
        <span
          className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
            value === 'active' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {value}
        </span>
      ),
    },
  ];

  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    setSortBy(column);
    setSortDirection(direction);
  };

  const handleRowClick = (product: Product) => {
    console.log('Product clicked:', product);
  };

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading products</p>
        <Button onClick={() => window.location.reload()} className="mt-2">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Products</h2>
        <Button>Add Product</Button>
      </div>

      <Table
        columns={columns}
        data={data?.products || []}
        loading={isLoading}
        loadingRows={10}
        emptyMessage="No products found"
        onRowClick={handleRowClick}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onSort={handleSort}
      />

      {/* Pagination */}
      {data?.totalPages && data.totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {page} of {data.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.min(data.totalPages, p + 1))}
            disabled={page === data.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}

async function fetchProducts({ page, sortBy, sortDirection }: {
  page: number;
  sortBy: string;
  sortDirection: 'asc' | 'desc';
}) {
  // Your API call implementation
  const response = await fetch(`/api/products?page=${page}&sort=${sortBy}&direction=${sortDirection}`);
  return response.json();
}
```

## Best Practices

### 1. Platform Configuration
Always configure the platform at the app level:
```typescript
// Do this once in your app root
initializePlatform('web'); // or 'ionic' or 'auto'
```

### 2. Consistent Styling
Use design tokens and utility classes:
```typescript
import { colors, spacing } from '@foodprepai/design-tokens';

const customStyle = {
  backgroundColor: colors.primary[500],
  padding: spacing[4],
};
```

### 3. Error Boundaries
Wrap components in error boundaries:
```typescript
<ErrorBoundary fallback={<div>Something went wrong</div>}>
  <Table data={data} columns={columns} />
</ErrorBoundary>
```

### 4. Accessibility
Ensure proper ARIA labels and keyboard navigation:
```typescript
<Button
  onClick={handleClick}
  aria-label="Delete user John Doe"
  disabled={loading}
>
  Delete
</Button>
```

### 5. Performance
Use loading states and skeleton screens:
```typescript
<Table
  data={data}
  loading={isLoading}
  loadingRows={5}
  emptyMessage="No data available"
/>
```

This comprehensive guide should help developers understand how to effectively use the unified component library across both platforms.