# FoodPrepAI Monorepo Type Architecture Design

## Executive Summary

This document outlines the comprehensive TypeScript type architecture designed for the FoodPrepAI monorepo, providing type safety, consistency, and maintainability across both the Next.js web application and Ionic React mobile application (IonicPOS).

## Architecture Overview

### Design Principles

1. **Type Safety First**: Eliminate runtime type errors through comprehensive compile-time checking
2. **Single Source of Truth**: Centralized type definitions to prevent duplication and inconsistencies
3. **Scalability**: Modular architecture that grows with the application
4. **Developer Experience**: IntelliSense, auto-completion, and clear error messages
5. **Runtime Validation**: Zod schemas for data validation at API boundaries
6. **Branded Types**: Strong typing for identifiers to prevent ID confusion
7. **Code Generation Ready**: Extensible architecture for future code generation needs

### Package Structure

```
packages/
├── shared-types/           # Core type definitions
│   ├── src/
│   │   ├── common/        # Base types and utilities
│   │   ├── user/          # User and authentication types
│   │   ├── company/       # Company and location types
│   │   ├── inventory/     # Inventory and UOM types
│   │   ├── orders/        # Order and delivery types
│   │   ├── api/           # API request/response types
│   │   ├── auth/          # Authentication types
│   │   └── index.ts       # Main exports
│   ├── package.json
│   └── tsconfig.json
│
├── shared-utils/           # Type-safe utility functions
│   ├── src/
│   │   ├── date/          # Date utilities
│   │   ├── validation/    # Validation functions
│   │   ├── formatting/    # Formatting utilities
│   │   ├── inventory/     # Inventory calculations
│   │   └── index.ts
│   ├── package.json
│   └── tsconfig.json
│
└── tools/
    └── tsconfig/           # Shared TypeScript configurations
        ├── base.json      # Base configuration
        ├── nextjs.json    # Next.js specific
        ├── react.json     # React/Ionic specific
        └── node.json      # Node.js specific
```

## Core Type Categories

### 1. Common Base Types (`common/`)

**Purpose**: Foundation types used across all domains

**Key Features**:
- MongoDB ObjectId handling
- Branded ID types for type safety
- Generic API response wrappers
- Pagination and filtering types
- Utility types for advanced TypeScript patterns

**Example**:
```typescript
// Branded types prevent ID confusion
export type CompanyId = Brand<ObjectId, 'CompanyId'>;
export type UserId = Brand<ObjectId, 'UserId'>;
export type LocationId = Brand<ObjectId, 'LocationId'>;

// Generic API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

// Advanced utility types
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;
```

### 2. User Types (`user/`)

**Purpose**: User management, authentication, and authorization

**Key Features**:
- Comprehensive user interface with POS support
- Permission-based access control
- JWT token payload structure
- PIN authentication for POS users
- User sync data for mobile apps

**Example**:
```typescript
export interface User extends BaseDocument {
  _id: UserId;
  email: string;
  userType: UserType;
  role?: SystemRole | ObjectId;
  permissions?: string[];
  locationIds: LocationId[];
  canUseIonicApp?: boolean;
  posAccess?: boolean;
  posSettings?: POSSettings;
}

export const STANDARD_PERMISSIONS = {
  'inventory:read': 'View inventory items and levels',
  'inventory:write': 'Create and update inventory items',
  'orders:read': 'View orders',
  'pos:access': 'Access POS system',
  // ... more permissions
} as const;
```

### 3. Company Types (`company/`)

**Purpose**: Multi-tenant company and location management

**Key Features**:
- Company entity with subscription support
- Location types (Central Kitchen, Retail Shop, Single Location)
- Location-specific settings and configurations
- Operating hours and contact information
- Role-based access control within companies

**Example**:
```typescript
export interface Company extends BaseDocument {
  _id: CompanyId;
  name: string;
  subdomain: string;
  ownerId: UserId;
  subscription?: CompanySubscription;
  settings?: CompanySettings;
}

export interface Location extends BaseDocument {
  _id: LocationId;
  companyId: CompanyId;
  locationType: LocationType;
  canSellToExternal: boolean;
  canDoTransfers: boolean;
  settings?: LocationSettings;
}
```

### 4. Inventory Types (`inventory/`)

**Purpose**: Comprehensive inventory management system

**Key Features**:
- Ingredient management with supplier details
- Unit of Measure (UOM) conversions
- Selling options with markup calculations
- Stock counting and transfers
- Inventory transactions and movements
- ABC analysis and reorder calculations

**Example**:
```typescript
export interface Ingredient extends BaseDocument {
  _id: IngredientId;
  name: string;
  baseUomId: UOMId;
  supplierDetails: SupplierDetail[];
  sellingDetails: SellingOption[];
  currentStock: number;
  reorderPoint: number | null;
}

export interface InventoryTransaction extends BaseDocument {
  transactionType: InventoryTransactionType;
  quantity: number;
  ingredientId: IngredientId;
  locationId: LocationId;
  syncStatus?: SyncStatus;
}
```

### 5. Order Types (`orders/`)

**Purpose**: Order management and delivery tracking

**Key Features**:
- Flexible order structure supporting multiple entity types
- Order items with pricing and tax calculations
- Delivery note integration
- Order status workflow management
- Multi-source order support (Web, Ionic, POS)
- Sync capabilities for offline operations

**Example**:
```typescript
export interface Order extends BaseDocument {
  _id: OrderId;
  orderNumber: string;
  status: OrderStatus;
  buyer: OrderEntity;
  seller?: OrderEntity;
  items: OrderItem[];
  orderSource?: OrderSource;
  syncStatus?: SyncStatus;
}

export interface OrderItem {
  itemType: ItemType;
  itemId: IngredientId | RecipeId;
  quantity: number;
  deliveredQuantity: number;
  unitPrice: number;
  lineTotal: number;
}
```

### 6. API Types (`api/`)

**Purpose**: Standardized API interfaces and error handling

**Key Features**:
- Namespace organization by domain (AuthAPI, UserAPI, etc.)
- Consistent request/response patterns
- Comprehensive error handling types
- Pagination and filtering interfaces
- API client interface definition

**Example**:
```typescript
export namespace UserAPI {
  export interface ListUsersQuery extends PaginationParams {
    search?: string;
    role?: string;
    locationId?: LocationId;
  }
  
  export interface GetUserResponse extends ApiResponse<UserProfile> {}
  export interface ListUsersResponse extends ApiResponse<PaginatedResponse<UserProfile>> {}
}

export interface APIClient {
  getUser(id: UserId): Promise<UserAPI.GetUserResponse>;
  listUsers(query?: UserAPI.ListUsersQuery): Promise<UserAPI.ListUsersResponse>;
  // ... other methods
}
```

### 7. Authentication Types (`auth/`)

**Purpose**: Authentication and authorization system

**Key Features**:
- JWT token structure and validation
- Session management
- Two-factor authentication support
- API key authentication for external systems
- Permission-based authorization
- Device and location-based access control

**Example**:
```typescript
export interface JWTPayload {
  sub: UserId;
  email: string;
  userType: UserType;
  companyId?: CompanyId;
  permissions?: string[];
  posAccess?: boolean;
  exp: number;
  iat: number;
}

export interface AuthSession {
  user: AuthUser;
  token: string;
  refreshToken?: string;
  permissions: string[];
  locationAccess: LocationId[];
}
```

## Advanced TypeScript Patterns

### 1. Branded Types

Prevent ID confusion with branded types:

```typescript
export type Brand<K, T> = K & { __brand: T };
export type CompanyId = Brand<ObjectId, 'CompanyId'>;
export type UserId = Brand<ObjectId, 'UserId'>;

// Helper functions for type-safe ID creation
export const createCompanyId = (id: string): CompanyId => id as CompanyId;
export const createUserId = (id: string): UserId => id as UserId;
```

### 2. Utility Types

Advanced utility types for flexible data manipulation:

```typescript
// Make certain fields optional
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Make certain fields required
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Deep partial for nested objects
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
```

### 3. Conditional Types

Context-aware types based on conditions:

```typescript
// User type varies based on user type
type UserWithCompany<T extends UserType> = T extends 'company_user' 
  ? User & { companyId: CompanyId }
  : User;

// API response type varies based on operation
type ApiResult<T extends 'success' | 'error'> = T extends 'success'
  ? { success: true; data: any }
  : { success: false; error: string };
```

### 4. Template Literal Types

Strongly typed string patterns:

```typescript
// Permission string patterns
type InventoryPermission = `inventory:${string}`;
type OrderPermission = `orders:${string}`;
type Permission = InventoryPermission | OrderPermission | `pos:${string}`;
```

## Runtime Validation with Zod

### Schema Definition

Every major type includes corresponding Zod schemas:

```typescript
export const CreateUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  userType: z.enum(['superuser', 'company_user']),
  permissions: z.array(z.string()).optional().default([])
});

export const CreateOrderSchema = z.object({
  buyer: z.object({
    entityType: z.enum(['CUSTOMER', 'SUPPLIER', 'BRANCH', 'CENTRAL_KITCHEN']),
    entityId: z.string()
  }),
  items: z.array(CreateOrderItemSchema).min(1, 'At least one item is required')
});
```

### Validation Helper

Generic validation function for any schema:

```typescript
export const validateWithSchema = <T>(
  data: unknown, 
  schema: z.ZodSchema<T>
): ValidationResult<T> => {
  try {
    const validatedData = schema.parse(data);
    return { isValid: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return { isValid: false, errors: ['Unknown validation error'] };
  }
};
```

## Type Safety Enforcement

### 1. Strict TypeScript Configuration

```json
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### 2. Type Guards

Runtime type checking functions:

```typescript
export const isSystemRole = (role: string): role is SystemRole => {
  return ['owner', 'admin', 'manager', 'user', 'storekeeper'].includes(role);
};

export const hasPermission = (user: UserAuth, permission: string): boolean => {
  return user.permissions?.includes(permission) ?? false;
};
```

### 3. Assertion Functions

Type assertions with runtime checks:

```typescript
export const assertValidObjectId = (id: string): asserts id is ObjectId => {
  if (!/^[0-9a-fA-F]{24}$/.test(id)) {
    throw new Error(`Invalid ObjectId: ${id}`);
  }
};
```

## Code Generation Strategy

### 1. OpenAPI Integration (Future)

Architecture supports future OpenAPI code generation:

```typescript
// Generated types would extend base interfaces
interface GeneratedUserAPI extends BaseAPI {
  '/users': {
    GET: {
      query: UserAPI.ListUsersQuery;
      response: UserAPI.ListUsersResponse;
    };
    POST: {
      body: CreateUserInput;
      response: UserAPI.CreateUserResponse;
    };
  };
}
```

### 2. Database Schema Generation

Types can be generated from database schemas:

```typescript
// Schema to TypeScript type generation
type GeneratedIngredient = SchemaToType<IngredientSchema>;
type GeneratedOrder = SchemaToType<OrderSchema>;
```

### 3. Form Type Generation

Generate form types from schemas:

```typescript
// Generate form types from Zod schemas
type CreateUserForm = z.infer<typeof CreateUserSchema>;
type UpdateUserForm = z.infer<typeof UpdateUserSchema>;
```

## Migration Strategy

### Phase 1: Foundation (Completed)
- ✅ Create shared-types package
- ✅ Create shared-utils package
- ✅ Set up TypeScript configurations
- ✅ Implement core type definitions
- ✅ Add Zod validation schemas

### Phase 2: Integration
- Update existing Next.js application to use shared types
- Replace local type definitions with branded types
- Implement API client with shared types
- Update database models to align with shared types

### Phase 3: Mobile Application
- Integrate Ionic React application with shared types
- Implement offline sync with typed data
- Add POS-specific type validations
- Ensure type consistency across platforms

### Phase 4: Advanced Features
- Implement code generation for API endpoints
- Add automated type testing
- Create development tools for type validation
- Optimize bundle size with tree shaking

## Performance Considerations

### 1. Bundle Size Optimization

- Tree-shakable exports for optimal bundle sizes
- Separate validation schemas to reduce client bundle
- Conditional type loading based on application needs

### 2. Compilation Performance

- Project references for incremental compilation
- Composite TypeScript configurations
- Optimized type resolution paths

### 3. Runtime Performance

- Efficient Zod schema validation
- Memoized type guards and validators
- Lazy loading of complex validation schemas

## Maintenance and Evolution

### 1. Versioning Strategy

- Semantic versioning for shared packages
- Breaking change documentation
- Migration guides for major updates

### 2. Testing Strategy

- Unit tests for utility functions
- Type-level testing with TypeScript
- Integration tests for schema validation

### 3. Documentation

- Comprehensive README files
- Usage examples and best practices
- API documentation generation

## Benefits Delivered

### 1. Type Safety
- Compile-time error detection
- Eliminated ID confusion with branded types
- Consistent data structures across applications

### 2. Developer Experience
- IntelliSense and auto-completion
- Clear error messages
- Standardized patterns and conventions

### 3. Maintainability
- Single source of truth for types
- Centralized validation logic
- Consistent API patterns

### 4. Scalability
- Modular architecture
- Extensible type system
- Code generation ready

### 5. Quality Assurance
- Runtime validation at API boundaries
- Type-safe database operations
- Consistent data transformation

## Conclusion

The FoodPrepAI monorepo type architecture provides a robust, scalable foundation for type safety across both web and mobile applications. The comprehensive type system eliminates common runtime errors, improves developer productivity, and ensures data consistency throughout the ecosystem.

The modular design allows for easy extension and maintenance while the integration of runtime validation ensures type safety at all application boundaries. This architecture positions the FoodPrepAI platform for continued growth and evolution while maintaining the highest standards of code quality and developer experience.