import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Company from './models/Company';
import User from './models/User';
import bcrypt from 'bcrypt';

dotenv.config(); // Load environment variables from .env file


const seed = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || '');
    console.log('Connected to MongoDB.');

    // Hash the password
    const hashedPassword = await bcrypt.hash('securepassword', 10);
    
    // Create a test user (owner)
    const ownerUser = await User.create({
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      displayName: 'Test Owner2',
      userType: 'company_user',
      role: 'owner', 
    });

    console.log('User created:', ownerUser);

    // Create a test company with the owner
    const testCompany = await Company.create({
      name: 'Example Company',
      subdomain: 'example',
      ownerId: ownerUser._id, // Associate owner with the company
    });

    console.log('Company created:', testCompany);

    // Update the owner user to associate with the company
    ownerUser.companyId = testCompany._id;
    await ownerUser.save();

    console.log('User updated with company association:', ownerUser);

    process.exit(0); // Exit script after successful seeding
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1); // Exit script with failure
  }
};

seed();
