import { z } from 'zod';

export const sellingOptionSchema = z.object({
  unitOfSelling: z.object({
    _id: z.string().min(1, 'Unit of selling is required'),
    name: z.string().optional(),
    shortCode: z.string().optional()
  }),
  priceWithoutTax: z.number().min(0, 'Price must be non-negative'),
  priceWithTax: z.number().min(0, 'Price with tax must be non-negative'),
  taxRate: z.number().min(0, 'Tax rate must be non-negative'),
  taxCategory: z.enum(['STANDARD', 'REDUCED', 'ZERO', 'EXEMPT']).default('STANDARD'),
  conversionFactor: z.number().min(0.01, 'Conversion factor must be positive'),
  visibility: z.object({
    type: z.enum(['ALL_LOCATIONS', 'SPECIFIC_LOCATIONS', 'EXTERNAL_ONLY']).default('ALL_LOCATIONS'),
    locations: z.array(z.string()).optional(),
    externalAccess: z.boolean().default(false)
  }),
  sourceType: z.enum(['CENTRAL_KITCHEN', 'EXTERNAL_SUPPLIER', 'BOTH']),
  markupType: z.enum(['AT_COST', 'MARKUP']).optional(),
  markupPercentage: z.number().min(0).optional()
});

export const ingredientSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().nullable(),
  reorderPoint: z.number().min(0).nullable(),
  baseUomId: z.string().min(1, 'Base UOM is required'),
  category: z.string().min(1, 'Category is required'),
  suppliers: z.array(z.string()).min(1, 'At least one supplier is required'),
  SKU: z.string().min(1, 'SKU is required'),
  canBeSold: z.boolean().default(false),
  defaultSupplierId: z.string().nullable(),
  sellingDetails: z.array(sellingOptionSchema).optional()
});
