import { z } from 'zod';

// Base price schema without computed fields
export const menuPriceSchema = z.object({
  basePrice: z.number().min(0, 'Base price must be non-negative'),
  vatRate: z.number().min(0, 'VAT rate must be non-negative'),
});

// Full price schema including computed fields
export const fullMenuPriceSchema = menuPriceSchema.extend({
  vatAmount: z.number().min(0),
  finalPrice: z.number().min(0),
});

// Direct inventory item schema
export const menuInventoryItemSchema = z.object({
  itemId: z.string().min(1, 'Inventory item is required'),
  unit: z.string().min(1, 'Unit is required'),
});

// Recipe component schema
export const menuRecipeComponentSchema = z.object({
  itemId: z.string().min(1, 'Inventory item is required'),
  quantity: z.number().positive('Quantity must be positive'),
  unit: z.string().min(1, 'Unit is required'),
});

// Create menu item schema
export const createMenuItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  type: z.enum(['single', 'recipe']),  
  prices: menuPriceSchema,
  inventoryItem: menuInventoryItemSchema.optional(),
  recipeComponents: z.array(menuRecipeComponentSchema).optional(),
  status: z.enum(['active', 'inactive']).default('active'),
}).refine(
  (data) => {
    if (data.type === 'single') {
      return !!data.inventoryItem && !data.recipeComponents;
    }
    return data.type === 'recipe' ? 
      (!data.inventoryItem && !!data.recipeComponents && data.recipeComponents.length > 0) : true;
  },
  {
    message: "Single items must have an inventoryItem (and no recipe components), and recipe items must have at least one component (and no inventory item)",
    path: ['type'],
  }
);

// Update menu item schema - manually make fields optional since .partial() isn't working
export const updateMenuItemSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required').optional(),
  // Support both lowercase and uppercase type values for compatibility
  type: z.enum(['single', 'recipe', 'SINGLE', 'RECIPE', 'DIRECT']).optional(),  
  prices: menuPriceSchema.optional(),
  inventoryItem: menuInventoryItemSchema.optional(),
  recipeComponents: z.array(menuRecipeComponentSchema).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  inventoryStatus: z.enum(['pending', 'linked']).optional(),
  companyId: z.string().min(1, 'Company ID is required'),
}).refine(
  (data) => {
    // Normalize type to lowercase for validation
    const normalizedType = data.type?.toLowerCase();
    
    // For single/SINGLE/DIRECT items with inventory data
    if ((normalizedType === 'single' || normalizedType === 'direct') && 
        (data.inventoryItem || data.recipeComponents !== undefined)) {
      return !!data.inventoryItem && !data.recipeComponents;
    }
    
    // For recipe/RECIPE items with component data
    if (normalizedType === 'recipe' && 
        (data.inventoryItem !== undefined || data.recipeComponents)) {
      // We'll relax this validation to allow empty recipeComponents arrays
      // for initially setting up a recipe structure
      return !data.inventoryItem; 
    }
    
    return true;
  },
  {
    message: "Single items must have an inventoryItem (and no recipe components), and recipe items must have at least one component (and no inventory item)",
    path: ['type'],
  }
);

// Create menu category schema
export const createMenuCategorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  order: z.number().int().nonnegative().optional(),
});

// Update menu category schema - manually define optional fields
export const updateMenuCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  order: z.number().int().nonnegative().optional(),
  status: z.enum(['active', 'inactive']).optional(),
});

// Helper function to compute final price with VAT
export function computeFinalPrice(basePrice: number, vatRate: number): {
  vatAmount: number;
  finalPrice: number;
} {
  const vatAmount = basePrice * (vatRate / 100);
  return {
    vatAmount,
    finalPrice: basePrice + vatAmount
  };
}

// Helper function to validate and compute full price
export function validateAndComputePrice(data: z.infer<typeof menuPriceSchema>) {
  const { basePrice, vatRate } = menuPriceSchema.parse(data);
  return {
    basePrice,
    vatRate,
    ...computeFinalPrice(basePrice, vatRate)
  };
}
