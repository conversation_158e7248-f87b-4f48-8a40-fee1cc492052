//src/app/api/recipes/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import Recipe from '@/models/Recipe';
import UOM from '@/models/UOM';
import { Types } from 'mongoose';

// GET /api/recipes
export async function GET(req: NextRequest) {
  try {
    // Authentication
    const auth = await requireAuth()(req);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = auth.tenantId;

    await dbConnect();

    // Support pagination and filtering
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const isSubRecipe = searchParams.get('isSubRecipe') === 'true';

    // Build base query with UOM validation included
    const baseQuery: Record<string, any> = {
      companyId: new Types.ObjectId(companyId),
      isSubRecipe: isSubRecipe,
      $expr: {
        $or: [
          { $eq: ["$baseYieldUOM", null] },
          { $eq: [{ $type: "$baseYieldUOM" }, "objectId"] }
        ]
      }
    };

    console.log('Base Query:', JSON.stringify(baseQuery, null, 2));

    try {
      // Get total count
      const total = await Recipe.countDocuments(baseQuery);
      console.log('Total recipes found:', total);

      // Get paginated results
      const recipes: any[] = await Recipe.find(baseQuery)
        .select({
          name: 1,
          description: 1,
          isSubRecipe: 1,
          yield: 1,
          baseYieldUOM: 1,
          Category: 1,
          stockable: 1,
          recipeIngredients: 1,
          companyId: 1
        })
        .populate({
          path: 'baseYieldUOM',
          select: 'name shortCode system baseType factorToCanonical'
        })
        .populate({
          path: 'recipeIngredients.unitOfMeasure',
          select: 'name shortCode system baseType factorToCanonical'
        })
        .sort({ name: 1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean() as any[];

      console.log('Recipes returned:', recipes.length);
      if (recipes.length > 0) {
        console.log('First recipe details:', {
          id: recipes[0]._id,
          name: recipes[0].name,
          yield: recipes[0].yield,
          baseYieldUOM: recipes[0].baseYieldUOM,
          ingredients: recipes[0].recipeIngredients?.length || 0
        });
      }

      return NextResponse.json({
        recipes: recipes.map((recipe: any) => ({
          ...recipe,
          _id: recipe._id.toString(),
          companyId: recipe.companyId.toString(),
          baseYieldUOM: recipe.baseYieldUOM ? {
            _id: recipe.baseYieldUOM._id?.toString(),
            name: recipe.baseYieldUOM.name,
            shortCode: recipe.baseYieldUOM.shortCode
          } : null,
          recipeIngredients: (recipe.recipeIngredients || []).map((ingredient: any) => ({
            ...ingredient,
            _id: ingredient._id?.toString(),
            ingredientId: ingredient.ingredientId?.toString(),
            recipesId: ingredient.recipesId?.toString(),
            unitOfMeasure: ingredient.unitOfMeasure ? {
              _id: ingredient.unitOfMeasure._id?.toString(),
              name: ingredient.unitOfMeasure.name,
              shortCode: ingredient.unitOfMeasure.shortCode
            } : null
          }))
        })),
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      });
    } catch (error: unknown) {
      console.error('[GET Recipes] Error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch recipes' },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('[GET Recipes] Outer Error:', error);
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json(
      { error: 'Failed to fetch recipes' },
      { status: 500 }
    );
  }
}

// POST /api/recipes
export async function POST(req: NextRequest) {
  try {
    // Authentication
    const auth = await requireAuth()(req);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = auth.tenantId;

    await dbConnect();

    const data: any = await req.json();
    
    // Validate UOMs
    const baseYieldUom = await UOM.findById(data.baseYieldUomId);
    const preferredYieldUom = await UOM.findById(data.preferredYieldUomId);
    
    if (!baseYieldUom || !preferredYieldUom) {
      return NextResponse.json({ error: 'Invalid yield UOMs' }, { status: 400 });
    }

    if (baseYieldUom.baseType !== preferredYieldUom.baseType) {
      return NextResponse.json({ error: 'Incompatible yield UOMs' }, { status: 400 });
    }

    // Validate ingredient UOMs
    for (const ingredient of data.recipeIngredients) {
      const baseUom = await UOM.findById(ingredient.baseUomId);
      const preferredUom = await UOM.findById(ingredient.preferredUomId);
      
      if (!baseUom || !preferredUom) {
        return NextResponse.json({ 
          error: `Invalid UOMs for ingredient: ${ingredient.ingredientName}` 
        }, { status: 400 });
      }

      if (baseUom.baseType !== preferredUom.baseType) {
        return NextResponse.json({ 
          error: `Incompatible UOMs for ingredient: ${ingredient.ingredientName}` 
        }, { status: 400 });
      }
    }

    const recipe = await Recipe.create({
      ...data,
      companyId: new Types.ObjectId(companyId)
    });

    return NextResponse.json(recipe, { status: 201 });
  } catch (error: unknown) {
    console.error('[POST Recipe] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    if (error instanceof Error && (error.message.includes('Invalid') || error.message.includes('Incompatible'))) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    return NextResponse.json(
      { error: 'Failed to create recipe' },
      { status: 500 }
    );
  }
}

// DELETE /api/recipes/bulk
export async function DELETE(req: NextRequest) {
  try {
    // Authentication
    const auth = await requireAuth()(req);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = auth.tenantId;

    await dbConnect();

    const { ids }: { ids: string[] } = await req.json();
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Valid recipe IDs array is required' },
        { status: 400 }
      );
    }

    // Validate and convert IDs
    if (!ids.every(id => Types.ObjectId.isValid(id))) {
      return NextResponse.json({ error: 'All IDs must be valid ObjectIds' }, { status: 400 });
    }
    const objectIds = ids.map(id => new Types.ObjectId(id));
    const result = await Recipe.deleteMany({
      _id: { $in: objectIds },
      companyId: new Types.ObjectId(companyId)
    });

    return NextResponse.json({
      message: `Successfully deleted ${result.deletedCount} recipes`
    });
  } catch (error: unknown) {
    console.error('[DELETE Recipes] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    if (error instanceof Error && error.message.includes('Valid recipe IDs array is required')) {
        return NextResponse.json({ error: error.message }, { status: 400 });
    }
    if (error instanceof Error && error.message.includes('All IDs must be valid ObjectIds')) {
        return NextResponse.json({ error: error.message }, { status: 400 });
    }
    return NextResponse.json(
      { error: 'Failed to delete recipes' },
      { status: 500 }
    );
  }
}