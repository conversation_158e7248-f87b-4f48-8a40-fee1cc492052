import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import Recipe, { IRecipe } from '@/models/Recipe';
import { Types } from 'mongoose';

export async function PUT(request: NextRequest) {
  try {
    // Authenticate and authorize
    const user = await requireAuth(request);
    const companyIdHeader = request.headers.get('company-id');
    if (!companyIdHeader) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }
    if (companyIdHeader !== user.tenantId) {
      return NextResponse.json({ error: 'Unauthorized company ID' }, { status: 403 });
    }
    await dbConnect();
    const companyId = companyIdHeader;
    
    const { ids, update }: { ids: string[]; update: Partial<IRecipe> } = await request.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({ error: 'Recipe IDs are required' }, { status: 400 });
    }

    // Validate all IDs
    if (!ids.every(id => Types.ObjectId.isValid(id))) {
      return NextResponse.json({ error: 'Invalid recipe ID format' }, { status: 400 });
    }

    // Convert string IDs to ObjectIds
    const objectIds = ids.map(id => new Types.ObjectId(id));

    // Sanitize the update payload
    const sanitizedUpdate = { ...update };
    delete (sanitizedUpdate as any)._id; // Prevent changing _id
    delete (sanitizedUpdate as any).companyId; // Prevent changing companyId
    delete (sanitizedUpdate as any).createdAt; // Prevent changing createdAt
    delete (sanitizedUpdate as any).updatedAt; // Prevent changing updatedAt

    // Update all recipes that belong to the company
    const result = await Recipe.updateMany(
      { 
        _id: { $in: objectIds },
        companyId: new Types.ObjectId(companyId)
      },
      { $set: sanitizedUpdate }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'No recipes found or they do not belong to the company' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: `Successfully updated ${result.modifiedCount} recipes`,
      modifiedCount: result.modifiedCount
    });
  } catch (error: unknown) {
    console.error('[PUT Bulk Update Recipes] Error:', error);
    const message = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json(
      { error: 'Failed to update recipes', details: message },
      { status: 500 }
    );
  }
}
