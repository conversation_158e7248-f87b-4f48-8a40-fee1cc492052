import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Recipe, { IR<PERSON><PERSON>e, RecipeIngredient } from '@/models/Recipe';
import { Ingredient } from '@/models/Ingredient';
import UOM, { IUOM } from '@/models/UOM';
import mongoose, { Types } from 'mongoose';
import { requireAuth } from '@/lib/auth-helpers';
import { convertUOMValue } from '@/utils/uomConversion';

// Helper function to get display units based on base UOM
function getDisplayUnits(uoms: IUOM[], baseUom: IUOM): { small: IUOM | null; large: IUOM | null } {
  if (!baseUom || !uoms) return { small: null, large: null };

  // Find UOMs of the same baseType
  const compatibleUoms = uoms.filter(uom => uom.baseType === baseUom.baseType);
  if (compatibleUoms.length === 0) return { small: null, large: null };

  // Sort by factorToCanonical (ascending)
  compatibleUoms.sort((a, b) => a.factorToCanonical - b.factorToCanonical);

  // Find the index of the baseUom
  const baseIndex = compatibleUoms.findIndex(uom => 
    (uom._id as Types.ObjectId)?.toString() === (baseUom._id as Types.ObjectId)?.toString()
  );

  // Get the unit just below (if exists) and the base unit itself (or the smallest if base is smallest)
  const small = baseIndex > 0 ? compatibleUoms[baseIndex - 1] : compatibleUoms[0];
  const large = compatibleUoms[baseIndex]; // Base unit is the 'large' unit for display

  return { small, large };
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await dbConnect();
    const auth = await requireAuth(request);
    const tenantId = auth.tenantId;
    const { id } = params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid Recipe ID' }, { status: 400 });
    }

    // Use .lean<IRecipe>() to type the plain JS object
    const recipe = await Recipe.findOne({ _id: id, companyId: tenantId })
      .populate<{ recipeIngredients: RecipeIngredient[] }>({
        path: 'recipeIngredients.ingredientId',
        model: Ingredient
      })
      .populate<{ recipeIngredients: RecipeIngredient[] }>({
        path: 'recipeIngredients.recipesId',
        model: Recipe
      })
      .lean<IRecipe>();

    if (!recipe) {
      return NextResponse.json({ message: 'Recipe not found' }, { status: 404 });
    }

    // Get all UOM IDs from recipe - Safely access properties after null check
    const uomIds = [
      recipe.baseYieldUOM, // Access after null check
      ...(recipe.recipeIngredients || []).map((i: RecipeIngredient) => i.unitOfMeasure) // Add type and default to empty array
    ].filter(uomId => uomId && mongoose.Types.ObjectId.isValid(uomId.toString())); // Filter valid ObjectIds

    // Fetch all necessary UOMs in one go
    const uoms: IUOM[] = await UOM.find({ _id: { $in: uomIds } }).lean<IUOM[]>(); // Type fetched UOMs
    const uomMap = new Map(uoms.map((uom: IUOM) => [(uom._id as Types.ObjectId).toString(), uom])); // Use IUOM type

    const baseUom = recipe.baseYieldUOM ? uomMap.get(recipe.baseYieldUOM.toString()) : undefined;

    // Prepare the response object, converting ObjectIds to strings
    const responseRecipe = {
      _id: recipe._id.toString(),
      companyId: recipe.companyId.toString(),
      name: recipe.name,
      description: recipe.description,
      yield: recipe.yield, // Add yield property
      baseYieldUOMId: recipe.baseYieldUOM?.toString(),
      baseYieldUOM: baseUom ? { ...baseUom, _id: (baseUom._id as Types.ObjectId).toString() } : null,
      yieldUomShortCode: baseUom?.shortCode || '',
      createdAt: recipe.createdAt,
      updatedAt: recipe.updatedAt,
      // Process ingredients
      recipeIngredients: (recipe.recipeIngredients || []).map((ingredient: RecipeIngredient) => { // Add type and default array
        const ingredientUom = ingredient.unitOfMeasure ? uomMap.get(ingredient.unitOfMeasure.toString()) : undefined;
        if (!ingredientUom) return { ...ingredient, smallQuantity: null, largeQuantity: null, unit: null, displayUnits: null }; // Handle missing UOM

        // Get display units (ensure types match)
        const { small: smallUom, large: largeUom } = getDisplayUnits(uoms, ingredientUom as any); // TODO: Refactor getDisplayUnits or UOM fetching to handle IUOM vs UOM mismatch

        // Calculate both quantities safely (ensure types match)
        const smallQuantity = smallUom ? convertUOMValue(ingredient.quantity, ingredientUom as any, smallUom as any) : null; // TODO: Refactor convertUOMValue or UOM fetching to handle IUOM vs UOM mismatch
        const largeQuantity = largeUom ? convertUOMValue(ingredient.quantity, ingredientUom as any, largeUom as any) : null; // TODO: Refactor convertUOMValue or UOM fetching to handle IUOM vs UOM mismatch

        // Determine ingredient details
        let ingredientDetails = {};
        if (ingredient.ingredientId) {
            // Assuming ingredientId might be populated or just an ID
            const details = typeof ingredient.ingredientId === 'object' && ingredient.ingredientId !== null && 'name' in ingredient.ingredientId
                ? ingredient.ingredientId
                : { name: 'Ingredient Details Missing', _id: ingredient.ingredientId?.toString() }; // Fallback
             ingredientDetails = {
                type: 'Ingredient',
                name: details.name,
                _id: details._id?.toString()
            };
        } else if (ingredient.recipesId) {
             const details = typeof ingredient.recipesId === 'object' && ingredient.recipesId !== null && 'name' in ingredient.recipesId
                ? ingredient.recipesId
                : { name: 'Sub-Recipe Details Missing', _id: ingredient.recipesId?.toString() }; // Fallback
            ingredientDetails = {
                type: 'Recipe',
                name: details.name,
                 _id: details._id?.toString()
            };
        }

        return {
          ...ingredient,
          ...ingredientDetails, // Spread ingredient details
          ingredientId: ingredient.ingredientId?.toString(), // Convert IDs to string
          recipesId: ingredient.recipesId?.toString(),     // Convert IDs to string
          unitOfMeasure: ingredient.unitOfMeasure?.toString(), // Convert IDs to string
          unit: ingredientUom ? { ...ingredientUom, _id: (ingredientUom._id as Types.ObjectId).toString() } : null,
          displayUnits: {
            small: smallUom ? { ...smallUom, _id: (smallUom._id as Types.ObjectId).toString() } : null,
            large: largeUom ? { ...largeUom, _id: (largeUom._id as Types.ObjectId).toString() } : null,
          },
          smallQuantity, // Add calculated quantity
          largeQuantity, // Add calculated quantity
        };
      }),
      // Process selling details
      sellingDetails: (recipe.sellingDetails || []).map((option: any) => ({ // Add type and default array
        ...option,
        _id: option._id?.toString(),
        sellingOptionId: option.sellingOptionId?.toString(),
      })),
    };

    return NextResponse.json(responseRecipe);

  } catch (error: any) {
    console.error('Error fetching recipe by ID:', error);
    return NextResponse.json({ message: 'Internal Server Error', error: error.message }, { status: 500 });
  }
}


// Helper function to populate UOMs
async function populateUOMs(recipeData: Partial<IRecipe>) { // Accept partial IRecipe
  // Use optional chaining and default arrays
  const uomIds = [
      recipeData.baseYieldUOM,
      ...(recipeData.recipeIngredients || []).map((i: any) => i.unitOfMeasure)
  ].filter(Boolean).map(id => id.toString()); // Map to strings for filtering

  const validUomIds = uomIds.filter(id => mongoose.Types.ObjectId.isValid(id));

  const uoms: IUOM[] = await UOM.find({ _id: { $in: validUomIds.map(id => new Types.ObjectId(id)) } }).lean<IUOM[]>(); // Type fetched UOMs
  const uomMap = new Map(uoms.map((uom: IUOM) => [
    (uom._id as Types.ObjectId).toString(), // Assert _id as ObjectId
    uom // Store the actual IUOM object in the map
  ])); 
  return { uoms, uomMap };
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await dbConnect();
    const auth = await requireAuth(request);
    const tenantId = auth.tenantId;
    const userId = auth.id;
    const { id } = params;
    const body = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid Recipe ID' }, { status: 400 });
    }

    // Prepare data for update, ensuring correct types
    const updateData = {
      ...body,
      companyId: tenantId, // Ensure companyId matches tenantId
      modifiedBy: userId,  // Set modifiedBy
      baseYieldUOM: body.baseYieldUOMId ? new Types.ObjectId(body.baseYieldUOMId) : undefined,
      recipeIngredients: body.recipeIngredients?.map((ing: any) => ({ // Use optional chaining
        ...ing,
        // Ensure nested ObjectIds are correctly typed
        unitOfMeasure: ing.unitOfMeasure ? new Types.ObjectId(ing.unitOfMeasure) : null,
        ingredientId: ing.ingredientId ? new Types.ObjectId(ing.ingredientId) : null,
        recipesId: ing.recipesId ? new Types.ObjectId(ing.recipesId) : null,
      })),
      sellingDetails: body.sellingDetails?.map((opt: any) => ({ // Use optional chaining
          ...opt,
          sellingOptionId: opt.sellingOptionId ? new Types.ObjectId(opt.sellingOptionId) : undefined,
          // Remove _id if present, as it's managed by MongoDB in subdocuments
          _id: undefined
      }))
    };

    // Remove fields that shouldn't be directly updated or are derived
    delete updateData._id; // Prevent changing the _id
    delete updateData.companyId; // Prevent changing the companyId

    const updatedRecipe = await Recipe.findOneAndUpdate(
      { _id: id, companyId: tenantId },
      { $set: updateData },
      { new: true, runValidators: true }
    ).lean<IRecipe>();

    if (!updatedRecipe) {
      return NextResponse.json({ message: 'Recipe not found or update failed' }, { status: 404 });
    }

    // Repopulate UOMs for the response
    const { uoms, uomMap } = await populateUOMs(updatedRecipe);
    const baseUom = updatedRecipe.baseYieldUOM ? uomMap.get(updatedRecipe.baseYieldUOM.toString()) : undefined;

    // Prepare response similar to GET
    const responseRecipe = {
       _id: updatedRecipe._id.toString(),
      companyId: updatedRecipe.companyId.toString(),
      name: updatedRecipe.name,
      description: updatedRecipe.description,
      yield: updatedRecipe.yield,
      baseYieldUOMId: updatedRecipe.baseYieldUOM?.toString(),
      baseYieldUOM: baseUom ? { ...baseUom, _id: (baseUom._id as Types.ObjectId).toString() } : null,
      yieldUomShortCode: baseUom?.shortCode || '',
      createdAt: updatedRecipe.createdAt,
      updatedAt: updatedRecipe.updatedAt,
      recipeIngredients: (updatedRecipe.recipeIngredients || []).map((ingredient: RecipeIngredient) => {
        const ingredientUom = ingredient.unitOfMeasure ? uomMap.get(ingredient.unitOfMeasure.toString()) : undefined;
        if (!ingredientUom) return { ...ingredient, smallQuantity: null, largeQuantity: null, unit: null, displayUnits: null };

        const { small: smallUom, large: largeUom } = getDisplayUnits(uoms, baseUom as any); // TODO: Refactor getDisplayUnits or UOM fetching to handle IUOM vs UOM mismatch
        const smallQuantity = smallUom ? convertUOMValue(ingredient.quantity, ingredientUom as any, smallUom as any) : null; // TODO: Refactor convertUOMValue or UOM fetching to handle IUOM vs UOM mismatch
        const largeQuantity = largeUom ? convertUOMValue(ingredient.quantity, ingredientUom as any, largeUom as any) : null; // TODO: Refactor convertUOMValue or UOM fetching to handle IUOM vs UOM mismatch

        // Determine ingredient details (Simplified for PUT response, assuming IDs are sufficient)
        let ingredientDetails = null;
        if (ingredient.ingredientId) {
             ingredientDetails = { type: 'Ingredient', name: 'Ingredient', _id: ingredient.ingredientId?.toString() }; // Placeholder name
        } else if (ingredient.recipesId) {
            ingredientDetails = { type: 'Recipe', name: 'Sub-Recipe', _id: ingredient.recipesId?.toString() }; // Placeholder name
        }

        return {
          ...ingredient,
          ...ingredientDetails,
          ingredientId: ingredient.ingredientId?.toString(),
          recipesId: ingredient.recipesId?.toString(),
          unitOfMeasure: ingredient.unitOfMeasure?.toString(),
          unit: ingredientUom ? { ...ingredientUom, _id: (ingredientUom._id as Types.ObjectId).toString() } : null,
          displayUnits: {
            small: smallUom ? { ...smallUom, _id: (smallUom._id as Types.ObjectId).toString() } : null,
            large: largeUom ? { ...largeUom, _id: (largeUom._id as Types.ObjectId).toString() } : null,
          },
          smallQuantity,
          largeQuantity,
        };
      }),
      sellingDetails: (updatedRecipe.sellingDetails || []).map((option: any) => ({ // Add type and default array
        ...option,
        _id: option._id?.toString(),
        sellingOptionId: option.sellingOptionId?.toString(),
      })),
    };

    return NextResponse.json(responseRecipe);

  } catch (error: any) {
    console.error('Error updating recipe:', error);
    // Handle potential validation errors
    if (error.name === 'ValidationError') {
      return NextResponse.json({ message: 'Validation Error', errors: error.errors }, { status: 400 });
    }
    return NextResponse.json({ message: 'Internal Server Error', error: error.message }, { status: 500 });
  }
}


export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await dbConnect();
    const auth = await requireAuth(request);
    const tenantId = auth.tenantId;
    const { id } = params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid Recipe ID' }, { status: 400 });
    }

    const deletedRecipe = await Recipe.findOneAndDelete({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(tenantId)
    });

    if (!deletedRecipe) {
      return NextResponse.json({ message: 'Recipe not found or already deleted' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Recipe deleted successfully' });

  } catch (error: any) {
    console.error('Error deleting recipe:', error);
    return NextResponse.json({ message: 'Internal Server Error', error: error.message }, { status: 500 });
  }
}