import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import Recipe from '@/models/Recipe';
import { Ingredient } from '@/models/Ingredient';
// import { convertUOMValue } from '@/utils/uomConversion';
import { UOM } from '@/models/UOM';

// Default fallback values in case real costs aren't available
const DEFAULT_COST_PER_UNIT = 0.00;

// Helper function to extract ObjectId from various formats
function extractObjectId(id: unknown): string | null {
  if (!id) return null;
  
  // Case 1: Regular string ID
  if (typeof id === 'string' && Types.ObjectId.isValid(id)) {
    return id;
  }
  
  // Case 2: MongoDB ObjectId instance
  if (id instanceof Types.ObjectId) {
    return id.toString();
  }
  
  // Case 3: MongoDB export format with $oid
  if (typeof id === 'object' && id !== null && '$oid' in id && typeof id.$oid === 'string') {
    return id.$oid;
  }
  
  // Case 4: Plain object with toString method (like ObjectId)
  if (typeof id === 'object' && id !== null && typeof id.toString === 'function') {
    const idStr = id.toString();
    if (Types.ObjectId.isValid(idStr)) {
      return idStr;
    }
  }
  
  console.warn('Invalid ObjectId format:', id);
  return null;
}

interface CostBreakdown {
  ingredientId: string;
  name: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  isSubRecipe: boolean;
}

interface CostingResponse {
  totalCost: number;
  costPerUnit: number;
  costBreakdown: CostBreakdown[];
  yield: number;
  yieldUnit: string;
}

// Add a proper Recipe interface to fix TypeScript errors
interface RecipeDocument {
  _id: Types.ObjectId;
  name: string;
  companyId: Types.ObjectId;
  recipeIngredients: Array<{
    ingredientId?: Types.ObjectId | string | { $oid: string } | unknown;
    recipesId?: Types.ObjectId | string | { $oid: string } | unknown;
    ingredientName: string;
    isSubRecipe: boolean;
    quantity: number;
    unitOfMeasure?: {
      _id: Types.ObjectId;
      name: string;
      shortCode: string;
      factorToCanonical?: number;
    };
  }>;
  yield: number;
  baseYieldUOM?: {
    name: string;
    shortCode: string;
  };
}

// GET /api/recipes/[id]/costing
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const params = await context.params;
    const { id } = params;
    const companyId = request.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    if (!id || !Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Valid recipe ID is required' }, { status: 400 });
    }

    // Fetch the recipe with populated units
    const recipe = await Recipe.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(companyId)
    })
    .populate({
      path: 'baseYieldUOM',
      select: 'name shortCode'
    })
    .populate({
      path: 'recipeIngredients.unitOfMeasure',
      select: 'name shortCode factorToCanonical'
    })
    .lean() as unknown as RecipeDocument;

    if (!recipe) {
      return NextResponse.json({ error: 'Recipe not found' }, { status: 404 });
    }

    // Process each ingredient to get costs
    const costBreakdown: CostBreakdown[] = [];
    let totalCost = 0;

    // Fetch all UOMs once for conversion calculations
    const allUoms = await UOM.find().lean();
    // const _uomMap = new Map(allUoms.map(uom => [uom._id.toString(), uom]));

    // Ensure type safety when accessing recipe properties
    console.log('Recipe data:', {
      _id: recipe._id ? recipe._id.toString() : 'unknown',
      name: recipe.name || 'unnamed',
      ingredientCount: recipe.recipeIngredients?.length || 0
    });
    
    // Process each ingredient in the recipe
    for (const ingredient of recipe.recipeIngredients || []) {
      // Add each ingredient to the cost breakdown, even if we can't get actual cost data
      // This ensures all ingredients appear in the UI
      
      if (ingredient.isSubRecipe) {
        // Handle sub-recipe costing
        // Define variable outside try/catch scope so it's accessible in catch block
        let recipeIdSafe = '';
        try {
          let costPerUnit = DEFAULT_COST_PER_UNIT;
          const subRecipeName = ingredient.ingredientName;
          const recipeIdStr = extractObjectId(ingredient.recipesId);
          recipeIdSafe = recipeIdStr || '';
          
          if (recipeIdStr) {
            const subRecipeId = new Types.ObjectId(recipeIdStr);
              
            const subRecipe = await Recipe.findById(subRecipeId).lean();
            if (subRecipe) {
              // Try to get sub-recipe costing
              try {
                const subRecipeCostingUrl = new URL(`/api/recipes/${(subRecipe as any)._id}/costing`, request.url);
                const subRecipeCostingResponse = await fetch(subRecipeCostingUrl, {
                  headers: {
                    'company-id': companyId
                  }
                });
                
                if (subRecipeCostingResponse.ok) {
                  const subRecipeCosting = await subRecipeCostingResponse.json();
                  costPerUnit = subRecipeCosting.costPerUnit || DEFAULT_COST_PER_UNIT;
                }
              } catch (error: unknown) {
                console.error('Error getting sub-recipe cost:', error);
                // Continue with default cost
              }
            }
          }
          
          const totalIngredientCost = costPerUnit * (ingredient.quantity || 1);
          
          costBreakdown.push({
            ingredientId: recipeIdSafe,
            name: subRecipeName,
            quantity: ingredient.quantity || 1,
            unit: ingredient.unitOfMeasure?.shortCode || 'unit',
            unitCost: costPerUnit,
            totalCost: totalIngredientCost,
            isSubRecipe: true
          });
          
          totalCost += totalIngredientCost;
          
        } catch (error: unknown) {
          console.error(`Error processing sub-recipe ${ingredient.ingredientName}:`, error);
          // Add with default values so it at least appears in the UI
          costBreakdown.push({
            ingredientId: recipeIdSafe,
            name: ingredient.ingredientName,
            quantity: ingredient.quantity || 1,
            unit: ingredient.unitOfMeasure?.shortCode || 'unit',
            unitCost: DEFAULT_COST_PER_UNIT,
            totalCost: DEFAULT_COST_PER_UNIT * (ingredient.quantity || 1),
            isSubRecipe: true
          });
        }
      } else {
        // Handle regular ingredient costing
        // Define variable outside try/catch scope so it's accessible in catch block
        let ingredientIdSafe = '';
        try {
          let unitCost = DEFAULT_COST_PER_UNIT;
          const unitName = ingredient.unitOfMeasure?.shortCode || 'unit';
          
          // Get actual ingredient data if available
          const ingredientIdStr = extractObjectId(ingredient.ingredientId);
          ingredientIdSafe = ingredientIdStr || '';
          
          if (ingredientIdStr) {
            const ingredientId = new Types.ObjectId(ingredientIdStr);
            
            const ingredientData = await Ingredient.findById(ingredientId)
              .populate('supplierDetails.supplierId')
              .lean();
            
            if (ingredientData) {
              // Try to get actual costing data
              if (ingredientData.supplierDetails && ingredientData.supplierDetails.length > 0) {
                const supplier = ingredientData.supplierDetails[0];
                
                if (supplier.unitsOfOrdering && supplier.unitsOfOrdering.length > 0) {
                  const orderUnit = supplier.unitsOfOrdering[0];
                  if (orderUnit.pricePerBaseUom && orderUnit.pricePerBaseUom > 0) {
                    unitCost = orderUnit.pricePerBaseUom;
                  }
                }
              }
            }
          }
          
          // Even if we couldn't get real cost data, add the ingredient
          const quantity = ingredient.quantity || 1;
          const totalIngredientCost = unitCost * quantity;
          
          costBreakdown.push({
            ingredientId: ingredientIdSafe,
            name: ingredient.ingredientName,
            quantity: quantity,
            unit: unitName,
            unitCost: unitCost,
            totalCost: totalIngredientCost,
            isSubRecipe: false
          });
          
          totalCost += totalIngredientCost;
          
        } catch (error: unknown) {
          console.error(`Error processing ingredient ${ingredient.ingredientName}:`, error);
          // Add with default values so it at least appears in the UI
          costBreakdown.push({
            ingredientId: ingredientIdSafe,
            name: ingredient.ingredientName,
            quantity: ingredient.quantity || 1,
            unit: ingredient.unitOfMeasure?.shortCode || 'unit',
            unitCost: DEFAULT_COST_PER_UNIT,
            totalCost: DEFAULT_COST_PER_UNIT * (ingredient.quantity || 1),
            isSubRecipe: false
          });
        }
      }
    }

    // Calculate cost per unit based on yield
    const costPerUnit = recipe.yield ? totalCost / recipe.yield : 0;

    const response: CostingResponse = {
      totalCost,
      costPerUnit,
      costBreakdown,
      yield: typeof recipe.yield === 'number' ? recipe.yield : 0,
      yieldUnit: recipe.baseYieldUOM?.shortCode || 'unit'
    };

    console.log('Final costing response:', {
      totalCost,
      costPerUnit,
      breakdownCount: costBreakdown.length,
      yieldInfo: `${typeof recipe.yield === 'number' ? recipe.yield : 0} ${recipe.baseYieldUOM?.shortCode || 'unit'}`
    });

    return NextResponse.json(response);
  } catch (error: unknown) {
    console.error('[GET Recipe Costing] Error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to calculate recipe costing' },
      { status: 500 }
    );
  }
}
