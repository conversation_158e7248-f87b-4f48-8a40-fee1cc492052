import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import CurrencyTax from '@/models/CurrencyTax';
import { Types } from 'mongoose';

export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    const companyId = req.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const currencyTax = await CurrencyTax.findOne({ 
      companyId: new Types.ObjectId(companyId)
    });

    if (!currencyTax) {
      // If no tax settings exist, return default settings
      return NextResponse.json({
        companyId: new Types.ObjectId(companyId),
        baseCurrency: 'USD',
        vatOnSales: true,
        vatOnPurchases: true,
        vatRateOnSales: 20,
        vatRateOnPurchases: 20,
        defaultVatCategory: 'STANDARD'
      });
    }

    return NextResponse.json(currencyTax);
  } catch (error: unknown) {
    console.error('Error in GET /api/currency-taxes:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
