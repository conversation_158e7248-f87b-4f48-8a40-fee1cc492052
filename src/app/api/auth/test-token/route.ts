import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import TestApiKey from '@/models/TestApiKey';
import { Types } from 'mongoose';
import jwt from 'jsonwebtoken';
import User from '@/models/User';

/**
 * Endpoint to get a temporary JWT token using an API key (for testing only)
 * This enables Postman testing without changing the main auth flow
 */
export async function POST(request: NextRequest) {
  // Only available in non-production environments
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    await dbConnect();
    let body;
    try {
      body = await request.json();
    } catch (_error: unknown) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }
    
    const { companyId } = body;
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }
    
    // Validate companyId format
    if (!Types.ObjectId.isValid(companyId)) {
      return NextResponse.json({ 
        error: 'Invalid company ID format. Must be a 24 character hex string.',
        debug: { receivedCompanyId: companyId }
      }, { status: 400 });
    }
    
    // Get API key from header
    const apiKey = request.headers.get('x-test-api-key');
    if (!apiKey) {
      return NextResponse.json({ error: 'API key is required' }, { status: 401 });
    }
    
    // Find valid API key
    const apiKeyDoc = await TestApiKey.findOne({
      key: apiKey,
      companyId: new Types.ObjectId(companyId),
      expiresAt: { $gt: new Date() }
    });
    
    if (!apiKeyDoc) {
      return NextResponse.json({ error: 'Invalid or expired API key' }, { status: 401 });
    }
    
    // Get user details for the token
    const user = await User.findById(apiKeyDoc.userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Generate a short-lived JWT token (expires in 1 hour)
    const token = jwt.sign(
      {
        id: user._id.toString(),      // Changed from userId to id for consistency
        email: user.email,
        role: apiKeyDoc.role,        // Changed from userRole to role for consistency
        companyId: companyId,        // Added missing companyId 
        userType: 'company_user',
      },
      process.env.JWT_SECRET || 'dev-secret',
      { expiresIn: '1h' }
    );
    
    return NextResponse.json({ 
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: apiKeyDoc.role
      }
    });
  } catch (error: any) {
    console.error('Test token generation error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
