import { NextRequest, NextResponse } from 'next/server';
import { hash } from 'bcryptjs';
import connectToDatabase from '@/lib/mongoDb';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Missing credentials' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({ error: 'User already exists' }, { status: 400 });
    }

    // Hash password
    const passwordHash = await hash(password, 10);

    // Create new user
    const user = new User({
      email,
      passwordHash,
      userType: 'company_user', // Default to company user
      createdAt: new Date(),
    });

    await user.save();

    return NextResponse.json({
      status: 'success',
      user: {
        _id: user._id,
        email: user.email,
        userType: user.userType,
        createdAt: user.createdAt
      }
    });
  } catch (error: unknown) {
    console.error('Sign up error:', error);
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
}
