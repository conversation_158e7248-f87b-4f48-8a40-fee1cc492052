import { NextRequest, NextResponse } from 'next/server';
import { getCompanyBySubdomain } from '@/lib/services/companyService';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const subdomain = searchParams.get('subdomain');

    if (!subdomain) {
      return NextResponse.json({ error: 'Subdomain is required' }, { status: 400 });
    }

    const company = await getCompanyBySubdomain(subdomain);

    if (!company) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
    }

    return NextResponse.json({ company });
  } catch (error: unknown) {
    console.error('[verify-tenant] Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
