// src/app/api/auth/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { signIn, signUp, verifyToken } from '@/lib/authUtils';

// POST /api/auth
export async function POST(req: NextRequest) {
  try {
    const { email, password, userType, companyId, action } = await req.json();
    if (action === 'signup') {
      const user = await signUp(email, password, userType, companyId);
      // Do not return passwordHash or sensitive info
      const { passwordHash: _passwordHash, ...userSafe } = user.toObject ? user.toObject() : user;
      return NextResponse.json(userSafe, { status: 201 });
    } else if (action === 'signin') {
      const token = await signIn(email, password);
      return NextResponse.json({ token }, { status: 200 });
    }
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error: any) {
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Auth error' }, { status: 401 });
  }
}

// GET /api/auth
export async function GET(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.split(' ')[1] || '';
    const user = await verifyToken(token);
    if (user) {
      return NextResponse.json({ user }, { status: 200 });
    }
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  } catch (error: any) {
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Token verification error' }, { status: 401 });
  }
}
