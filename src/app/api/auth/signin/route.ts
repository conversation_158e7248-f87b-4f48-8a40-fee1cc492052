import { NextRequest, NextResponse } from 'next/server';
import { sign } from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import dbConnect from '@/lib/db';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    await dbConnect();
    const user = await User.findOne({ email });

    if (!user) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    // Create session token
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT secret not configured');
    }

    const sessionToken = sign(
      { 
        id: user._id,
        userType: user.userType,
        role: user.role,
        companyId: user.companyId,
        permissions: user.permissions || [],
      }, 
      secret, 
      { expiresIn: '24h' }
    );

    // Set session cookie
    // Set session cookie using NextResponse
    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Return user data based on type
    let response;
    if (user.userType === 'company_user') {
      response = NextResponse.json({
        token: sessionToken, // Include token for Ionic app
        user: {
          uid: user._id,
          email: user.email,
          userType: 'company_user',
          companyId: user.companyId,
          role: user.role,
          permissions: user.permissions,
          lastLogin: user.lastLogin,
          createdAt: user.createdAt
        }
      });
    } else {
      response = NextResponse.json({
        token: sessionToken, // Include token for Ionic app
        user: {
          uid: user._id,
          email: user.email,
          userType: 'superuser',
          lastLogin: user.lastLogin,
          createdAt: user.createdAt
        }
      });
    }
    response.cookies.set('auth-token', sessionToken, {
      httpOnly: true,
      secure: String(process.env.NODE_ENV) === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
    });
    return response;
  } catch (error: unknown) {
    console.error('Sign in error:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Internal server error' }, { status: 500 });
  }
}
