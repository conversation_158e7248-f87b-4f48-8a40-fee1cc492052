import { NextRequest, NextResponse } from 'next/server';

export async function POST(_request: NextRequest) {
  try {
    const response = NextResponse.json({ status: 'success' });
    
    // Clear the session cookie
    response.cookies.delete('auth-token'); // Changed from 'session' to 'auth-token' for consistency
    
    return response;
  } catch (error: unknown) {
    console.error('Sign out error:', error);
    return NextResponse.json({ error: 'Failed to sign out' }, { status: 500 });
  }
}
