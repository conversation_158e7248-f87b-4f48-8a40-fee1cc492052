// src/app/api/auth/session/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { sign } from 'jsonwebtoken';
import { compare } from 'bcryptjs';
import connectToDatabase from '@/lib/mongoDb';
import User from '@/models/User';

const EXPIRES_IN = 60 * 60 * 24 * 5 * 1000; // 5 days

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Missing credentials' }, { status: 400 });
    }

    // Connect to database and find user
    await connectToDatabase();
    const user = await User.findOne({ email });

    if (!user) {
      console.log('[Session API] User not found');
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    // Verify password
    const isValidPassword = await compare(password, user.passwordHash);
    if (!isValidPassword) {
      console.log('[Session API] Invalid password');
      return NextResponse.json({ error: 'Invalid password' }, { status: 401 });
    }

    // Create JWT token
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT secret not configured');
    }

    const token = sign(
      {
        sub: user._id.toString(),           // Standard JWT subject claim
        tenant_id: user.companyId.toString(), // Standard tenant claim
        email: user.email,
        role: user.role,
        userType: user.userType,
        // Legacy fields for backward compatibility
        id: user._id,
        companyId: user.companyId
      },
      secret,
      { expiresIn: '5d' }
    );

    // **Log the token**
    console.log('[Session API] JWT created:', token);

    // Set cookie in response
    const response = NextResponse.json({ status: 'success' });
    const host = request.headers.get('host') || '';
    const isLocalhost = host.includes('localhost');

    // **Log cookie settings**
    console.log('[Session API] Setting auth-token cookie with options:', {
      token,
      maxAge: EXPIRES_IN,
      domain: isLocalhost ? '.localhost' : undefined, // This allows sharing across subdomains on localhost
    });
    
    response.cookies.set('auth-token', token, {
      maxAge: EXPIRES_IN,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      domain: undefined, // Avoid specifying the domain during local development
    });

    return response;
  } catch (error: unknown) {
    console.error('Session creation error:', error);
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
}
