import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import jwt from 'jsonwebtoken';

/**
 * ADMIN-ONLY ENDPOINT - TEMPORARY USE
 * This endpoint generates a properly formatted JWT token for admin users
 * It should be removed after initial setup
 */
export async function POST(request: NextRequest) {
  // Only allow in non-production environments
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    await dbConnect();
    const { email, companyId } = await request.json();
    
    if (!email || !companyId) {
      return NextResponse.json({ error: 'Email and companyId are required' }, { status: 400 });
    }
    
    // Find the user by email and company
    const user = await User.findOne({ 
      email,
      companyId
    });
    
    if (!user) {
      return NextResponse.json({ 
        error: 'User not found',
        debug: { email, companyId }
      }, { status: 404 });
    }
    
    // Verify the user has required role
    if (user.role !== 'admin' && user.role !== 'owner') {
      return NextResponse.json({ 
        error: 'User does not have admin or owner role',
        debug: { userRole: user.role }
      }, { status: 403 });
    }
    
    // Generate JWT token with correct fields
    const token = jwt.sign(
      {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        companyId: user.companyId.toString(),
        userType: 'company_user',
      },
      process.env.JWT_SECRET || 'dev-secret',
      { expiresIn: '5d' }
    );
    
    // Set the auth-token cookie
    const response = NextResponse.json({ 
      success: true,
      message: 'Authentication token created successfully',
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        companyId: user.companyId.toString()
      }
    });
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: String(process.env.NODE_ENV) === 'production',
      sameSite: 'lax',
      maxAge: 5 * 24 * 60 * 60, // 5 days
      path: '/',
    });
    return response;
  } catch (error: unknown) {
    console.error('Admin token generation error:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Internal server error' }, { status: 500 });
  }
}
