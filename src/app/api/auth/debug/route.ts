import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';

interface JwtPayload {
  id: string;
  email: string;
  userType: 'superuser' | 'company_user';
  companyId?: string;
  role?: 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper';
}

export async function GET(request: NextRequest) {
  try {
    // Get the token from cookies or Authorization header
    let token = request.cookies.get('auth-token')?.value;
    
    // If no cookie token found, check Authorization header
    if (!token) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }
    
    if (!token) {
      return NextResponse.json({ 
        status: 'error', 
        message: 'No token found',
        cookies: Array.from(request.cookies.getAll()).map(c => c.name),
        headers: Object.fromEntries([...request.headers.entries()].filter(([k]) => !k.includes('auth')))
      });
    }
    
    // Verify the token
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET is not defined');
    }
    
    const payload = verify(token, secret) as JwtPayload;
    
    return NextResponse.json({
      status: 'success',
      user: {
        id: payload.id,
        email: payload.email,
        userType: payload.userType,
        companyId: payload.companyId,
        role: payload.role
      },
      headers: {
        'company-id': request.headers.get('company-id')
      }
    });
  } catch (error: any) {
    return NextResponse.json({ 
      status: 'error', 
      message: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
