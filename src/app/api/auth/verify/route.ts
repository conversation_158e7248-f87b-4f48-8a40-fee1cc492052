// src/app/api/auth/verify/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    // Use requireAuth to verify the session
    const auth = await requireAuth()(request);

    if (!auth) {
      // requireAuth already handles logging and returns null/throws on failure
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to DB to update lastLogin (requireAuth doesn't handle this)
    await dbConnect();
    try {
      await User.findByIdAndUpdate(auth.id, { $set: { lastLogin: new Date() } });
    } catch (dbError) {
      console.error('[Verify API] Failed to update lastLogin:', dbError);
      // Non-critical error, proceed with returning auth info
    }

    // Return standardized user data from AuthUser
    // Note: Permissions might need re-fetching if not included/up-to-date in token
    const userData = {
      uid: auth.id,
      email: auth.email,
      userType: auth.metadata?.userType || 'unknown', // Get from metadata if available
      companyId: auth.tenantId, 
      role: auth.role,
      permissions: auth.metadata?.permissions || [], // Get from metadata if available
      // lastLogin and createdAt are not part of AuthUser, fetch if needed or omit
    };

    return NextResponse.json(userData);

  } catch (error: unknown) {
    // Catch errors potentially thrown by requireAuth or other issues
    console.error('[Verify API] Verify session error:', error);
    // Return a generic error to avoid leaking details
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
}
