import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/mongoDb';
import Supplier from '@/models/Supplier';
import { Types } from 'mongoose';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { id } = await params;
    const companyId = new Types.ObjectId(auth.tenantId);
    await dbConnect();
    const supplier = await Supplier.findOne({
      _id: new Types.ObjectId(id),
      companyId,
    });

    if (!supplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(supplier);
  } catch (error: unknown) {
    console.error('[GET Supplier] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json({ error: 'Failed to fetch supplier' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { id } = await params;
    const companyId = new Types.ObjectId(auth.tenantId);
    await dbConnect();
    const data = await request.json();
    
    const supplier = await Supplier.findOneAndUpdate(
      {
        _id: new Types.ObjectId(id),
        companyId,
      },
      {
        $set: {
          ...data,
          updatedAt: new Date(),
        },
      },
      { new: true }
    );

    if (!supplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(supplier);
  } catch (error: unknown) {
    console.error('[PUT Supplier] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json({ error: 'Failed to update supplier' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { id } = await params;
    const companyId = new Types.ObjectId(auth.tenantId);
    await dbConnect();

    const supplier = await Supplier.findOneAndDelete({
      _id: new Types.ObjectId(id),
      companyId,
    });

    if (!supplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Supplier successfully deleted',
      deletedId: id,
    });
  } catch (error: unknown) {
    console.error('[DELETE Supplier] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json({ error: 'Failed to delete supplier' }, { status: 500 });
  }
}
