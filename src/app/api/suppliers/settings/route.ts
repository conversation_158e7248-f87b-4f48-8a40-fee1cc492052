import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/mongoDb';
import SupplierSettings from '@/models/SupplierSettings';
import { Types } from 'mongoose';

export async function GET(request: NextRequest) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = new Types.ObjectId(auth.tenantId);
    await dbConnect();

    let settings = await SupplierSettings.findOne({ companyId });
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = await SupplierSettings.create({
        companyId,
        requiredFields: {
          taxNumber: false,
          address: false,
          phone: false,
          email: false,
          paymentTerms: false,
        }
      });
    }
    
    return NextResponse.json(settings);
  } catch (error: unknown) {
    console.error('[GET Supplier Settings] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json({ error: 'Failed to fetch supplier settings' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = new Types.ObjectId(auth.tenantId);
    await dbConnect();

    const data = await request.json();
    
    const settings = await SupplierSettings.findOneAndUpdate(
      { companyId },
      { 
        $set: {
          requiredFields: data.requiredFields,
          updatedAt: new Date()
        }
      },
      { new: true, upsert: true }
    );

    return NextResponse.json(settings);
  } catch (error: unknown) {
    console.error('[PUT Supplier Settings] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json({ error: 'Failed to update supplier settings' }, { status: 500 });
  }
}
