import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/mongoDb';
import Supplier from '@/models/Supplier';
import { Types } from 'mongoose';

export async function GET(request: NextRequest) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = new Types.ObjectId(auth.tenantId);

    await dbConnect();

    const suppliers = await Supplier.find({ companyId });
    return NextResponse.json(suppliers);
  } catch (error: unknown) {
    console.error('[GET Suppliers] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json(
      { error: 'Failed to fetch suppliers' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = new Types.ObjectId(auth.tenantId);

    await dbConnect();

    const data = await request.json();
    const supplier = await Supplier.create({
      ...data,
      companyId,
    });

    return NextResponse.json(supplier, { status: 201 });
  } catch (error: unknown) {
    console.error('[POST Supplier] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json(
      { error: 'Failed to create supplier' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const auth = await requireAuth()(request);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = new Types.ObjectId(auth.tenantId);

    await dbConnect();

    const { ids } = await request.json();
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'At least one supplier ID is required' },
        { status: 400 }
      );
    }

    const objectIds = ids.map(id => new Types.ObjectId(id));

    const result = await Supplier.deleteMany({
      _id: { $in: objectIds },
      companyId,
    });

    return NextResponse.json({
      message: `Successfully deleted ${result.deletedCount} suppliers for company ${auth.tenantId}.`,
      deletedCount: result.deletedCount,
    });
  } catch (error: unknown) {
    console.error('[DELETE Suppliers] Error:', error);
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json(
      { error: 'Failed to delete suppliers' },
      { status: 500 }
    );
  }
}