import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose, { Types, Model } from 'mongoose';
import Order, { IOrder } from '@/models/Order';
import Location from '@/models/Location';
import { requireAuth } from '@/lib/auth-helpers';
import { Ingredient, IIngredient } from '@/models/Ingredient';
import Recipe, { IRecipe } from '@/models/Recipe';
import UOM from '@/models/UOM';
import Customer from '@/models/Customer';
import OrderSync from '@/models/OrderSync';
import { getToken } from 'next-auth/jwt';

try {
  mongoose.model('Order');
} catch (error) {
  mongoose.model('Order', Order.schema);
}
try {
  mongoose.model('Location');
} catch (error) {
  mongoose.model('Location', Location.schema);
}
try {
  mongoose.model('Ingredient');
} catch (error) {
  mongoose.model('Ingredient', Ingredient.schema);
}
try {
  mongoose.model('Recipe');
} catch (error) {
  mongoose.model('Recipe', Recipe.schema);
}
try {
  mongoose.model('UOM');
} catch (error) {
  mongoose.model('UOM', UOM.schema);
}
try {
  mongoose.model('Customer');
} catch (error) {
  mongoose.model('Customer', Customer.schema);
}
try {
  mongoose.model('OrderSync');
} catch (error) {
  mongoose.model('OrderSync', OrderSync.schema);
}

type PopulatedOrderItem = Record<string, unknown>

// GET /api/orders
export async function GET(req: NextRequest) {
  const startTime = Date.now(); // Alternative timer start
  const timerLabel = `[GET /api/orders] Request ID: ${Math.random().toString(36).substring(2, 9)}`;
  console.time(timerLabel); // Start timer with a unique label
  try {
    await dbConnect();
    const auth = await requireAuth(req);
    console.log('>>> GET /api/orders - Auth object received:', JSON.stringify(auth));

    const tenantId = auth.tenantId;

    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const statusFilter = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const createdAtGte = searchParams.get('createdAt[gte]');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const tenantObjectId = new Types.ObjectId(tenantId);

    const query: Record<string, unknown> = { companyId: tenantObjectId };
    
    if (statusFilter !== 'all') {
      query.status = statusFilter;
    }
    
    if (createdAtGte) {
      query.createdAt = { $gte: new Date(createdAtGte) };
    }
    
    if (search) {
      query.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'items.description': { $regex: search, $options: 'i' } }
      ];
    }

    console.log('>>> GET /api/orders - Calling countDocuments...');
    const total = await Order.countDocuments(query);
    console.log('>>> GET /api/orders - countDocuments returned:', total);

    console.log('>>> GET /api/orders - Calling find...');
    // Determine sort option based on parameters
    const sortOption: Record<string, 1 | -1> = {};
    sortOption[sortBy] = sortOrder === 'asc' ? 1 : -1;
    
    console.log('>>> GET /api/orders - Query:', JSON.stringify(query), 'Sort:', JSON.stringify(sortOption)); // Debug log
    const orders = await Order.find(query)
      .populate({
        path: 'sellerLocationId',
        select: 'name',
        model: Location,
        match: { companyId: tenantObjectId }
      })
      .populate({
        path: 'items.itemId',
        select: 'name description',
        model: Ingredient,
        match: { companyId: tenantObjectId }
      })
      .populate({
        path: 'items.uomId',
        select: 'name shortCode',
        model: UOM,
        match: { companyId: tenantObjectId }
      })
      .populate({
        path: 'buyer.buyerId',
        select: 'name email',
        model: Customer,
        match: { companyId: tenantObjectId }
      })
      .sort(sortOption)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean(); // Use lean() for better performance
    console.log('>>> GET /api/orders - find returned, count:', orders.length);

    console.log(`Returning ${orders.length} lean orders`); // Log lean orders count

    const totalPages = Math.ceil(total / limit);
    
    // Transform orders to include properly named populated fields
    const transformedOrders = orders.map(order => {
      const transformed: any = { ...order };
      
      // Map sellerLocationId to sellerLocation
      if (order.sellerLocationId) {
        transformed.sellerLocation = order.sellerLocationId;
      }
      
      // Map buyer information
      if (order.buyer) {
        // Keep original buyer information
        transformed.buyer = { ...order.buyer };
        
        // If there's a buyerId and it's a populated field with a name, add it to buyer
        if (order.buyer.buyerId && 
            typeof order.buyer.buyerId === 'object' && 
            (order.buyer.buyerId as any).name) {
          transformed.buyer.name = (order.buyer.buyerId as any).name;
        }
      }
      
      return transformed;
    });

    return NextResponse.json({
      success: true,
      orders: transformedOrders,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/orders:', error);

    // Check for Authentication error first
    if (error instanceof Error && /authentication required|unauthorized/i.test(error.message)) {
      console.warn('>>> GET /api/orders - Authentication Error:', error.message);
      return NextResponse.json({ success: false, message: error.message || 'Authentication required.' }, { status: 401 });
    }

    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json({ success: false, message: 'Invalid ID format provided.', details: { field: error.path, value: error.value } }, { status: 400 });
    }
    
    // Generic error
    let errorMessage = 'Internal Server Error';
    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('>>> GET /api/orders - Caught Error Message:', error.message);
      console.error('>>> GET /api/orders - Caught Error Stack:', error.stack);
    } else {
      console.error('>>> GET /api/orders - Caught non-Error object:', error);
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  } finally {
    console.timeEnd(timerLabel); // End timer
    // console.log(`[GET /api/orders] Request processed in ${Date.now() - startTime}ms`); // Alternative timer end log
  }
}

// POST /api/orders
export async function POST(req: NextRequest) {
  const startTime = Date.now(); // Alternative timer start
  const timerLabel = `[POST /api/orders] Request ID: ${Math.random().toString(36).substring(2, 9)}`;
  console.time(timerLabel); // Start timer with a unique label
  try {
    await dbConnect();
    const auth = await requireAuth(req);

    const tenantId = auth.tenantId;
    const userId = auth.id; // Use auth.id for user identifier

    const data = await req.json();
    console.log('>>> POST /api/orders - Request data:', JSON.stringify(data));

    // Calculate line totals if items are present and quantity/unitPrice are valid
    const itemsWithLineTotals = (data.items || []).map((item: any) => ({
      ...item,
      lineTotal: item.quantity && item.unitPrice ? item.quantity * item.unitPrice : 0,
    }));

    // Add companyId (using tenantId) and audit fields to the data
    const newOrderData = {
      ...data,
      companyId: tenantId,    // Use tenantId from auth object
      createdBy: userId,      // Use userId (from auth.id)
      modifiedBy: userId,     // Use userId (from auth.id)
      items: itemsWithLineTotals, // Use items with calculated line totals
    };

    console.log('>>> POST /api/orders - Processed data for creation:', JSON.stringify(newOrderData));

    const newOrder = new Order(newOrderData);
    await newOrder.save();
    console.log('>>> POST /api/orders - Order created successfully:', newOrder._id);

    // Restore the success response - Use 'order' key as expected by tests
    return NextResponse.json({ success: true, message: 'Order created successfully.', order: newOrder }, { status: 201 });
    
  } catch (error: unknown) {
    console.error('Error in POST /api/orders:', error);

    // Check for Authentication error first
    if (error instanceof Error && /authentication required|unauthorized/i.test(error.message)) {
      console.warn('>>> POST /api/orders - Authentication Error:', error.message);
      return NextResponse.json({ success: false, message: error.message || 'Authentication required.' }, { status: 401 });
    }

    let errorMessage = 'An unknown error occurred while processing the request.';

    // Specific Mongoose Error Handling - Updated check
    if ((error instanceof mongoose.Error.ValidationError) || (error instanceof Error && error.name === 'ValidationError')) {
      // Cast to access Mongoose-specific properties
      const validationError = error as mongoose.Error.ValidationError;
      const errors = Object.values(validationError.errors).map(el => ({ field: el.path, message: el.message }));
      console.warn('>>> POST /api/orders - Validation Error:', JSON.stringify(errors));
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed.', 
          details: errors 
        }, 
        { status: 400 }
      );
    }

    if (error instanceof mongoose.Error.CastError) {
      console.warn(`>>> POST /api/orders - Cast Error: Path=${error.path}, Value=${error.value}`);
      return NextResponse.json({ success: false, message: 'Invalid ID format provided.', details: { field: error.path, value: error.value } }, { status: 400 });
    }

    // Check for MongoDB duplicate key error (code 11000)
    if (error instanceof Error && 'code' in error && (error as any).code === 11000) {
      const mongoError = error as any; // Type assertion
      const field = Object.keys(mongoError.keyPattern)[0]; // Get the field that caused the duplicate error
      console.error(`>>> POST /api/orders - Duplicate Key Error (Code 11000) on field: ${field}`);
      return NextResponse.json({ success: false, message: `Duplicate value error: An order with this ${field} already exists.` }, { status: 409 });
    }

    // Generic error
    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('>>> POST /api/orders - Caught Error Message:', error.message);
      console.error('>>> POST /api/orders - Caught Error Stack:', error.stack);
    } else {
      console.error('>>> POST /api/orders - Caught non-Error object:', error);
    }
    return NextResponse.json(
      { 
        success: false, 
        message: 'An unexpected error occurred on the server.',
        // Optionally include details in non-production environments
        details: process.env.NODE_ENV !== 'production' ? errorMessage : undefined 
      }, 
      { status: 500 }
    );
  } finally {
    console.timeEnd(timerLabel); // End timer
    // console.log(`[POST /api/orders] Request processed in ${Date.now() - startTime}ms`); // Alternative timer end log
  }
}
