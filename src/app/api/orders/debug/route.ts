import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Order from '@/models/Order';
import { Types } from 'mongoose';

// GET /api/orders/debug
export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    const companyId = req.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    // Get all orders for the company
    const orders = await Order.find({ 
      companyId: new Types.ObjectId(companyId) 
    });

    // Return detailed debug info
    return NextResponse.json({
      totalOrders: orders.length,
      orders: orders.map(order => ({
        _id: order._id,
        orderNumber: order.orderNumber,
        status: order.status,
        createdAt: order.createdAt,
        companyId: order.companyId
      })),
      query: { companyId: new Types.ObjectId(companyId) }
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/orders/debug:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
