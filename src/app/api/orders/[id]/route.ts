import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Order, { IOrder } from '@/models/Order';
import Location from '@/models/Location';
import UOM from '@/models/UOM';
import { Ingredient } from '@/models/Ingredient';
import Recipe from '@/models/Recipe';
import Customer from '@/models/Customer';
import mongoose, { Types } from 'mongoose';
import { MongoServerError } from 'mongodb';
import { requireAuth } from '@/lib/auth-helpers'; // Import requireAuth

// Define a type combining the base order item structure and populated fields using intersection
type ProcessedOrderItem = IOrder['items'][number] & {
  itemName?: string;
  itemDescription?: string;
  uomName?: string;
  uomShortCode?: string;
};

// GET /api/orders/[id]
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req); // Add auth check
    const { id } = await context.params;
    
    if (!auth.tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required from authentication' }, { status: 400 });
    }

    if (!id || !Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid order ID' }, { status: 400 });
    }

    const order = await Order.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(auth.tenantId),
    })
    .populate({
      path: 'sellerLocationId',
      select: 'name',
      model: Location,
      match: { companyId: new Types.ObjectId(auth.tenantId) }
    })
    .populate({
      path: 'items.uomId',
      select: 'name shortCode',
      model: UOM,
      match: { companyId: new Types.ObjectId(auth.tenantId) }
    });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Populate buyer information based on buyerType
    if (order.buyer) {
      const buyerModel = order.buyer.buyerType === 'CUSTOMER' ? Customer : Location;
      const buyer = await buyerModel.findOne({
        _id: order.buyer.buyerId,
        companyId: new Types.ObjectId(auth.tenantId)
      }).select('name').lean();

      if (buyer) {
        order.buyer = {
          ...order.buyer,
          buyerName: (buyer as unknown as { name: string }).name
        };
      }
    }

    // Manually populate items.itemId based on itemType
    if (order.items) {
      // Explicitly type the result of the map using the corrected type alias
      const populatedItems: ProcessedOrderItem[] = await Promise.all(order.items.map(async (item: IOrder['items'][number]): Promise<ProcessedOrderItem> => {
        // If item.itemId is already populated (object), use its _id
        const itemIdString = typeof item.itemId === 'string' ? item.itemId : item.itemId?._id?.toString();

        // Handle cases where item.itemId might be missing or invalid
        if (!itemIdString) {
            console.warn('Item missing itemId:', item);
            // Return the base item structure, ensuring it conforms to ProcessedOrderItem
            return { ...item, itemName: 'Unknown Item', itemDescription: 'Missing ID' }; 
        }

        try {
          if (item.itemType === 'INGREDIENT') {
            const ingredient = await (Ingredient.findOne({
              _id: new Types.ObjectId(itemIdString),
              companyId: new Types.ObjectId(auth.tenantId)
            })
            .select('name description baseUomId')
            .populate('baseUomId', 'name shortCode')
            .lean());

            if (!ingredient) return item;

            return {
              ...item,
              itemName: ingredient.name,
              itemDescription: ingredient.description,
              // Reinstate type assertion for populated lean object
              uomName: (ingredient.baseUomId as unknown as { name: string; shortCode: string })?.name,
              uomShortCode: (ingredient.baseUomId as unknown as { name: string; shortCode: string })?.shortCode
            }; 
          } else {
            const recipe = await (Recipe.findOne({
              _id: new Types.ObjectId(itemIdString),
              companyId: new Types.ObjectId(auth.tenantId)
            })
            .select('name description baseYieldUomId')
            .populate('baseYieldUomId', 'name shortCode')
            .lean());

            if (!recipe) return item;

            // Define expected type for recipe after lean and populate
            type PopulatedRecipe = { name: string; description: string; baseYieldUomId?: { name: string; shortCode: string } };
            const typedRecipe = recipe as unknown as PopulatedRecipe;

            return {
              ...item,
              itemName: typedRecipe.name,
              itemDescription: typedRecipe.description,
              // Reinstate type assertion and add optional chaining
              uomName: typedRecipe.baseYieldUomId?.name,
              uomShortCode: typedRecipe.baseYieldUomId?.shortCode
            }; 
          }
        } catch (error: unknown) {
          console.error('Error populating item:', error);
          // Return the base item structure on error, ensuring it conforms
          return { ...item, itemName: 'Error Populating', itemDescription: 'Error' };
        }
      }));

      // Assign the populated items back
      order.items = populatedItems;
    }

    return NextResponse.json({ message: 'Order fetched successfully', order });
  } catch (error: unknown) {
    console.error('Error in GET /api/orders/[id]:', error);
    if (error instanceof Error && /authentication required|unauthorized/i.test(error.message)) {
      console.warn(`>>> GET /api/orders/[id] - Authentication Error:`, error.message);
      return NextResponse.json({ success: false, message: error.message || 'Authentication required.' }, { status: 401 });
    }
    // Specific Mongoose Error Handling
    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json({ success: false, message: 'Invalid ID format provided.', details: { field: error.path, value: error.value } }, { status: 400 });
    }
    // Generic Error Handling
    let errorMessage = 'An unexpected error occurred while fetching the order.';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}

// PUT /api/orders/[id]
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req); // Add auth check
    const { id } = await context.params;

    if (!auth.tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required from authentication' }, { status: 400 });
    }

    if (!id || !Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid order ID format' }, { status: 400 });
    }

    const data = await req.json();

    // Calculate line totals if not provided
    if (data.items) {
      // Type the input item correctly and ensure the return type matches
      data.items = data.items.map((item: IOrder['items'][number]) => {
        const canCalculate = typeof item.quantity === 'number' && typeof item.unitPrice === 'number';
        const calculatedLineTotal = canCalculate ? item.quantity * item.unitPrice : undefined;

        return {
          ...item,
          // Use provided lineTotal, else calculated (if possible), else keep original/undefined
          lineTotal: item.lineTotal ?? calculatedLineTotal ?? item.lineTotal
        };
      });
    }

    const order = await Order.findOneAndUpdate(
      {
        _id: new Types.ObjectId(id),
        companyId: new Types.ObjectId(auth.tenantId)
      },
      { $set: { ...data, modifiedBy: auth.id } }, // Add modifiedBy
      { new: true, runValidators: true }
    )
      .populate({
        path: 'sellerLocationId',
        select: 'name',
        model: Location,
        match: { companyId: new Types.ObjectId(auth.tenantId) }
      })
      .populate({
        path: 'items.uomId',
        select: 'name shortCode',
        model: UOM,
        match: { companyId: new Types.ObjectId(auth.tenantId) }
      });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Manually populate items.itemId based on itemType AFTER update
    if (order.items) {
      // Use the same ProcessedOrderItem type and logic as GET
      const populatedItems: ProcessedOrderItem[] = await Promise.all(
        order.items.map(async (item: IOrder['items'][number]): Promise<ProcessedOrderItem> => {
          const itemIdString = typeof item.itemId === 'string' ? item.itemId : item.itemId?._id?.toString();

          if (!itemIdString) {
            console.warn('Item missing itemId during PUT population:', item);
            return { ...item, itemName: 'Unknown Item', itemDescription: 'Missing ID' };
          }

          try {
            if (item.itemType === 'INGREDIENT') {
              const ingredient = await (Ingredient.findOne({
                _id: new Types.ObjectId(itemIdString),
                companyId: new Types.ObjectId(auth.tenantId)
              })
              .select('name description baseUomId')
              .populate('baseUomId', 'name shortCode')
              .lean());

              if (!ingredient) return item;

              return {
                ...item,
                itemName: ingredient.name,
                itemDescription: ingredient.description,
                // Reinstate type assertion for populated lean object
                uomName: (ingredient.baseUomId as unknown as { name: string; shortCode: string })?.name,
                uomShortCode: (ingredient.baseUomId as unknown as { name: string; shortCode: string })?.shortCode
              };
            } else {
              const recipe = await (Recipe.findOne({
                _id: new Types.ObjectId(itemIdString),
                companyId: new Types.ObjectId(auth.tenantId)
              })
              .select('name description baseYieldUomId')
              .populate('baseYieldUomId', 'name shortCode')
              .lean());

              if (!recipe) return item;

              // Use the same type definition as GET
              type PopulatedRecipe = { name: string; description: string; baseYieldUomId?: { name: string; shortCode: string } };
              const typedRecipe = recipe as unknown as PopulatedRecipe;

              return {
                ...item,
                itemName: typedRecipe.name,
                itemDescription: typedRecipe.description,
                // Reinstate type assertion and add optional chaining
                uomName: typedRecipe.baseYieldUomId?.name,
                uomShortCode: typedRecipe.baseYieldUomId?.shortCode
              };
            }
          } catch (error: unknown) {
            console.error('Error populating item during PUT:', error);
            return { ...item, itemName: 'Error Populating', itemDescription: 'Error' };
          }
        })
      );
      // Assign the populated items back
      order.items = populatedItems;
    }

    return NextResponse.json({ message: 'Order updated successfully', order });
  } catch (error: unknown) {
    console.error('Error in PUT /api/orders/[id]:', error);
    if (error instanceof Error && /authentication required|unauthorized/i.test(error.message)) {
      console.warn(`>>> PUT /api/orders/[id] - Authentication Error:`, error.message);
      return NextResponse.json({ success: false, message: error.message || 'Authentication required.' }, { status: 401 });
    }
    if (error instanceof mongoose.Error.ValidationError) {
      const errors = Object.values(error.errors).map(el => ({ field: el.path, message: el.message }));
      return NextResponse.json({ success: false, message: 'Validation failed.', details: errors }, { status: 400 });
    }

    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json({ success: false, message: 'Invalid ID format provided.', details: { field: error.path, value: error.value } }, { status: 400 });
    }

    // Check for duplicate key errors (e.g., if orderNumber uniqueness is enforced and updated)
    if (error instanceof MongoServerError && error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return NextResponse.json({ success: false, message: `Duplicate value error for field: ${field}.`, details: { field, value: error.keyValue[field] } }, { status: 409 });
    }

    // Generic Error Handling
    let errorMessage = 'An unexpected error occurred while updating the order.';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}

// PATCH /api/orders/[id]
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req); // Add auth check
    const { id } = await context.params;

    if (!auth.tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required from authentication' }, { status: 400 });
    }

    const body = await req.json();
    
    // Validate status
    if (body.status && !['DRAFT', 'INCOMING', 'CONFIRMED', 'NOT_DELIVERED', 'PARTIALLY_DELIVERED', 'DELIVERED', 'CANCELLED', 'APPROVED'].includes(body.status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }

    // Find order and validate company access
    const order = await Order.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(auth.tenantId),
    });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Special handling for closing a partially delivered order
    if (body.status === 'DELIVERED' && order.status === 'PARTIALLY_DELIVERED') {
      // This is a close order operation
      order.status = 'DELIVERED';
      order.modifiedBy = new Types.ObjectId(auth.id); // Add modifiedBy
      await order.save();
      return NextResponse.json({ order });
    }

    // Normal status update
    if (body.status) {
      order.status = body.status;
    }

    // Update other fields if provided
    if (body.items) {
      order.items = body.items;
    }

    // Always update modifiedBy if changes were made
    order.modifiedBy = new Types.ObjectId(auth.id); // Add modifiedBy

    await order.save();
    return NextResponse.json({ order });
  } catch (error: unknown) {
    console.error('Error in PATCH /api/orders/[id]:', error);
    if (error instanceof Error && /authentication required|unauthorized/i.test(error.message)) {
      console.warn(`>>> PATCH /api/orders/[id] - Authentication Error:`, error.message);
      return NextResponse.json({ success: false, message: error.message || 'Authentication required.' }, { status: 401 });
    }
    if (error instanceof mongoose.Error.ValidationError) {
      const errors = Object.values(error.errors).map(el => ({ field: el.path, message: el.message }));
      return NextResponse.json({ success: false, message: 'Validation failed during patch.', details: errors }, { status: 400 });
    }

    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json({ success: false, message: 'Invalid ID format provided.', details: { field: error.path, value: error.value } }, { status: 400 });
    }

    // Generic Error Handling
    let errorMessage = 'An unexpected error occurred while patching the order.';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}

// DELETE /api/orders/[id]
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req); // Add auth check
    const { id } = await context.params;

    if (!auth.tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required from authentication' }, { status: 400 });
    }

    const result = await Order.deleteOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(auth.tenantId)
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Order deleted successfully' });
  } catch (error: unknown) {
    console.error('Error in DELETE /api/orders/[id]:', error);
    if (error instanceof Error && /authentication required|unauthorized/i.test(error.message)) {
      console.warn(`>>> DELETE /api/orders/[id] - Authentication Error:`, error.message);
      return NextResponse.json({ success: false, message: error.message || 'Authentication required.' }, { status: 401 });
    }
    // Specific Mongoose Error Handling
    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json({ success: false, message: 'Invalid ID format provided.', details: { field: error.path, value: error.value } }, { status: 400 });
    }

    // Generic Error Handling
    let errorMessage = 'An unexpected error occurred while deleting the order.';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
