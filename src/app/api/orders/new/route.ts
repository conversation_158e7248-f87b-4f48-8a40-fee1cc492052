import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Types, Model } from 'mongoose';
import Order from '@/models/Order';
import Location from '@/models/Location';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory';
import { Ingredient, IIngredient } from '@/models/Ingredient';
import Recipe, { IRecipe } from '@/models/Recipe';
import UOM, { IUOM } from '@/models/UOM';

// Define type for selling detail items found within Ingredient/Recipe
interface SellingDetailItem {
  _id?: Types.ObjectId | string;
  id?: Types.ObjectId | string;
  unitOfSelling?: Types.ObjectId | string;
  // Add other known properties if available
  [key: string]: any; 
}

// Define the structure of the items returned by the map
interface AvailableOrderItem {
  inventoryId: Types.ObjectId; 
  itemId: Types.ObjectId;
  itemType: 'INGREDIENT' | 'RECIPE';
  name: string;
  description?: string;
  category: string;
  sellingOptionId: string;
  sellingDetails: SellingDetailItem | null;
  currentStock: number;
  baseUom: IUOM | null;
  isActive: boolean;
}

export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    const companyId = req.headers.get('company-id');
    const searchParams = req.nextUrl.searchParams;
    const locationId = searchParams.get('locationId');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    // Get locations for this company (for the seller location dropdown)
    const locations = await Location.find({
      companyId: new Types.ObjectId(companyId)
    }).select('name').lean();
    
    // If locationId is provided, fetch available branch inventories for that location
    let availableItems: (AvailableOrderItem | null)[] = [];
    let filteredAvailableItems: AvailableOrderItem[] = [];
    if (locationId) {
      console.log(`Fetching branch inventories for location: ${locationId}`);
      
      try {
        // Fetch active branch inventories for this location (including items with zero stock)
        const branchInventories = await BranchInventory.find({
          companyId: new Types.ObjectId(companyId),
          locationId: new Types.ObjectId(locationId),
          isActive: true
          // Removed 'currentStock: { $gt: 0 }' filter to include items with zero stock
        }).lean() as IBranchInventory[]; // Cast the result here
        
        console.log(`Found ${branchInventories.length} branch inventory items for location ${locationId}`);
        
        // If there are no items, let's do some additional debugging
        if (!branchInventories || branchInventories.length === 0) {
          console.log('No active branch inventories found for location: ${locationId}');
          
          // Try a more relaxed query to help diagnose the issue
          const allBranchInventories = await BranchInventory.find({
            companyId: new Types.ObjectId(companyId),
            locationId: new Types.ObjectId(locationId)
          }).lean();
          
          console.log(`Found ${allBranchInventories.length} branch inventory items without the isActive filter`);
          
          if (allBranchInventories.length > 0) {
            // Log a sample item to see what it looks like
            console.log('Sample inventory item:', JSON.stringify(allBranchInventories[0], null, 2));
          }
        }
      
      // Populate item details, UOMs, and selling options
      availableItems = await Promise.all(branchInventories.map(async (inventory: IBranchInventory): Promise<AvailableOrderItem | null> => {
        // Get the item details based on itemType
        const model = inventory.itemType === 'INGREDIENT' ? Ingredient : Recipe;
        const item = await (model as Model<any>).findById(inventory.itemId).lean() as IIngredient | IRecipe | null;
        
        if (!item) return null;
        
        // Get UOM information
        const baseUom = await UOM.findById(inventory.baseUomId).lean() as IUOM | null;
        
        // Find selling option details from the item
        let sellingDetails: SellingDetailItem | null = null;
        try {
          if (inventory.itemType === 'INGREDIENT' && item.sellingDetails) {
            const ingredientItem = item as IIngredient;
            sellingDetails = ingredientItem.sellingDetails?.find(
              (sd: SellingDetailItem) => sd && sd._id && sd._id.toString() === inventory.sellingOptionId.toString()
            ) ?? null; // Ensure result is null if undefined
          } else if (inventory.itemType === 'RECIPE' && item.sellingDetails) {
            const recipeItem = item as IRecipe;
            sellingDetails = recipeItem.sellingDetails?.find((sd: SellingDetailItem) => {
              if (!sd) return false;
              
              if (sd.id) {
                return sd.id.toString() === inventory.sellingOptionId.toString();
              }
              
              if (sd._id) {
                return sd._id.toString() === inventory.sellingOptionId.toString();
              }
              
              return sd.id === inventory.sellingOptionId.toString() || sd._id === inventory.sellingOptionId.toString();
            }) ?? null; // Ensure result is null if undefined
          }
          
          const itemSellingDetails = (item as IIngredient | IRecipe).sellingDetails;
          if (!sellingDetails && itemSellingDetails && itemSellingDetails.length > 0) {
            console.log(`Could not find matching selling option for ${inventory.itemId}, using first available option`);
            sellingDetails = itemSellingDetails[0];
          }
        } catch (error) {
          console.error(`Error processing selling details for item ${inventory.itemId}:`, error);
          return null; 
        }
        
        // Get UOM for selling if details found
        let sellingUom = null;
        if (sellingDetails && sellingDetails.unitOfSelling) {
          sellingUom = await UOM.findById(sellingDetails.unitOfSelling).lean();
        }
        
        // Construct the final AvailableOrderItem object
        return {
          inventoryId: inventory._id as Types.ObjectId, // Add explicit cast
          itemId: inventory.itemId,
          itemType: inventory.itemType,
          name: item.name, 
          description: item.description, 
          category: inventory.category, 
          sellingOptionId: inventory.sellingOptionId.toString(), 
          sellingDetails: sellingDetails, 
          currentStock: inventory.currentStock, 
          baseUom: baseUom,
          isActive: inventory.isActive 
        };
      }));
      
      // Filter out any null values that occurred from items not found or errors
      filteredAvailableItems = availableItems.filter((item): item is AvailableOrderItem => item !== null);
      console.log(`Returning ${filteredAvailableItems.length} available items after filtering`);
        
      if (filteredAvailableItems.length > 0) {
        console.log('Sample available item:', JSON.stringify(filteredAvailableItems[0], null, 2));
      }
    } catch (error) {
      console.error('Error fetching branch inventories:', error);
    }
  }

  // Generate a unique order number
  const today = new Date();
  const dateStr = today.getFullYear().toString() +
                 (today.getMonth() + 1).toString().padStart(2, '0') +
                 today.getDate().toString().padStart(2, '0');
  
  // Determine the order prefix based on the creation location
  // Orders created from the web app (HQ) should start with HQ
  const orderPrefix = 'HQ';
  
  // Find the last order for today with the same prefix to increment counter
  const lastOrder = await Order.findOne({
    companyId: new Types.ObjectId(companyId),
    orderNumber: { $regex: `^${orderPrefix}-${dateStr}-` }
  }).sort({ orderNumber: -1 }).lean();
  
  let orderCount = 1;
  if (lastOrder && (lastOrder as any).orderNumber) {
    // Parse the counter from PREFIX-YYYYMMDD-XXXX format
    const matches = (lastOrder as any).orderNumber.match(new RegExp(`^${orderPrefix}-\d{8}-(\d{4})`));
    if (matches && matches[1]) {
      orderCount = parseInt(matches[1], 10) + 1;
    }
  }
  
  // Format the new order number
  const orderNumber = `${orderPrefix}-${dateStr}-${orderCount.toString().padStart(4, '0')}`;
  
  // Ensure all items have required properties
  const sanitizedItems = (locationId ? filteredAvailableItems : []).map((item: AvailableOrderItem) => ({
    ...item,
    inventoryId: item.inventoryId?.toString() || '',
    itemId: item.itemId?.toString() || '',
    itemType: item.itemType || 'INGREDIENT',
    name: item.name || 'Unknown Item',
    category: item.category || 'Uncategorized',
    currentStock: item.currentStock || 0,
    sellingDetails: item.sellingDetails || {
      priceWithoutTax: 0,
      priceWithTax: 0,
      taxRate: 0,
      unitName: 'Unit',
      unitShortCode: 'ea'
    }
  }));
  
  return NextResponse.json({ 
    locations,
    orderNumber,
    initialData: {
      orderNumber,
      status: 'DRAFT',
      items: []
    },
    availableItems: sanitizedItems,
    debug: {
      branchInventoryCount: sanitizedItems.length,
      locationId,
      query: { companyId, locationId, isActive: true }
    }
  });
} catch (error) {
  console.error('Error in /api/orders/new:', error);
  return NextResponse.json({ error: 'Failed to prepare new order' }, { status: 500 });
}
}
