import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import dbConnect from '@/lib/db';
import Order from '@/models/Order';
import OrderSync from '@/models/OrderSync';
import { Types } from 'mongoose';

// POST /api/orders/sync
enum SyncStatus { PENDING = 'PENDING', PROCESSING = 'PROCESSING', COMPLETED = 'COMPLETED', PARTIAL = 'PARTIAL', FAILED = 'FAILED' }
export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    const companyId = req.headers.get('company-id');
    const locationId = req.headers.get('location-id');
    if (!companyId || !locationId) {
      return NextResponse.json({ error: 'company-id and location-id headers are required' }, { status: 400 });
    }

    const payload = await req.json();
    if (!Array.isArray(payload.orders)) {
      return NextResponse.json({ error: 'Request body must have an orders array' }, { status: 400 });
    }

    const syncId = uuidv4();
    const syncRecord = await OrderSync.create({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      syncId,
      status: SyncStatus.PROCESSING,
      startTime: new Date(),
      ordersReceived: payload.orders.length,
      ordersProcessed: 0,
      syncErrors: []
    });

    let processedCount = 0;
    const errors: { orderId: string; error: string }[] = [];
    for (const ord of payload.orders) {
      try {
        // Upsert order from IonicPOS
        await (Order as any).findOrCreateFromSync(ord, companyId, syncId);
        processedCount++;
      } catch (err: any) {
        errors.push({ orderId: ord.branchId || ord.orderId || '', error: err.message });
      }
    }

    syncRecord.ordersProcessed = processedCount;
    syncRecord.syncErrors = errors;
    syncRecord.status = errors.length > 0 ? SyncStatus.PARTIAL : SyncStatus.COMPLETED;
    syncRecord.endTime = new Date();
    await syncRecord.save();

    return NextResponse.json({ message: 'Sync completed', syncId, processedCount, errors });
  } catch (error: unknown) {
    console.error('Error in POST /api/orders/sync:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
