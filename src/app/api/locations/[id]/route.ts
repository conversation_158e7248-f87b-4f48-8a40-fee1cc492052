import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import Location from '@/models/Location';

// PUT method: Update an existing location
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { 
      name, 
      locationType, 
      canSellToExternal, 
      canDoTransfers, 
      canBuyfromExternalSuppliers,
      address, 
      contactInfo 
    } = await request.json();
    const companyId = request.headers.get('company-id');
    const { id: locationId } = await context.params;

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    await dbConnect();
    console.log('Updating location with data:', {
      name,
      locationType,
      canSellToExternal,
      canDoTransfers,
      canBuyfromExternalSuppliers
    });

    const location = await Location.findOneAndUpdate(
      { _id: locationId, companyId },
      { 
        name, 
        locationType, 
        canSellToExternal: <PERSON><PERSON><PERSON>(canSellToExternal), 
        canDoTransfers: <PERSON><PERSON><PERSON>(canDoTransfers),
        canBuyfromExternalSuppliers: Boolean(canBuyfromExternalSuppliers),
        address,
        contactInfo
      },
      { new: true }
    );

    if (!location) {
      return NextResponse.json({ error: 'Location not found' }, { status: 404 });
    }

    console.log('Updated location:', location);
    return NextResponse.json(location);
  } catch (error: unknown) {
    console.error('[PUT Location] Error:', error);
    return NextResponse.json({ error: 'Failed to update location' }, { status: 500 });
  }
}

// DELETE method: Delete a location
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const companyId = request.headers.get('company-id');
    const { id: locationId } = await context.params;

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    await dbConnect();
    const location = await Location.findOne({ _id: locationId, companyId });

    if (!location) {
      return NextResponse.json({ error: 'Location not found' }, { status: 404 });
    }

    await Location.findOneAndDelete({ _id: locationId, companyId });
    return NextResponse.json({ message: 'Location deleted successfully' });
  } catch (error: unknown) {
    console.error('[DELETE Location] Error:', error);
    return NextResponse.json({ error: 'Failed to delete location' }, { status: 500 });
  }
}
