// src/app/api/locations/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import Location from '@/models/Location';

// GET method: Fetch all locations for a given company
export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const companyId = request.headers.get('company-id'); 
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const locations = await Location.find({ companyId });
    return NextResponse.json(locations);
  } catch (error: unknown) {
    console.error('[GET Locations] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch locations' }, { status: 500 });
  }
}

// POST method: Create a new location
export async function POST(request: NextRequest) {
  try {
    const { 
      name, 
      locationType, 
      canSellToExternal, 
      canDoTransfers, 
      canBuyfromExternalSuppliers,
      address, 
      contactInfo 
    } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!companyId || !name || !locationType) {
      return NextResponse.json({ error: 'Company ID, name, and location type are required' }, { status: 400 });
    }

    console.log('Creating location with data:', {
      name,
      locationType,
      canSellToExternal,
      canDoTransfers,
      canBuyfromExternalSuppliers
    });

    await dbConnect();
    const location = await Location.create({
      companyId,
      name,
      locationType,
      canSellToExternal: Boolean(canSellToExternal),
      canDoTransfers: Boolean(canDoTransfers),
      canBuyfromExternalSuppliers: Boolean(canBuyfromExternalSuppliers),
      address,
      contactInfo
    });

    console.log('Created location:', location);
    return NextResponse.json(location);
  } catch (error: unknown) {
    console.error('[POST Location] Error:', error);
    return NextResponse.json({ error: 'Failed to create location' }, { status: 500 });
  }
}
