import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import TaxSettings from '@/models/TaxSettings';

// GET: Fetch tax settings for a company
export async function GET(request: NextRequest) {
  try {
    const companyId = request.headers.get('company-id');
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    await dbConnect();
    let settings = await TaxSettings.findOne({ companyId });

    // If no settings exist, create default settings
    if (!settings) {
      settings = await TaxSettings.create({
        companyId,
        currency: 'USD',
        vatOnSales: 20,
        vatOnPurchases: 20,
        enableVatOnSales: true,
        enableVatOnPurchases: true,
        defaultVatCategory: 'STANDARD'
      });
    }

    return NextResponse.json(settings);
  } catch (error: unknown) {
    console.error('[GET Tax Settings] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch tax settings' }, { status: 500 });
  }
}

// PUT: Update tax settings
export async function PUT(request: NextRequest) {
  try {
    const companyId = request.headers.get('company-id');
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const updates = await request.json();
    
    // Validate the updates
    if (updates.vatOnSales < 0 || updates.vatOnSales > 100) {
      return NextResponse.json({ error: 'VAT on sales must be between 0 and 100' }, { status: 400 });
    }
    if (updates.vatOnPurchases < 0 || updates.vatOnPurchases > 100) {
      return NextResponse.json({ error: 'VAT on purchases must be between 0 and 100' }, { status: 400 });
    }

    await dbConnect();
    const settings = await TaxSettings.findOneAndUpdate(
      { companyId },
      { 
        ...updates,
        companyId // ensure companyId is preserved
      },
      { new: true, upsert: true }
    );

    return NextResponse.json(settings);
  } catch (error: unknown) {
    console.error('[PUT Tax Settings] Error:', error);
    return NextResponse.json({ error: 'Failed to update tax settings' }, { status: 500 });
  }
}
