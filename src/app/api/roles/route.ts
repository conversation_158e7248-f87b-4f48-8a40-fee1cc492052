import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import Role from '@/models/Role';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const companyId = request.headers.get('company-id');
    if (!companyId) return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });

    const roles = await Role.find({ companyId });
    return NextResponse.json(roles);
  } catch (error: unknown) {
    console.error('[GET Roles] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch roles' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, permissions } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!companyId || !name) {
      return NextResponse.json({ error: 'Company ID and name are required' }, { status: 400 });
    }

    await dbConnect();
    const role = await Role.create({ companyId, name, permissions });
    return NextResponse.json(role);
  } catch (error: unknown) {
    console.error('[POST Role] Error:', error);
    return NextResponse.json({ error: 'Failed to create role' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { roleId, permissions } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!companyId || !roleId || !permissions) {
      return NextResponse.json({ error: 'Company ID, Role ID and permissions are required' }, { status: 400 });
    }

    await dbConnect();
    const role = await Role.findOneAndUpdate(
      { _id: roleId, companyId },
      { permissions },
      { new: true }
    );

    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    return NextResponse.json(role);
  } catch (error: unknown) {
    console.error('[PUT Role] Error:', error);
    return NextResponse.json({ error: 'Failed to update role' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { roleId } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!companyId || !roleId) {
      return NextResponse.json({ error: 'Company ID and Role ID are required' }, { status: 400 });
    }

    await dbConnect();
    const role = await Role.findOne({ _id: roleId, companyId });

    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    if (role.name === 'admin') {
      return NextResponse.json({ error: 'Admin role cannot be deleted' }, { status: 403 });
    }

    await Role.findOneAndDelete({ _id: roleId, companyId });
    return NextResponse.json({ message: 'Role deleted successfully' });
  } catch (error: unknown) {
    console.error('[DELETE Role] Error:', error);
    return NextResponse.json({ error: 'Failed to delete role' }, { status: 500 });
  }
}
