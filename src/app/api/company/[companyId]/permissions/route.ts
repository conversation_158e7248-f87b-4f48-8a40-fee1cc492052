// src/app/api/company/[companyId]/permissions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import Permission from '@/models/Permission';
import mongoose from 'mongoose';
import { applyCorsHeaders, handleCorsOptions } from '@/middleware/cors-middleware';

// Sample permissions for development
async function createSamplePermissions() {
  const samplePermissions = [
    // User Management permissions
    { name: 'users.view', description: 'View users', category: 'User Management', isSystemLevel: false },
    { name: 'users.create', description: 'Create new users', category: 'User Management', isSystemLevel: false },
    { name: 'users.edit', description: 'Edit user details', category: 'User Management', isSystemLevel: false },
    { name: 'users.delete', description: 'Delete users', category: 'User Management', isSystemLevel: false },
    
    // Role Management permissions
    { name: 'roles.view', description: 'View roles', category: 'Role Management', isSystemLevel: false },
    { name: 'roles.create', description: 'Create new roles', category: 'Role Management', isSystemLevel: false },
    { name: 'roles.edit', description: 'Edit role details', category: 'Role Management', isSystemLevel: false },
    { name: 'roles.delete', description: 'Delete roles', category: 'Role Management', isSystemLevel: false },
    
    // Menu Management permissions
    { name: 'menu.view', description: 'View menu items', category: 'Menu', isSystemLevel: false },
    { name: 'menu.create', description: 'Create menu items', category: 'Menu', isSystemLevel: false },
    { name: 'menu.edit', description: 'Edit menu items', category: 'Menu', isSystemLevel: false },
    { name: 'menu.delete', description: 'Delete menu items', category: 'Menu', isSystemLevel: false },
    
    // Order Management permissions
    { name: 'orders.view', description: 'View orders', category: 'Orders', isSystemLevel: false },
    { name: 'orders.create', description: 'Create orders', category: 'Orders', isSystemLevel: false },
    { name: 'orders.process', description: 'Process and update orders', category: 'Orders', isSystemLevel: false },
    { name: 'orders.cancel', description: 'Cancel orders', category: 'Orders', isSystemLevel: false },
    
    // Reports permissions
    { name: 'reports.view', description: 'View reports', category: 'Reports', isSystemLevel: false },
    { name: 'reports.export', description: 'Export reports', category: 'Reports', isSystemLevel: false },
    
    // Settings permissions
    { name: 'settings.view', description: 'View system settings', category: 'Settings', isSystemLevel: true },
    { name: 'settings.edit', description: 'Edit system settings', category: 'Settings', isSystemLevel: true },
  ];
  
  // Use create many to efficiently insert all permissions
  try {
    await Permission.insertMany(samplePermissions, { ordered: false });
    console.log('Sample permissions created successfully');
  } catch (error: any) {
    // If error is due to duplicates, that's expected and fine
    if (error.code === 11000) {
      console.log('Some permissions already exist, skipped duplicates');
    } else {
      console.error('Error creating sample permissions:', error);
      throw error;
    }
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

// GET all permissions
export const GET = withAuth(
  async (
    req: NextRequest,
    user: any,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      console.log('Permissions API called - auth passed');
      await dbConnect();
      console.log('DB connected');
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const companyId = params.companyId;
      console.log('Company ID:', companyId);
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        console.error('Invalid company ID format:', companyId);
        return applyCorsHeaders(
          NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 }),
          req
        );
      }

      // Ensure user has access to this company
      console.log('User:', user.id, 'userType:', user.userType, 'companyId:', user.companyId?.toString());
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        console.error('Unauthorized access attempt - user companyId:', user.companyId, 'requested companyId:', companyId);
        return applyCorsHeaders(
          NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 }),
          req
        );
      }

      // Get query parameters
      const { searchParams } = new URL(req.url);
      const category = searchParams.get('category') || '';
      const includeSystem = searchParams.has('includeSystem') ? searchParams.get('includeSystem') === 'true' : true;
      
      // Build the query
      const query: any = {};
      
      if (category) {
        query.category = category;
      }
      
      // If not including system permissions, filter them out
      if (!includeSystem) {
        query.isSystemLevel = false;
      }
      
      // Get permissions
      console.log('Querying permissions with:', query);
      const permissions = await Permission.find(query).sort({ category: 1, name: 1 }).lean();
      console.log('Found permissions count:', permissions.length);
      
      // Create some sample permissions if none exist (for development only)
      if (permissions.length === 0) {
        console.log('No permissions found - creating sample permissions');
        await createSamplePermissions();
        const newPermissions = await Permission.find(query).sort({ category: 1, name: 1 }).lean();
        console.log('Created sample permissions count:', newPermissions.length);
        
        // Group permissions by category
        const groupedPermissions = newPermissions.reduce((acc: Record<string, any[]>, permission) => {
          const category = permission.category;
          if (!acc[category]) {
            acc[category] = [];
          }
          acc[category].push(permission);
          return acc;
        }, {});
        
        return applyCorsHeaders(
          NextResponse.json({ 
            permissions: newPermissions,
            groupedPermissions
          }),
          req
        );
      }
      
      // Group permissions by category
      const groupedPermissions = permissions.reduce((acc: Record<string, any[]>, permission) => {
        const category = permission.category;
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(permission);
        return acc;
      }, {});
      
      console.log('Permission categories:', Object.keys(groupedPermissions));
      
      return applyCorsHeaders(
        NextResponse.json({ 
          permissions,
          groupedPermissions
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error fetching permissions:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  { allowedRoles: ['owner', 'admin', 'partner'], requiredPermissions: ['permissions.read'] }
);

// POST to create a new permission
export const POST = withAuth(
  async (
    req: NextRequest,
    user: any,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await dbConnect();
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const companyId = params.companyId;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Only owners and superusers can create permissions
      if (user.role !== 'owner' && user.role !== 'partner' && user.userType !== 'superuser') {
        return NextResponse.json({ error: 'Only company owners can create permissions' }, { status: 403 });
      }

      // Get the permission data
      const permissionData = await req.json();
      
      // Validate required fields
      if (!permissionData.name || !permissionData.category) {
        return NextResponse.json({ error: 'Permission name and category are required' }, { status: 400 });
      }
      
      // Check if permission with same name already exists
      const existingPermission = await Permission.findOne({
        name: permissionData.name
      });
      
      if (existingPermission) {
        return NextResponse.json({ error: 'A permission with this name already exists' }, { status: 409 });
      }
      
      // Create the new permission
      const newPermission = new Permission({
        name: permissionData.name,
        description: permissionData.description || '',
        category: permissionData.category,
        isSystemLevel: false // Custom permissions are never system level
      });
      
      await newPermission.save();
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'Permission created successfully',
          permission: newPermission 
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error creating permission:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  { allowedRoles: ['owner', 'partner'], requiredPermissions: ['permissions.create'] }
);

// PATCH to update a permission
export const PATCH = withAuth(
  async (
    req: NextRequest,
    user: any,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await dbConnect();
      const { companyId } = await context.params;
      const { permissionId, name, description, category } = await req.json();
      if (!permissionId) {
        return NextResponse.json({ error: 'Permission ID is required' }, { status: 400 });
      }
      const perm = await Permission.findById(permissionId);
      if (!perm) {
        return NextResponse.json({ error: 'Permission not found' }, { status: 404 });
      }
      if (perm.isSystemLevel) {
        return NextResponse.json({ error: 'System permissions cannot be edited' }, { status: 403 });
      }
      if (perm.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized to edit this permission' }, { status: 403 });
      }
      if (name !== undefined) perm.name = name;
      if (description !== undefined) perm.description = description;
      if (category !== undefined) perm.category = category;
      await perm.save();
      return applyCorsHeaders(NextResponse.json(perm), req);
    } catch (error: unknown) {
      console.error('[PATCH Company Permission] Error:', error);
      return applyCorsHeaders(NextResponse.json({ error: 'Failed to update permission' }), req);
    }
  },
  { allowedRoles: ['owner', 'partner'], requiredPermissions: ['permissions.edit'] }
);

// DELETE to remove a permission
export const DELETE = withAuth(
  async (
    req: NextRequest,
    user: any,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await dbConnect();
      const { companyId } = await context.params;
      const { permissionId } = await req.json();
      if (!permissionId) {
        return NextResponse.json({ error: 'Permission ID is required' }, { status: 400 });
      }
      const perm = await Permission.findById(permissionId);
      if (!perm) {
        return NextResponse.json({ error: 'Permission not found' }, { status: 404 });
      }
      if (perm.isSystemLevel) {
        return NextResponse.json({ error: 'System permissions cannot be deleted' }, { status: 403 });
      }
      if (perm.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized to delete this permission' }, { status: 403 });
      }
      await Permission.findByIdAndDelete(permissionId);
      return applyCorsHeaders(NextResponse.json({ message: 'Permission deleted successfully' }), req);
    } catch (error: unknown) {
      console.error('[DELETE Company Permission] Error:', error);
      return applyCorsHeaders(NextResponse.json({ error: 'Failed to delete permission' }), req);
    }
  },
  { allowedRoles: ['owner', 'partner'], requiredPermissions: ['permissions.delete'] }
);
