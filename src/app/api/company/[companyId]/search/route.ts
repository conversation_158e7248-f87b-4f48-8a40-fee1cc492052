import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Ingredient } from '@/models/Ingredient';
import Recipe from '@/models/Recipe';
import { Types } from 'mongoose';
import UOM from '@/models/UOM'; // Import UOM model to ensure it's registered

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect();
    const companyId = request.headers.get('company-id');
    const params = await context.params;
    
    if (!companyId || companyId !== params.companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 });
    }

    const searchQuery = request.nextUrl.searchParams.get('query');
    if (!searchQuery) {
      return NextResponse.json({ error: 'Search query is required' }, { status: 400 });
    }

    console.log('Search query:', searchQuery);
    console.log('Company ID:', companyId);

    // Fetch ingredients
    const ingredients = await Ingredient.find({
      companyId: new Types.ObjectId(companyId),
      name: { $regex: searchQuery, $options: 'i' }
    })
    .select('_id name baseUomId')
    .populate('baseUomId', '_id name shortCode')
    .limit(5)
    .lean();

    console.log('Found ingredients:', ingredients.length);
    console.log('Ingredient results:', JSON.stringify(ingredients, null, 2));

    // Fetch sub-recipes
    const recipeQuery = {
      companyId: new Types.ObjectId(companyId),
      isSubRecipe: true,
      name: { $regex: searchQuery, $options: 'i' }
    };
    console.log('Recipe query:', JSON.stringify(recipeQuery, null, 2));

    const recipes = await Recipe.find(recipeQuery)
      .select('_id name baseYieldUOM')
      .populate({
        path: 'baseYieldUOM',
        model: 'UOM',
        select: '_id name shortCode'
      })
      .limit(5)
      .lean();

    console.log('Raw recipes before transform:', JSON.stringify(recipes, null, 2));
    console.log('Found recipes:', recipes.length);
    console.log('Raw recipe results:', JSON.stringify(recipes, null, 2));

    // Transform and combine results
    const results = [
      ...ingredients.map(ing => ({
        id: ing._id.toString(),
        name: ing.name,
        baseUomId: typeof ing.baseUomId === 'object' && ing.baseUomId !== null && '_id' in ing.baseUomId ? (ing.baseUomId._id as unknown as import('mongoose').Types.ObjectId).toString() : '',
        baseUomName: typeof ing.baseUomId === 'object' && ing.baseUomId !== null && 'name' in ing.baseUomId ? (ing.baseUomId.name as string) : '',
        baseUomShortCode: typeof ing.baseUomId === 'object' && ing.baseUomId !== null && 'shortCode' in ing.baseUomId ? (ing.baseUomId.shortCode as string) : '',
        type: 'ingredient'
      })),
      ...recipes.map(recipe => ({
        id: typeof recipe._id === 'string' ? recipe._id : (recipe._id as unknown as Types.ObjectId).toString(),
        name: recipe.name,
        baseUomId: typeof recipe.baseYieldUOM === 'object' && recipe.baseYieldUOM !== null && '_id' in recipe.baseYieldUOM ? (recipe.baseYieldUOM._id as unknown as import('mongoose').Types.ObjectId).toString() : '',
        baseUomName: typeof recipe.baseYieldUOM === 'object' && recipe.baseYieldUOM !== null && 'name' in recipe.baseYieldUOM ? (recipe.baseYieldUOM.name as string) : '',
        baseUomShortCode: typeof recipe.baseYieldUOM === 'object' && recipe.baseYieldUOM !== null && 'shortCode' in recipe.baseYieldUOM ? (recipe.baseYieldUOM.shortCode as string) : '',
        type: 'recipe'
      }))
    ].sort((a, b) => a.name.localeCompare(b.name));

    console.log('Combined results:', results.length);
    console.log('Final transformed results:', JSON.stringify(results, null, 2));

    return NextResponse.json(results);
  } catch (error: unknown) {
    console.error('Error searching:', error);
    return NextResponse.json(
      { error: 'Failed to search' },
      { status: 500 }
    );
  }
}
