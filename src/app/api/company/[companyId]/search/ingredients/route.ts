import { NextRequest, NextResponse } from "next/server";
import dbConnect from '@/lib/db';


import { Ingredient } from '@/models/Ingredient';
import Recipe from '@/models/Recipe';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Check authentication using JWT
    // (Assume requireAuth or similar is imported from @/lib/auth)
    // Example:
    // const user = await requireAuth(request);
    // if (!user) {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    // }

    await dbConnect();
    const headerCompanyId = request.headers.get('company-id');
    const { companyId } = await context.params;
    if (!headerCompanyId || companyId !== headerCompanyId) {
      return NextResponse.json({ error: "Invalid company ID" }, { status: 403 });
    }

    const searchQuery = request.nextUrl.searchParams.get("query");
    if (!searchQuery) {
      return NextResponse.json({ error: "Search query is required" }, { status: 400 });
    }

    // Search in ingredients collection
    const ingredients = await Ingredient.find({
      companyId,
      name: { $regex: searchQuery, $options: "i" }
    })
    .select('_id name baseUomId')
    .limit(5)
    .lean();

    // Search in recipes collection
    const recipes = await Recipe.find({
      companyId,
      name: { $regex: searchQuery, $options: "i" }
    })
    .select('_id name yieldBaseUomId')
    .limit(5)
    .lean();

    // Combine and format results
    const results = [
      ...ingredients.map(ing => ({
        id: ing._id.toString(),
        name: ing.name,
        baseUomId: ing.baseUomId,
        type: "ingredient"
      })),
      ...recipes.map(recipe => ({
        id: typeof recipe._id === 'string' ? recipe._id : (recipe._id as unknown as import('mongoose').Types.ObjectId).toString(),
        name: recipe.name,
        baseUomId: recipe.yieldBaseUomId,
        type: "recipe"
      }))
    ];

    return NextResponse.json(results);
  } catch (error: unknown) {
    console.error("Error searching ingredients:", error);
    return NextResponse.json(
      { error: "Failed to search ingredients" },
      { status: 500 }
    );
  }
}
