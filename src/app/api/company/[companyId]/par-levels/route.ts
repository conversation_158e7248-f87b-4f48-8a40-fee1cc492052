import { NextRequest, NextResponse } from 'next/server';
import { isValidCompanyUser } from '@/lib/server-auth';
import BranchInventory from '@/models/BranchInventory';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

interface ParLevelUpdate {
  inventoryId: string;
  branchParLevel?: number;
  centralKitchenParLevel?: number;
}

interface BulkParLevelUpdate {
  locationId?: string;
  updates: ParLevelUpdate[];
}

export async function GET(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  try {
    await dbConnect();
    
    const { companyId } = params;
    const authResult = await isValidCompanyUser(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Authentication failed' }, { status: 401 });
    }
    const searchParams = request.nextUrl.searchParams;
    const locationId = searchParams.get('locationId');
    const itemType = searchParams.get('itemType') as 'RECIPE' | 'INGREDIENT' | null;

    // Build query
    const query: any = {
      companyId,
      isActive: true
    };

    if (locationId) {
      query.locationId = locationId;
    }

    if (itemType) {
      query.itemType = itemType;
    }

    // Get par level information
    const parLevels = await BranchInventory.find(query)
      .populate('itemId', 'name')
      .populate('locationId', 'name')
      .populate('baseUomId', 'name abbreviation')
      .select('itemId locationId itemType currentStock parLevel branchParLevel centralKitchenParLevel centralKitchenStock baseUomId category')
      .lean();

    const formattedParLevels = parLevels.map(item => ({
      inventoryId: item._id.toString(),
      itemId: item.itemId._id?.toString() || item.itemId.toString(),
      itemName: (item.itemId as any).name || 'Unknown Item',
      locationId: (item.locationId as any)._id?.toString() || item.locationId.toString(),
      locationName: (item.locationId as any).name || 'Unknown Location',
      itemType: item.itemType,
      category: item.category,
      currentStock: item.currentStock,
      branchParLevel: item.branchParLevel || item.parLevel,
      centralKitchenParLevel: item.centralKitchenParLevel || 0,
      centralKitchenStock: item.centralKitchenStock || 0,
      baseUOM: (item.baseUomId as any)?.name || 'unit',
      // Status indicators
      belowBranchPar: item.currentStock < (item.branchParLevel || item.parLevel),
      belowCentralPar: (item.centralKitchenStock || 0) < (item.centralKitchenParLevel || 0),
      reorderRecommended: item.currentStock < (item.branchParLevel || item.parLevel) && 
                         (item.centralKitchenStock || 0) > 0
    }));

    return NextResponse.json({
      success: true,
      data: formattedParLevels,
      summary: {
        totalItems: formattedParLevels.length,
        itemsBelowBranchPar: formattedParLevels.filter(item => item.belowBranchPar).length,
        itemsBelowCentralPar: formattedParLevels.filter(item => item.belowCentralPar).length,
        reorderRecommendations: formattedParLevels.filter(item => item.reorderRecommended).length
      }
    });

  } catch (error) {
    console.error('Error fetching par levels:', error);
    return NextResponse.json(
      { error: 'Failed to fetch par levels' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  try {
    await dbConnect();
    
    const { companyId } = params;
    const authResult = await isValidCompanyUser(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Authentication failed' }, { status: 401 });
    }
    const body: BulkParLevelUpdate = await request.json();

    if (!body.updates || !Array.isArray(body.updates)) {
      return NextResponse.json(
        { error: 'Invalid request format. Expected updates array.' },
        { status: 400 }
      );
    }

    const results: any[] = [];
    const errors: any[] = [];

    // Process each update
    for (const update of body.updates) {
      try {
        const { inventoryId, branchParLevel, centralKitchenParLevel } = update;

        if (!inventoryId) {
          errors.push({ inventoryId, error: 'Missing inventoryId' });
          continue;
        }

        // Build update object
        const updateData: any = {
          lastUpdated: new Date()
        };

        if (branchParLevel !== undefined) {
          updateData.branchParLevel = Math.max(0, branchParLevel);
          // Also update the legacy parLevel field for backwards compatibility
          updateData.parLevel = Math.max(0, branchParLevel);
        }

        if (centralKitchenParLevel !== undefined) {
          updateData.centralKitchenParLevel = Math.max(0, centralKitchenParLevel);
        }

        // Find and update the inventory item
        const updatedItem = await BranchInventory.findOneAndUpdate(
          {
            _id: new mongoose.Types.ObjectId(inventoryId),
            companyId: new mongoose.Types.ObjectId(companyId),
            isActive: true
          },
          updateData,
          { new: true, runValidators: true }
        ).populate('itemId', 'name')
         .populate('locationId', 'name');

        if (!updatedItem) {
          errors.push({ inventoryId, error: 'Inventory item not found or access denied' });
          continue;
        }

        results.push({
          inventoryId: (updatedItem._id as any).toString(),
          itemName: (updatedItem.itemId as any)?.name || 'Unknown Item',
          locationName: (updatedItem.locationId as any)?.name || 'Unknown Location',
          branchParLevel: updatedItem.branchParLevel || updatedItem.parLevel,
          centralKitchenParLevel: updatedItem.centralKitchenParLevel || 0,
          updated: true
        });

      } catch (updateError) {
        console.error(`Error updating par level for ${update.inventoryId}:`, updateError);
        errors.push({
          inventoryId: update.inventoryId,
          error: updateError instanceof Error ? updateError.message : 'Update failed'
        });
      }
    }

    return NextResponse.json({
      success: errors.length === 0,
      updated: results,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        totalUpdates: body.updates.length,
        successful: results.length,
        failed: errors.length
      }
    });

  } catch (error) {
    console.error('Error updating par levels:', error);
    return NextResponse.json(
      { error: 'Failed to update par levels' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  try {
    await dbConnect();
    
    const { companyId } = params;
    const authResult = await isValidCompanyUser(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Authentication failed' }, { status: 401 });
    }
    const body = await request.json();
    const { locationId, itemType, defaultBranchParLevel, defaultCentralParLevel } = body;

    if (!locationId) {
      return NextResponse.json(
        { error: 'locationId is required' },
        { status: 400 }
      );
    }

    // Build query for bulk update
    const query: any = {
      companyId: new mongoose.Types.ObjectId(companyId),
      locationId: new mongoose.Types.ObjectId(locationId),
      isActive: true
    };

    if (itemType && ['RECIPE', 'INGREDIENT'].includes(itemType)) {
      query.itemType = itemType;
    }

    const updateData: any = {
      lastUpdated: new Date()
    };

    if (defaultBranchParLevel !== undefined) {
      updateData.branchParLevel = Math.max(0, defaultBranchParLevel);
      updateData.parLevel = Math.max(0, defaultBranchParLevel); // Backwards compatibility
    }

    if (defaultCentralParLevel !== undefined) {
      updateData.centralKitchenParLevel = Math.max(0, defaultCentralParLevel);
    }

    // Perform bulk update
    const result = await BranchInventory.updateMany(query, updateData);

    return NextResponse.json({
      success: true,
      message: 'Par levels updated successfully',
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
      query: {
        companyId,
        locationId,
        itemType: itemType || 'all'
      },
      updates: {
        branchParLevel: defaultBranchParLevel,
        centralKitchenParLevel: defaultCentralParLevel
      }
    });

  } catch (error) {
    console.error('Error bulk updating par levels:', error);
    return NextResponse.json(
      { error: 'Failed to bulk update par levels' },
      { status: 500 }
    );
  }
}