// src/app/api/company/[companyId]/central-kitchen/catalog/route.ts
import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import dbConnect from '@/lib/db';
import { Types } from 'mongoose';
import '@/models/Recipe';  // Ensure Recipe model is registered
import '@/models/Ingredient';  // Ensure Ingredient model is registered
import '@/models/UOM';  // Ensure UOM model is registered
import '@/models/Location';  // Ensure Location model is registered
import Recipe from '@/models/Recipe';
import { Ingredient } from '@/models/Ingredient';
import Location from '@/models/Location';
import UOM from '@/models/UOM';

// Helper function to verify authentication
const verifyAuth = async () => {
  const cookieStore = await cookies();
  const authTokenCookie = cookieStore.get('auth-token');

  if (!authTokenCookie) {
    return { isAuthenticated: false, error: 'No auth-token cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(authTokenCookie.value, secret) as {
      id: string;
      email: string;
      companyId: string;
      role: string;
    };
    return { isAuthenticated: true, userId: decoded.id, companyId: decoded.companyId };
  } catch (_error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// Helper function to normalize catalog item data
function normalizeCatalogItem(
  item: any,
  sourceType: 'recipe' | 'ingredient',
  uoms: any[] = []
) {
  // Extract the most relevant selling detail
  const sellingDetail = item.sellingDetails && item.sellingDetails.length > 0 
    ? item.sellingDetails[0] 
    : null;
  
  // Find the UOM object - handle different field names between models
  let baseUom;
  if (sourceType === 'recipe') {
    // Recipe model uses baseYieldUOM
    baseUom = item.baseYieldUOM;
  } else {
    // Ingredient model uses baseUomId
    baseUom = item.baseUomId;
  }
  
  const unitOfSelling = sellingDetail ? sellingDetail.unitOfSelling : baseUom;
  const uom = uoms.find(u => u._id.toString() === unitOfSelling?.toString());
  
  return {
    id: item._id.toString(),
    name: item.name,
    description: item.description || "",
    type: sourceType.toUpperCase(),
    category: item.category || "Uncategorized",
    unitOfSelling: {
      id: unitOfSelling?.toString() || "",
      name: uom?.name || "Unit",
      shortCode: uom?.shortCode || "UN"
    },
    price: sellingDetail ? sellingDetail.priceWithoutTax || 0 : 0,
    priceWithTax: sellingDetail ? sellingDetail.priceWithTax || 0 : 0,
    taxRate: sellingDetail ? sellingDetail.taxRate || 0 : 0,
    currentStock: typeof item.currentStock === 'number' ? item.currentStock : 0,
    image: item.image || null,
    visibility: sellingDetail ? sellingDetail.visibility : {
      type: "ALL_LOCATIONS",
      externalAccess: false
    },
    lastUpdated: item.updatedAt || item.createdAt
  };
}

// Helper function to check if item is visible to a location
function isItemVisibleToLocation(item: any, locationId: string): boolean {
  if (!item.sellingDetails || item.sellingDetails.length === 0) {
    return false;
  }

  // Check visibility for any selling detail
  for (const detail of item.sellingDetails) {
    if (!detail.visibility) continue;
    
    const { type, locations = [] } = detail.visibility;
    
    if (type === 'ALL_LOCATIONS') {
      return true;
    }
    
    if (type === 'SPECIFIC_LOCATIONS' && locations.includes(locationId)) {
      return true;
    }
    
    if (type === 'EXCLUDE_LOCATIONS' && !locations.includes(locationId)) {
      return true;
    }
  }
  
  return false;
}

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Verify authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json(
        { error: 'Authentication failed', details: authResult.error },
        { status: 401 }
      );
    }

    // Get parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    // Validate company access
    try {
      await validateCompanyAccess(companyId, requestCompanyId);
    } catch (error: any) {
      return Response.json(
        { error: 'Access denied', details: error.message },
        { status: 403 }
      );
    }

    // Connect to database
    await dbConnect();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const requestingLocationId = searchParams.get('locationId');
    const categoryFilter = searchParams.get('category');
    const typeFilter = searchParams.get('type'); // 'RECIPE', 'INGREDIENT', or null for all
    const searchQuery = searchParams.get('search')?.trim();
    const centralKitchenId = searchParams.get('centralKitchenId'); // ID of the central kitchen

    // Identify if location is a kitchen
    let isCentralKitchen = false;
    let centralKitchen = null;
    
    console.log('[Catalog API] Searching for kitchen with params:', { 
      centralKitchenId, 
      companyId,
      requestingLocationId
    });
    
    if (centralKitchenId) {
      try {
        // Log the exact query
        console.log('[Catalog API] Looking for kitchen with query:', { 
          _id: centralKitchenId, 
          companyId: companyId
        });
        
        centralKitchen = await Location.findOne({
          _id: new Types.ObjectId(centralKitchenId),
          companyId: new Types.ObjectId(companyId)
        });
        
        console.log('[Catalog API] Kitchen search result:', centralKitchen ? 'Found' : 'Not found');
        if (centralKitchen) {
          console.log('[Catalog API] Kitchen details:', { 
            id: centralKitchen._id.toString(), 
            name: centralKitchen.name, 
            type: centralKitchen.locationType 
          });
        }
      } catch (error: unknown) {
        console.error('[Catalog API] Error finding kitchen:', error);
      }
      
      if (!centralKitchen) {
        return Response.json(
          { error: 'Central kitchen location not found' },
          { status: 404 }
        );
      }
      
      // Check if location is a valid kitchen type ('KITCHEN' or 'CENTRAL_KITCHEN')
      isCentralKitchen = centralKitchen.locationType === 'KITCHEN' || centralKitchen.locationType === 'CENTRAL_KITCHEN';
      console.log('[Catalog API] Kitchen type check:', { 
        locationType: centralKitchen.locationType,
        isValidKitchen: isCentralKitchen
      });
    } else {
      // Find the first central kitchen location if none specified
      console.log('[Catalog API] No centralKitchenId provided, looking for any central kitchen location');
      centralKitchen = await Location.findOne({
        companyId: new Types.ObjectId(companyId),
        locationType: { $in: ['KITCHEN', 'CENTRAL_KITCHEN'] }
      });
      
      console.log('[Catalog API] Fallback kitchen search result:', centralKitchen ? 'Found' : 'Not found');
      if (centralKitchen) {
        console.log('[Catalog API] Found fallback kitchen:', {
          id: centralKitchen._id.toString(),
          name: centralKitchen.name,
          type: centralKitchen.locationType
        });
        isCentralKitchen = true;
      }
    }
    
    console.log('[Catalog API] Final kitchen check:', { 
      isCentralKitchen, 
      locationName: centralKitchen?.name,
      locationType: centralKitchen?.locationType 
    });
    
    if (!isCentralKitchen) {
      console.log('[Catalog API] Rejecting request - location is not a kitchen type');
      return Response.json(
        { error: 'No central kitchen location found', details: `Location ${centralKitchen?.name} has type ${centralKitchen?.locationType} but requires type KITCHEN` },
        { status: 404 }
      );
    }

    // Get all UOMs for reference
    const uoms = await UOM.find({ companyId: new Types.ObjectId(companyId) });

    // Prepare filters
    const baseFilters = {
      companyId: new Types.ObjectId(companyId),
      canBeSold: true
    };
    
    const typeFilters: any = {};
    if (typeFilter) {
      typeFilters.itemType = typeFilter.toUpperCase();
    }
    
    // Find sellable recipes - note Recipe uses baseYieldUOM, not baseUomId
    const recipePromise = typeFilter && typeFilter.toUpperCase() === 'INGREDIENT' 
      ? Promise.resolve([]) 
      : Recipe.find({
          ...baseFilters,
          ...(categoryFilter ? { category: { $regex: categoryFilter, $options: 'i' } } : {}),
          ...(searchQuery ? { name: { $regex: searchQuery, $options: 'i' } } : {})
        }).populate('baseYieldUOM'); // Correct field name for Recipe model

    // Find sellable ingredients
    const ingredientPromise = typeFilter && typeFilter.toUpperCase() === 'RECIPE' 
      ? Promise.resolve([]) 
      : Ingredient.find({
          ...baseFilters,
          ...(categoryFilter ? { category: { $regex: categoryFilter, $options: 'i' } } : {}),
          ...(searchQuery ? { name: { $regex: searchQuery, $options: 'i' } } : {})
        }).populate('baseUomId'); // Correct field name for Ingredient model

    // Execute queries in parallel
    const [recipes, ingredients] = await Promise.all([recipePromise, ingredientPromise]);

    // Filter items by visibility if a requesting location is specified
    let visibleRecipes = recipes;
    let visibleIngredients = ingredients;
    
    if (requestingLocationId) {
      visibleRecipes = recipes.filter(recipe => 
        isItemVisibleToLocation(recipe, requestingLocationId)
      );
      
      visibleIngredients = ingredients.filter(ingredient => 
        isItemVisibleToLocation(ingredient, requestingLocationId)
      );
    }

    // Normalize the data
    const normalizedRecipes = visibleRecipes.map(recipe => 
      normalizeCatalogItem(recipe, 'recipe', uoms)
    );
    
    const normalizedIngredients = visibleIngredients.map(ingredient => 
      normalizeCatalogItem(ingredient, 'ingredient', uoms)
    );

    // Combined results
    const catalogItems = [...normalizedRecipes, ...normalizedIngredients];

    // Return the response
    return Response.json({
      success: true,
      centralKitchen: {
        id: centralKitchen._id.toString(),
        name: centralKitchen.name
      },
      items: catalogItems
    });
  } catch (error: any) {
    console.error('Error retrieving catalog:', error);
    return Response.json(
      { error: 'Failed to retrieve catalog', details: error.message },
      { status: 500 }
    );
  }
}
