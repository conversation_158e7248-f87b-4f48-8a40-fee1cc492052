// src/app/api/company/[companyId]/reports/transfer-revenue/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import mongoose, { Types } from 'mongoose';
import InventoryTransaction from '@/models/InventoryTransaction';
import Location from '@/models/Location';

// GET - Generate transfer revenue report
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Get companyId from URL parameters
    const params = await context.params;
    const urlCompanyId = params.companyId;

    // Authenticate and authorize
    const auth = await requireAuth()(req); // requireAuth will handle basic auth
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Tenant Isolation Check: Ensure the user belongs to the company they are querying
    if (auth.tenantId !== urlCompanyId) {
      console.warn(`[Report API] Tenant mismatch: User tenant ${auth.tenantId} != URL company ${urlCompanyId}`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Use the verified tenantId from auth for database operations
    const companyId = auth.tenantId; 
    
    // Connect to database
    await dbConnect();
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate') as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to last 30 days
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate') as string) : new Date();
    const sourceLocationId = searchParams.get('sourceLocationId');
    const destinationLocationId = searchParams.get('destinationLocationId');
    const itemType = searchParams.get('itemType');
    const groupBy = searchParams.get('groupBy') || 'day'; // day, week, month
    
    // Add one day to end date to include the entire end date
    endDate.setDate(endDate.getDate() + 1);
    
    // Interface for MongoDB query match stage
    interface MatchStage {
      'metadata.companyId': Types.ObjectId;
      'metadata.transactionType': string;
      timestamp: { $gte: Date; $lt: Date };
      transferMarkup?: { $exists: boolean };
      'metadata.locationId'?: Types.ObjectId;
      'destinationLocationId'?: Types.ObjectId;
      'metadata.itemType'?: string;
    }
    
    // Build match stage using the verified tenantId from auth
    const matchStage: MatchStage = {
      'metadata.companyId': new Types.ObjectId(companyId),
      'metadata.transactionType': 'TRANSFER_OUT',
      timestamp: { $gte: startDate, $lt: endDate },
    };
    
    // Check for markup filter parameter
    const markupFilter = searchParams.get('markupFilter'); // 'with', 'without', or null/undefined for all
    if (markupFilter === 'with') {
      matchStage.transferMarkup = { $exists: true };
      console.log('Filtering for transfers WITH markup');
    } else if (markupFilter === 'without') {
      // Query for documents where transferMarkup field doesn't exist or is null
      matchStage.transferMarkup = { $exists: false };
      console.log('Filtering for transfers WITHOUT markup');
    }
    
    console.log('Query date range:', { startDate, endDate });
    
    // Add debugging to check if ANY transfers exist and verify transferMarkup field
    const basicTransfers = await InventoryTransaction.find({
      'metadata.companyId': new Types.ObjectId(companyId),
      'metadata.transactionType': 'TRANSFER_OUT'
    }).sort({ createdAt: -1 }).limit(10);
    
    console.log(`DEBUG: Found ${basicTransfers.length} transfers total`);
    if (basicTransfers.length > 0) {
      console.log('Sample transfer fields:', Object.keys(basicTransfers[0].toObject()));
      console.log('Has transferMarkup field:', basicTransfers[0].toObject().hasOwnProperty('transferMarkup'));
      
      // Check recent transfers for markup field more thoroughly
      console.log('\nDETAILED TRANSFER ANALYSIS:');
      let transfersWithMarkup = 0;
      for (let i = 0; i < Math.min(basicTransfers.length, 10); i++) {
        const transfer = basicTransfers[i].toObject();
        const hasMarkup = transfer.hasOwnProperty('transferMarkup') && transfer.transferMarkup !== null;
        console.log(`- Transfer ${i+1} (${transfer._id}): Has markup = ${hasMarkup}`);
        if (hasMarkup) {
          transfersWithMarkup++;
          console.log(`  Markup details: ${JSON.stringify(transfer.transferMarkup)}`);
        }
      }
      console.log(`SUMMARY: Found ${transfersWithMarkup} out of ${basicTransfers.length} recent transfers with markup\n`);
    }
    
    if (sourceLocationId) {
      matchStage['metadata.locationId'] = new Types.ObjectId(sourceLocationId);
    }
    
    if (destinationLocationId) {
      matchStage['destinationLocationId'] = new Types.ObjectId(destinationLocationId);
    }
    
    if (itemType) {
      matchStage['metadata.itemType'] = itemType;
    }
    
    // Type for MongoDB date grouping formats
    type DateFormat = 
      | { $week: string }
      | { $month: string }
      | { $dateToString: { format: string; date: string } };
    
    // Determine group by format
    let dateFormat: DateFormat;
    switch (groupBy) {
      case 'week':
        dateFormat = { $week: '$timestamp' };
        break;
      case 'month':
        dateFormat = { $month: '$timestamp' };
        break;
      case 'day':
      default:
        dateFormat = { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } };
        break;
    }
    
    // Aggregate transfer revenue
    interface TransferRevenueGroup {
      _id: {
        date: string;
        sourceLocationId: Types.ObjectId;
        destinationLocationId: Types.ObjectId;
        itemType: string;
      };
      sourceName: string;
      destinationName: string;
      transactionCount: number;
      totalQuantity: number;
      totalRevenue: number;
      totalValueOriginal: number;
      totalValueMarkedUp: number;
      transactionsWithMarkup: number;
      transactionsWithoutMarkup: number;
      markupPercentage?: number; // Average markup percentage for this group
      transactions: any[];
    }
    
    const transferRevenue: TransferRevenueGroup[] = await InventoryTransaction.aggregate([
      { $match: matchStage },
      { 
        $lookup: {
          from: 'locations',
          localField: 'metadata.locationId',
          foreignField: '_id',
          as: 'sourceLocation'
        }
      },
      { 
        $lookup: {
          from: 'locations',
          localField: 'destinationLocationId',
          foreignField: '_id',
          as: 'destinationLocation'
        }
      },
      {
        $addFields: {
          sourceName: { $arrayElemAt: ['$sourceLocation.name', 0] },
          destinationName: { $arrayElemAt: ['$destinationLocation.name', 0] },
          
          // IMPROVED MARKUP DETECTION - better handle null/undefined values and structural differences
          hasTransferMarkup: { 
            $cond: { 
              if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
              then: true, 
              else: false 
            } 
          },
          
          // Calculate cost values with better null handling
          baseCost: { $ifNull: ['$cost', 0] },  // Base cost regardless of markup
          
          // Extract markup values with safer conditional logic
          markupOriginalCost: { 
            $cond: { 
              if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
              then: { $ifNull: ["$transferMarkup.originalCost", 0] }, 
              else: 0 
            } 
          },
          markupFinalCost: { 
            $cond: { 
              if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
              then: { $ifNull: ["$transferMarkup.markedUpCost", 0] }, 
              else: 0 
            } 
          },
          
          // Calculate effective cost values (which one to use)
          effectiveOriginalCost: { 
            $cond: { 
              if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
              then: { $ifNull: ["$transferMarkup.originalCost", { $ifNull: ['$cost', 0] }] }, 
              else: { $ifNull: ['$cost', 0] } 
            } 
          },
          effectiveMarkedUpCost: { 
            $cond: { 
              if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
              then: { $ifNull: ["$transferMarkup.markedUpCost", { $ifNull: ['$cost', 0] }] }, 
              else: { $ifNull: ['$cost', 0] } 
            } 
          },
          
          // Calculate revenue metrics with improved null handling and type safety
          revenue: { 
            $cond: {
              if: { $eq: [{ $type: "$transferMarkup" }, "object"] },
              then: { 
                $multiply: [
                  { $abs: { $ifNull: ['$difference', 0] } }, // Quantity with safety
                  {
                    $subtract: [
                      { $ifNull: ["$transferMarkup.markedUpCost", 0] },
                      { $ifNull: ["$transferMarkup.originalCost", 0] }
                    ]
                  }
                ]
              },
              else: 0 // No markup, no revenue
            }
          },
          
          // Compute total values with better null handling
          totalValueOriginal: {
            $multiply: [
              { $abs: { $ifNull: ['$difference', 0] } },
              { 
                $cond: { 
                  if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
                  then: { $ifNull: ["$transferMarkup.originalCost", { $ifNull: ['$cost', 0] }] }, 
                  else: { $ifNull: ['$cost', 0] } 
                } 
              }
            ]
          },
          totalValueMarkedUp: {
            $multiply: [
              { $abs: { $ifNull: ['$difference', 0] } },
              { 
                $cond: { 
                  if: { $eq: [{ $type: "$transferMarkup" }, "object"] }, 
                  then: { $ifNull: ["$transferMarkup.markedUpCost", { $ifNull: ['$cost', 0] }] }, 
                  else: { $ifNull: ['$cost', 0] } 
                } 
              }
            ]
          },
          
          // Debug values to help troubleshoot
          debugFields: {
            transferMarkupType: { $type: "$transferMarkup" },
            hasOriginalCost: { $cond: { if: { $gt: [{ $ifNull: ["$transferMarkup.originalCost", 0] }, 0] }, then: true, else: false } },
            hasMarkedUpCost: { $cond: { if: { $gt: [{ $ifNull: ["$transferMarkup.markedUpCost", 0] }, 0] }, then: true, else: false } },
            quantity: { $abs: { $ifNull: ['$difference', 0] } }
          }
        }
      },
      {
        $group: {
          _id: {
            date: dateFormat,
            sourceLocationId: '$metadata.locationId',
            destinationLocationId: '$destinationLocationId',
            itemType: '$metadata.itemType'
          },
          sourceName: { $first: '$sourceName' },
          destinationName: { $first: '$destinationName' },
          transactionCount: { $sum: 1 },
          totalQuantity: { $sum: { $abs: { $ifNull: ['$difference', 0] } } },
          totalRevenue: { $sum: { $ifNull: ['$revenue', 0] } },
          totalValueOriginal: { $sum: { $ifNull: ['$totalValueOriginal', 0] } },
          totalValueMarkedUp: { $sum: { $ifNull: ['$totalValueMarkedUp', 0] } },
          transactionsWithMarkup: { $sum: { $cond: ['$hasTransferMarkup', 1, 0] } },
          transactionsWithoutMarkup: { $sum: { $cond: ['$hasTransferMarkup', 0, 1] } },
          markupPercentage: {
            $avg: {
              $cond: [
                { $and: [
                  '$hasTransferMarkup',
                  { $eq: [{ $ifNull: ['$transferMarkup.markupType', 'NONE'] }, 'PERCENTAGE'] }
                ]},
                { $ifNull: ['$transferMarkup.markupValue', 0] },
                null
              ]
            }
          },
          debugInfo: {
            $push: {
              hasTransferMarkup: '$hasTransferMarkup',
              markupType: { $ifNull: ['$transferMarkup.markupType', 'NONE'] },
              markupOriginalCost: '$markupOriginalCost',
              markupFinalCost: '$markupFinalCost',
              revenue: '$revenue',
              debugFields: '$debugFields'
            }
          },
          transactions: {
            $push: {
              _id: '$_id',
              timestamp: '$timestamp',
              itemId: '$metadata.itemId',
              itemType: '$metadata.itemType',
              quantity: { $abs: { $ifNull: ['$difference', 0] } },
              hasMarkup: '$hasTransferMarkup',
              originalCost: '$effectiveOriginalCost',
              markedUpCost: '$effectiveMarkedUpCost',
              markupType: { $ifNull: ['$transferMarkup.markupType', 'NONE'] },
              markupValue: { $ifNull: ['$transferMarkup.markupValue', 0] },
              revenue: { $ifNull: ['$revenue', 0] }
            }
          }
        }
      },
      {
        $sort: {
          '_id.date': 1,
          '_id.sourceLocationId': 1,
          '_id.destinationLocationId': 1,
          '_id.itemType': 1
        }
      }
    ]);
    
    interface DestinationTotal {
      locationId: string;
      name: string;
      revenue: number;
      transactions: number;
    }

    interface CategoryTotal {
      itemType: string;
      revenue: number;
      transactions: number;
    }
    
    interface ReportSummary {
      totalRevenue: number;
      totalTransactions: number;
      totalQuantity: number;
      totalValueOriginal: number;
      totalValueMarkedUp: number;
      averageMarkupPercentage: number;
      transactionsWithMarkup: number;
      transactionsWithoutMarkup: number;
      markupPercentage: number;
      topDestinations: DestinationTotal[];
      topCategories: CategoryTotal[];
    }
    
    // Generate summary statistics
    const summary: ReportSummary = {
      totalRevenue: 0,
      totalTransactions: 0,
      totalQuantity: 0,
      totalValueOriginal: 0,
      totalValueMarkedUp: 0,
      averageMarkupPercentage: 0,
      transactionsWithMarkup: 0,
      transactionsWithoutMarkup: 0,
      markupPercentage: 0,
      topDestinations: [],
      topCategories: []
    };
    
    if (transferRevenue.length > 0) {
      // Calculate overall totals
      for (const group of transferRevenue) {
        summary.totalRevenue += group.totalRevenue;
        summary.totalTransactions += group.transactionCount;
        summary.totalQuantity += group.totalQuantity;
        summary.totalValueOriginal += group.totalValueOriginal;
        summary.totalValueMarkedUp += group.totalValueMarkedUp;
        
        // Track transfers with and without markup
        if (group.transactionsWithMarkup) {
          summary.transactionsWithMarkup += group.transactionsWithMarkup;
        }
        if (group.transactionsWithoutMarkup) {
          summary.transactionsWithoutMarkup += group.transactionsWithoutMarkup;
        }
      }
      
      // Calculate average markup percentage
      summary.averageMarkupPercentage = summary.totalValueMarkedUp / summary.totalValueOriginal - 1;
      
      // Calculate overall markup percentage
      if (summary.transactionsWithMarkup > 0) {
        summary.markupPercentage = (summary.transactionsWithMarkup / summary.totalTransactions) * 100;
      }
      
      // DestinationTotal interface already defined above

      // Aggregate by destination for top destinations
      const destinationTotals: Record<string, DestinationTotal> = {};
      for (const group of transferRevenue) {
        const destId = group._id.destinationLocationId.toString();
        if (!destinationTotals[destId]) {
          destinationTotals[destId] = {
            locationId: destId,
            name: group.destinationName,
            revenue: 0,
            transactions: 0
          };
        }
        destinationTotals[destId].revenue += group.totalRevenue;
        destinationTotals[destId].transactions += group.transactionCount;
      }
      
      // Sort destinations by revenue
      summary.topDestinations = Object.values(destinationTotals)
        .sort((a: DestinationTotal, b: DestinationTotal) => b.revenue - a.revenue)
        .slice(0, 5);
      
      // CategoryTotal interface already defined above

      // Aggregate by item type for top categories
      const categoryTotals: Record<string, CategoryTotal> = {};
      for (const group of transferRevenue) {
        const itemType = group._id.itemType;
        if (!categoryTotals[itemType]) {
          categoryTotals[itemType] = {
            itemType,
            revenue: 0,
            transactions: 0
          };
        }
        categoryTotals[itemType].revenue += group.totalRevenue;
        categoryTotals[itemType].transactions += group.transactionCount;
      }
      
      // Sort categories by revenue
      summary.topCategories = Object.values(categoryTotals)
        .sort((a: CategoryTotal, b: CategoryTotal) => b.revenue - a.revenue);
    }
    
    // Return response with detailed debug information
    return NextResponse.json({
      summary,
      details: transferRevenue,
      debug: {
        sampleTransfers: basicTransfers.slice(0, 5).map(t => ({
          id: (typeof t._id === 'string' ? t._id : (t._id as Types.ObjectId).toString()),
          date: t.timestamp,
          hasMarkupField: t.toObject().hasOwnProperty('transferMarkup'),
          transferMarkup: t.transferMarkup || 'null',
          metadata: t.metadata,
          difference: t.difference,
          previousStock: t.previousStock,
          newStock: t.newStock
        })),
        transactionCount: basicTransfers.length,
        query: {
          startDate,
          endDate,
          sourceLocationId: sourceLocationId || 'all',
          destinationLocationId: destinationLocationId || 'all'
        }
      }
    }, { status: 200 });
    
  } catch (error: unknown) {
    console.error('Error generating transfer revenue report:', error);
    // Check if it's an authentication/authorization error from requireAuth
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 }); // Or 403 depending on context
    }
    return NextResponse.json({ 
      error: 'Failed to generate transfer revenue report',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
