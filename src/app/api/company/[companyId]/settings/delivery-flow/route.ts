import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';
import dbConnect from '@/lib/db';
import CompanySettings from '@/models/CompanySettings';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// Helper function to verify authentication
const verifyAuth = async (request: NextRequest) => {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session');

  if (!sessionCookie) {
    return { isAuthenticated: false, error: 'No session cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionCookie.value, secret) as {
      userId: string;
      companyId?: string;
    };
    return { isAuthenticated: true, userId: decoded.userId, companyId: decoded.companyId };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

interface HandoverStep {
  stepType: 'DISPATCH' | 'QUALITY_CHECK' | 'DRIVER' | 'SECURITY' | 'SHOP' | 'CUSTOMER';
  required: boolean;
}

interface DeliveryFlowSettings {
  handoverSteps: HandoverStep[];
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Get and validate company ID
    const { companyId } = await context.params;
    const headerCompanyId = request.headers.get('company-id');

    // Verify authentication
    const authResult = await verifyAuth(request);
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error || 'Unauthorized' }, { status: 401 });
    }

    // Validate company access
    await validateCompanyAccess(companyId, headerCompanyId);

    // Connect to database
    await dbConnect();

    // Get company settings
    let settings = await CompanySettings.findOne({ companyId: new Types.ObjectId(companyId) });

    // If no settings exist, create default settings
    if (!settings) {
      settings = await CompanySettings.create({
        companyId: new Types.ObjectId(companyId),
        handoverSteps: [
          { stepType: 'DISPATCH', required: true },
          { stepType: 'DRIVER', required: true },
          { stepType: 'SHOP', required: true },
        ],
      });
    }

    return NextResponse.json(settings);
  } catch (error: any) {
    console.error('Error in GET /api/company/[companyId]/settings/delivery-flow:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Get and validate company ID
    const { companyId } = await context.params;
    const headerCompanyId = request.headers.get('company-id');

    // Verify authentication
    const authResult = await verifyAuth(request);
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error || 'Unauthorized' }, { status: 401 });
    }

    // Validate company access
    await validateCompanyAccess(companyId, headerCompanyId);

    // Connect to database
    await dbConnect();

    // Get request body
    const updates: DeliveryFlowSettings = await request.json();

    // Validate handover steps
    if (!Array.isArray(updates.handoverSteps)) {
      return NextResponse.json(
        { error: 'Invalid handover steps format' },
        { status: 400 }
      );
    }

    // Validate step types
    const validStepTypes = ['DISPATCH', 'QUALITY_CHECK', 'DRIVER', 'SECURITY', 'SHOP', 'CUSTOMER'];
    const invalidSteps = updates.handoverSteps.filter(
      step => !validStepTypes.includes(step.stepType)
    );

    if (invalidSteps.length > 0) {
      return NextResponse.json(
        { error: `Invalid step types: ${invalidSteps.map(s => s.stepType).join(', ')}` },
        { status: 400 }
      );
    }

    // Update settings
    const settings = await CompanySettings.findOneAndUpdate(
      { companyId: new Types.ObjectId(companyId) },
      { handoverSteps: updates.handoverSteps },
      { new: true, upsert: true }
    );

    return NextResponse.json(settings);
  } catch (error: any) {
    console.error('Error in PATCH /api/company/[companyId]/settings/delivery-flow:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
