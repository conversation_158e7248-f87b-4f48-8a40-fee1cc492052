// src/app/api/company/[companyId]/inventory-count/[countId]/submit/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { hasRequiredCompanyRole } from '@/lib/server-auth';
import StockCount from '@/models/StockCount';
import { Types } from 'mongoose';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; countId: string }> }
) {
  try {
    const { companyId, countId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has appropriate role
    const authResult = await hasRequiredCompanyRole(request, companyId, 'storekeeper');
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Unauthorized' }, { status: 401 });
    }

    // Get the current stock count
    const stockCount = await StockCount.findOne({
      _id: new Types.ObjectId(countId),
      companyId: new Types.ObjectId(companyId)
    });

    if (!stockCount) {
      return NextResponse.json({ error: 'Inventory count not found' }, { status: 404 });
    }

    // Only allow submission of IN_PROGRESS counts
    if (stockCount.status !== 'IN_PROGRESS') {
      return NextResponse.json({
        error: `Cannot submit count with status: ${stockCount.status}`
      }, { status: 400 });
    }

    // Check if all items have been counted
    const uncountedItems = stockCount.items.filter((item: any) => item.countedStock === 0);
    if (uncountedItems.length > 0) {
      return NextResponse.json({
        error: 'Cannot submit count with uncounted items',
        uncountedCount: uncountedItems.length
      }, { status: 400 });
    }

    // Update the stock count status
    stockCount.status = 'PENDING_APPROVAL';
    await stockCount.save();

    return NextResponse.json({
      success: true,
      message: 'Inventory count submitted for approval',
      countId: stockCount._id
    });
  } catch (error: any) {
    console.error('Error submitting inventory count:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
