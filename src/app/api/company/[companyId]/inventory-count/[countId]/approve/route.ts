// src/app/api/company/[companyId]/inventory-count/[countId]/approve/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { hasRequiredCompanyRole } from '@/lib/server-auth';
import StockCount from '@/models/StockCount';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory';
import InventoryTransaction from '@/models/InventoryTransaction';
import mongoose, { Types } from 'mongoose';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; countId: string }> }
) {
  try {
    const { companyId, countId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has manager role or higher (only managers can approve)
    const authResult = await hasRequiredCompanyRole(request, companyId, 'manager');
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Unauthorized' }, { status: 401 });
    }

    // Get the request data
    const data = await request.json();
    const { approved, notes } = data;

    // Get the current stock count
    const stockCount = await StockCount.findOne({
      _id: new Types.ObjectId(countId),
      companyId: new Types.ObjectId(companyId)
    });

    if (!stockCount) {
      return NextResponse.json({ error: 'Inventory count not found' }, { status: 404 });
    }

    // Only allow approval of PENDING_APPROVAL counts
    if (stockCount.status !== 'PENDING_APPROVAL') {
      return NextResponse.json({
        error: `Cannot approve/reject count with status: ${stockCount.status}`
      }, { status: 400 });
    }

    // Start a session for atomic operations
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Update the stock count status
      stockCount.status = approved ? 'APPROVED' : 'REJECTED';
      stockCount.approvedBy = new Types.ObjectId(authResult.userId);
      stockCount.approvedAt = new Date();
      
      if (notes) {
        stockCount.notes = notes;
      }

      await stockCount.save({ session });

      // If approved, update inventory levels
      if (approved) {
        // Process each item in the count
        for (const item of stockCount.items) {
          // Get the current inventory record
          const inventory = await BranchInventory.findOne({
            companyId: stockCount.companyId,
            locationId: stockCount.locationId,
            itemId: item.itemId,
            itemType: item.itemType,
          }).session(session) as IBranchInventory | null;

          if (inventory) {
            // Create a snapshot before updating
            inventory.createSnapshot('STOCK_COUNT', stockCount._id);
            
            // Update the inventory with the counted value
            await BranchInventory.updateOne(
              { _id: inventory._id },
              {
                $set: {
                  currentStock: item.countedStock,
                  lastCountDate: stockCount.countDate,
                  isLocked: false,
                  unlockedAt: new Date(),
                  stockCountId: null,
                  lastUpdated: new Date()
                }
              },
              { session }
            );

            // Create inventory transaction record
            await InventoryTransaction.create([{
              timestamp: new Date(),
              metadata: {
                companyId: stockCount.companyId,
                locationId: stockCount.locationId,
                itemId: item.itemId,
                itemType: item.itemType,
                transactionType: 'COUNT',
                referenceId: stockCount._id,
                referenceType: 'STOCK_COUNT',
                userId: stockCount.approvedBy
              },
              previousStock: item.systemStock,
              newStock: item.countedStock,
              difference: item.difference,
              notes: item.notes || 'Inventory count adjustment',
              countBatch: stockCount._id.toString(),
              countStatus: 'APPROVED',
              approvedBy: stockCount.approvedBy,
              approvedAt: stockCount.approvedAt
            }], { session });
          }
        }
      } else {
        // If rejected, just unlock the inventory
        await BranchInventory.updateMany(
          {
            companyId: stockCount.companyId,
            locationId: stockCount.locationId,
            stockCountId: stockCount._id
          },
          {
            $set: {
              isLocked: false,
              unlockedAt: new Date(),
              stockCountId: null
            }
          },
          { session }
        );
      }

      await session.commitTransaction();

      return NextResponse.json({
        success: true,
        message: approved 
          ? 'Inventory count approved and stock levels updated' 
          : 'Inventory count rejected',
        countId: stockCount._id
      });
    } catch (error: any) {
      await session.abortTransaction();
      console.error('Error processing inventory count approval:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    } finally {
      session.endSession();
    }
  } catch (error: any) {
    console.error('Error processing inventory count approval:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
