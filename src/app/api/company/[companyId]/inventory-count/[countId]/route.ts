// src/app/api/company/[companyId]/inventory-count/[countId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { hasRequiredCompanyRole } from '@/lib/server-auth';
import StockCount from '@/models/StockCount';
import mongoose, { Types } from 'mongoose';

// GET endpoint to retrieve a specific inventory count
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; countId: string }> }
) {
  try {
    const { companyId, countId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has access to company
    const authResult = await hasRequiredCompanyRole(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Unauthorized' }, { status: 401 });
    }

    // Get the stock count with populated item details
    const stockCount = await StockCount.findOne({
      _id: new Types.ObjectId(countId),
      companyId: new Types.ObjectId(companyId)
    });

    if (!stockCount) {
      return NextResponse.json({ error: 'Inventory count not found' }, { status: 404 });
    }

    // Get item details (name, etc.) for each counted item
    const populatedCount = await StockCount.aggregate([
      { $match: { _id: new Types.ObjectId(countId) } },
      { $unwind: '$items' },
      {
        $lookup: {
          from: 'recipes',
          localField: 'items.itemId',
          foreignField: '_id',
          as: 'recipeDetails'
        }
      },
      {
        $lookup: {
          from: 'ingredients',
          localField: 'items.itemId',
          foreignField: '_id',
          as: 'ingredientDetails'
        }
      },
      {
        $addFields: {
          'items.itemName': {
            $cond: {
              if: { $eq: ['$items.itemType', 'RECIPE'] },
              then: { $arrayElemAt: ['$recipeDetails.name', 0] },
              else: { $arrayElemAt: ['$ingredientDetails.name', 0] }
            }
          },
          'items.itemCategory': {
            $cond: {
              if: { $eq: ['$items.itemType', 'RECIPE'] },
              then: { $arrayElemAt: ['$recipeDetails.category', 0] },
              else: { $arrayElemAt: ['$ingredientDetails.category', 0] }
            }
          }
        }
      },
      {
        $group: {
          _id: '$_id',
          companyId: { $first: '$companyId' },
          locationId: { $first: '$locationId' },
          status: { $first: '$status' },
          countDate: { $first: '$countDate' },
          startedBy: { $first: '$startedBy' },
          approvedBy: { $first: '$approvedBy' },
          approvedAt: { $first: '$approvedAt' },
          notes: { $first: '$notes' },
          items: { $push: '$items' },
          isLocked: { $first: '$isLocked' },
          lockedAt: { $first: '$lockedAt' },
          unlockedAt: { $first: '$unlockedAt' }
        }
      }
    ]);

    if (!populatedCount || populatedCount.length === 0) {
      return NextResponse.json({ error: 'Failed to retrieve count details' }, { status: 500 });
    }

    return NextResponse.json(populatedCount[0]);
  } catch (error: any) {
    console.error('Error retrieving inventory count:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// PUT endpoint to update an inventory count
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; countId: string }> }
) {
  try {
    const { companyId, countId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has appropriate role
    const authResult = await hasRequiredCompanyRole(request, companyId, 'storekeeper');
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Unauthorized' }, { status: 401 });
    }

    // Get the request data
    const data = await request.json();
    const { items, notes } = data;

    // Validate data
    if (!items || !Array.isArray(items)) {
      return NextResponse.json({ error: 'Invalid items data' }, { status: 400 });
    }

    // Get the current stock count
    const stockCount = await StockCount.findOne({
      _id: new Types.ObjectId(countId),
      companyId: new Types.ObjectId(companyId)
    });

    if (!stockCount) {
      return NextResponse.json({ error: 'Inventory count not found' }, { status: 404 });
    }

    // Only allow updates to IN_PROGRESS counts
    if (stockCount.status !== 'IN_PROGRESS') {
      return NextResponse.json({
        error: `Cannot update count with status: ${stockCount.status}`
      }, { status: 400 });
    }

    // Start a session for atomic operations
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Process each submitted item
      for (const item of items) {
        const { itemId, countedStock, notes: itemNotes } = item;
        
        // Find the matching item in the stock count
        const existingItemIndex = stockCount.items.findIndex(
          (i: any) => i.itemId.toString() === itemId
        );

        if (existingItemIndex === -1) {
          continue; // Skip items not in the original count
        }

        // Update the counted stock and calculate difference
        const existingItem = stockCount.items[existingItemIndex];
        existingItem.countedStock = countedStock;
        existingItem.difference = countedStock - existingItem.systemStock;
        
        if (itemNotes) {
          existingItem.notes = itemNotes;
        }
      }

      // Update notes if provided
      if (notes) {
        stockCount.notes = notes;
      }

      // Save the updated stock count
      await stockCount.save({ session });
      await session.commitTransaction();
      
      return NextResponse.json({
        success: true,
        message: 'Inventory count updated successfully',
        countId: stockCount._id
      });
    } catch (error: any) {
      await session.abortTransaction();
      console.error('Error updating inventory count:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    } finally {
      session.endSession();
    }
  } catch (error: any) {
    console.error('Error updating inventory count:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
