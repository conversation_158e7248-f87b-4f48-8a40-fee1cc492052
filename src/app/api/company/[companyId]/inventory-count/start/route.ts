// src/app/api/company/[companyId]/inventory-count/start/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { hasRequiredCompanyRole } from '@/lib/server-auth';
import StockCount from '@/models/StockCount';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory';
import mongoose, { Types } from 'mongoose';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params;
    await dbConnect();

    // Validate company access with required role (storekeeper or higher)
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has access and appropriate role
    const authResult = await hasRequiredCompanyRole(request, companyId, 'storekeeper');
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { locationId, notes, categoryFilter, itemTypeFilter } = data;

    // Validate required fields
    if (!locationId) {
      return NextResponse.json({ error: 'Location ID is required' }, { status: 400 });
    }

    // Start a session for atomic operations
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Check if there's already an in-progress count for this location
      const existingCount = await StockCount.findOne({
        companyId: new Types.ObjectId(companyId),
        locationId: new Types.ObjectId(locationId),
        status: 'IN_PROGRESS'
      }).session(session);

      if (existingCount) {
        await session.abortTransaction();
        session.endSession();
        return NextResponse.json({
          error: 'There is already an in-progress inventory count for this location',
          existingCountId: existingCount._id
        }, { status: 400 });
      }

      // Get inventory items based on filters
      const inventoryQuery: any = {
        companyId: new Types.ObjectId(companyId),
        locationId: new Types.ObjectId(locationId),
        isActive: true
      };

      if (itemTypeFilter && ['RECIPE', 'INGREDIENT', 'ALL'].includes(itemTypeFilter)) {
        if (itemTypeFilter !== 'ALL') {
          inventoryQuery.itemType = itemTypeFilter;
        }
      }

      if (categoryFilter) {
        inventoryQuery.category = categoryFilter;
      }

      const inventoryItems = await BranchInventory.find(inventoryQuery)
        .session(session) as unknown as IBranchInventory[];

      // Create count items array
      const items = inventoryItems.map(item => ({
        itemId: item.itemId,
        itemType: item.itemType,
        sellingOptionId: item.sellingOptionId || 'DEFAULT',
        systemStock: item.currentStock,
        countedStock: 0, // Will be filled during the count
        difference: -item.currentStock, // Initial difference (negative of current stock)
        baseUomId: item.baseUomId
      }));

      // Create the stock count record
      const stockCount = await StockCount.create([{
        companyId: new Types.ObjectId(companyId),
        locationId: new Types.ObjectId(locationId),
        status: 'IN_PROGRESS',
        countDate: new Date(),
        startedBy: new Types.ObjectId(authResult.userId),
        notes,
        items,
        isLocked: true,
        lockedAt: new Date()
      }], { session });

      // Optionally lock the inventory for counting
      await BranchInventory.updateMany(
        inventoryQuery,
        {
          $set: {
            isLocked: true,
            lockedBy: new Types.ObjectId(authResult.userId),
            lockedAt: new Date(),
            stockCountId: stockCount[0]._id
          }
        },
        { session }
      );

      await session.commitTransaction();
      session.endSession();

      return NextResponse.json({
        success: true,
        countId: stockCount[0]._id,
        itemCount: items.length,
        message: 'Inventory count started successfully'
      });
    } catch (error: any) {
      await session.abortTransaction();
      session.endSession();
      console.error('Error starting inventory count:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Inventory count error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
