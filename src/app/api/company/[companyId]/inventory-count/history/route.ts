// src/app/api/company/[companyId]/inventory-count/history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { isValidCompanyUser } from '@/lib/server-auth';
import StockCount from '@/models/StockCount';
import mongoose, { Types } from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has access to company
    const authResult = await isValidCompanyUser(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get('locationId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const skip = (page - 1) * limit;

    // Build query object
    const queryObj: any = {
      companyId: new Types.ObjectId(companyId)
    };

    if (locationId) {
      queryObj.locationId = new Types.ObjectId(locationId);
    }

    if (status && ['IN_PROGRESS', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'CANCELLED'].includes(status)) {
      queryObj.status = status;
    }

    // Date filtering
    if (startDate || endDate) {
      queryObj.countDate = {};
      
      if (startDate) {
        queryObj.countDate.$gte = new Date(startDate);
      }
      
      if (endDate) {
        // Set time to end of day for end date
        const endDateObj = new Date(endDate);
        endDateObj.setHours(23, 59, 59, 999);
        queryObj.countDate.$lte = endDateObj;
      }
    }

    // Get total count for pagination
    const totalCount = await StockCount.countDocuments(queryObj);

    // Lookup location and user information
    const stockCounts = await StockCount.aggregate([
      { $match: queryObj },
      { $sort: { countDate: -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'locations',
          localField: 'locationId',
          foreignField: '_id',
          as: 'locationDetails'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'startedBy',
          foreignField: '_id',
          as: 'startedByUser'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'approvedBy',
          foreignField: '_id',
          as: 'approvedByUser'
        }
      },
      {
        $addFields: {
          locationName: { $arrayElemAt: ['$locationDetails.name', 0] },
          startedByName: { 
            $concat: [
              { $arrayElemAt: ['$startedByUser.firstName', 0] }, 
              ' ', 
              { $arrayElemAt: ['$startedByUser.lastName', 0] }
            ]
          },
          approvedByName: { 
            $cond: {
              if: { $gt: [{ $size: '$approvedByUser' }, 0] },
              then: {
                $concat: [
                  { $arrayElemAt: ['$approvedByUser.firstName', 0] }, 
                  ' ', 
                  { $arrayElemAt: ['$approvedByUser.lastName', 0] }
                ]
              },
              else: ''
            }
          },
          itemCount: { $size: '$items' },
          // Calculate summary stats
          totalSystemStock: { $sum: '$items.systemStock' },
          totalCountedStock: { $sum: '$items.countedStock' },
          totalDifference: { $sum: '$items.difference' },
          // Count variance items
          varianceItems: {
            $size: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $ne: ['$$item.difference', 0] }
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          companyId: 1,
          locationId: 1,
          locationName: 1,
          status: 1,
          countDate: 1,
          startedBy: 1,
          startedByName: 1,
          approvedBy: 1,
          approvedByName: 1,
          approvedAt: 1,
          notes: 1,
          itemCount: 1,
          varianceItems: 1,
          totalSystemStock: 1,
          totalCountedStock: 1,
          totalDifference: 1,
          isLocked: 1,
          lockedAt: 1,
          unlockedAt: 1
        }
      }
    ]);

    return NextResponse.json({
      counts: stockCounts,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error: any) {
    console.error('Error fetching inventory count history:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
