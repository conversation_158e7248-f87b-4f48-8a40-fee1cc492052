// src/app/api/company/[companyId]/markup-rules/route.ts
import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import dbConnect from '@/lib/db';
import { Types } from 'mongoose';
import MarkupRule from '@/models/MarkupRule';

// Helper function to verify authentication
const verifyAuth = async () => {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session');

  if (!sessionCookie) {
    return { isAuthenticated: false, error: 'No session cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionCookie.value, secret) as {
      userId: string;
      companyId?: string;
    };
    return { isAuthenticated: true, userId: decoded.userId, companyId: decoded.companyId };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// GET - Fetch markup rules
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const categoryFilter = searchParams.get('category');
    const locationFilter = searchParams.get('location');
    const itemTypeFilter = searchParams.get('itemType');
    
    // Build query
    const query: any = { companyId: new Types.ObjectId(companyId) };
    
    if (activeOnly) {
      query.isActive = true;
    }
    
    // Add conditional filters if provided
    const conditionFilters = [];
    
    if (categoryFilter) {
      conditionFilters.push({
        conditions: {
          $elemMatch: {
            type: 'CATEGORY',
            value: categoryFilter
          }
        }
      });
    }
    
    if (locationFilter) {
      conditionFilters.push({
        conditions: {
          $elemMatch: {
            type: 'LOCATION',
            value: locationFilter
          }
        }
      });
    }
    
    if (itemTypeFilter) {
      conditionFilters.push({
        conditions: {
          $elemMatch: {
            type: 'ITEM_TYPE',
            value: itemTypeFilter
          }
        }
      });
    }
    
    // Add condition filters to main query if any exist
    if (conditionFilters.length > 0) {
      query.$or = conditionFilters;
    }
    
    // Fetch markup rules
    const markupRules = await MarkupRule.find(query)
      .sort({ priority: 1, createdAt: -1 })
      .lean();
    
    // Return response
    return Response.json(markupRules, { status: 200 });
    
  } catch (error: unknown) {
    console.error('Error fetching markup rules:', error);
    return Response.json({ 
      error: 'Failed to fetch markup rules',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST - Create a new markup rule
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated || !authResult.userId) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.name || !data.markupType || data.markupValue === undefined || !data.conditions || !Array.isArray(data.conditions) || data.conditions.length === 0) {
      return Response.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Validate markup type and value
    if (!['PERCENTAGE', 'FIXED'].includes(data.markupType)) {
      return Response.json({ error: 'Invalid markup type' }, { status: 400 });
    }
    
    if (typeof data.markupValue !== 'number' || data.markupValue < 0) {
      return Response.json({ error: 'Markup value must be a non-negative number' }, { status: 400 });
    }
    
    // Validate each condition
    for (const condition of data.conditions) {
      if (!condition.type || !condition.value) {
        return Response.json({ error: 'Each condition must have a type and value' }, { status: 400 });
      }
      
      if (!['CATEGORY', 'LOCATION', 'ITEM_TYPE', 'SPECIFIC_ITEM'].includes(condition.type)) {
        return Response.json({ error: `Invalid condition type: ${condition.type}` }, { status: 400 });
      }
    }
    
    // Create new markup rule
    const markupRule = await MarkupRule.create({
      companyId: new Types.ObjectId(companyId),
      name: data.name,
      description: data.description || '',
      priority: data.priority || 10,
      conditions: data.conditions,
      markupType: data.markupType,
      markupValue: data.markupValue,
      applyTax: !!data.applyTax,
      isActive: data.isActive !== false, // Default to true if not specified
      createdBy: new Types.ObjectId(authResult.userId)
    });
    
    return Response.json({
      success: true,
      message: 'Markup rule created successfully',
      markupRule: markupRule.toObject()
    }, { status: 201 });
    
  } catch (error: unknown) {
    console.error('Error creating markup rule:', error);
    return Response.json({ 
      error: 'Failed to create markup rule',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PATCH - Update an existing markup rule
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.ruleId) {
      return Response.json({ error: 'Rule ID is required' }, { status: 400 });
    }
    
    // Find the rule
    const markupRule = await MarkupRule.findOne({
      _id: new Types.ObjectId(data.ruleId),
      companyId: new Types.ObjectId(companyId)
    });
    
    if (!markupRule) {
      return Response.json({ error: 'Markup rule not found' }, { status: 404 });
    }
    
    // Update fields if provided
    if (data.name) markupRule.name = data.name;
    if (data.description !== undefined) markupRule.description = data.description;
    if (data.priority !== undefined) markupRule.priority = data.priority;
    if (data.markupType) {
      if (!['PERCENTAGE', 'FIXED'].includes(data.markupType)) {
        return Response.json({ error: 'Invalid markup type' }, { status: 400 });
      }
      markupRule.markupType = data.markupType;
    }
    if (data.markupValue !== undefined) {
      if (typeof data.markupValue !== 'number' || data.markupValue < 0) {
        return Response.json({ error: 'Markup value must be a non-negative number' }, { status: 400 });
      }
      markupRule.markupValue = data.markupValue;
    }
    if (data.applyTax !== undefined) markupRule.applyTax = data.applyTax;
    if (data.isActive !== undefined) markupRule.isActive = data.isActive;
    
    // Update conditions if provided
    if (data.conditions && Array.isArray(data.conditions)) {
      // Validate each condition
      for (const condition of data.conditions) {
        if (!condition.type || !condition.value) {
          return Response.json({ error: 'Each condition must have a type and value' }, { status: 400 });
        }
        
        if (!['CATEGORY', 'LOCATION', 'ITEM_TYPE', 'SPECIFIC_ITEM'].includes(condition.type)) {
          return Response.json({ error: `Invalid condition type: ${condition.type}` }, { status: 400 });
        }
      }
      
      markupRule.conditions = data.conditions;
    }
    
    // Save the updated rule
    await markupRule.save();
    
    return Response.json({
      success: true,
      message: 'Markup rule updated successfully',
      markupRule: markupRule.toObject()
    }, { status: 200 });
    
  } catch (error: unknown) {
    console.error('Error updating markup rule:', error);
    return Response.json({ 
      error: 'Failed to update markup rule',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE - Delete a markup rule
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const ruleId = searchParams.get('ruleId');
    
    if (!ruleId) {
      return Response.json({ error: 'Rule ID is required' }, { status: 400 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Find and delete the rule
    const result = await MarkupRule.deleteOne({
      _id: new Types.ObjectId(ruleId),
      companyId: new Types.ObjectId(companyId)
    });
    
    if (result.deletedCount === 0) {
      return Response.json({ error: 'Markup rule not found' }, { status: 404 });
    }
    
    return Response.json({
      success: true,
      message: 'Markup rule deleted successfully'
    }, { status: 200 });
    
  } catch (error: unknown) {
    console.error('Error deleting markup rule:', error);
    return Response.json({ 
      error: 'Failed to delete markup rule',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
