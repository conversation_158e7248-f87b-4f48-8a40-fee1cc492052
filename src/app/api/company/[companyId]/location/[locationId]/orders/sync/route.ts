// src/app/api/company/[companyId]/location/[locationId]/orders/sync/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { validateIonicAuth } from '@/lib/ionic-auth';
import { logSyncEvent } from '@/lib/sync-logger';
import dbConnect from '@/lib/db';
import Order from '@/models/Order';
import OrderSync from '@/models/OrderSync';
import DeliveryNote from '@/models/DeliveryNote';
import BranchInventory from '@/models/BranchInventory';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { applyCorsHeaders, handleCorsOptions } from '@/lib/cors';

/**
 * Order sync request payload format:
 * 
 * {
 *   syncId: string,
 *   orders: [
 *     {
 *       orderId: string,
 *       branchId: string,
 *       status: string,
 *       items: [
 *         {
 *           itemId: string,
 *           quantity: number,
 *           unitPrice: number,
 *         }
 *       ],
 *       createdAt: string (ISO datetime),
 *       metadata: {...}
 *     }
 *   ],
 *   syncTimestamp: string (ISO datetime)
 * }
 */

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  await dbConnect();
  
  try {
    // Get params from URL
    const params = await context.params;
    const { companyId, locationId } = params;
    
    // For development, temporarily skip authentication checks
    console.log('Authentication skipped for development - REMOVE IN PRODUCTION');
    
    /*
    // Validate authentication - UNCOMMENT FOR PRODUCTION
    const authResult = await validateIonicAuth(req);
    if (!authResult.isAuthenticated) {
      const response = NextResponse.json(
        { error: authResult.error || 'Authentication failed' },
        { status: 401 }
      );
      return applyCorsHeaders(response, req);
    }
    
    // Verify company access - UNCOMMENT FOR PRODUCTION
    const headerCompanyId = req.headers.get('company-id');
    if (!headerCompanyId || headerCompanyId !== companyId) {
      const response = NextResponse.json(
        { error: 'Company ID mismatch' },
        { status: 403 }
      );
      return applyCorsHeaders(response, req);
    }
    */
    
    // Parse request body
    const body = await req.json();
    const { syncId, orders, syncTimestamp } = body;
    
    if (!syncId || !Array.isArray(orders) || !syncTimestamp) {
      const response = NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
      return applyCorsHeaders(response, req);
    }
    
    // Create a sync record to track this operation
    const syncRecord = await OrderSync.create({
      companyId,
      locationId,
      syncId,
      status: 'PROCESSING',
      startTime: new Date(),
      ordersReceived: orders.length,
      ordersProcessed: 0,
      syncErrors: []
    });
    
    // Check for order number preservation flags in the request
    const globalPreserveOrderNumbers = 
      body.preserveOrderNumbers === true || 
      body.orderNumbersAreImportant === true || 
      body.doNotGenerateNewOrderNumbers === true;
      
    if (globalPreserveOrderNumbers) {
      console.log('Global order number preservation enabled');
    }
    
    // Process each order
    const results = [];
    const errors = [];
    const processedOrderIds = [];
    
    for (const orderData of orders) {
      try {
        // Basic validation
        if (!orderData.orderId || !Array.isArray(orderData.items) || orderData.items.length === 0) {
          throw new Error('Invalid order format');
        }
        
        // Determine order source and numbering strategy
        // Also check for ORD- prefix which indicates orders from Ionic
        const isFromIonicApp = orderData.orderSource === 'IONIC' || 
                              orderData.ionicOrderNumber || 
                              (orderData.orderNumber && orderData.orderNumber.startsWith('ORD-'));
        const isFromMainApp = !isFromIonicApp;
        
        // Check if we should preserve the original order number
        // Always preserve numbers for orders from Ionic
        const shouldPreserveOrderNumber =
          isFromIonicApp ||
          orderData.preserveOrderNumber === true ||
          body.preserveOrderNumbers === true ||
          body.orderNumbersAreImportant === true ||
          body.doNotGenerateNewOrderNumbers === true;
        
        // Determine the order number
        const originalOrderNumber = orderData.orderNumber || orderData.ionicOrderNumber;
        let orderNumber;
        
        // SIMPLIFIED LOGIC: Just two clear scenarios
        
        // Scenario 1: Order from Ionic POS (via sync) - ALWAYS keep the original order number
        if (isFromIonicApp && originalOrderNumber) {
          orderNumber = originalOrderNumber;
          console.log('Using original order number from Ionic POS:', originalOrderNumber);
        }
        // Scenario 2: Order created in main app - Use HQ prefix with date
        else {
          // Format: HQ-YYYYMMDD-XXXX where XXXX is a sequential number or random ID
          const today = new Date();
          const dateString = today.toISOString().split('T')[0].replace(/-/g, '');
          const randomId = Math.floor(1000 + Math.random() * 9000); // 4-digit number
          orderNumber = `HQ-${dateString}-${randomId}`;
          console.log('Generated new order number for main app:', orderNumber);
        }
        
        // Log the order number decision for debugging
        console.log('Order number decision:', {
          isFromIonicApp,
          isFromMainApp,
          shouldPreserveOrderNumber,
          originalOrderNumber,
          usedOrderNumber: orderNumber,
          wasPreserved: originalOrderNumber === orderNumber
        });

        // Modify the newOrder object to include source and original number
        const newOrder = {
          companyId,
          orderNumber,
          originalOrderNumber: originalOrderNumber, // Store the original order number
          orderSource: isFromIonicApp ? 'IONIC' : (isFromMainApp ? 'WEB' : 'UNKNOWN'),
          status: 'INCOMING', // Default status for Ionic orders
          syncStatus: 'SYNCED', // Set the sync status to SYNCED for new orders
          buyer: {
            buyerType: 'CUSTOMER',
            buyerId: new mongoose.Types.ObjectId(locationId) // The location is the buyer
          },
          sellerLocationId: new mongoose.Types.ObjectId(locationId), // The location is also the seller for branch orders
          // Process items more carefully
          items: Array.isArray(orderData.items) ? orderData.items.map((item: any) => {
            // Create safe object IDs, handling potential validation errors
            let itemId: any;
            let uomId: any;
            
            // Get uom object from the item if available
            const uomObject = item.uom || null;
            const uomShortCode = uomObject?.shortCode || 'unit';
            
            try {
              // First use the MongoDB ID from the UOM object if available
              if (uomObject && uomObject._id && typeof uomObject._id === 'string' && uomObject._id.length === 24) {
                uomId = new mongoose.Types.ObjectId(uomObject._id);
                console.log(`Using UOM ObjectId from UOM object: ${uomId}`);
              }
              // Then try the uomId directly if it's a valid MongoID 
              else if (item.uomId && typeof item.uomId === 'string' && item.uomId.length === 24) {
                uomId = new mongoose.Types.ObjectId(item.uomId);
                console.log(`Using UOM ObjectId from item.uomId: ${uomId}`);
              }
              // If neither is valid, use a default UOM
              else {
                console.log(`Using default UOM for item with invalid uomId: ${item.uomId}`);
                uomId = new mongoose.Types.ObjectId('6769b2e6fe8078cda3ce7d92'); // default UOM
              }
            } catch (e) {
              console.log('Invalid uomId:', item.uomId);
              uomId = new mongoose.Types.ObjectId('6769b2e6fe8078cda3ce7d92');
            }
            
            try {
              // Try to create ObjectId for itemId if valid
              if (item.itemId && typeof item.itemId === 'string' && item.itemId.length === 24) {
                itemId = new mongoose.Types.ObjectId(item.itemId);
                console.log(`Using valid itemId: ${itemId}`);
              } 
              // Otherwise log the issue but keep the original value as a string
              else {
                console.log(`Non-MongoDB format itemId: ${item.itemId}, preserving as-is`);
                itemId = item.itemId || new mongoose.Types.ObjectId('000000000000000000000000');
              }
            } catch (e) {
              console.log('Invalid itemId:', item.itemId);
              itemId = new mongoose.Types.ObjectId('000000000000000000000000');
            }
            
            return {
              itemType: item.itemType || 'INGREDIENT',
              itemId: itemId,
              description: item.description || 'Item from Ionic app',
              quantity: Number(item.quantity) || 0,
              deliveredQuantity: 0,
              uomId: uomId,
              // Store the UOM shortcode for reference
              uomShortCode: uomShortCode,
              unitPrice: Number(item.unitPrice) || 0,
              lineTotal: (Number(item.quantity) || 0) * (Number(item.unitPrice) || 0)
            };
          }) : [], // Empty array if no items
          lastSyncId: syncId,
          lastSyncTimestamp: new Date(syncTimestamp),
          branchId: orderData.branchId || orderData.orderId
        };
        
        // Validate against central kitchen stock before processing
        const stockValidationErrors = await validateCentralKitchenStock(orderData.items, companyId, locationId);
        if (stockValidationErrors.length > 0) {
          throw new Error(`Stock validation failed: ${stockValidationErrors.join(', ')}`);
        }

        // Use the enhanced Order model's findOrCreateFromSync method
        console.log(`Using findOrCreateFromSync for order: ${orderData.orderId}`);
        const orderDataForSync = {
          ...orderData,
          ...newOrder,  // Include the fields we've already prepared
        };
        
        // Call the new method to find or create the order
        let savedOrder;
        try {
          savedOrder = await (Order as any).findOrCreateFromSync(orderDataForSync, companyId, syncId);
          console.log(`Order processed with findOrCreateFromSync: ${savedOrder._id}, status=${savedOrder.syncStatus}`);
        } catch (orderError) {
          console.error('Error in findOrCreateFromSync:', orderError);
          // Fall back to the original method if there's an error
          console.log('Falling back to manual save method due to error');
          
          // Check if we've already processed this order (idempotence)
          const existingOrder = await Order.findOne({
            branchId: orderData.branchId || orderData.orderId,
            orderSource: newOrder.orderSource
          });
          
          if (existingOrder) {
            // Update existing order
            existingOrder.status = orderData.status || existingOrder.status;
            existingOrder.items = newOrder.items;
            existingOrder.lastSyncId = syncId;
            existingOrder.lastSyncTimestamp = new Date(syncTimestamp);
            existingOrder.syncStatus = 'SYNCED';
            
            // IMPORTANT: Always keep the existing order number for orders already in the database
            console.log(`Keeping existing order number: ${existingOrder.orderNumber}`);
            
            // Only update the originalOrderNumber if it's not already set
            if (!existingOrder.originalOrderNumber && originalOrderNumber) {
              existingOrder.originalOrderNumber = originalOrderNumber;
            }
            
            savedOrder = await existingOrder.save();
            console.log(`Updated existing order: ${savedOrder._id}, orderNumber: ${savedOrder.orderNumber}`);
          } else {
            // We already added syncStatus to newOrder when we created it
            // Create new order
            savedOrder = await Order.create(newOrder);
            console.log(`Created new order: ${savedOrder._id}, orderNumber: ${savedOrder.orderNumber}`);
          }
        }
        
        // Check if this order already has delivery notes
        const existingDeliveryNotes = await DeliveryNote.find({
          orderId: savedOrder._id,
          companyId: new mongoose.Types.ObjectId(companyId)
        }).lean();

        let deliveryNote;
        
        if (existingDeliveryNotes && existingDeliveryNotes.length > 0) {
          // Use the most recent existing delivery note
          deliveryNote = existingDeliveryNotes[0];
          console.log(`Found existing delivery note: ${deliveryNote._id} for order ${savedOrder._id}`);
        } else {
          // Auto-create a delivery note for this order
          deliveryNote = await createDeliveryNoteForOrder(savedOrder, companyId, locationId);
          console.log(`Created new delivery note: ${deliveryNote?._id} for order ${savedOrder._id}`);
        }
        
        // Update the order with the delivery note ID - preserve existing notes
        if (deliveryNote) {
          // Get current delivery note IDs if they exist
          const currentDeliveryNoteIds = Array.isArray(savedOrder.deliveryNoteIds) ? 
            savedOrder.deliveryNoteIds : [];
          
          // Add the new delivery note ID if it's not already in the array
          if (!currentDeliveryNoteIds.some((id: any) => id.toString() === deliveryNote._id.toString())) {
            savedOrder.deliveryNoteIds = [...currentDeliveryNoteIds, deliveryNote._id];
            await savedOrder.save();
            
            console.log(`Updated order ${savedOrder._id} with delivery note ID ${deliveryNote._id}`);
            console.log(`Order now has ${savedOrder.deliveryNoteIds.length} delivery notes`);
          }
        }
        
        // SIMPLIFIED: Just use the order number stored in the database for the response
        console.log('Order saved with order number:', {
          orderNumber: savedOrder.orderNumber,
          originalOrderNumber: savedOrder.originalOrderNumber,
          orderSource: savedOrder.orderSource
        });
        
        results.push({
          branchOrderId: orderData.orderId,
          centralOrderId: savedOrder._id.toString(),
          status: 'success',
          orderNumber: savedOrder.orderNumber,
          originalOrderNumber: savedOrder.originalOrderNumber || orderData.orderNumber || orderData.ionicOrderNumber,
          deliveryNoteId: deliveryNote ? deliveryNote._id.toString() : null
        });
        
        processedOrderIds.push(savedOrder._id);
        
      } catch (error: unknown) {
        console.error('Error processing order:', error);
        errors.push({
          orderId: orderData.orderId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    // Update the sync record with careful error handling
    try {
      const syncStatus = errors.length === 0 ? 'COMPLETED' : 'PARTIAL';
      console.log(`Setting sync record status to: ${syncStatus}`);
      
      syncRecord.status = syncStatus;
      syncRecord.endTime = new Date();
      syncRecord.ordersProcessed = results.length;
      syncRecord.syncErrors = errors;
      await syncRecord.save();
      
      console.log(`Successfully updated sync record ${syncRecord._id}`);
    } catch (syncRecordError) {
      console.error('Error updating sync record:', syncRecordError);
      // Don't let this error stop the process - we'll still return the results
    }
    
    // Log the sync event
    await logSyncEvent(
      companyId,
      locationId,
      syncId,
      'SYNC_COMPLETE',
      {
        type: 'ORDER',
        status: syncRecord.status,
        itemsReceived: orders.length,
        itemsProcessed: results.length,
        errors: errors.length
      }
    );
    
    console.log(`Completed processing ${results.length} orders, ${errors.length} errors`);
    
    // Log order numbers mapping for debugging
    const orderNumberMappings = results.map(result => ({
      branchOrderId: result.branchOrderId,
      orderNumber: result.orderNumber,
      originalOrderNumber: result.originalOrderNumber,
      preserved: result.orderNumber === result.originalOrderNumber
    }));
    console.log('Order number mappings:', orderNumberMappings);
    
    const response = NextResponse.json({
      syncId,
      status: syncRecord.status,
      processedOrders: results.map(order => ({
        branchOrderId: order.branchOrderId,
        centralOrderId: order.centralOrderId,
        orderNumber: order.orderNumber,
        originalOrderNumber: order.originalOrderNumber,
        deliveryNoteId: order.deliveryNoteId,
        status: order.status,
        syncStatus: 'SYNCED', // Mark as synced in the response
        preserved: order.orderNumber === order.originalOrderNumber,
        // Add more details to help with debugging
        preservationStatus: {
          wasPreserved: order.orderNumber === order.originalOrderNumber,
          originalFormat: order.originalOrderNumber?.startsWith('ORD-') ? 'ORD-FORMAT' : 'OTHER',
          finalFormat: order.orderNumber?.startsWith('ORD-') ? 'ORD-FORMAT' : 
                       order.orderNumber?.startsWith('IONIC-') ? 'IONIC-FORMAT' : 'OTHER'
        }
      })),
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    });
    return applyCorsHeaders(response, req);
    
  } catch (error: unknown) {
    console.error('Error in order sync:', error);
    const response = NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
    return applyCorsHeaders(response, req);
  }
}

/**
 * Creates a delivery note for an order coming from Ionic app
 */
async function createDeliveryNoteForOrder(order: any, companyId: string, locationId: string) {
  try {
    // Check if there are existing delivery notes for this order
    const existingDeliveryNotes = await DeliveryNote.find({
      orderId: order._id
    }).sort({ createdAt: 1 });
    
    const deliveryNoteCount = existingDeliveryNotes.length;
    let deliveryNoteNumber;
    
    // Log the order details for debugging
    console.log('Creating delivery note for order with number:', {
      orderNumber: order.orderNumber,
      originalOrderNumber: order.originalOrderNumber,
      orderId: order._id,
      status: order.status,
      orderSource: order.orderSource
    });
    
    // SIMPLIFIED: Create delivery note number based on order number format
    
    // For orders with ORD- prefix (from Ionic), replace ORD- with DN-
    if (order.orderNumber && order.orderNumber.startsWith('ORD-')) {
      if (deliveryNoteCount === 0) {
        deliveryNoteNumber = order.orderNumber.replace('ORD-', 'DN-');
      } else {
        deliveryNoteNumber = `${order.orderNumber.replace('ORD-', 'DN-')}-${deliveryNoteCount + 1}`;
      }
      console.log(`Creating delivery note from Ionic order: ${deliveryNoteNumber}`);
    }
    // For orders with HQ- prefix (from main app), replace HQ- with DN-
    else if (order.orderNumber && order.orderNumber.startsWith('HQ-')) {
      if (deliveryNoteCount === 0) {
        deliveryNoteNumber = order.orderNumber.replace('HQ-', 'DN-');
      } else {
        deliveryNoteNumber = `${order.orderNumber.replace('HQ-', 'DN-')}-${deliveryNoteCount + 1}`;
      }
      console.log(`Creating delivery note from main app order: ${deliveryNoteNumber}`);
    }
    // For any other order formats
    else {
      if (deliveryNoteCount === 0) {
        deliveryNoteNumber = `DN-${order.orderNumber || uuidv4().substring(0, 8).toUpperCase()}`;
      } else {
        deliveryNoteNumber = `DN-${order.orderNumber || uuidv4().substring(0, 8).toUpperCase()}-${deliveryNoteCount + 1}`;
      }
      console.log(`Creating delivery note with generic format: ${deliveryNoteNumber}`);
    }
    
    console.log('Derived delivery note number:', {
      orderNumber: order.orderNumber,
      existingDeliveryNotes: deliveryNoteCount,
      deliveryNoteNumber
    });
    
    // Log for debugging
    console.log('Creating delivery note for order:', {
      orderId: order._id.toString(),
      companyId: companyId,
      locationId: locationId,
      items: order.items.length
    });
    
    // Handle missing or invalid items
    let validItems = [];
    if (Array.isArray(order.items)) {
      validItems = order.items.map((item: any) => {
        // Ensure all required fields are present
        return {
          itemId: item.itemId,
          quantityPlanned: item.quantity || 0,
          uomId: item.uomId
        };
      }).filter((item: any) => item.itemId && item.uomId); // Filter out invalid items
    }
    
    if (validItems.length === 0) {
      console.warn('No valid items found in order, creating with empty items array');
    }
    
    // Create delivery note - handle potential errors
    try {
      const deliveryNote = await DeliveryNote.create({
        companyId: new mongoose.Types.ObjectId(companyId),
        deliveryNoteNumber,
        status: 'PENDING_REVIEW', // Requires dispatch staff review before finalization
        source: 'IONIC', // Track that this came from the Ionic app
        reviewed: false, // Initially not reviewed
        orderId: order._id,
        deliveryDate: new Date(), // Default to today
        items: validItems,
        handoverFlow: [
          {
            stepType: 'DISPATCH',
            status: 'PENDING',
            confirmedItems: []
          },
          {
            stepType: 'DRIVER',
            status: 'PENDING',
            confirmedItems: []
          },
          {
            stepType: 'SHOP',
            status: 'PENDING',
            confirmedItems: []
          }
        ]
      });
      
      console.log('Successfully created delivery note:', deliveryNote._id.toString());
      return deliveryNote;
    } catch (createError) {
      console.error('Specific error creating delivery note:', createError);
      // Skip delivery note creation for now - we can still process the order
      return null;
    }
  } catch (error: unknown) {
    console.error('Error in createDeliveryNoteForOrder function:', error);
    return null;
  }
}

/**
 * Validates order items against central kitchen stock levels and ordering rules
 */
async function validateCentralKitchenStock(orderItems: any[], companyId: string, locationId: string): Promise<string[]> {
  const errors: string[] = [];
  
  try {
    for (const item of orderItems) {
      if (!item.itemId || !item.quantity) {
        continue; // Skip invalid items
      }

      // Find the branch inventory record for this item
      const branchInventory = await BranchInventory.findOne({
        companyId: new mongoose.Types.ObjectId(companyId),
        locationId: new mongoose.Types.ObjectId(locationId),
        itemId: new mongoose.Types.ObjectId(item.itemId),
        isActive: true
      });

      if (!branchInventory) {
        errors.push(`Item ${item.itemId} not found in branch inventory`);
        continue;
      }

      // Check if item is orderable
      if (branchInventory.isOrderable === false) {
        errors.push(`Item ${item.itemId} is not available for ordering`);
        continue;
      }

      // Check central kitchen stock availability
      const centralKitchenStock = branchInventory.centralKitchenStock || 0;
      if (centralKitchenStock <= 0) {
        errors.push(`Item ${item.itemId} is out of stock at central kitchen`);
        continue;
      }

      // Check minimum order quantity
      const minOrderQty = branchInventory.minOrderQuantity || 0;
      if (item.quantity < minOrderQty) {
        errors.push(`Item ${item.itemId} minimum order quantity is ${minOrderQty}, requested ${item.quantity}`);
        continue;
      }

      // Check maximum order quantity
      const maxOrderQty = branchInventory.maxOrderQuantity;
      if (maxOrderQty && item.quantity > maxOrderQty) {
        errors.push(`Item ${item.itemId} maximum order quantity is ${maxOrderQty}, requested ${item.quantity}`);
        continue;
      }

      // Check if central kitchen has enough stock
      if (item.quantity > centralKitchenStock) {
        errors.push(`Item ${item.itemId} insufficient central kitchen stock: available ${centralKitchenStock}, requested ${item.quantity}`);
        continue;
      }

      // Check UOM conversion if needed
      const orderingConversionFactor = branchInventory.orderingConversionFactor || 1;
      const adjustedQuantity = item.quantity * orderingConversionFactor;
      
      if (adjustedQuantity > centralKitchenStock) {
        errors.push(`Item ${item.itemId} insufficient stock after UOM conversion: available ${centralKitchenStock}, required ${adjustedQuantity}`);
        continue;
      }
    }
  } catch (error) {
    console.error('Error validating central kitchen stock:', error);
    errors.push('Failed to validate stock levels');
  }

  return errors;
}
