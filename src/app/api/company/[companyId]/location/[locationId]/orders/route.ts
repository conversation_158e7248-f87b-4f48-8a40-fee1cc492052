import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Order from '@/models/Order';
import { Types } from 'mongoose';

/**
 * GET /api/company/[companyId]/location/[locationId]/orders
 * Retrieves orders for a specific location
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string; locationId: string }> }
) {
  const { companyId, locationId } = await params;
  try {
    await dbConnect();
    
    const companyIdHeader = req.headers.get('company-id');
    
    // Validate company ID from header matches URL parameter
    if (companyIdHeader && companyIdHeader !== companyId) {
      return NextResponse.json(
        { error: 'Company ID mismatch between header and URL parameter' },
        { status: 400 }
      );
    }
    
    // Support pagination and filtering
    const searchParams = req.nextUrl.searchParams;
    const page: number = parseInt(searchParams.get('page') || '1', 10);
    const limit: number = parseInt(searchParams.get('limit') || '50', 10);
    const status: string | null = searchParams.get('status');
    const syncStatus: string | null = searchParams.get('syncStatus');
    
    // Build query
    const query: Record<string, any> = {
      companyId: new Types.ObjectId(companyId),
      sellerLocationId: new Types.ObjectId(locationId)
    };
    
    // Add status filter if provided
    if (status) {
      query.status = status;
    }
    
    // Add sync status filter if provided
    if (syncStatus) {
      query.syncStatus = syncStatus;
    }
    
    // Get total count for pagination
    const total: number = await Order.countDocuments(query);
    
    // Get paginated results
    const orders: any[] = await Order.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean() as any[];
    
    // Return orders with pagination info
    return NextResponse.json({
      orders,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    console.error(`Error in GET /api/company/${companyId}/location/${locationId}/orders:`, error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export function OPTIONS() {
  return new NextResponse(null, { status: 204 });
}
