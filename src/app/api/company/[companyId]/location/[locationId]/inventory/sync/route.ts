// src/app/api/company/[companyId]/location/[locationId]/inventory/sync/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { validateIonicAuth } from '@/lib/ionic-auth';
import { validateLocationAccess } from '@/lib/location-validation';
import BranchInventory from '@/models/BranchInventory';
import type { IBranchInventory } from '@/models/BranchInventory';
import InventoryTransaction from '@/models/InventoryTransaction';
import InventorySync from '@/models/InventorySync';
import { applyCorsHeaders, handleCorsOptions } from '@/lib/cors';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    await dbConnect();
    
    const params = await context.params;
    const { companyId, locationId } = params;
    const headerCompanyId = req.headers.get('company-id');
    
    // Validate authentication
    const authResult = await validateIonicAuth(req);
    if (!authResult.isAuthenticated) {
      return applyCorsHeaders(Response.json({ error: authResult.error }, { status: 401 }), req);
    }
    
    // Validate company and location access
    try {
      await validateLocationAccess(companyId, locationId, headerCompanyId);
    } catch (error: unknown) {
      return applyCorsHeaders(Response.json({ error: (error as Error).message }, { status: 403 }), req);
    }
    
    // Parse request data
    const data = await req.json();
    const { updates, syncTimestamp } = data;
    
    // Validate request data
    if (!updates || !Array.isArray(updates) || !syncTimestamp) {
      return applyCorsHeaders(Response.json(
        { error: 'Invalid request format. Required: updates array and syncTimestamp' },
        { status: 400 }
      ), req);
    }
    
    // Create sync record
    const syncRecord = new InventorySync({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      syncId: new Types.ObjectId(),
      syncTimestamp: new Date(syncTimestamp),
      status: 'PROCESSING',
      items: [],
      source: 'IONIC',
      userId: authResult.userId
    });
    
    await syncRecord.save();
    
    // Process each inventory update
    const results = {
      status: 'success' as 'success' | 'partial' | 'error',
      updatedItems: [] as string[],
      errors: [] as { itemId: string; error: string }[],
      lastSyncTimestamp: new Date(syncTimestamp)
    };
    
    // Process updates in a transaction if possible
    const session = await BranchInventory.startSession();
    
    try {
      await session.withTransaction(async () => {
        for (const update of updates) {
          const { itemId, currentStock, transactions = [] } = update;
          
          if (!itemId || currentStock === undefined) {
            results.errors.push({
              itemId: itemId || 'unknown',
              error: 'Invalid update format. Required: itemId and currentStock'
            });
            continue;
          }
          
          try {
            // Find the inventory item
            const inventoryItem = (await BranchInventory.findOne({
              companyId: new Types.ObjectId(companyId),
              locationId: new Types.ObjectId(locationId),
              itemId: new Types.ObjectId(itemId)
            }).session(session)) as IBranchInventory | null;
            
            if (!inventoryItem) {
              results.errors.push({
                itemId,
                error: 'Inventory item not found'
              });
              continue;
            }
            
            // Record previous stock for transaction history
            const previousStock = inventoryItem.currentStock;
            
            // Update the stock level
            inventoryItem.currentStock = currentStock;
            inventoryItem.lastUpdated = new Date();
            inventoryItem.lastSyncId = syncRecord.syncId.toString();
            inventoryItem.lastSyncTimestamp = new Date(syncTimestamp);
            inventoryItem.syncSource = 'IONIC';
            
            // Save the updated inventory item
            await inventoryItem.save({ session });
            
            // Record transactions if provided
            if (transactions.length > 0) {
              for (const txn of transactions) {
                const { type, quantity, menuItemId, timestamp, reason } = txn;
                
                // Create inventory transaction record
                const transaction = new InventoryTransaction({
                  timestamp: new Date(timestamp),
                  metadata: {
                    companyId: new Types.ObjectId(companyId),
                    locationId: new Types.ObjectId(locationId),
                    itemId: new Types.ObjectId(itemId),
                    transactionType: type.toUpperCase(),
                    userId: authResult.userId
                  },
                  previousStock,
                  newStock: currentStock,
                  difference: currentStock - previousStock,
                  notes: reason || `${type} transaction from IONIC POS`,
                });
                
                // If this is related to a menu item, add the reference
                if (menuItemId) {
                  transaction.metadata.referenceId = new Types.ObjectId(menuItemId);
                  transaction.metadata.referenceType = 'ORDER';
                }
                
                await transaction.save({ session });
              }
            } else {
              // Create a general adjustment transaction if no specific transactions provided
              const transaction = new InventoryTransaction({
                timestamp: new Date(syncTimestamp),
                metadata: {
                  companyId: new Types.ObjectId(companyId),
                  locationId: new Types.ObjectId(locationId),
                  itemId: new Types.ObjectId(itemId),
                  transactionType: 'ADJUSTMENT',
                  userId: authResult.userId
                },
                previousStock,
                newStock: currentStock,
                difference: currentStock - previousStock,
                notes: 'Stock adjustment from IONIC POS'
              });
              
              await transaction.save({ session });
            }
            
            // Mark as successfully updated
            results.updatedItems.push(itemId);
            
            // Add to sync record items
            syncRecord.items.push({
              itemId: new Types.ObjectId(itemId),
              previousStock,
              newStock: currentStock,
              status: 'SUCCESS'
            });
          } catch (error: unknown) {
            console.error(`Error updating item ${itemId}:`, error);
            
            // Record the error
            results.errors.push({
              itemId,
              error: (error as Error).message
            });
            
            // Add to sync record items
            syncRecord.items.push({
              itemId: new Types.ObjectId(itemId),
              status: 'ERROR',
              error: (error as Error).message
            });
          }
        }
      });
      
      // Update the overall status
      if (results.errors.length === 0) {
        results.status = 'success';
        syncRecord.status = 'COMPLETED';
      } else if (results.updatedItems.length > 0) {
        results.status = 'partial';
        syncRecord.status = 'PARTIAL';
      } else {
        results.status = 'error';
        syncRecord.status = 'FAILED';
      }
      
    } catch (error: unknown) {
      console.error('Transaction error:', error);
      results.status = 'error';
      syncRecord.status = 'FAILED';
      syncRecord.error = (error as Error).message;
    } finally {
      await session.endSession();
    }
    
    // Update sync record with final status
    syncRecord.completedAt = new Date();
    syncRecord.itemsProcessed = results.updatedItems.length;
    syncRecord.itemsFailed = results.errors.length;
    await syncRecord.save();
    
    // Return response with CORS headers
    return applyCorsHeaders(Response.json({
      status: results.status,
      updatedItems: results.updatedItems,
      errors: results.errors.length > 0 ? results.errors : undefined,
      lastSyncTimestamp: results.lastSyncTimestamp.toISOString()
    }), req);
    
  } catch (error: unknown) {
    console.error('Error in inventory sync:', error);
    return applyCorsHeaders(Response.json({ 
      error: 'Internal server error',
      details: (error as Error).message 
    }, { status: 500 }), req);
  }
}