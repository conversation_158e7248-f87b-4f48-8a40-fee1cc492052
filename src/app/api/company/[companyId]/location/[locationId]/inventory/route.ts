// src/app/api/company/[companyId]/location/[locationId]/inventory/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
// Import models in correct order
import '@/models/UOM';  // Base model needed by others
import '@/models/Supplier';  // Base model needed by others
import '@/models/Location';  // Base model needed by others
import '@/models/Recipe';  // Register Recipe model
import '@/models/Ingredient';  // Register Ingredient model
import BranchInventory from '@/models/BranchInventory';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    await dbConnect();

    // Get companyId and locationId from URL parameters
    const params = await context.params;
    const { companyId: urlCompanyId, locationId } = params;
    const searchParams = req.nextUrl.searchParams;

    // Authenticate and authorize
    const auth = await requireAuth()(req); // Handles basic auth
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // --- Tenant Isolation Check ---
    // Ensure the user belongs to the company specified in the URL
    if (auth.tenantId !== urlCompanyId) {
      console.warn(`[Inventory API] Tenant mismatch: User tenant ${auth.tenantId} != URL company ${urlCompanyId}`);
      return NextResponse.json({ error: 'Forbidden: Access denied to this company' }, { status: 403 });
    }
    // --- End Tenant Isolation Check ---

    // TODO: Add Location-Level Access Check: Verify if auth.metadata.locationsAccess includes locationId

    // Use the verified tenantId from auth for database operations
    const companyId = auth.tenantId; 

    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const search = searchParams.get('search') || '';
    const itemType = searchParams.get('itemType') as 'RECIPE' | 'INGREDIENT' | null;
    const category = searchParams.get('category') || '';
    const belowReorder = searchParams.get('belowReorder') === 'true';
    const includeSellingOptions = searchParams.get('includeSellingOptions') === 'true';

    // Build match stage
    const matchStage: any = {
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      isActive: true
    };

    if (itemType) {
      matchStage.itemType = itemType;
    }

    if (belowReorder) {
      matchStage.$expr = { $lte: ['$currentStock', '$reorderPoint'] };
    }

    console.log('Initial match stage:', JSON.stringify(matchStage, null, 2));

    // Use aggregation pipeline for better control and performance
    const pipeline: any[] = [
      { $match: matchStage },
      {
        $lookup: {
          from: 'recipes',
          localField: 'itemId',
          foreignField: '_id',
          as: 'recipeDetails'
        }
      },
      {
        $lookup: {
          from: 'ingredients',
          localField: 'itemId',
          foreignField: '_id',
          as: 'ingredientDetails'
        }
      },
      {
        $addFields: {
          itemDetails: {
            $cond: {
              if: { $eq: ['$itemType', 'RECIPE'] },
              then: { $arrayElemAt: ['$recipeDetails', 0] },
              else: { $arrayElemAt: ['$ingredientDetails', 0] }
            }
          }
        }
      },
      // Lookup for base UOM
      {
        $lookup: {
          from: 'uoms',
          localField: 'itemDetails.baseUomId',
          foreignField: '_id',
          as: 'baseUomDetails'
        }
      },
      {
        $addFields: {
          baseUomId: {
            $let: {
              vars: {
                uom: { $arrayElemAt: ['$baseUomDetails', 0] }
              },
              in: {
                _id: '$$uom._id',
                name: '$$uom.name',
                shortCode: '$$uom.shortCode'
              }
            }
          }
        }
      }
    ];

    // Add selling options lookup if requested
    if (includeSellingOptions) {
      pipeline.push(
        // First lookup for base yield UOM for recipes
        {
          $lookup: {
            from: 'uoms',
            localField: 'itemDetails.baseYieldUOM',
            foreignField: '_id',
            as: 'baseYieldUOMDetails'
          }
        },
        // Then lookup for selling UOMs
        {
          $lookup: {
            from: 'uoms',
            localField: 'itemDetails.sellingDetails.unitOfSelling',
            foreignField: '_id',
            as: 'sellingUoms'
          }
        },
        {
          $addFields: {
            'itemDetails.baseYieldUOM': {
              $cond: {
                if: { $eq: ['$itemType', 'RECIPE'] },
                then: {
                  $let: {
                    vars: {
                      uom: { $arrayElemAt: ['$baseYieldUOMDetails', 0] }
                    },
                    in: {
                      _id: '$$uom._id',
                      name: '$$uom.name',
                      shortCode: '$$uom.shortCode'
                    }
                  }
                },
                else: null
              }
            },
            'itemDetails.sellingDetails': {
              $map: {
                input: '$itemDetails.sellingDetails',
                as: 'selling',
                in: {
                  _id: '$$selling._id',
                  unitOfSelling: {
                    $let: {
                      vars: {
                        uom: {
                          $arrayElemAt: [
                            {
                              $filter: {
                                input: '$sellingUoms',
                                as: 'uom',
                                cond: { $eq: ['$$uom._id', '$$selling.unitOfSelling'] }
                              }
                            },
                            0
                          ]
                        }
                      },
                      in: {
                        _id: '$$uom._id',
                        name: '$$uom.name',
                        shortCode: '$$uom.shortCode'
                      }
                    }
                  },
                  conversionFactor: '$$selling.conversionFactor'
                }
              }
            }
          }
        }
      );
    }

    pipeline.push(
      {
        $group: {
          _id: '$itemId',
          itemId: { $first: '$itemId' },
          itemType: { $first: '$itemType' },
          currentStock: { $sum: '$currentStock' },
          baseUomId: { $first: '$baseUomId' },
          name: { $first: '$name' },
          description: { $first: '$description' },
          category: { $first: '$category' },
          belowReorderPoint: { $first: '$belowReorderPoint' },
          isActive: { $first: '$isActive' },
          isLocked: { $first: '$isLocked' },
          itemDetails: { $first: '$itemDetails' }
        }
      },
      {
        $lookup: {
          from: 'uoms',
          localField: 'baseUomId._id',
          foreignField: '_id',
          as: 'baseUom'
        }
      },
      {
        $addFields: {
          baseUom: { $arrayElemAt: ['$baseUom', 0] },
          // Add computed fields
          name: { $ifNull: ['$itemDetails.name', 'Unknown'] },
          description: { $ifNull: ['$itemDetails.description', ''] },
          belowReorderPoint: {
            $cond: {
              if: { $lte: ['$currentStock', '$reorderPoint'] },
              then: true,
              else: false
            }
          }
        }
      },
      {
        $sort: {
          belowReorderPoint: -1, // Show items below reorder point first
          name: 1                // Then sort by name
        }
      }
    );

    // Add category filter if provided
    if (category) {
      pipeline.push({
        $match: {
          $or: [
            { 'itemDetails.Category': category },
            { 'itemDetails.category': category }
          ]
        }
      });
    }

    // Add search if provided
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'itemDetails.name': { $regex: search, $options: 'i' } },
            { 'itemDetails.description': { $regex: search, $options: 'i' } }
          ]
        }
      });
    }

    // Count total before pagination
    const countPipeline = [...pipeline, { $count: 'total' }];
    const [countResult] = await BranchInventory.aggregate(countPipeline);
    const total = countResult?.total || 0;

    console.log('Total items before pagination:', total);

    pipeline.push(
      { $skip: (page - 1) * limit },
      { $limit: limit },
      {
        $project: {
          _id: 1,
          itemId: {
            _id: { $ifNull: ['$itemDetails._id', '$itemId'] },
            name: { $ifNull: ['$itemDetails.name', 'Unknown'] }
          },
          itemType: 1,
          currentStock: { $ifNull: ['$currentStock', 0] },
          parLevel: { $ifNull: ['$parLevel', 0] },
          reorderPoint: { $ifNull: ['$reorderPoint', 0] },
          baseUomId: {
            _id: { $ifNull: ['$baseUomId._id', '$baseUomId'] },
            name: { $ifNull: ['$baseUomId.name', 'Unknown'] },
            shortCode: { $ifNull: ['$baseUomId.shortCode', 'N/A'] }
          },
          lastUpdated: 1,
          name: 1,
          description: 1,
          category: 1,
          belowReorderPoint: 1,
          isActive: 1,
          isLocked: 1,
          orderBudgetQuantity: { $ifNull: ['$orderBudgetQuantity', null] },
          orderBudgetPeriod: { $ifNull: ['$orderBudgetPeriod', null] },
          orderBudgetLastReset: { $ifNull: ['$orderBudgetLastReset', null] },
          orderBudgetUsed: { $ifNull: ['$orderBudgetUsed', 0] },
          itemDetails: {
            _id: 1,
            name: 1,
            description: 1,
            Category: 1,
            category: 1,
            yield: 1,
            baseYieldUOM: 1,
            stockable: 1,
            canBeSold: 1,
            sellingDetails: {
              $cond: {
                if: { $eq: [includeSellingOptions, true] },
                then: '$itemDetails.sellingDetails',
                else: []
              }
            }
          }
        }
      }
    );

    const inventory = await BranchInventory.aggregate(pipeline);

    console.log('Final inventory items count:', inventory.length);
    console.log('Sample item structure:', inventory[0] ? JSON.stringify(inventory[0], null, 2) : 'No items found');

    const response = {
      data: inventory,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };

    console.log('Pagination details:', response.pagination);

    return NextResponse.json(response);
  } catch (error: unknown) {
    console.error('Error in inventory GET:', error);
    // Check if it's an authentication/authorization error from requireAuth
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: (error as any).status || 401 }); // Use status if available
    }
    
    // Include error details in development
    const errorMessage = process.env.NODE_ENV === 'development' 
      ? (error instanceof Error ? error.message : 'Internal server error')
      : 'Internal server error';
      
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
