import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';

// Import models in correct order
import '@/models/UOM';  // Base model needed by others
import '@/models/Supplier';  // Base model needed by others
import '@/models/Location';  // Base model needed by others
import '@/models/Recipe';  // Register Recipe model
import '@/models/Ingredient';  // Register Ingredient model
import BranchInventory from '@/models/BranchInventory';
import InventoryTransaction from '@/models/InventoryTransaction';
import Company from '@/models/Company';
import User, { IUser } from '@/models/User';  // Add User model import
import Location from '@/models/Location';

// Helper function to verify authentication
const verifyAuth = async () => {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session');

  if (!sessionCookie) {
    return { isAuthenticated: false, error: 'No session cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionCookie.value, secret) as {
      userId: string;
      companyId?: string;
    };

    // Get user from database to ensure we have the latest companyId
    const user = await User.findById(decoded.userId).lean() as IUser | null;
    if (!user) {
      return { isAuthenticated: false, error: 'User not found' };
    }

    return { 
      isAuthenticated: true, 
      userId: decoded.userId,
      companyId: user.companyId?.toString()
    };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  const { companyId, locationId } = await context.params;
  try {
    await dbConnect();

    const headerCompanyId = req.headers.get('company-id');

    console.log('POST Inventory Count - Request params:', { companyId, locationId });
    console.log('POST Inventory Count - Company ID from header:', headerCompanyId);

    // Verify authentication
    const authResult = await verifyAuth();
    console.log('POST Inventory Count - Auth result:', authResult);
    
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Validate company access
    try {
      await validateCompanyAccess(companyId, headerCompanyId);
    } catch (error: unknown) {
      console.log('POST Inventory Count - Company validation error:', error);
      return NextResponse.json({ error: (error as Error).message }, { status: 403 });
    }

    // Get company settings
    const company = await Company.findById(companyId);
    const autoApproveStockCounts = company?.settings?.autoApproveStockCounts || false;

    const body = await req.json();
    const items: any[] = body.items;
    const notes = body.notes;
    const countBatch = body.countBatch;
    console.log('POST Inventory Count - Request body:', JSON.stringify(body, null, 2));
    console.log('POST Inventory Count - Items count:', items.length);
    console.log('POST Inventory Count - Sample items:', items.slice(0, 3));

    // Start a session for stock updates only
    const session = await BranchInventory.startSession();
    let transactionId;

    try {
      // Process stock updates in transaction if auto-approve is enabled
      if (autoApproveStockCounts) {
        await session.withTransaction(async () => {
          console.log('POST Inventory Count - Starting stock update transaction');
          
          // Process items sequentially for stock updates
          for (const item of items as any[]) {
            console.log('Processing stock update for item:', item.itemId);
            
            const inventory = await BranchInventory.findOne({
              companyId: new Types.ObjectId(companyId),
              locationId: new Types.ObjectId(locationId),
              'itemId': new Types.ObjectId(item.itemId)
            }).session(session);

            if (!inventory) {
              console.log('Inventory not found for item:', item.itemId);
              throw new Error(`Inventory item not found: ${item.itemId}`);
            }

            const previousStock = inventory.currentStock ?? 0;
            const newStock = item.countedQuantity;

            console.log('Updating stock for item:', {
              itemId: item.itemId,
              from: previousStock,
              to: newStock
            });

            const updateResult = await BranchInventory.findOneAndUpdate(
              {
                companyId: new Types.ObjectId(companyId),
                locationId: new Types.ObjectId(locationId),
                'itemId': new Types.ObjectId(item.itemId)
              },
              { $set: { currentStock: newStock } },
              { session, new: true }
            );

            if (!updateResult) {
              throw new Error(`Failed to update inventory for item: ${item.itemId}`);
            }
          }
        });
      }

      await session.endSession();

      // Process time-series records outside of transaction
      console.log('POST Inventory Count - Creating time-series records');
      const processedItems = items.map(item => ({
        _id: new Types.ObjectId(),
        timestamp: new Date(),
        metadata: {
          companyId: new Types.ObjectId(companyId),
          locationId: new Types.ObjectId(locationId),
          itemId: new Types.ObjectId(item.itemId),
          transactionType: 'COUNT',
          userId: authResult.userId
        },
        previousStock: item.systemStock ?? 0,
        newStock: item.countedQuantity,
        difference: item.countedQuantity - (item.systemStock ?? 0),
        countBatch,
        countStatus: autoApproveStockCounts ? 'APPROVED' : 'SUBMITTED',
        notes
      }));

      console.log('Sample processed item:', JSON.stringify(processedItems[0], null, 2));

      // Insert time-series records without transaction
      const transactions = await InventoryTransaction.insertMany(processedItems);
      console.log('POST Inventory Count - Time-series records created:', transactions.length);
      transactionId = transactions[0]._id;

      return NextResponse.json({
        message: autoApproveStockCounts 
          ? 'Inventory count completed and stock levels updated' 
          : 'Inventory count submitted for approval',
        transactionId,
        status: autoApproveStockCounts ? 'APPROVED' : 'SUBMITTED'
      });
    } catch (error: unknown) {
      await session.endSession();
      console.error('POST Inventory Count - Transaction error:', error);
      if (error instanceof Error) {
        console.error('Error stack:', error.stack);
        if ('errors' in error) {
          console.error('Validation errors:', JSON.stringify(error.errors, null, 2));
        }
      }
      throw error;
    }
  } catch (error: unknown) {
    console.error('Error in inventory count POST:', error);
    const errorMessage = process.env.NODE_ENV === 'development'
      ? (error instanceof Error ? error.message : 'Internal server error')
      : 'Internal server error';
      
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch recent counts
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    await dbConnect();

    const { companyId, locationId } = await context.params;
    const headerCompanyId = req.headers.get('company-id');

    console.log('GET Inventory Count - Request params:', { companyId, locationId });
    console.log('GET Inventory Count - Company ID from header:', headerCompanyId);

    // Verify authentication
    const authResult = await verifyAuth();
    console.log('GET Inventory Count - Auth result:', authResult);
    
    if (!authResult.isAuthenticated || !authResult.companyId) {
      console.log('Authentication failed:', authResult.error);
      return NextResponse.json({ error: authResult.error || 'Authentication failed' }, { status: 401 });
    }

    // Validate company access
    try {
      await validateCompanyAccess(companyId, headerCompanyId);
      
      // Additional check to ensure the user belongs to this company
      if (authResult.companyId !== companyId) {
        throw new Error('User does not belong to this company');
      }
    } catch (error: unknown) {
      console.log('Company validation failed:', error);
      return NextResponse.json({ error: (error as Error).message }, { status: 403 });
    }

    // Validate location exists and belongs to company
    const location = await Location.findOne({
      _id: new Types.ObjectId(locationId),
      companyId: new Types.ObjectId(companyId)
    }).lean();

    if (!location) {
      return NextResponse.json(
        { error: 'Location not found or does not belong to company' },
        { status: 404 }
      );
    }

    // Get recent inventory counts
    const query = {
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      transactionType: 'COUNT'
    };
    
    console.log('GET Inventory Count - Query for transactions:', query);

    const transactions = await InventoryTransaction.find(query)
      .sort({ timestamp: -1 })
      .limit(10)
      .lean();

    console.log('GET Inventory Count - Found transactions:', transactions.length);

    return NextResponse.json({ transactions });
  } catch (error: unknown) {
    console.error('Error in inventory count GET:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT endpoint to approve a count
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  const { companyId, locationId } = await context.params;
  try {
    await dbConnect();

    const headerCompanyId = req.headers.get('company-id');
    const body = await req.json();
    const countId: string = body.countId;

    // Verify authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Validate company access
    try {
      await validateCompanyAccess(companyId, headerCompanyId);
    } catch (error: unknown) {
      return NextResponse.json({ error: (error as Error).message }, { status: 403 });
    }

    // Start a session for the transaction
    const session = await BranchInventory.startSession();
    
    try {
      await session.withTransaction(async () => {
        // Get the count transaction
        const count = await InventoryTransaction.findOne({
          _id: new Types.ObjectId(countId),
          companyId: new Types.ObjectId(companyId),
          locationId: new Types.ObjectId(locationId),
          transactionType: 'COUNT',
          countStatus: 'SUBMITTED'
        }).session(session);

        if (!count) {
          throw new Error('Count not found or already approved');
        }

        // Update stock levels
        await Promise.all((count as any).items.map(async (item: any) => {
          await BranchInventory.updateOne(
            {
              companyId: new Types.ObjectId(companyId),
              locationId: new Types.ObjectId(locationId),
              'itemId': item.metadata.itemId
            },
            { $set: { currentStock: item.quantity } }
          ).session(session);
        }));

        // Update count status
        (count as any).countStatus = 'APPROVED';
        (count as any).approvedBy = new Types.ObjectId(authResult.userId);
        (count as any).approvedAt = new Date();
        await (count as any).save({ session });
      });

      await session.endSession();

      return NextResponse.json({
        message: 'Inventory count approved and stock levels updated'
      });
    } catch (error: unknown) {
      await session.endSession();
      console.error('Error approving count:', error);
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Failed to approve count' },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('Error approving count:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to approve count' },
      { status: 500 }
    );
  }
}