import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import { Types } from 'mongoose';
import StockCount from '@/models/StockCount';
import BranchInventory from '@/models/BranchInventory';
import InventoryTransaction from '@/models/InventoryTransaction';
import type { IBranchInventory } from '@/models/BranchInventory';
import type { IStockCount } from '@/models/StockCount';

// GET /api/company/[companyId]/location/[locationId]/stock-count
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    await requireAuth(request);

    const { companyId, locationId } = await context.params;
    const headerCompanyId = request.headers.get('company-id');
    
    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 });
    }

    await dbConnect();

    // Support pagination and status filtering
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');

    const query: any = {
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId)
    };

    if (status) {
      query.status = status;
    }

    const stockCounts = await StockCount.find(query)
      .populate('startedBy', 'displayName')
      .populate('approvedBy', 'displayName')
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ countDate: -1 });

    return NextResponse.json(stockCounts);
  } catch (error: unknown) {
    console.error('Error in stock count GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/company/[companyId]/location/[locationId]/stock-count
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    const user = await requireAuth(request);

    const { companyId, locationId } = await context.params;
    const headerCompanyId = request.headers.get('company-id');
    
    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 });
    }

    await dbConnect();
    const data = await request.json();

    // Check if there's already an in-progress count
    const existingCount = await StockCount.findOne({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      status: 'IN_PROGRESS'
    });

    if (existingCount) {
      return NextResponse.json(
        { error: 'There is already a stock count in progress' },
        { status: 409 }
      );
    }

    // Get all active inventory items
    const inventoryItems = (await BranchInventory.find({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      isActive: true
    }).populate('itemId')) as IBranchInventory[];

    // Create stock count items
    const items = inventoryItems.map(inv => ({
      itemId: inv.itemId,
      itemType: inv.itemType,
      sellingOptionId: inv.sellingOptionId,
      systemStock: inv.currentStock,
      countedStock: 0, // Will be updated during counting
      difference: -inv.currentStock, // Initial difference
      baseUomId: inv.baseUomId
    }));

    // Create stock count
    const stockCount = new StockCount({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      status: 'IN_PROGRESS',
      countDate: new Date(),
      startedBy: new Types.ObjectId(user.id),
      items,
      notes: data.notes
    });

    // Lock inventory items
    const bulkOps = inventoryItems.map(inv => ({
      updateOne: {
        filter: { _id: inv._id },
        update: {
          $set: {
            isLocked: true,
            lockedBy: new Types.ObjectId(user.id),
            lockedAt: new Date(),
            stockCountId: stockCount._id
          }
        }
      }
    }));

    // Use transaction to ensure consistency
    const dbSession = await StockCount.startSession();
    dbSession.startTransaction();

    try {
      await Promise.all([
        stockCount.save({ session: dbSession }),
        BranchInventory.bulkWrite(bulkOps, { session: dbSession })
      ]);

      await dbSession.commitTransaction();
      return NextResponse.json(stockCount);
    } catch (error: unknown) {
      await dbSession.abortTransaction();
      throw error;
    } finally {
      dbSession.endSession();
    }
  } catch (error: unknown) {
    console.error('Error in stock count POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/company/[companyId]/location/[locationId]/stock-count/[countId]
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string; countId: string }> }
) {
  try {
    const user = await requireAuth(request);

    const { companyId, locationId, countId } = await context.params;
    const headerCompanyId = request.headers.get('company-id');
    
    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 });
    }

    await dbConnect();
    const data = await request.json();

    const stockCount = (await StockCount.findOne({
      _id: new Types.ObjectId(countId),
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId)
    })) as IStockCount | null;

    if (!stockCount) {
      return NextResponse.json({ error: 'Stock count not found' }, { status: 404 });
    }

    if (data.action === 'update-counts') {
      // Update counted quantities
      data.items.forEach((item: { itemId: string; countedStock: number; notes?: string }) => {
        const countItem = stockCount.items.find(
          (i: IStockCount['items'][number]) => i.itemId.toString() === item.itemId
        );
        if (countItem) {
          countItem.countedStock = item.countedStock;
          countItem.difference = item.countedStock - countItem.systemStock;
          countItem.notes = item.notes;
        }
      });

      await stockCount.save();
      return NextResponse.json(stockCount);
    }

    if (data.action === 'submit') {
      stockCount.status = 'PENDING_APPROVAL';
      await stockCount.save();
      return NextResponse.json(stockCount);
    }

    if (data.action === 'approve' || data.action === 'reject') {
      if (!user.role || !['owner', 'admin', 'manager'].includes(user.role)) {
        return NextResponse.json(
          { error: 'Only managers and above can approve/reject stock counts' },
          { status: 403 }
        );
      }

      stockCount.status = data.action === 'approve' ? 'APPROVED' : 'REJECTED';
      stockCount.approvedBy = new Types.ObjectId(user.id);
      stockCount.approvedAt = new Date();

      if (data.action === 'approve') {
        // Create adjustment transactions and update inventory
        const dbSession = await StockCount.startSession();
        dbSession.startTransaction();

        try {
          // Update each inventory item and create adjustment transactions
          await Promise.all(stockCount.items.map(async (item: IStockCount['items'][number]) => {
            const inventory = (await BranchInventory.findOne({
              companyId: new Types.ObjectId(companyId),
              locationId: new Types.ObjectId(locationId),
              itemId: item.itemId
            }).session(dbSession)) as IBranchInventory | null;

            if (inventory) {
              // Create snapshot before updating
              inventory.createSnapshot('Stock count adjustment', stockCount._id as Types.ObjectId);

              // Create adjustment transaction
              const transaction = new InventoryTransaction({
                companyId: new Types.ObjectId(companyId),
                locationId: new Types.ObjectId(locationId),
                itemId: item.itemId,
                itemType: item.itemType,
                transactionType: 'COUNT_ADJUSTMENT',
                quantity: item.difference,
                baseUomId: item.baseUomId,
                stockCountId: stockCount._id,
                notes: item.notes,
                timestamp: new Date(),
                userId: new Types.ObjectId(user.id),
                previousStock: inventory.currentStock,
                newStock: item.countedStock,
                approvalDetails: {
                  approvedBy: new Types.ObjectId(user.id),
                  approvedAt: new Date(),
                  reason: 'Stock count adjustment'
                }
              });

              // Update inventory
              inventory.currentStock = item.countedStock;
              inventory.lastCountDate = new Date();
              inventory.isLocked = false;
              inventory.lockedBy = undefined;
              inventory.lockedAt = undefined;
              inventory.stockCountId = undefined;

              await Promise.all([
                transaction.save({ session: dbSession }),
                inventory.save({ session: dbSession })
              ]);
            }
          }));

          await stockCount.save({ session: dbSession });
          await dbSession.commitTransaction();
        } catch (error: unknown) {
          await dbSession.abortTransaction();
          throw error;
        } finally {
          dbSession.endSession();
        }
      } else {
        // If rejected, just unlock inventory
        await BranchInventory.updateMany(
          {
            companyId: new Types.ObjectId(companyId),
            locationId: new Types.ObjectId(locationId),
            stockCountId: stockCount._id
          },
          {
            $set: {
              isLocked: false,
              lockedBy: undefined,
              lockedAt: undefined,
              stockCountId: undefined
            }
          }
        );
        await stockCount.save();
      }

      return NextResponse.json(stockCount);
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error: unknown) {
    console.error('Error in stock count PATCH:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
