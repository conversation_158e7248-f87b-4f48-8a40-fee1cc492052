// src/app/api/company/[companyId]/selling-options/route.ts
import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import dbConnect from '@/lib/db';
import mongoose, { Types } from 'mongoose';
import '@/models/Recipe';  // Ensure Recipe model is registered
import '@/models/Ingredient';  // Ensure Ingredient model is registered
import '@/models/UOM';  // Ensure UOM model is registered
import '@/models/Location';  // Ensure Location model is registered
import Recipe from '@/models/Recipe';
import { Ingredient } from '@/models/Ingredient';
import BranchInventory from '@/models/BranchInventory';
import Location from '@/models/Location';

// Helper function to verify authentication
const verifyAuth = async () => {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session');

  if (!sessionCookie) {
    return { isAuthenticated: false, error: 'No session cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionCookie.value, secret) as {
      userId: string;
      companyId?: string;
    };
    return { isAuthenticated: true, userId: decoded.userId, companyId: decoded.companyId };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// Helper function to normalize selling option data
function normalizeSellingOption(
  option: any,
  parentName: string,
  parentId: string,
  sourceType: 'recipe' | 'ingredient'
) {
  return {
    id: option._id?.toString() || new Types.ObjectId().toString(),
    name: parentName,
    sourceType,
    sourceId: parentId,
    unitOfSellingShortCode: option.unitOfSelling?.toString() || 'N/A',
    priceWithoutTax: Number(option.priceWithoutTax) || 0,
    priceWithTax: Number(option.priceWithTax) || 0,
    visibility: {
      type: option.visibility?.type || 'ALL_LOCATIONS',
      locations: option.visibility?.locations || [],
      externalAccess: option.visibility?.externalAccess || false
    }
  };
}

// Helper function to sync branch inventory with selling options
async function syncBranchInventory(
  companyId: string,
  sellingOption: {
    id: string;
    sourceId: string;
    sourceType: 'recipe' | 'ingredient';
    visibility: {
      type: 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';
      locations?: string[];
    };
    unitOfSellingShortCode: string;
  },
  session?: any
) {
  // Get relevant locations based on visibility
  let locationIds: string[] = [];
  if (sellingOption.visibility.type === 'ALL_LOCATIONS') {
    const locations = await Location.find({ companyId }).select('_id');
    locationIds = locations.map(loc => loc._id.toString());
  } else if (sellingOption.visibility.type === 'SPECIFIC_LOCATIONS' && sellingOption.visibility.locations) {
    locationIds = sellingOption.visibility.locations;
  }

  // Skip if no locations or external only
  if (locationIds.length === 0 || sellingOption.visibility.type === 'EXTERNAL_ONLY') {
    return;
  }

  // Get the source item (recipe or ingredient)
  const Model = sellingOption.sourceType === 'recipe' ? Recipe : Ingredient;
  const sourceItem = await (Model as any).findById(sellingOption.sourceId); // Cast Model to any to resolve union type issue
  
  if (!sourceItem) {
    throw new Error(`${sellingOption.sourceType} not found`);
  }

  // For ingredients, ensure we have the canonical baseUomId
  let baseUomId: Types.ObjectId | string | undefined;
  if (sellingOption.sourceType === 'ingredient') {
    baseUomId = (sourceItem as any).baseUomId;
    if (!baseUomId) {
      throw new Error(`Ingredient ${sellingOption.sourceId} missing baseUomId`);
    }
  }

  // Create or update branch inventory for each location
  const operations = locationIds.map(async (locationId) => {
    const existingInventory = await BranchInventory.findOne({
      companyId,
      locationId,
      itemId: sellingOption.sourceId,
      itemType: sellingOption.sourceType.toUpperCase(),
      sellingOptionId: sellingOption.id
    }).session(session);

    if (existingInventory) {
      // Update existing inventory, ensuring baseUomId is correct for ingredients
      if (sellingOption.sourceType === 'ingredient' && baseUomId) {
        existingInventory.baseUomId = baseUomId as Types.ObjectId; // Ensure baseUomId is defined and cast to ObjectId
      }
      existingInventory.isActive = true;
      await existingInventory.save({ session });
    } else {
      // Create new inventory with correct baseUomId
      await BranchInventory.create([{
        companyId,
        locationId,
        itemId: sellingOption.sourceId,
        itemType: sellingOption.sourceType.toUpperCase(),
        sellingOptionId: sellingOption.id,
        baseUomId: baseUomId,
        currentStock: 0,
        parLevel: 0,
        reorderPoint: 0,
        isActive: true
      }], { session });
    }
  });

  await Promise.all(operations);
}

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      console.error('Authentication failed:', authResult.error);
      return Response.json(
        { error: 'Authentication failed', details: authResult.error },
        { status: 401 }
      );
    }

    let params;
    try {
      params = await context.params;
    } catch (error: unknown) {
      console.error('Failed to get params:', error);
      return Response.json(
        { error: 'Invalid parameters', details: 'Failed to get company ID from URL' },
        { status: 400 }
      );
    }

    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    if (!companyId || !requestCompanyId) {
      console.error('Missing required IDs:', { companyId, requestCompanyId });
      return Response.json(
        { error: 'Missing required parameters', details: 'Company ID is required in both URL and headers' },
        { status: 400 }
      );
    }

    const searchParams = req.nextUrl.searchParams;
    const locationsParam = searchParams.get('locations');
    const locationIds = locationsParam?.split(',').filter(Boolean);
    const sourceType = searchParams.get('sourceType'); // 'recipe', 'ingredient', or null for all

    console.log('Request params:', {
      companyId,
      requestCompanyId,
      locationsParam,
      locationIds,
      sourceType
    });

    try {
      await validateCompanyAccess(companyId, requestCompanyId);
    } catch (error: unknown) {
      console.error('Company access validation failed:', error);
      return Response.json(
        { error: 'Access denied', details: error instanceof Error ? error.message : 'Company access validation failed' },
        { status: 403 }
      );
    }

    try {
      await dbConnect();
      console.log('Database connected successfully');
    } catch (error: unknown) {
      console.error('Database connection failed:', error);
      return Response.json(
        { error: 'Database error', details: 'Failed to connect to database' },
        { status: 500 }
      );
    }

    // Verify models are registered
    console.log('Available models:', Object.keys(mongoose.models));

    const companyObjId = new Types.ObjectId(companyId);

    // Convert location IDs to ObjectIds, filtering out invalid ones
    const locationObjectIds = locationIds?.map(id => {
      try {
        return new Types.ObjectId(id);
      } catch (error: unknown) {
        console.warn(`Invalid location ID: ${id}`);
        return null;
      }
    }).filter((id): id is Types.ObjectId => id !== null) || [];

    console.log('Location ObjectIds:', locationObjectIds.map(id => id.toString()));

    // Build visibility filter based on location IDs
    const visibilityFilter = locationIds?.length 
      ? {
          $and: [
            { companyId: companyObjId },
            { canBeSold: true },
            { 'sellingDetails.0': { $exists: true } },
            {
              $or: [
                { 
                  'sellingDetails': { 
                    $elemMatch: { 
                      'visibility.type': 'ALL_LOCATIONS'
                    }
                  }
                },
                { 
                  'sellingDetails': { 
                    $elemMatch: { 
                      'visibility.type': 'SPECIFIC_LOCATIONS',
                      'visibility.locations': { 
                        $in: locationObjectIds
                      }
                    }
                  }
                }
              ]
            }
          ]
        }
      : {
          companyId: companyObjId,
          canBeSold: true,
          'sellingDetails.0': { $exists: true }
        };

    console.log('Visibility filter:', JSON.stringify(visibilityFilter, null, 2));

    try {
      // Fetch data based on sourceType
      const [recipes, ingredients] = await Promise.all([
        sourceType === 'ingredient' 
          ? Promise.resolve([]) 
          : mongoose.models.Recipe.find(visibilityFilter)
            .select('_id name sellingDetails')
            .lean()
            .exec(),
        sourceType === 'recipe' 
          ? Promise.resolve([]) 
          : mongoose.models.Ingredient.find(visibilityFilter)
            .select('_id name sellingDetails')
            .lean()
            .exec()
      ]).catch(error => {
        console.error('Database query failed:', error);
        throw new Error('Failed to fetch selling options from database');
      });

      console.log('Query results:', {
        recipesCount: recipes.length,
        ingredientsCount: ingredients.length
      });

      // Map and normalize the selling options, filtering by location visibility
      const recipeOptions = recipes.flatMap(recipe => 
        (recipe.sellingDetails || [])
          .filter((option: any) => {
            // If no locations selected, include all options
            if (!locationIds?.length) return true;
            
            // Always include ALL_LOCATIONS options
            if (option.visibility?.type === 'ALL_LOCATIONS') return true;
            
            // For SPECIFIC_LOCATIONS, check if any selected location is in the option's locations
            if (option.visibility?.type === 'SPECIFIC_LOCATIONS' && option.visibility.locations) {
              const optionLocationIds = option.visibility.locations.map((loc: any) => loc.toString());
              return locationIds.some(id => optionLocationIds.includes(id));
            }
            
            return false;
          })
          .map((option: any) => normalizeSellingOption(option, recipe.name, (typeof recipe._id === 'string' ? recipe._id : (recipe._id as unknown as Types.ObjectId).toString()), 'recipe'))
      );

      const ingredientOptions = ingredients.flatMap(ingredient => 
        (ingredient.sellingDetails || [])
          .filter((option: any) => {
            // If no locations selected, include all options
            if (!locationIds?.length) return true;
            
            // Always include ALL_LOCATIONS options
            if (option.visibility?.type === 'ALL_LOCATIONS') return true;
            
            // For SPECIFIC_LOCATIONS, check if any selected location is in the option's locations
            if (option.visibility?.type === 'SPECIFIC_LOCATIONS' && option.visibility.locations) {
              const optionLocationIds = option.visibility.locations.map((loc: any) => loc.toString());
              return locationIds.some(id => optionLocationIds.includes(id));
            }
            
            return false;
          })
          .map((option: any) => normalizeSellingOption(option, ingredient.name, (typeof ingredient._id === 'string' ? ingredient._id : (ingredient._id as unknown as Types.ObjectId).toString()), 'ingredient'))
      );

      // Combine all options
      const allOptions = [...recipeOptions, ...ingredientOptions];

      console.log('Response summary:', {
        totalOptions: allOptions.length,
        recipeOptionsCount: recipeOptions.length,
        ingredientOptionsCount: ingredientOptions.length,
        locationFilter: locationIds,
        sourceTypeFilter: sourceType
      });

      return Response.json(allOptions, {
        status: 200,
      });
    } catch (dbError) {
      console.error('Database query error:', dbError);
      return Response.json(
        { error: 'Database error', details: dbError instanceof Error ? dbError.message : 'Failed to query database' },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('Error in GET /selling-options:', error);
    return Response.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json({ message: authResult.error }, {
        status: 401,
      });
    }

    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");

    try {
      await validateCompanyAccess(companyId, requestCompanyId);
    } catch (error: unknown) {
      return Response.json(
        { message: error instanceof Error ? error.message : 'Company access validation failed' },
        { status: 403 }
      );
    }

    const data = await req.json();
    const { sellingOptions } = data;

    await dbConnect();
    const session = await mongoose.startSession(); // Use imported mongoose directly
    
    try {
      await session.withTransaction(async () => {
        // Process each selling option
        for (const option of sellingOptions) {
          // Update recipe/ingredient selling options
          if (option.sourceType === 'recipe') {
            await Recipe.findByIdAndUpdate(
              option.sourceId,
              { 'sellingOptions.$[elem]': option },
              { 
                arrayFilters: [{ 'elem._id': option.id }],
                session 
              }
            );
          } else {
            await Ingredient.findByIdAndUpdate(
              option.sourceId,
              { 'sellingOptions.$[elem]': option },
              { 
                arrayFilters: [{ 'elem._id': option.id }],
                session 
              }
            );
          }

          // Sync branch inventory
          await syncBranchInventory(companyId, option, session);
        }
      });

      return Response.json({ message: 'Selling options updated successfully' });
    } catch (error: unknown) {
      throw error;
    } finally {
      session.endSession();
    }
  } catch (error: unknown) {
    console.error('Error updating selling options:', error);
    return Response.json(
      { message: error instanceof Error ? error.message : 'Failed to update selling options' },
      { status: 500 }
    );
  }
}
