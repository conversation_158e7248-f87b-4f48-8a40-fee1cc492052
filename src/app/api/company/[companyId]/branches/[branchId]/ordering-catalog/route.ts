import { NextRequest, NextResponse } from 'next/server';
import { isValidCompanyUser } from '@/lib/server-auth';
import BranchInventory from '@/models/BranchInventory';
import { Ingredient } from '@/models/Ingredient'; 
import Recipe from '@/models/Recipe';
import UOM from '@/models/UOM';
import dbConnect from '@/lib/db';

interface OrderingCatalogItem {
  itemId: string;
  itemType: 'RECIPE' | 'INGREDIENT';
  name: string;
  category: string;
  sellingOptionId: string;
  // Stock information
  centralKitchenStock: number;
  branchCurrentStock: number;
  branchParLevel: number;
  centralKitchenParLevel: number;
  // Ordering details
  orderingUOM: string;
  orderingConversionFactor: number;
  minOrderQuantity: number;
  maxOrderQuantity?: number;
  leadTimeDays: number;
  isOrderable: boolean;
  orderingNotes?: string;
  // Pricing
  baseUOM: string;
  unitCost?: number;
  // Availability calculation
  availableToOrder: number;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { companyId: string; branchId: string } }
) {
  try {
    await dbConnect();
    
    const { companyId, branchId } = params;
    const authResult = await isValidCompanyUser(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: authResult.message || 'Authentication failed' }, { status: 401 });
    }
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const itemType = searchParams.get('itemType') as 'RECIPE' | 'INGREDIENT' | null;

    // Build query
    const query: any = {
      companyId,
      locationId: branchId,
      isActive: true,
      isOrderable: { $ne: false }
    };

    if (category) {
      query.category = category;
    }

    if (itemType) {
      query.itemType = itemType;
    }

    // Get branch inventory items with ordering capabilities
    const branchInventoryItems = await BranchInventory.find(query)
      .populate('baseUomId', 'name abbreviation')
      .populate('itemId')
      .lean();

    const catalogItems: OrderingCatalogItem[] = [];

    for (const inventoryItem of branchInventoryItems) {
      try {
        // Get the source item (ingredient or recipe)
        const sourceItem = inventoryItem.itemType === 'INGREDIENT'
          ? await Ingredient.findById(inventoryItem.itemId).populate('baseUomId', 'name abbreviation').lean()
          : await Recipe.findById(inventoryItem.itemId).lean();

        if (!sourceItem) continue;

        // Find the specific selling option
        const sellingOption = (sourceItem as any).sellingDetails?.find(
          (option: any) => option._id?.toString() === inventoryItem.sellingOptionId?.toString()
        );

        if (!sellingOption) continue;

        // Check if item is available for ordering at this branch
        const isOrderable = inventoryItem.isOrderable !== false && 
                            (inventoryItem.centralKitchenStock || 0) > 0;

        // Calculate available to order (considering central kitchen stock and max order limits)
        let availableToOrder = inventoryItem.centralKitchenStock || 0;
        if (inventoryItem.maxOrderQuantity) {
          availableToOrder = Math.min(availableToOrder, inventoryItem.maxOrderQuantity);
        }

        const catalogItem: OrderingCatalogItem = {
          itemId: inventoryItem.itemId.toString(),
          itemType: inventoryItem.itemType,
          name: (sourceItem as any).name,
          category: inventoryItem.category || 'Uncategorized',
          sellingOptionId: inventoryItem.sellingOptionId.toString(),
          // Stock information
          centralKitchenStock: inventoryItem.centralKitchenStock || 0,
          branchCurrentStock: inventoryItem.currentStock,
          branchParLevel: inventoryItem.branchParLevel || inventoryItem.parLevel,
          centralKitchenParLevel: inventoryItem.centralKitchenParLevel || 0,
          // Ordering details
          orderingUOM: inventoryItem.orderingUOM || (inventoryItem.baseUomId as any)?.name || 'unit',
          orderingConversionFactor: inventoryItem.orderingConversionFactor || 1,
          minOrderQuantity: inventoryItem.minOrderQuantity || 0,
          maxOrderQuantity: inventoryItem.maxOrderQuantity,
          leadTimeDays: inventoryItem.leadTimeDays || 0,
          isOrderable,
          orderingNotes: inventoryItem.orderingNotes,
          // Pricing
          baseUOM: (inventoryItem.baseUomId as any)?.name || 'unit',
          unitCost: inventoryItem.costBasis,
          // Availability
          availableToOrder: isOrderable ? availableToOrder : 0
        };

        catalogItems.push(catalogItem);
      } catch (itemError) {
        console.error(`Error processing inventory item ${inventoryItem._id}:`, itemError);
        continue;
      }
    }

    // Sort by category, then by name
    catalogItems.sort((a, b) => {
      if (a.category !== b.category) {
        return a.category.localeCompare(b.category);
      }
      return a.name.localeCompare(b.name);
    });

    return NextResponse.json({
      success: true,
      data: catalogItems,
      metadata: {
        totalItems: catalogItems.length,
        orderableItems: catalogItems.filter(item => item.isOrderable).length,
        categories: [...new Set(catalogItems.map(item => item.category))].sort()
      }
    });

  } catch (error) {
    console.error('Error fetching ordering catalog:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ordering catalog' },
      { status: 500 }
    );
  }
}