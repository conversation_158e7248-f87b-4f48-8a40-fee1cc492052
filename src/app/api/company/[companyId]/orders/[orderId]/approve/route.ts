import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { requireAuth } from '@/lib/auth-helpers';
import Order from '@/models/Order';
import { Types } from 'mongoose';

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; orderId: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req);
    const { companyId, orderId } = await context.params;

    if (auth.tenantId !== companyId) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 403 });
    }

    const { action, notes } = await req.json(); // action: 'approve' | 'reject' | 'modify'

    const order = await Order.findOne({
      _id: new Types.ObjectId(orderId),
      companyId: new Types.ObjectId(companyId),
      'seller.sellerType': 'CENTRAL_KITCHEN',
      'buyer.buyerType': 'BRANCH'
    });

    if (!order) {
      return NextResponse.json({ success: false, message: 'Shop order not found' }, { status: 404 });
    }

    if (order.approvalStatus !== 'PENDING') {
      return NextResponse.json({ 
        success: false, 
        message: `Order is already ${order.approvalStatus}` 
      }, { status: 400 });
    }

    // Update order based on action
    if (action === 'approve') {
      order.approvalStatus = 'APPROVED';
      order.status = 'CONFIRMED';
      order.approvedBy = new Types.ObjectId(auth.id);
      order.approvedAt = new Date();
    } else if (action === 'reject') {
      order.approvalStatus = 'REJECTED';
      order.status = 'CANCELLED';
      order.approvedBy = new Types.ObjectId(auth.id);
      order.approvedAt = new Date();
    }

    if (notes) {
      order.approvalNotes = notes;
    }

    order.modifiedBy = new Types.ObjectId(auth.id);
    await order.save();

    return NextResponse.json({
      success: true,
      message: `Order ${action}d successfully`,
      data: {
        orderId: order._id,
        orderNumber: order.orderNumber,
        status: order.status,
        approvalStatus: order.approvalStatus
      }
    });

  } catch (error) {
    console.error('Error processing order approval:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}