// src/app/api/company/[companyId]/inventory/current/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { isValidCompanyUser } from '@/lib/server-auth';
import BranchInventory from '@/models/BranchInventory';
import mongoose, { Types } from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Validate user has access to company
    const authResult = await isValidCompanyUser(request, companyId);
    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get('locationId');
    const itemType = searchParams.get('itemType'); // 'RECIPE' or 'INGREDIENT'
    const category = searchParams.get('category');
    const query = searchParams.get('query') || '';
    const lowStock = searchParams.get('lowStock') === 'true';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '50', 10);
    const skip = (page - 1) * limit;

    // Build query object
    const queryObj: any = {
      companyId: new Types.ObjectId(companyId),
      isActive: true
    };

    if (locationId) {
      queryObj.locationId = new Types.ObjectId(locationId);
    }

    if (itemType && ['RECIPE', 'INGREDIENT'].includes(itemType)) {
      queryObj.itemType = itemType;
    }

    if (category) {
      queryObj.category = category;
    }

    if (query) {
      // We need to join with recipes/ingredients to search by name
      // This will be implemented with an aggregation pipeline
    }

    if (lowStock) {
      // Items where currentStock is below reorderPoint
      queryObj.$expr = { $lt: ['$currentStock', '$reorderPoint'] };
    }

    // Get total count for pagination
    const totalCount = await BranchInventory.countDocuments(queryObj);

    // Perform the main query
    const inventoryQuery = BranchInventory.find(queryObj)
      .sort({ itemType: 1, category: 1 })
      .skip(skip)
      .limit(limit);

    // If we're searching by name, we need to use aggregation instead
    let inventory;
    if (query) {
      // Create aggregation pipeline to join with recipes/ingredients for name search
      inventory = await BranchInventory.aggregate([
        { $match: queryObj },
        {
          $lookup: {
            from: 'recipes',
            localField: 'itemId',
            foreignField: '_id',
            as: 'recipeDetails'
          }
        },
        {
          $lookup: {
            from: 'ingredients',
            localField: 'itemId',
            foreignField: '_id',
            as: 'ingredientDetails'
          }
        },
        {
          $addFields: {
            itemName: {
              $cond: {
                if: { $eq: ['$itemType', 'RECIPE'] },
                then: { $arrayElemAt: ['$recipeDetails.name', 0] },
                else: { $arrayElemAt: ['$ingredientDetails.name', 0] }
              }
            }
          }
        },
        {
          $match: {
            itemName: { $regex: query, $options: 'i' }
          }
        },
        { $sort: { itemType: 1, category: 1 } },
        { $skip: skip },
        { $limit: limit }
      ]);
    } else {
      inventory = await inventoryQuery.exec();
    }

    // Get detailed item information
    const inventoryWithDetails = await Promise.all(
      inventory.map(async (item: any) => {
        let itemDetails = null;
        if (item.itemType === 'RECIPE') {
          // Get recipe details
          const Recipe = mongoose.model('Recipe');
          itemDetails = await Recipe.findById(item.itemId);
        } else if (item.itemType === 'INGREDIENT') {
          // Get ingredient details
          const Ingredient = mongoose.model('Ingredient');
          itemDetails = await Ingredient.findById(item.itemId);
        }

        return {
          ...item.toObject ? item.toObject() : item,
          itemDetails: itemDetails ? {
            name: itemDetails.name,
            description: itemDetails.description,
            category: itemDetails.category
          } : null
        };
      })
    );

    return NextResponse.json({
      inventory: inventoryWithDetails,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error: any) {
    console.error('Error fetching inventory:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
}
