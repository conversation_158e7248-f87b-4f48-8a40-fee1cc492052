// src/app/api/company/[companyId]/inventory/transfer-request/route.ts
import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import TransferRequest from '@/models/TransferRequest';
import Location from '@/models/Location';
import { Ingredient } from '@/models/Ingredient';
import Recipe from '@/models/Recipe';
import BranchInventory from '@/models/BranchInventory';
import InventoryTransaction from '@/models/InventoryTransaction';

// Helper function to verify authentication
const verifyAuth = async () => {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session');

  if (!sessionCookie) {
    return { isAuthenticated: false, error: 'No session cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionCookie.value, secret) as {
      userId: string;
      companyId?: string;
    };
    return { isAuthenticated: true, userId: decoded.userId, companyId: decoded.companyId };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// Helper function to check if user has access to location
const checkLocationAccess = async (
  companyId: string,
  locationId: string
) => {
  const location = await Location.findOne({
    _id: new Types.ObjectId(locationId),
    companyId: new Types.ObjectId(companyId)
  });
  
  if (!location) {
    throw new Error('Location not found or access denied');
  }
  
  return location;
};

// Helper function to check item availability at source location
const checkItemAvailability = async (
  companyId: string,
  locationId: string,
  itemId: string,
  itemType: 'INGREDIENT' | 'RECIPE',
  quantity: number
) => {
  if (itemType === 'INGREDIENT') {
    // Check central kitchen inventory
    if (await Location.findOne({ _id: new Types.ObjectId(locationId), type: 'KITCHEN' })) {
      const ingredient: any = await (Ingredient as any).findOne({
        _id: new Types.ObjectId(itemId),
        companyId: new Types.ObjectId(companyId)
      });
      
      if (!ingredient) {
        throw new Error(`Ingredient ${itemId} not found`);
      }
      
      if (ingredient.currentStock < quantity) {
        throw new Error(`Insufficient stock for ingredient ${ingredient.name}`);
      }
      
      return {
        name: ingredient.name,
        available: true,
        currentStock: ingredient.currentStock,
        costBasis: ingredient.costBasis || 0
      };
    }
    
    // Check branch inventory
    const branchInventory: any = await (BranchInventory as any).findOne({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      itemId: new Types.ObjectId(itemId),
      itemType: 'INGREDIENT'
    });
    
    if (!branchInventory) {
      throw new Error(`Ingredient ${itemId} not found at location ${locationId}`);
    }
    
    if (branchInventory.currentStock < quantity) {
      throw new Error(`Insufficient stock at location ${locationId}`);
    }
    
    return {
      name: branchInventory.name || 'Unknown',
      available: true,
      currentStock: branchInventory.currentStock,
      costBasis: branchInventory.costBasis || 0
    };
  }
  
  if (itemType === 'RECIPE') {
    // Check central kitchen inventory
    if (await Location.findOne({ _id: new Types.ObjectId(locationId), type: 'KITCHEN' })) {
      const recipe: any = await (Recipe as any).findOne({
        _id: new Types.ObjectId(itemId),
        companyId: new Types.ObjectId(companyId)
      });
      
      if (!recipe) {
        throw new Error(`Recipe ${itemId} not found`);
      }
      
      if (recipe.currentStock < quantity) {
        throw new Error(`Insufficient stock for recipe ${recipe.name}`);
      }
      
      return {
        name: recipe.name,
        available: true,
        currentStock: recipe.currentStock,
        costBasis: 0 // Cost basis typically determined by components
      };
    }
    
    // Check branch inventory
    const branchInventory: any = await (BranchInventory as any).findOne({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      itemId: new Types.ObjectId(itemId),
      itemType: 'RECIPE'
    });
    
    if (!branchInventory) {
      throw new Error(`Recipe ${itemId} not found at location ${locationId}`);
    }
    
    if (branchInventory.currentStock < quantity) {
      throw new Error(`Insufficient stock at location ${locationId}`);
    }
    
    return {
      name: branchInventory.name || 'Unknown',
      available: true,
      currentStock: branchInventory.currentStock,
      costBasis: branchInventory.costBasis || 0
    };
  }
  
  throw new Error(`Invalid item type: ${itemType}`);
};

// GET - Fetch transfer requests
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status');
    const sourceLocationId = searchParams.get('sourceLocationId');
    const destinationLocationId = searchParams.get('destinationLocationId');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = parseInt(searchParams.get('skip') || '0');
    
    // Build query
    const query: any = { companyId: new Types.ObjectId(companyId) };
    
    if (status) {
      query.status = status;
    }
    
    if (sourceLocationId) {
      query.sourceLocationId = new Types.ObjectId(sourceLocationId);
    }
    
    if (destinationLocationId) {
      query.destinationLocationId = new Types.ObjectId(destinationLocationId);
    }
    
    // Fetch requests
    const transferRequests = await TransferRequest.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const totalCount = await TransferRequest.countDocuments(query);
    
    // Return response
    return Response.json({
      totalCount,
      results: transferRequests
    }, { status: 200 });
    
  } catch (error: unknown) {
    console.error('Error fetching transfer requests:', error);
    return Response.json({ 
      error: 'Failed to fetch transfer requests',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST - Create a new transfer request
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated || !authResult.userId) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.sourceLocationId || !data.destinationLocationId || !data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return Response.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Validate locations
    try {
      await checkLocationAccess(companyId, data.sourceLocationId);
      await checkLocationAccess(companyId, data.destinationLocationId);
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Location access error' }, { status: 403 });
    }
    
    // Validate items and check availability
    const itemDetails = [];
    
    for (const item of data.items) {
      try {
        const availability = await checkItemAvailability(
          companyId,
          data.sourceLocationId,
          item.itemId,
          item.itemType,
          item.quantity
        );
        
        itemDetails.push({
          ...item,
          name: availability.name,
          available: availability.available,
          currentStock: availability.currentStock,
          costBasis: availability.costBasis
        });
      } catch (error: unknown) {
        return Response.json({ 
          error: 'Item availability error', 
          details: error instanceof Error ? error.message : 'Unknown error' 
        }, { status: 400 });
      }
    }
    
    // Determine initial status and approval flags
    const initialStatus = 'PENDING';
    const sourceApproved = false;
    const destinationApproved = false;
    
    // If source and destination are the same, auto-approve one side
    if (data.sourceLocationId === data.destinationLocationId) {
      return Response.json({ error: 'Source and destination cannot be the same' }, { status: 400 });
    }
    
    // Create new transfer request
    const transferRequest = await TransferRequest.create({
      companyId: new Types.ObjectId(companyId),
      sourceLocationId: new Types.ObjectId(data.sourceLocationId),
      destinationLocationId: new Types.ObjectId(data.destinationLocationId),
      requestedBy: new Types.ObjectId(authResult.userId),
      items: data.items.map((item: any) => ({
        itemId: new Types.ObjectId(item.itemId),
        itemType: item.itemType,
        quantity: item.quantity,
        unitId: new Types.ObjectId(item.unitId),
        cost: item.cost || 0,
        requestedQuantity: item.quantity
      })),
      notes: data.notes || '',
      status: initialStatus,
      sourceApproved,
      destinationApproved,
      priceMarkup: data.priceMarkup
    });
    
    return Response.json({
      success: true,
      message: 'Transfer request created successfully',
      transferRequest: transferRequest.toObject()
    }, { status: 201 });
    
  } catch (error: unknown) {
    console.error('Error creating transfer request:', error);
    return Response.json({ 
      error: 'Failed to create transfer request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PATCH - Update transfer request (approve, reject, etc.)
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated || !authResult.userId) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.transferRequestId || !data.action) {
      return Response.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Find the transfer request
    const transferRequest = await TransferRequest.findOne({
      _id: new Types.ObjectId(data.transferRequestId),
      companyId: new Types.ObjectId(companyId)
    });
    
    if (!transferRequest) {
      return Response.json({ error: 'Transfer request not found' }, { status: 404 });
    }
    
    // Check if request is already completed or cancelled
    if (['COMPLETED', 'CANCELLED', 'REJECTED'].includes(transferRequest.status)) {
      return Response.json({ 
        error: 'Cannot modify a completed, cancelled, or rejected request' 
      }, { status: 400 });
    }
    
    // Process action
    const userId = new Types.ObjectId(authResult.userId);
    const now = new Date();
    
    switch (data.action) {
      case 'APPROVE_SOURCE':
        // Validate that user has access to source location
        try {
          await checkLocationAccess(companyId, transferRequest.sourceLocationId.toString());
        } catch (error: unknown) {
          return Response.json({ error: 'Access denied to source location' }, { status: 403 });
        }
        
        // Update approval fields
        transferRequest.sourceApproved = true;
        transferRequest.sourceApprovedBy = userId;
        transferRequest.sourceApprovedAt = now;
        
        // Update status if both sides are now approved
        if (transferRequest.destinationApproved) {
          transferRequest.status = 'APPROVED';
        } else {
          transferRequest.status = 'SOURCE_APPROVED';
        }
        break;
        
      case 'APPROVE_DESTINATION':
        // Validate that user has access to destination location
        try {
          await checkLocationAccess(companyId, transferRequest.destinationLocationId.toString());
        } catch (error: unknown) {
          return Response.json({ error: 'Access denied to destination location' }, { status: 403 });
        }
        
        // Update approval fields
        transferRequest.destinationApproved = true;
        transferRequest.destinationApprovedBy = userId;
        transferRequest.destinationApprovedAt = now;
        
        // Update status if both sides are now approved
        if (transferRequest.sourceApproved) {
          transferRequest.status = 'APPROVED';
        } else {
          transferRequest.status = 'DESTINATION_APPROVED';
        }
        break;
        
      case 'REJECT':
        // Check if user has access to either location
        try {
          const hasSourceAccess = await checkLocationAccess(companyId, transferRequest.sourceLocationId.toString())
            .then(() => true)
            .catch(() => false);
            
          const hasDestAccess = await checkLocationAccess(companyId, transferRequest.destinationLocationId.toString())
            .then(() => true)
            .catch(() => false);
            
          if (!hasSourceAccess && !hasDestAccess) {
            return Response.json({ error: 'Access denied to both locations' }, { status: 403 });
          }
        } catch (error: unknown) {
          return Response.json({ error: 'Location access error' }, { status: 403 });
        }
        
        // Update status to rejected
        transferRequest.status = 'REJECTED';
        break;
        
      case 'CANCEL':
        // Only requestor or admin can cancel
        if (transferRequest.requestedBy.toString() !== userId.toString()) {
          // TODO: Add admin check
          return Response.json({ error: 'Only the requestor can cancel a request' }, { status: 403 });
        }
        
        // Update status to cancelled
        transferRequest.status = 'CANCELLED';
        break;
        
      case 'UPDATE_QUANTITIES':
        // Check if quantities array is provided
        if (!data.items || !Array.isArray(data.items)) {
          return Response.json({ error: 'Items array required for quantity updates' }, { status: 400 });
        }
        
        // Check if user has access to source location (only source can update quantities)
        try {
          await checkLocationAccess(companyId, transferRequest.sourceLocationId.toString());
        } catch (error: unknown) {
          return Response.json({ error: 'Access denied to source location' }, { status: 403 });
        }
        
        // Update quantities
        for (const updatedItem of data.items) {
          const item = transferRequest.items.find(
            i => i.itemId.toString() === updatedItem.itemId && i.itemType === updatedItem.itemType
          );
          
          if (item) {
            item.approvedQuantity = updatedItem.approvedQuantity || item.requestedQuantity;
          }
        }
        break;
        
      default:
        return Response.json({ error: 'Invalid action' }, { status: 400 });
    }
    
    // Save the updated request
    await transferRequest.save();
    
    // Return success response
    return Response.json({
      success: true,
      message: `Transfer request ${data.action.toLowerCase()} successfully`,
      transferRequest: transferRequest.toObject()
    }, { status: 200 });
    
  } catch (error: unknown) {
    console.error('Error updating transfer request:', error);
    return Response.json({ 
      error: 'Failed to update transfer request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT - Process an approved transfer request
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated || !authResult.userId) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }
    
    // Get and validate parameters
    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = req.headers.get("company-id");
    const userId = authResult.userId;
    
    try {
      await validateCompanyAccess(companyId, requestCompanyId || '');
    } catch (error: unknown) {
      return Response.json({ error: error instanceof Error ? error.message : 'Access denied' }, { status: 403 });
    }
    
    // Connect to database
    await dbConnect();
    
    // Get request body
    const data = await req.json();
    
    // Validate required fields
    if (!data.transferRequestId || !data.action) {
      return Response.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Find the transfer request
    const transferRequest = await TransferRequest.findOne({
      _id: new Types.ObjectId(data.transferRequestId),
      companyId: new Types.ObjectId(companyId)
    });
    
    if (!transferRequest) {
      return Response.json({ error: 'Transfer request not found' }, { status: 404 });
    }
    
    // Check if request is approved
    if (transferRequest.status !== 'APPROVED') {
      return Response.json({ 
        error: 'Only approved requests can be processed', 
        currentStatus: transferRequest.status
      }, { status: 400 });
    }
    
    // Process action
    switch (data.action) {
      case 'PROCESS_TRANSFER':
        // Process the transfer (this would call the inventory movement API)
        // Normally we'd use fetch here, but for simplicity and to avoid circular dependencies,
        // we'll just create a mock response
        
        // You would implement actual transfer out and transfer in logic here
        // For now, we'll just update the status
        transferRequest.status = 'COMPLETED';
        transferRequest.transferOutTransactionId = new Types.ObjectId();
        transferRequest.transferInTransactionId = new Types.ObjectId();
        
        await transferRequest.save();
        
        return Response.json({
          success: true,
          message: 'Transfer processed successfully',
          transferRequest: transferRequest.toObject()
        }, { status: 200 });
        
      default:
        return Response.json({ error: 'Invalid action' }, { status: 400 });
    }
    
  } catch (error: unknown) {
    console.error('Error processing transfer request:', error);
    return Response.json({ 
      error: 'Failed to process transfer request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
