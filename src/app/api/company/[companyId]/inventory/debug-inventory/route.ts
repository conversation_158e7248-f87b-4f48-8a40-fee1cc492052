// Debug route to diagnose BranchInventory issues
import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory';
import { Ingredient } from '@/models/Ingredient';
// import Recipe from '@/models/Recipe';
import { Types } from 'mongoose';

export async function GET(req: NextRequest, context: { params: Promise<{ companyId: string }> }) {
  try {
    // Connect to database
    await dbConnect();
    
    const { companyId } = await context.params;
    
    // Authenticate and authorize
    const authUser = await requireAuth(['admin'])(req);
    if (authUser.tenantId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }
    
    // Use the same values from your test case
    const ingredientId = '676aca53d253bdfa34d20544';
    const locationId = '67697ac879a92509b1f9f245';
    // const unitId = '676a982bd253bdfa34d2050c';
    
    // Detailed diagnostic logging
    console.log(`DEBUG: Fetching ingredient ${ingredientId}`);
    
    // Load the ingredient
    const ingredient = await Ingredient.findById(ingredientId);
    if (!ingredient) {
      return NextResponse.json({ error: 'Ingredient not found' }, { status: 404 });
    }
    
    // Detailed logging of the ingredient's selling details
    console.log('DEBUG: Ingredient found:', ingredient.name);
    console.log('DEBUG: Selling details:', JSON.stringify(ingredient.sellingDetails, null, 2));
    
    // Collect information about selling options for troubleshooting
    const diagnosticInfo = {
      ingredient: {
        name: ingredient.name,
        id: (ingredient._id as Types.ObjectId).toString(),
        baseUomId: ingredient.baseUomId?.toString(),
        sellingDetailsCount: ingredient.sellingDetails?.length || 0
      },
      sellingDetails: ingredient.sellingDetails?.map((option: any) => ({
        id: (option._id as Types.ObjectId).toString(),
        unitOfSelling: (option.unitOfSelling as any).toString(),
        visibility: option.visibility?.type
      }))
    };
    
    // Create a test branch inventory with direct debug mode that skips validation
    const branchInventory = new BranchInventory({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      itemId: new Types.ObjectId(ingredientId),
      itemType: 'INGREDIENT',
      currentStock: 0,
      pendingStock: 5,
      minStock: 0,
      maxStock: 0,
      parLevel: 0,
      reorderPoint: 0,
      sellingOptionId: ingredient.sellingDetails?.[0]?.unitOfSelling,
      isActive: true,
      isLocked: false,
      baseUomId: ingredient.baseUomId,
      lastUpdated: new Date()
    }) as unknown as IBranchInventory;
    
    // Critical workaround: Set the validation skip flag
    branchInventory.skipSellingOptionValidation = true;
    
    // Log pre-save state
    console.log('DEBUG: Pre-save branch inventory:', {
      sellingOptionId: branchInventory.sellingOptionId,
      skipFlag: branchInventory.skipSellingOptionValidation
    });
    
    // Attempt to save it
    try {
      await branchInventory.save();
      console.log('DEBUG: Branch inventory saved successfully');
      
      return NextResponse.json({
        success: true,
        message: 'Branch inventory created successfully',
        diagnosticInfo,
        branchInventory: {
          id: (branchInventory._id as Types.ObjectId).toString(),
          sellingOptionId: (branchInventory.sellingOptionId as Types.ObjectId)?.toString(),
          skipValidation: branchInventory.skipSellingOptionValidation
        }
      });
    } catch (saveError) {
      console.error('DEBUG: Branch inventory save error:', saveError);
      
      return NextResponse.json({
        success: false,
        error: saveError instanceof Error ? saveError.message : String(saveError),
        stack: saveError instanceof Error ? saveError.stack : undefined,
        diagnosticInfo
      });
    }
  } catch (error: unknown) {
    console.error('DEBUG ERROR:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
