import { NextRequest, NextResponse } from 'next/server';
import mongoose, { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import BranchInventory from '@/models/BranchInventory';
import { requireAuth } from '@/lib/auth-helpers';

/**
 * POST /api/company/:companyId/inventory/migrate
 * 
 * Migration endpoint to add pendingStock field to existing inventory records
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authenticate and authorize
    const { companyId } = await params;
    let authUser: any;
    try {
      authUser = await requireAuth()(req);
    } catch (error: any) {
      return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 401 });
    }
    // Check admin role
    if (authUser.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required for this operation' }, { status: 403 });
    }
    // Validate company ID matches
    if (authUser.tenantId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }

    // Connect to the database
    await dbConnect();

    // Get all inventory records for this company that don't have pendingStock
    const inventoryItems = await BranchInventory.find({
      companyId: new Types.ObjectId(companyId),
      $or: [
        { pendingStock: { $exists: false } },
        { pendingStock: null }
      ]
    }) as any[];

    if (!inventoryItems || inventoryItems.length === 0) {
      return NextResponse.json({ message: 'No inventory items need migration' }, { status: 200 });
    }

    // Update each inventory item to add pendingStock
    const updateResults = [];
    let successCount = 0;
    let errorCount = 0;

    for (const item of inventoryItems) {
      try {
        item.pendingStock = 0;
        await item.save();
        updateResults.push({
          itemId: (item.itemId as Types.ObjectId).toString(),
          status: 'updated',
          pendingStock: 0
        });
        successCount++;
      } catch (error: unknown) {
        console.error(`Error updating item ${(item._id as Types.ObjectId).toString()}:`, error);
        updateResults.push({
          itemId: (item.itemId as Types.ObjectId).toString(),
          status: 'error',
          error: error instanceof Error ? error.message : String(error)
        });
        errorCount++;
      }
    }

    // Return results
    return NextResponse.json({
      success: true,
      message: `Migration completed. ${successCount} items updated, ${errorCount} errors.`,
      totalItems: inventoryItems.length,
      successCount,
      errorCount,
      updates: updateResults
    });
  } catch (error: unknown) {
    console.error('Error in migration:', error);
    return NextResponse.json({ error: 'Server error during migration' }, { status: 500 });
  }
}
