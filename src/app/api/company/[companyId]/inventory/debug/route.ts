import { NextRequest, NextResponse } from 'next/server';
import mongoose, { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import BranchInventory from '@/models/BranchInventory';
import { requireAuth } from '@/lib/auth-helpers';

/**
 * GET /api/company/:companyId/inventory/debug
 * 
 * Debug endpoint to view raw inventory data
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    // Authenticate user
    let authUser;
    try {
      authUser = await requireAuth()(req);
    } catch (error: any) {
      return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 401 });
    }

    // Validate company ID matches tenantId
    const { companyId } = await params;
    if (authUser.tenantId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }

    // Connect to the database
    await dbConnect();

    // Get location ID from query params
    const locationId = req.nextUrl.searchParams.get('locationId');
    const itemId = req.nextUrl.searchParams.get('itemId');
    
    if (!locationId) {
      return NextResponse.json({ error: 'Location ID is required' }, { status: 400 });
    }

    // Find inventory items
    const query: any = {
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId)
    };
    
    if (itemId) {
      query.itemId = new Types.ObjectId(itemId);
    }
    
    const inventoryItems = await BranchInventory.find(query).lean();

    if (!inventoryItems || inventoryItems.length === 0) {
      return NextResponse.json({ error: 'No inventory items found' }, { status: 404 });
    }

    // Return raw inventory data
    return NextResponse.json({
      success: true,
      apiVersion: "1.0",
      timestamp: new Date().toISOString(),
      count: inventoryItems.length,
      items: inventoryItems.map(item => ({
        ...item,
        _id: item._id.toString(),
        companyId: item.companyId.toString(),
        locationId: item.locationId.toString(),
        itemId: item.itemId.toString(),
        baseUomId: item.baseUomId.toString()
      }))
    });
  } catch (error: unknown) {
    console.error('Error fetching inventory debug data:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
