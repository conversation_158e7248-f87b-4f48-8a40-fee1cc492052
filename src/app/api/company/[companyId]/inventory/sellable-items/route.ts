import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { Ingredient } from '@/models/Ingredient';
import Recipe from '@/models/Recipe';
import { IRecipe } from '@/models/Recipe';

// Define interfaces for the populated documents
interface PopulatedUOM {
  _id: string;
  name: string;
  shortCode: string;
}

/**
 * GET /api/company/[companyId]/inventory/sellable-items
 * 
 * Returns all inventory items (ingredients and recipes) that can be sold and used in menu items
 * Supports filtering by search term, type (ingredient/recipe), and category
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect();
    const { companyId } = await context.params;

    // Support pagination and filtering
    const searchParams = req.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || ''; // 'ingredient' or 'recipe'
    const category = searchParams.get('category') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;

    // Base query for both ingredients and recipes
    const baseQuery: any = { 
      companyId: new Types.ObjectId(companyId),
      canBeSold: true 
    };

    // Add search filter if provided
    if (search) {
      baseQuery.name = { $regex: search, $options: 'i' };
    }

    // Add category filter if provided
    if (category) {
      baseQuery.category = category;
    }

    let items = [];
    let total = 0;

    // Get ingredients matching criteria
    if (!type || type === 'ingredient') {
      const ingredients = await Ingredient.find(baseQuery)
        .select('_id name description baseUomId category canBeSold sellingDetails')
        .populate<{ baseUomId: PopulatedUOM }>('baseUomId', 'name shortCode')
        .sort({ name: 1 })
        .skip(type ? 0 : skip)
        .limit(type ? 1000 : limit)
        .lean();

      // Transform to standardized format
      items.push(...ingredients.map(ingredient => ({
        id: ingredient._id.toString(),
        name: ingredient.name,
        description: ingredient.description,
        type: 'ingredient',
        unit: ingredient.baseUomId?.shortCode || '',
        unitName: ingredient.baseUomId?.name || '',
        category: ingredient.category,
        sellingDetails: ingredient.sellingDetails || []
      })));

      if (!type) {
        total += await Ingredient.countDocuments(baseQuery);
      }
    }

    // Get recipes matching criteria
    if (!type || type === 'recipe') {
      const recipes = await Recipe.find(baseQuery)
        .select('_id name description baseYieldUOM category canBeSold sellingDetails')
        .populate<{ baseYieldUOM: PopulatedUOM }>('baseYieldUOM', 'name shortCode')
        .sort({ name: 1 })
        .skip(type ? 0 : skip)
        .limit(type ? 1000 : limit)
        .lean();

      // Transform to standardized format
      items.push(...recipes.map((recipe: any) => ({
        id: recipe._id.toString(),
        name: recipe.name,
        description: recipe.description,
        type: 'recipe',
        unit: recipe.baseYieldUOM?.shortCode || '',
        unitName: recipe.baseYieldUOM?.name || '',
        category: recipe.category,
        sellingDetails: recipe.sellingDetails || []
      })));

      if (!type) {
        total += await Recipe.countDocuments(baseQuery);
      }
    }

    // Sort combined results
    if (type) {
      items.sort((a, b) => a.name.localeCompare(b.name));
      items = items.slice(skip, skip + limit);
      total = items.length;
    }

    return NextResponse.json({
      items,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    console.error('Error in GET /api/company/[companyId]/inventory/sellable-items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sellable inventory items', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
