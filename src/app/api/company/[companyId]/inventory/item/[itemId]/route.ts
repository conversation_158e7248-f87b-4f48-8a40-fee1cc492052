import { NextRequest, NextResponse } from 'next/server';
import mongoose, { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory';
import { requireAuth } from '@/lib/auth-helpers';

/**
 * GET /api/company/:companyId/inventory/item/:itemId
 * 
 * Get inventory details for a specific item
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string; itemId: string }> }
) {
  try {
    // Authenticate user
    let authUser: any;
    try {
      authUser = await requireAuth()(req);
    } catch (error: any) {
      return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 401 });
    }

    // Validate company ID matches
    const { companyId, itemId } = await params;
    if (authUser.tenantId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }

    // Connect to the database
    await dbConnect();

    // Get location ID from query params
    const locationId = req.nextUrl.searchParams.get('locationId');
    if (!locationId) {
      return NextResponse.json({ error: 'Location ID is required' }, { status: 400 });
    }

    // Find inventory item
    const inventoryItem = await BranchInventory.findOne({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      itemId: new Types.ObjectId(itemId)
    }) as IBranchInventory | null;

    if (!inventoryItem) {
      return NextResponse.json({ error: 'Inventory item not found' }, { status: 404 });
    }

    // Return detailed inventory information
    return NextResponse.json({
      success: true,
      apiVersion: "1.0",
      timestamp: new Date().toISOString(),
      inventoryItem: {
        id: (inventoryItem._id as Types.ObjectId).toString(),
        itemId: (inventoryItem.itemId as Types.ObjectId).toString(),
        itemType: inventoryItem.itemType,
        currentStock: inventoryItem.currentStock,
        pendingStock: inventoryItem.pendingStock || 0,
        parLevel: inventoryItem.parLevel,
        reorderPoint: inventoryItem.reorderPoint,
        baseUomId: (inventoryItem.baseUomId as Types.ObjectId).toString(),
        locationId: (inventoryItem.locationId as Types.ObjectId).toString(),
        companyId: (inventoryItem.companyId as Types.ObjectId).toString(),
        lastUpdated: inventoryItem.lastUpdated,
        isActive: inventoryItem.isActive
      },
      debugInfo: {
        queryParams: {
          companyId: companyId,
          locationId: locationId,
          itemId: itemId
        },
        rawDocument: JSON.parse(JSON.stringify(inventoryItem))
      }
    });
  } catch (error: unknown) {
    console.error('Error fetching inventory item:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
