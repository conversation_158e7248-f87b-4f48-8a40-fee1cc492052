// src/app/api/company/[companyId]/inventory/movement/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { isValidCompanyUser } from '@/lib/server-auth';
import { validateTestApiKey } from '@/middleware/testApiKey';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory';
import InventoryTransaction, { IInventoryTransaction } from '@/models/InventoryTransaction';
import Recipe from '@/models/Recipe';
import { Ingredient } from '@/models/Ingredient';
import mongoose, { Types } from 'mongoose';
import User from '@/models/User';

// Define interfaces for the request data
interface InventoryMovementItem {
  itemId: string;
  itemType: 'INGREDIENT' | 'RECIPE';
  quantity: number;
  unitId: string;
  cost?: number;
  lotNumber?: string;
  expiryDate?: string;
  reason?: string; 
  expectedQuantity?: number;
}

interface InventoryMovementRequest {
  transactionType: 'PURCHASE' | 'WASTAGE' | 'ADJUSTMENT' | 'PRODUCTION_INPUT' | 
                  'PRODUCTION_OUTPUT' | 'TRANSFER_OUT' | 'TRANSFER_IN' | 'COUNT' |
                  'SALE' | 'RECEIVED' | 'DISPATCHED' | 'THEFT';
  locationId: string;
  notes?: string;
  reference?: {
    type: 'STOCK_COUNT' | 'PURCHASE_ORDER' | 'PRODUCTION' | 'TRANSFER' | 
          'ADJUSTMENT' | 'ORDER' | 'DELIVERY_NOTE' | 'WASTAGE_REPORT';
    id: string;
  };
  items: InventoryMovementItem[];
  
  // Specific fields by type
  supplierName?: string;
  invoiceNumber?: string;
  purchaseOrderRef?: string;
  disposalMethod?: string;
  sourceLocationId?: string;
  destinationLocationId?: string;
  transferReference?: string;
  recipeId?: string;  // For production transactions
  countBatch?: string; // For count transactions
}

interface AffectedItem {
  itemId: string;
  itemName: string;
  previousStock: number;
  newStock: number;
  difference: number;
  transactionId?: Types.ObjectId;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params;
    await dbConnect();

    // Validate company access
    const companyHeader = request.headers.get('company-id');
    if (!companyHeader || companyHeader !== companyId) {
      return NextResponse.json({ error: 'Invalid company header' }, { status: 400 });
    }

    // Try normal JWT authentication first
    let authResult = await isValidCompanyUser(request, companyId);
    
    // If JWT auth fails and we're not in production, try test API key
    if (!authResult.isValid && process.env.NODE_ENV !== 'production') {
      const apiKeyResult = await validateTestApiKey(request, companyId);
      if (apiKeyResult.isValid) {
        // If API key is valid, use its authentication results
        authResult = {
          isValid: true,
          userId: apiKeyResult.userId,
          userRole: apiKeyResult.role as any, // Cast to match expected type
          userType: 'company_user'
        };
      }
    }
    
    // If both authentication methods fail, return unauthorized
    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json() as InventoryMovementRequest;
    
    // Basic validation
    if (!data.locationId || !data.transactionType || !data.items || data.items.length === 0) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Validate items in request
    for (const item of data.items) {
      if (!item.itemId || !item.itemType || !item.quantity || !item.unitId) {
        return NextResponse.json({ error: 'Missing required item fields' }, { status: 400 });
      }
    }
    
    // Transaction type specific validation
    const validationResult = await validateTransactionSpecificFields(data, companyId);
    if (!validationResult.valid) {
      return NextResponse.json({ error: validationResult.error }, { status: 400 });
    }

    // We'll collect these to create them outside the transaction
    const transactionRecords: Partial<IInventoryTransaction>[] = [];
    const affectedItems: AffectedItem[] = [];
    
    // First validate all inventory operations without committing
    // to ensure data integrity
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
      // Process each item in the request
      for (const item of data.items) {
        // Get current inventory for this item
        const inventory = await BranchInventory.findOne({
          companyId: new Types.ObjectId(companyId),
          locationId: new Types.ObjectId(data.locationId),
          itemId: new Types.ObjectId(item.itemId),
          itemType: item.itemType
        }).session(session) as IBranchInventory | null;

        // Check if inventory must exist for this transaction type
        const creationAllowed = ['PURCHASE', 'RECEIVED', 'TRANSFER_IN', 'PRODUCTION_OUTPUT'].includes(data.transactionType);
        if (!inventory && !creationAllowed) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: `Cannot perform ${data.transactionType} on non-existent inventory item: ${item.itemId}` },
            { status: 400 }
          );
        }

        // Calculate new stock levels for both current and pending stock
        const previousStock = inventory ? inventory.currentStock : 0;
        const previousPendingStock = inventory ? inventory.pendingStock || 0 : 0;
        
        // Calculate new current stock
        const newStock = calculateNewStock(previousStock, item.quantity, data.transactionType);
        
        // Calculate new pending stock
        const newPendingStock = calculatePendingStock(previousPendingStock, item.quantity, data.transactionType);
        
        // Special validation for RECEIVED transaction
        if (data.transactionType === 'RECEIVED' && item.quantity > previousPendingStock) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: `Cannot receive ${item.quantity} units of item ${item.itemId} as only ${previousPendingStock} units are pending` },
            { status: 400 }
          );
        }
        
        // Ensure no negative stock for non-count transactions
        if (newStock < 0 && data.transactionType !== 'COUNT' && data.transactionType !== 'ADJUSTMENT') {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: `Transaction would result in negative stock for item ${item.itemId}` },
            { status: 400 }
          );
        }

        // Update or create inventory record
        if (inventory) {
          // For COUNT transactions, store the theoretical value before updating
          const systemStockBeforeCount = data.transactionType === 'COUNT' ? inventory.currentStock : undefined;
          
            // Update existing inventory
          await BranchInventory.updateOne(
            { _id: inventory._id },
            { 
              $set: {
                currentStock: newStock,
                pendingStock: newPendingStock,
                lastUpdated: new Date(),
                lastCountDate: data.transactionType === 'COUNT' ? new Date() : inventory.lastCountDate
              }
            },
            { session }
          );
        } else {
          // Create new inventory record for new items
          await BranchInventory.create([{
            companyId: new Types.ObjectId(companyId),
            locationId: new Types.ObjectId(data.locationId),
            itemId: new Types.ObjectId(item.itemId),
            itemType: item.itemType,
            currentStock: newStock,
            pendingStock: newPendingStock,
            baseUomId: new Types.ObjectId(item.unitId),
            lastUpdated: new Date(),
            sellingOptionId: 'DEFAULT', // Default value
            parLevel: 0,                 // Default value
            reorderPoint: 0,             // Default value
            isActive: true,
            isLocked: false
          }], { session });
        }

        // Prepare transaction record fields (to be created outside the transaction)
        const transactionData: Partial<IInventoryTransaction> = {
          timestamp: new Date(),
          metadata: {
            companyId: new Types.ObjectId(companyId),
            locationId: new Types.ObjectId(data.locationId),
            itemId: new Types.ObjectId(item.itemId),
            itemType: item.itemType,
            transactionType: data.transactionType,
            referenceId: data.reference?.id ? new Types.ObjectId(data.reference.id) : undefined,
            referenceType: data.reference?.type,
            userId: new Types.ObjectId(authResult.userId)
          },
          previousStock,
          newStock,
          difference: newStock - previousStock,
          previousPendingStock,
          newPendingStock,
          pendingDifference: newPendingStock - previousPendingStock,
          notes: data.notes,
          unitId: new Types.ObjectId(item.unitId),
        };
        
        // Add transaction-type specific fields
        if (data.transactionType === 'PURCHASE') {
          transactionData.cost = item.cost;
          transactionData.lotNumber = item.lotNumber;
          transactionData.expiryDate = item.expiryDate ? new Date(item.expiryDate) : undefined;
          transactionData.supplierName = data.supplierName;
          transactionData.invoiceNumber = data.invoiceNumber;
          transactionData.purchaseOrderRef = data.purchaseOrderRef;
        } else if (data.transactionType === 'WASTAGE') {
          transactionData.reason = item.reason || data.notes;
          transactionData.disposalMethod = data.disposalMethod;
        } else if (data.transactionType === 'PRODUCTION_INPUT' || data.transactionType === 'PRODUCTION_OUTPUT') {
          transactionData.recipeId = data.recipeId ? new Types.ObjectId(data.recipeId) : undefined;
          transactionData.expectedQuantity = item.expectedQuantity;
          if (item.expectedQuantity && item.expectedQuantity > 0) {
            transactionData.variancePercentage = ((item.quantity - item.expectedQuantity) / item.expectedQuantity) * 100;
          }
        } else if (data.transactionType === 'TRANSFER_OUT') {
          transactionData.destinationLocationId = data.destinationLocationId ? new Types.ObjectId(data.destinationLocationId) : undefined;
          transactionData.transferReference = data.transferReference;
        } else if (data.transactionType === 'TRANSFER_IN') {
          transactionData.sourceLocationId = data.sourceLocationId ? new Types.ObjectId(data.sourceLocationId) : undefined;
          transactionData.transferReference = data.transferReference;
        } else if (data.transactionType === 'COUNT') {
          transactionData.countBatch = data.countBatch;
          transactionData.countStatus = 'DRAFT';
          transactionData.systemStockBeforeCount = previousStock;
        }
        
        // Save transaction data for creation after transaction is committed
        transactionRecords.push(transactionData);
        
        // Store affected items for response
        affectedItems.push({
          itemId: item.itemId,
          itemName: await getItemName(item.itemId, item.itemType),
          previousStock,
          newStock,
          difference: newStock - previousStock
        });
      }
      
      // Handle special cases based on transaction type
      await handleTransactionSpecificActions(data, companyId, affectedItems, session);
      
      // Commit the inventory changes
      await session.commitTransaction();
      session.endSession();
      
      // Now create the time-series transaction records OUTSIDE the transaction
      const createdTransactions: IInventoryTransaction[] = [];
      for (const transactionData of transactionRecords) {
        const transaction = await InventoryTransaction.create([transactionData]);
        createdTransactions.push(transaction[0]);
      }
      
      // Update affected items with transaction IDs
      for (let i = 0; i < affectedItems.length; i++) {
        if (i < createdTransactions.length) {
          affectedItems[i].transactionId = createdTransactions[i]._id as Types.ObjectId;
        }
      }
      
      return NextResponse.json({
        success: true,
        transactionId: new Types.ObjectId(),
        movementDate: new Date(),
        affectedItems
      });
    } catch (error: unknown) {
      // Rollback the transaction if there's an error in the inventory operations
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error: any) {
    console.error('Inventory movement error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process inventory movement' },
      { status: 500 }
    );
  }
}

/**
 * Calculate new stock based on transaction type
 */
function calculateNewStock(currentStock: number, quantity: number, transactionType: string): number {
  switch (transactionType) {
    case 'PURCHASE':
      // Purchase doesn't affect current stock, only pending stock
      return currentStock;
    case 'PRODUCTION_OUTPUT':
    case 'TRANSFER_IN':
    case 'RECEIVED':
      return currentStock + quantity;
    case 'WASTAGE':
    case 'PRODUCTION_INPUT':
    case 'TRANSFER_OUT':
    case 'DISPATCHED':
    case 'SALE':
    case 'THEFT':
      return currentStock - quantity;
    case 'ADJUSTMENT':
      // Quantity can be positive or negative for adjustments
      return currentStock + quantity;
    case 'COUNT':
      // Direct override
      return quantity;
    default:
      throw new Error(`Unsupported transaction type: ${transactionType}`);
  }
}

/**
 * Calculate new pending stock based on transaction type and quantity
 */
function calculatePendingStock(pendingStock: number, quantity: number, transactionType: string): number {
  switch (transactionType) {
    case 'PURCHASE':
      // Purchase increases pending stock
      return pendingStock + quantity;
    case 'RECEIVED':
      // Received decreases pending stock as items move to available stock
      return Math.max(0, pendingStock - quantity);
    default:
      // Other transaction types don't affect pending stock
      return pendingStock;
  }
}

/**
 * Get item name for logging purposes
 */
async function getItemName(itemId: string, itemType: string): Promise<string> {
  try {
    if (itemType === 'RECIPE') {
      const recipe = await Recipe.findById(itemId);
      return recipe?.name || 'Unknown Recipe';
    } else {
      const ingredient = await Ingredient.findById(itemId);
      return ingredient?.name || 'Unknown Ingredient';
    }
  } catch (error: unknown) {
    return 'Unknown Item';
  }
}

/**
 * Validate transaction-specific fields
 */
async function validateTransactionSpecificFields(data: InventoryMovementRequest, companyId: string): Promise<{ valid: boolean, error?: string }> {
  try {
    switch (data.transactionType) {
      case 'PURCHASE':
        // Validate ingredient types
        for (const item of data.items) {
          if (item.itemType !== 'INGREDIENT') {
            return { valid: false, error: 'PURCHASE transactions can only be applied to ingredients' };
          }
          if (item.cost === undefined || item.cost <= 0) {
            return { valid: false, error: 'Cost is required for purchase transactions' };
          }
        }
        break;
        
      case 'PRODUCTION_INPUT':
      case 'PRODUCTION_OUTPUT':
        // Validate recipe reference
        if (!data.recipeId) {
          return { valid: false, error: 'Recipe ID is required for production transactions' };
        }
        // Verify recipe exists
        const recipe = await Recipe.findOne({ 
          _id: new Types.ObjectId(data.recipeId),
          companyId: new Types.ObjectId(companyId)
        });
        if (!recipe) {
          return { valid: false, error: 'Invalid recipe reference' };
        }
        break;
        
      case 'TRANSFER_OUT':
        // Validate destination location
        if (!data.destinationLocationId) {
          return { valid: false, error: 'Destination location is required for transfer out' };
        }
        break;
        
      case 'TRANSFER_IN':
        // Validate source location
        if (!data.sourceLocationId) {
          return { valid: false, error: 'Source location is required for transfer in' };
        }
        break;
        
      case 'WASTAGE':
        // Validate reason
        for (const item of data.items) {
          if (!item.reason && !data.notes) {
            return { valid: false, error: 'Reason is required for wastage transactions' };
          }
        }
        break;
    }
    
    return { valid: true };
  } catch (error: unknown) {
    return { valid: false, error: 'Validation error: ' + (error as Error).message };
  }
}

/**
 * Handle transaction-specific side effects
 */
async function handleTransactionSpecificActions(
  data: InventoryMovementRequest, 
  companyId: string,
  affectedItems: any[],
  session: mongoose.ClientSession
): Promise<void> {
  // Handle production-related effects (e.g., updating recipe production records)
  // Handle transfer-related effects (e.g., creating pending transfers)
  // Handle count-related effects (e.g., updating count status)
  
  // These would be expanded based on specific business requirements
  return Promise.resolve();
}
