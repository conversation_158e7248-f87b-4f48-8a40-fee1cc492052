import { NextRequest, NextResponse } from 'next/server';
import { isValidCompanyUser } from '@/lib/server-auth';
import dbConnect from '@/lib/db';
import InventoryTransaction from '@/models/InventoryTransaction';
import BranchInventory from '@/models/BranchInventory';
import mongoose from 'mongoose';
import { format as formatDate } from 'date-fns';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    // Extract companyId from params
    const { companyId } = await params;

    // Validate user has access to this company
    const validation = await isValidCompanyUser(request, companyId);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.message }, { status: 401 });
    }

    // Connect to the database
    await dbConnect();

    // Get query parameters
    const url = new URL(request.url);
    const locationId = url.searchParams.get('locationId');
    const transactionType = url.searchParams.get('transactionType');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const search = url.searchParams.get('search');
    const outputFormat = url.searchParams.get('format') || 'csv';

    // Build query
    const query: any = {
      'metadata.companyId': new mongoose.Types.ObjectId(companyId),
    };

    if (locationId) {
      query['metadata.locationId'] = new mongoose.Types.ObjectId(locationId);
    }

    if (transactionType) {
      query['metadata.transactionType'] = transactionType;
    }

    // Date filters
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        // Add one day to include the entire end date
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        query.timestamp.$lt = endDateObj;
      }
    }

    // Perform the search aggregation
    const pipeline: any[] = [
      { $match: query },
      // Join with BranchInventory to get item details
      {
        $lookup: {
          from: 'branchinventories',
          let: { 
            itemId: '$metadata.itemId', 
            locationId: '$metadata.locationId' 
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$itemId', '$$itemId'] },
                    { $eq: ['$locationId', '$$locationId'] }
                  ]
                }
              }
            }
          ],
          as: 'inventoryItem'
        }
      },
      { $unwind: { path: '$inventoryItem', preserveNullAndEmptyArrays: true } },
      // Join with users collection to get user info
      {
        $lookup: {
          from: 'users',
          localField: 'metadata.userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
      // Join with locations collection
      {
        $lookup: {
          from: 'locations',
          localField: 'metadata.locationId',
          foreignField: '_id',
          as: 'location'
        }
      },
      { $unwind: { path: '$location', preserveNullAndEmptyArrays: true } },
      // Join with UOMs
      {
        $lookup: {
          from: 'uoms',
          localField: 'inventoryItem.baseUomId',
          foreignField: '_id',
          as: 'uom'
        }
      },
      { $unwind: { path: '$uom', preserveNullAndEmptyArrays: true } },
      // Join with items
      {
        $lookup: {
          from: 'items',
          localField: 'metadata.itemId',
          foreignField: '_id',
          as: 'item'
        }
      },
      { $unwind: { path: '$item', preserveNullAndEmptyArrays: true } },
      // Project the fields we need
      {
        $project: {
          _id: 1,
          timestamp: 1,
          previousStock: 1,
          newStock: 1,
          difference: 1,
          notes: 1,
          'metadata.transactionType': 1,
          'metadata.reference': 1,
          itemName: { $ifNull: ['$item.name', 'Unknown Item'] },
          itemType: '$metadata.itemType',
          locationName: { $ifNull: ['$location.name', 'Unknown Location'] },
          userName: { 
            $concat: [
              { $ifNull: ['$user.firstName', ''] }, 
              ' ', 
              { $ifNull: ['$user.lastName', ''] }
            ] 
          },
          uomName: { $ifNull: ['$uom.shortCode', { $ifNull: ['$uom.name', ''] }] }
        }
      }
    ];

    // Add search filter if present
    if (search) {
      pipeline.unshift({
        $match: {
          $or: [
            { 'item.name': { $regex: search, $options: 'i' } },
            { 'metadata.reference': { $regex: search, $options: 'i' } }
          ]
        }
      });
    }

    // Execute the query
    const transactions = await InventoryTransaction.aggregate(pipeline);

    // Format the data for export
    let exportData = '';
    const timestamp = formatDate(new Date(), 'yyyy-MM-dd_HH-mm');
    const fileName = `inventory_movements_${timestamp}.csv`;

    if (outputFormat === 'csv') {
      // Create CSV header
      exportData = 'Date,Time,Location,Item,Type,Previous Stock,Change,New Stock,Unit,User,Reference,Notes\n';
      
      // Add rows
      transactions.forEach(transaction => {
        const date = formatDate(new Date(transaction.timestamp), 'yyyy-MM-dd');
        const time = formatDate(new Date(transaction.timestamp), 'HH:mm:ss');
        
        // Format transaction type
        const typeLabels: Record<string, string> = {
          COUNT: 'Inventory Count',
          ADJUSTMENT: 'Manual Adjustment',
          SALE: 'Sale',
          RECEIVED: 'Stock Received',
          DISPATCHED: 'Stock Dispatched'
        };
        const txnType = String(transaction.metadata.transactionType);
        const typeLabel = typeLabels[txnType] || txnType;
        
        // Escape any commas in text fields
        const escapeCSV = (text: string) => `"${(text || '').replace(/"/g, '""')}"`;
        
        exportData += [
          date,
          time,
          escapeCSV(transaction.locationName),
          escapeCSV(transaction.itemName),
          escapeCSV(typeLabel),
          transaction.previousStock,
          transaction.difference > 0 ? `+${transaction.difference}` : transaction.difference,
          transaction.newStock,
          escapeCSV(transaction.uomName),
          escapeCSV(transaction.userName.trim()),
          escapeCSV(transaction.metadata.reference || ''),
          escapeCSV(transaction.notes || '')
        ].join(',') + '\n';
      });
    } else {
      // Default to JSON if format isn't CSV
      return NextResponse.json({ transactions });
    }

    // Create the CSV response
    const response = new NextResponse(exportData);
    response.headers.set('Content-Type', 'text/csv');
    response.headers.set('Content-Disposition', `attachment; filename=${fileName}`);
    
    return response;
  } catch (error: any) {
    console.error('Error exporting inventory movements:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to export inventory movements' },
      { status: 500 }
    );
  }
}
