import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { requireAuth } from '@/lib/auth-helpers';
import Order from '@/models/Order';
import BranchInventory from '@/models/BranchInventory';
import Location from '@/models/Location';
import { Types } from 'mongoose';

interface OrderItem {
  itemId: string;
  branchInventoryId: string;
  quantity: number;
  unitPrice?: number;
  notes?: string;
}

interface CreateOrderRequest {
  items: OrderItem[];
  requestedDeliveryDate?: string;
  notes?: string;
  priority?: 'URGENT' | 'NORMAL' | 'LOW';
}

// GET - Fetch shop orders
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req);
    const { companyId } = await context.params;

    if (auth.tenantId !== companyId) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 403 });
    }

    const searchParams = req.nextUrl.searchParams;
    const locationId = searchParams.get('locationId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    if (!locationId) {
      return NextResponse.json({ success: false, message: 'Location ID is required' }, { status: 400 });
    }

    // Build query for shop orders
    const query: any = {
      companyId: new Types.ObjectId(companyId),
      'buyer.buyerType': 'BRANCH',
      'buyer.buyerId': locationId,
      'seller.sellerType': 'CENTRAL_KITCHEN'
    };

    if (status) {
      query.status = status.toUpperCase();
    }

    const orders = await Order.find(query)
      .populate('sellerLocationId', 'name')
      .populate('items.itemId', 'name description')
      .populate('items.uomId', 'name shortCode')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const total = await Order.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching shop orders:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new shop order
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req);
    const { companyId } = await context.params;

    if (auth.tenantId !== companyId) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 403 });
    }

    const data: CreateOrderRequest = await req.json();
    const { items, requestedDeliveryDate, notes, priority = 'NORMAL' } = data;

    if (!items || items.length === 0) {
      return NextResponse.json({ success: false, message: 'Order must contain at least one item' }, { status: 400 });
    }

    // Get user's location - in real implementation, get from user profile
    const userLocationId = req.headers.get('x-location-id') || 'temp-location-id';
    
    // Find user's location and central kitchen
    const [shopLocation, centralKitchen] = await Promise.all([
      Location.findOne({ 
        _id: new Types.ObjectId(userLocationId),
        companyId: new Types.ObjectId(companyId),
        locationType: 'RETAIL_SHOP'
      }),
      Location.findOne({
        companyId: new Types.ObjectId(companyId),
        locationType: 'CENTRAL_KITCHEN'
      })
    ]);

    if (!shopLocation) {
      return NextResponse.json({ success: false, message: 'Shop location not found' }, { status: 404 });
    }

    if (!centralKitchen) {
      return NextResponse.json({ success: false, message: 'Central kitchen not found' }, { status: 404 });
    }

    // Validate and process order items
    const processedItems = [];
    let totalAmount = 0;

    for (const item of items) {
      // Get branch inventory item from central kitchen
      const branchInventoryItem = await BranchInventory.findOne({
        _id: new Types.ObjectId(item.branchInventoryId),
        companyId: new Types.ObjectId(companyId),
        locationId: centralKitchen._id,
        isOrderable: true,
        isActive: true
      }).populate('itemId').populate('baseUomId');

      if (!branchInventoryItem) {
        return NextResponse.json({ 
          success: false, 
          message: `Item ${item.itemId} is not available for ordering` 
        }, { status: 400 });
      }

      // Validate quantity constraints
      if (item.quantity < (branchInventoryItem.minOrderQuantity || 1)) {
        return NextResponse.json({
          success: false,
          message: `Minimum order quantity for ${branchInventoryItem.itemId.name} is ${branchInventoryItem.minOrderQuantity || 1}`
        }, { status: 400 });
      }

      if (branchInventoryItem.maxOrderQuantity && item.quantity > branchInventoryItem.maxOrderQuantity) {
        return NextResponse.json({
          success: false,
          message: `Maximum order quantity for ${branchInventoryItem.itemId.name} is ${branchInventoryItem.maxOrderQuantity}`
        }, { status: 400 });
      }

      // Check stock availability
      if (item.quantity > branchInventoryItem.currentStock) {
        return NextResponse.json({
          success: false,
          message: `Insufficient stock for ${branchInventoryItem.itemId.name}. Available: ${branchInventoryItem.currentStock}`
        }, { status: 400 });
      }

      const unitPrice = item.unitPrice || branchInventoryItem.costBasis || 0;
      const lineTotal = item.quantity * unitPrice;
      totalAmount += lineTotal;

      processedItems.push({
        itemType: branchInventoryItem.itemType,
        itemId: branchInventoryItem.itemId._id,
        description: branchInventoryItem.itemId.name,
        quantity: item.quantity,
        deliveredQuantity: 0,
        uomId: branchInventoryItem.baseUomId._id,
        unitPrice,
        lineTotal,
        notes: item.notes
      });
    }

    // Generate order number
    const orderCount = await Order.countDocuments({ companyId: new Types.ObjectId(companyId) });
    const orderNumber = `SH-${String(orderCount + 1).padStart(6, '0')}`;

    // Create order
    const newOrder = new Order({
      companyId: new Types.ObjectId(companyId),
      orderNumber,
      status: 'INCOMING', // Pending central kitchen approval
      buyer: {
        buyerType: 'BRANCH',
        buyerId: shopLocation._id.toString(),
        buyerName: shopLocation.name
      },
      seller: {
        sellerType: 'CENTRAL_KITCHEN',
        sellerId: centralKitchen._id.toString(),
        sellerName: centralKitchen.name
      },
      sellerLocationId: centralKitchen._id,
      buyerLocationId: shopLocation._id,
      items: processedItems,
      deliveryNoteIds: [],
      orderSource: 'WEB',
      syncStatus: 'NOT_SYNCED',
      version: 1,
      createdBy: new Types.ObjectId(auth.id),
      modifiedBy: new Types.ObjectId(auth.id),
      // Custom fields for internal transfers
      orderType: 'INTERNAL_TRANSFER',
      priority,
      requestedDeliveryDate: requestedDeliveryDate ? new Date(requestedDeliveryDate) : undefined,
      approvalStatus: totalAmount > 1000 ? 'PENDING' : 'AUTO_APPROVED', // Auto-approve small orders
      estimatedReadyTime: new Date(Date.now() + (24 * 60 * 60 * 1000)), // 1 day lead time
      totalAmount,
      orderNotes: notes
    });

    await newOrder.save();

    // If auto-approved, update status
    if (newOrder.approvalStatus === 'AUTO_APPROVED') {
      newOrder.status = 'CONFIRMED';
      newOrder.approvedBy = new Types.ObjectId(auth.id);
      newOrder.approvedAt = new Date();
      await newOrder.save();
    }

    return NextResponse.json({
      success: true,
      message: 'Order created successfully',
      data: {
        orderId: newOrder._id,
        orderNumber: newOrder.orderNumber,
        status: newOrder.status,
        approvalStatus: newOrder.approvalStatus,
        totalAmount,
        estimatedReadyTime: newOrder.estimatedReadyTime
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating shop order:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}