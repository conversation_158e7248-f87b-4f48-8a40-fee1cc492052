import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { requireAuth } from '@/lib/auth-helpers';
import BranchInventory from '@/models/BranchInventory';
import { Ingredient } from '@/models/Ingredient';
import Recipe from '@/models/Recipe';
import UOM from '@/models/UOM';
import Location from '@/models/Location';
import { Types } from 'mongoose';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect();
    const auth = await requireAuth(req);
    const { companyId } = await context.params;

    if (auth.tenantId !== companyId) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 403 });
    }

    const searchParams = req.nextUrl.searchParams;
    const locationId = searchParams.get('locationId');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const itemType = searchParams.get('itemType') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    if (!locationId) {
      return NextResponse.json({ success: false, message: 'Location ID is required' }, { status: 400 });
    }

    // Find central kitchen location for this company
    const centralKitchen = await Location.findOne({
      companyId: new Types.ObjectId(companyId),
      locationType: 'CENTRAL_KITCHEN'
    });

    if (!centralKitchen) {
      return NextResponse.json({ success: false, message: 'Central kitchen not found' }, { status: 404 });
    }

    // Build query for orderable items
    const query: any = {
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(centralKitchen._id),
      isOrderable: true,
      isActive: true,
      currentStock: { $gt: 0 } // Only show items in stock
    };

    if (itemType) {
      query.itemType = itemType.toUpperCase();
    }

    // Get orderable items from central kitchen
    const items = await BranchInventory.find(query)
      .populate({
        path: 'itemId',
        populate: [
          { path: 'baseUomId', model: UOM },
          { path: 'sellingDetails.uomId', model: UOM }
        ]
      })
      .populate('baseUomId')
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    const total = await BranchInventory.countDocuments(query);

    // Format items for shop catalog
    const catalogItems = items.map((item: any) => {
      const itemDetails = item.itemId;
      const baseUom = item.baseUomId;
      
      return {
        id: item._id,
        itemId: item.itemId._id,
        itemType: item.itemType,
        name: itemDetails.name,
        description: itemDetails.description,
        category: item.category || itemDetails.category,
        currentStock: item.currentStock,
        centralKitchenStock: item.centralKitchenStock || item.currentStock,
        parLevel: item.parLevel,
        reorderPoint: item.reorderPoint,
        minOrderQuantity: item.minOrderQuantity || 1,
        maxOrderQuantity: item.maxOrderQuantity || item.currentStock,
        orderingUOM: item.orderingUOM || baseUom?.shortCode || 'units',
        orderingConversionFactor: item.orderingConversionFactor || 1,
        leadTimeDays: item.leadTimeDays || 1,
        orderingNotes: item.orderingNotes,
        baseUom: {
          id: baseUom?._id,
          name: baseUom?.name,
          shortCode: baseUom?.shortCode
        },
        // Add basic pricing info if available
        estimatedCost: item.costBasis || 0,
        isAvailable: item.currentStock > (item.minOrderQuantity || 1)
      };
    });

    // Apply search and category filters client-side for now (can optimize later)
    let filteredItems = catalogItems;
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredItems = filteredItems.filter(item => 
        item.name.toLowerCase().includes(searchLower) ||
        item.description?.toLowerCase().includes(searchLower) ||
        item.category?.toLowerCase().includes(searchLower)
      );
    }

    if (category) {
      filteredItems = filteredItems.filter(item => 
        item.category?.toLowerCase() === category.toLowerCase()
      );
    }

    return NextResponse.json({
      success: true,
      data: filteredItems,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      centralKitchen: {
        id: centralKitchen._id,
        name: centralKitchen.name
      }
    });

  } catch (error) {
    console.error('Error fetching orderable items:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}