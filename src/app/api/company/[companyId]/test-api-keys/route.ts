import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { isValidCompanyUser, hasRequiredCompanyRole } from '@/lib/server-auth';
import TestApiKey from '@/models/TestApiKey';
import { Types } from 'mongoose';
import crypto from 'crypto';

// Create a new API key
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  // Block in production
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    const { companyId } = await params;
    await dbConnect();

    // Only admins and owners can create API keys
    const rawAuthResult = await isValidCompanyUser(request, companyId);
    console.log('Raw auth result:', JSON.stringify(rawAuthResult));
    
    const authResult = await hasRequiredCompanyRole(request, companyId, 'admin');
    console.log('Role check result:', JSON.stringify(authResult));
    
    if (!authResult.isValid) {
      return NextResponse.json({ 
        error: 'Unauthorized. Admin or owner access required.',
        debug: { rawAuthResult, authResult } 
      }, { status: 401 });
    }

    const { userId, role, description, expiryDays = 30 } = await request.json();

    if (!userId || !role || !description) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Generate key using static method
    const keyString = crypto.randomBytes(32).toString('hex');
    
    // Set expiry date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiryDays);

    // Create new API key
    const apiKey = await TestApiKey.create({
      key: keyString,
      userId: new Types.ObjectId(userId),
      companyId: new Types.ObjectId(companyId),
      role,
      description,
      expiresAt
    });

    return NextResponse.json({
      id: apiKey._id,
      key: apiKey.key, // Only returned when created
      expiresAt: apiKey.expiresAt,
      description: apiKey.description,
      role: apiKey.role
    });
  } catch (error: any) {
    console.error('Error creating API key:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
}

// List API keys (without exposing the actual keys)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  // Block in production
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    const { companyId } = await params;
    await dbConnect();

    // Admin and owner only endpoint
    const authResult = await hasRequiredCompanyRole(request, companyId, 'admin');
    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Unauthorized. Admin or owner access required.' }, { status: 401 });
    }

    // Find all API keys for this company
    const apiKeys = await TestApiKey.find(
      { companyId: new Types.ObjectId(companyId) },
      { key: 0 } // Exclude actual key from results
    ).populate('userId', 'name email');

    return NextResponse.json(apiKeys);
  } catch (error: any) {
    console.error('Error listing API keys:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
}


