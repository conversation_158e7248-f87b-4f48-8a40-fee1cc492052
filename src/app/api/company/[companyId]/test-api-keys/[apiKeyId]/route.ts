import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { isValidCompanyUser, hasRequiredCompanyRole } from '@/lib/server-auth';
import TestApiKey from '@/models/TestApiKey';
import { Types } from 'mongoose';

// Delete an API key
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string; apiKeyId: string }> }
) {
  // Block in production
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    const resolvedParams = await params;
    const companyId = resolvedParams.companyId;
    const apiKeyId = resolvedParams.apiKeyId;
    
    await dbConnect();

    // Admin and owner only endpoint
    const authResult = await hasRequiredCompanyRole(request, companyId, 'admin');
    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Unauthorized. Admin or owner access required.' }, { status: 401 });
    }

    // Delete the API key
    const result = await TestApiKey.deleteOne({
      _id: new Types.ObjectId(apiKeyId),
      companyId: new Types.ObjectId(companyId)
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: 'API key not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting API key:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
}
