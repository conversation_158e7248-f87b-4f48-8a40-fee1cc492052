import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { withAuth } from '@/lib/auth-helpers';
import { applyCorsHeaders } from '@/middleware/cors-middleware';

// PUT handler for updating users
export const PUT = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string; userId: string }> }
  ) => {
    try {
      await dbConnect();
      
      // Await params to fix Next.js warning about synchronous access
      const params = await context.params;
      const { companyId, userId } = params;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Get the user data from the request body
      const userData = await req.json();
      console.log('User data received in PUT request:', userData);
      console.log('LocationIds received:', userData.locationIds);
      
      // Find the user to update
      const userToUpdate = await User.findOne({ 
        _id: userId, 
        companyId: new mongoose.Types.ObjectId(companyId) 
      });
      
      if (!userToUpdate) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      // Validate role is allowed for the current user
      const allowedRoles: Record<string, string[]> = {
        'owner': ['admin', 'manager', 'user', 'storekeeper'],
        'admin': ['manager', 'user', 'storekeeper'],
        'manager': ['user', 'storekeeper']
      };
      
      if (userData.role) {
        // Special case: partner role from Supabase should be treated as owner
        if (user.role === 'partner') {
          console.log('Partner role detected, granting owner privileges');
          // Allow partners to modify any user
        }
        // Standard permission check
        else if (user.role && !allowedRoles[user.role]?.includes(userData.role) && user.userType !== 'superuser') {
          return NextResponse.json({ 
            error: 'Unauthorized role assignment', 
            details: `Users with role '${user.role}' cannot assign role '${userData.role}'`
          }, { status: 403 });
        }
      }

      // Update basic fields
      if (userData.displayName) userToUpdate.displayName = userData.displayName;
      if (userData.email) userToUpdate.email = userData.email;
      if (userData.role) userToUpdate.role = userData.role;
      if (userData.isActive !== undefined) userToUpdate.isActive = userData.isActive;
      if (userData.canUseIonicApp !== undefined) userToUpdate.canUseIonicApp = userData.canUseIonicApp;
      
      // Update password if provided
      if (userData.password && userData.password.trim() !== '') {
        const salt = await bcrypt.genSalt(10);
        userToUpdate.passwordHash = await bcrypt.hash(userData.password, salt);
      }
      
      // Update location IDs (support for multiple locations)
      // Initialize locationIds if it doesn't exist to ensure the field is present in MongoDB
      if (!userToUpdate.locationIds) {
        console.log('locationIds field not found on user document, initializing as empty array');
        userToUpdate.locationIds = [];
      }
      
      if (userData.locationIds) {
        console.log('Processing locationIds from request:', userData.locationIds);
        
        // Ensure locationIds is always treated as an array of strings
        const locationIdsArray = Array.isArray(userData.locationIds) 
          ? userData.locationIds 
          : (typeof userData.locationIds === 'string' ? [userData.locationIds] : []);
        
        console.log('Normalized locationIds array:', locationIdsArray);
        
        // Validate all location IDs
        const validLocationIds = locationIdsArray
          .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
          .map((id: string) => new mongoose.Types.ObjectId(id));
        
        console.log('Valid location IDs after conversion:', validLocationIds);
        
        // Always set locationIds (empty array if none are valid)
        userToUpdate.locationIds = validLocationIds;
        console.log('Setting locationIds on user object:', userToUpdate.locationIds);
        
        // Explicitly mark this field as modified to ensure it's saved
        userToUpdate.markModified('locationIds');
      } else {
        console.log('locationIds missing in request');
        // If locationIds is missing but we're updating a user, ensure it exists as an empty array
        // This ensures the field is present in the MongoDB document
        userToUpdate.locationIds = userToUpdate.locationIds || [];
        userToUpdate.markModified('locationIds');
      }
      
      // Update permissions if provided
      if (userData.permissions && Array.isArray(userData.permissions)) {
        userToUpdate.permissions = userData.permissions;
      }

      userToUpdate.updatedAt = new Date();
      
      // Try to log the full user object before saving to see exactly what's going on
      try {
        console.log('User object before saving:', JSON.stringify(userToUpdate));
        console.log('locationIds type:', typeof userToUpdate.locationIds);
        console.log('locationIds instanceof Array:', userToUpdate.locationIds instanceof Array);
        console.log('locationIds direct access:', userToUpdate.locationIds);
        
        if (Array.isArray(userToUpdate.locationIds)) {
          console.log('locationIds length:', userToUpdate.locationIds.length);
          userToUpdate.locationIds.forEach((id: mongoose.Types.ObjectId, index: number) => {
            console.log(`locationId[${index}] type:`, typeof id);
            console.log(`locationId[${index}] value:`, id);
            console.log(`locationId[${index}] instanceof ObjectId:`, id instanceof mongoose.Types.ObjectId);
          });
        }
      } catch (err) {
        console.error('Error logging user object:', err);
      }
      
      // Save the updated user
      await userToUpdate.save();
      console.log('User saved successfully');
      
      // Check what was actually saved
      const checkUser = await User.findById(userToUpdate._id);
      console.log('User after save from DB:', checkUser);
      console.log('locationIds after save:', checkUser?.locationIds);
      
      // Return the updated user (excluding sensitive data)
      const updatedUser = userToUpdate.toObject();
      delete updatedUser.passwordHash;
      
      console.log('Updated user being returned:', updatedUser);
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'User updated successfully', 
          user: updatedUser 
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error updating user:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'admin', 'manager'] // Allow owners, admins, and managers to update users
);

// DELETE handler for removing users
export const DELETE = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string; userId: string }> }
  ) => {
    try {
      await dbConnect();
      
      // Await params to fix Next.js warning about synchronous access
      const params = await context.params;
      const { companyId, userId } = params;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Find the user to delete
      const userToDelete = await User.findOne({ 
        _id: userId, 
        companyId: new mongoose.Types.ObjectId(companyId) 
      });
      
      if (!userToDelete) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      // Check if the user trying to delete has permission
      if (user.role !== 'owner' && user.role !== 'admin' && user.userType !== 'superuser') {
        return NextResponse.json({ error: 'Only owners and admins can delete users' }, { status: 403 });
      }
      
      // Don't allow deleting owners unless you're a superuser
      if (userToDelete.role === 'owner' && user.userType !== 'superuser') {
        return NextResponse.json({ error: 'Cannot delete owner accounts' }, { status: 403 });
      }
      
      // Soft delete: Mark as deleted rather than actually removing
      userToDelete.isDeleted = true;
      userToDelete.isActive = false;
      userToDelete.updatedAt = new Date();
      
      await userToDelete.save();
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'User deleted successfully'
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error deleting user:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'admin'] // Allow only owners and admins to delete users
);

// GET handler for single user details
export const GET = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string; userId: string }> }
  ) => {
    try {
      await dbConnect();
      
      // Await params to fix Next.js warning about synchronous access
      const params = await context.params;
      const { companyId, userId } = params;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(userId)) {
        return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Find the user
      const targetUser = await User.findOne({ 
        _id: userId, 
        companyId: new mongoose.Types.ObjectId(companyId),
        isDeleted: { $ne: true }
      });
      
      if (!targetUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      // Return the user data (excluding sensitive information)
      const userData = targetUser.toObject();
      delete userData.passwordHash;
      
      return applyCorsHeaders(
        NextResponse.json({ user: userData }),
        req
      );
    } catch (error: unknown) {
      console.error('Error fetching user details:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'admin', 'manager'] // Allow owners, admins, and managers to view user details
);
