// src/app/api/company/[companyId]/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth-helpers';
import connectToDatabase from '@/lib/mongoDb';
import User from '@/models/User';
import mongoose from 'mongoose';
import { applyCorsHeaders, handleCorsOptions } from '@/middleware/cors-middleware';
import bcrypt from 'bcryptjs';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

// GET handler for fetching users
export const GET = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning about synchronous access
      const params = await context.params;
      const companyId = params.companyId;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Get query parameters for optional filtering
      const { searchParams } = new URL(req.url);
      const locationId = searchParams.get('locationId');
      const role = searchParams.get('role');
      
      // Only filter by company
      const query: any = { companyId: new mongoose.Types.ObjectId(companyId) };
      // Optionally filter by location or role if provided
      if (locationId && mongoose.Types.ObjectId.isValid(locationId)) {
        query.locationIds = new mongoose.Types.ObjectId(locationId);
      }
      if (role) {
        query.role = role;
      }
      // Exclude soft-deleted users by default
      if (!new URL(req.url).searchParams.has('includeDeleted')) {
        query.isDeleted = { $ne: true };
      }
      
      // Execute the query
      const users = await User.find(query)
        .select('-passwordHash -pin')  // Exclude sensitive fields
        .sort({ displayName: 1 })     // Sort by display name
        .lean();                      // Convert to plain JavaScript objects
      
      console.log('Users query:', query);
      console.log('Found users count:', users.length);
      console.log('First user locationIds:', users[0]?.locationIds);
      
      return applyCorsHeaders(
        NextResponse.json({ users }, { status: 200 }),
        req
      );
    } catch (error: unknown) {
      console.error('Error fetching users:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: 'An error occurred while fetching users.' },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'admin', 'manager'] // Allow owners, admins, and managers to view users
)

// POST handler for creating new users
export const POST = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    // Debug logging - log headers and cookies
    console.log('POST /api/company/[companyId]/users - Request headers:', Object.fromEntries(req.headers));
    console.log('POST /api/company/[companyId]/users - Request cookies:', req.cookies.getAll());
    console.log('POST /api/company/[companyId]/users - User object:', user);
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning about synchronous access
      const params = await context.params;
      const companyId = params.companyId;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Get the user data from the request body
      const userData = await req.json();
      
      // Validate required fields
      if (!userData.email || !userData.displayName || !userData.role || !userData.password) {
        return NextResponse.json({ 
          error: 'Missing required fields', 
          details: 'Email, displayName, role, and password are required' 
        }, { status: 400 });
      }
      
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
      }
      
      // Check if email already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        return NextResponse.json({ error: 'Email already in use' }, { status: 409 });
      }

      // Validate role is allowed for the current user
      const allowedRoles: Record<string, string[]> = {
        'owner': ['admin', 'manager', 'user', 'storekeeper'],
        'admin': ['manager', 'user', 'storekeeper'],
        'manager': ['user', 'storekeeper']
      };
      
      if (user.role !== 'owner' && userData.role === 'owner') {
        return NextResponse.json({ error: 'Only company owners can create owner accounts' }, { status: 403 });
      }
      
      // Check if user has permission to create users with the requested role
      // For debugging, let's log the entire user object
      console.log('User object for permission check:', JSON.stringify(user));
      console.log('User data being created:', userData);
      
      // Determine the effective role - fallback to 'owner' for admins/partners
      const effectiveRole = user.role === 'partner' ? 'owner' : user.role as string;
      console.log('Role check:', { 
        originalRole: user.role,
        effectiveRole, 
        requestedRole: userData.role,
        allowedRoles: allowedRoles[effectiveRole] || [],
        hasPermission: effectiveRole ? allowedRoles[effectiveRole]?.includes(userData.role) : false,
        isSuperuser: user.userType === 'superuser'
      });
      
      // Special case: partner role from Supabase should be treated as owner
      if (user.role === 'partner') {
        console.log('Partner role detected, granting owner privileges');
        // Allow partners to create any user type
      }
      // Standard permission check
      else if (effectiveRole && !allowedRoles[effectiveRole]?.includes(userData.role) && user.userType !== 'superuser') {
        console.log('Permission denied for role assignment');
        return NextResponse.json({ 
          error: 'Unauthorized role assignment', 
          details: 'An error occurred while fetching users.'
        }, { status: 403 });
      }

      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash(userData.password, salt);

      // Create the new user
      const newUser = new User({
        email: userData.email,
        displayName: userData.displayName,
        passwordHash,
        userType: 'company_user',
        role: userData.role,
        companyId: new mongoose.Types.ObjectId(companyId),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        // Always initialize locationIds as an empty array to ensure field exists
        locationIds: []
      });
      
      // Handle locationIds processing
      if (userData.locationIds && Array.isArray(userData.locationIds)) {
        console.log('Processing locationIds for new user:', userData.locationIds);
        
        // Validate all location IDs
        const validLocationIds = userData.locationIds
          .filter((id: string) => mongoose.Types.ObjectId.isValid(id))
          .map((id: string) => new mongoose.Types.ObjectId(id));
        
        console.log('Valid location IDs for new user:', validLocationIds);
        
        // Set locationIds (even if empty) to ensure field exists in MongoDB
        newUser.locationIds = validLocationIds;
      }
      
      // For backward compatibility - handle single locationId if provided
      // This should be removed once all code is updated to use locationIds
      if (userData.locationId && mongoose.Types.ObjectId.isValid(userData.locationId)) {
        console.log('Single locationId provided, adding to locationIds array:', userData.locationId);
        if (!newUser.locationIds) newUser.locationIds = [];
        newUser.locationIds.push(new mongoose.Types.ObjectId(userData.locationId));
      }
      
      if (userData.permissions && Array.isArray(userData.permissions)) {
        newUser.permissions = userData.permissions;
      }

      // Save the user to the database
      await newUser.save();

      // Return the new user (excluding sensitive data)
      const createdUser = newUser.toObject();
      delete createdUser.passwordHash;
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'User created successfully', 
          user: createdUser 
        }, { status: 201 }),
        req
      );
    } catch (error: unknown) {
      console.error('Error creating user:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: 'An error occurred while fetching users.' },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'admin', 'manager'] // Allow owners, admins, and managers to create users
)