// src/app/api/company/[companyId]/users/sync/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { applyCorsHeaders, handleCorsOptions } from '@/middleware/cors-middleware';
import { requireAuth } from '@/lib/auth-helpers';
import { validateIonicAuth } from '@/lib/ionic-auth';
import { getUserPermissions } from '@/lib/syncUtils';
import { validateLocationAccess } from '@/lib/location-validation';
import mongoose from 'mongoose';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

export async function GET(req: NextRequest, context: { params: Promise<{ companyId: string }> }) {
  try {
    let authUser: any;
    // Try Ionic POS auth first
    const ionicAuth = await validateIonicAuth(req);
    if (ionicAuth.isAuthenticated) {
      authUser = { id: ionicAuth.userId, companyId: ionicAuth.companyId, permissions: ionicAuth.permissions };
    } else {
      // Fallback to web auth
      authUser = await requireAuth(['owner','admin','partner'])(req);
      authUser.permissions = (await getUserPermissions(authUser.id, authUser.tenantId)).permissions;
    }
    const params = await context.params;
    const companyId = params.companyId;
    await dbConnect();
    // Validate header vs params
    const headerCompanyId = req.headers.get('company-id');
    if (headerCompanyId) {
      const locationId = req.headers.get('location-id');
      if (locationId) await validateLocationAccess(companyId, locationId, headerCompanyId);
      else if (companyId !== headerCompanyId) {
        return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
      }
    }
    // Query params
    const url = new URL(req.url);
    const sinceParam = url.searchParams.get('since');
    const since = sinceParam ? new Date(sinceParam) : new Date(0);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const page = parseInt(url.searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    // Build filter
    const filter: any = { companyId: new mongoose.Types.ObjectId(companyId), lastModified: { $gt: since } };
    // Fetch users
    const rawUsers = await User.find(filter).skip(skip).limit(limit).lean() as any[];
    const users = await Promise.all(rawUsers.map(async (u: any) => {
      const permInfo = await getUserPermissions(u._id.toString(), companyId);
      return { ...u, permissions: permInfo.permissions };
    }));
    // Deleted user IDs
    const deletedList = await User.find({ companyId: new mongoose.Types.ObjectId(companyId), isDeleted: true, lastModified: { $gt: since } })
      .select('_id').lean();
    const deletedUserIds = deletedList.map(d => d._id);
    // Pagination
    const totalCount = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);
    const pagination = { totalCount, page, totalPages, hasMore: page < totalPages };
    return applyCorsHeaders(
      NextResponse.json({ users, deletedUserIds, syncTimestamp: new Date().toISOString(), pagination }),
      req
    );
  } catch (error: unknown) {
    console.error('Error in user sync:', error);
    return applyCorsHeaders(
      NextResponse.json(
        { error: 'Internal server error', details: (error as Error).message },
        { status: 500 }
      ),
      req
    );
  }
}

// POST endpoint to handle updates from IonicPOS
export async function POST(req: NextRequest, context: { params: Promise<{ companyId: string }> }) {
  try {
    let authUser: any;
    const ionicAuth = await validateIonicAuth(req);
    if (ionicAuth.isAuthenticated) {
      authUser = { id: ionicAuth.userId, companyId: ionicAuth.companyId };
    } else {
      authUser = await requireAuth(['owner','admin','partner'])(req);
    }
    const params = await context.params;
    const companyId = params.companyId;
    await dbConnect();
    const headerCompanyId = req.headers.get('company-id');
    if (headerCompanyId && companyId !== headerCompanyId) {
      return applyCorsHeaders(NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 }), req);
    }
    const { syncId, userUpdates } = await req.json();
    if (!syncId || !Array.isArray(userUpdates)) {
      return applyCorsHeaders(NextResponse.json({ error: 'Invalid request format' }, { status: 400 }), req);
    }
    const results: any[] = [];
    const errors: any[] = [];
    for (const update of userUpdates) {
      try {
        if (!update._id) throw new Error('User ID is required');
        const userDoc = await User.findOne({
          _id: new mongoose.Types.ObjectId(update._id),
          companyId: new mongoose.Types.ObjectId(companyId)
        });
        if (!userDoc) throw new Error(`User not found: ${update._id}`);
        const { _id, passwordHash, ...updateData } = update;
        userDoc.set({
          ...updateData,
          lastModified: new Date(),
          syncStatus: 'synced',
          modifiedFields: Object.keys(updateData)
        });
        await userDoc.save();
        results.push({ userId: userDoc._id.toString(), status: 'success' });
      } catch (e) {
        errors.push({ userId: update._id, error: e instanceof Error ? e.message : 'Unknown error' });
      }
    }
    console.log(`[User Sync POST] Processed ${results.length} updates with ${errors.length} errors`);
    return applyCorsHeaders(
      NextResponse.json({ results, errors, serverTime: new Date().toISOString() }),
      req
    );
  } catch (error: unknown) {
    console.error('[POST User Sync] Error:', error);
    return applyCorsHeaders(
      NextResponse.json({ error: 'Internal server error', details: (error as Error).message }, { status: 500 }),
      req
    );
  }
}