// src/app/api/company/[companyId]/roles/[roleId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth-helpers';
import connectToDatabase from '@/lib/mongoDb';
import Role from '@/models/Role';
import mongoose from 'mongoose';
import { applyCorsHeaders, handleCorsOptions } from '@/middleware/cors-middleware';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

// GET a specific role by ID
export const GET = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string; roleId: string }> }
  ) => {
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const { companyId, roleId } = params;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(roleId)) {
        return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Get the role
      const role = await Role.findOne({
        _id: new mongoose.Types.ObjectId(roleId),
        companyId: new mongoose.Types.ObjectId(companyId)
      }).lean();
      
      if (!role) {
        return NextResponse.json({ error: 'Role not found' }, { status: 404 });
      }
      
      return applyCorsHeaders(
        NextResponse.json({ role }),
        req
      );
    } catch (error: unknown) {
      console.error('Error fetching role:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'admin', 'partner'] // Only owner and admin can view role details
);

// PUT to update a role
export const PUT = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string; roleId: string }> }
  ) => {
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const { companyId, roleId } = params;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(roleId)) {
        return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Only owners can update roles
      if (user.role !== 'owner' && user.role !== 'partner' && user.userType !== 'superuser') {
        return NextResponse.json({ error: 'Only company owners can update roles' }, { status: 403 });
      }

      // Find the role
      const role = await Role.findOne({
        _id: new mongoose.Types.ObjectId(roleId),
        companyId: new mongoose.Types.ObjectId(companyId)
      });
      
      if (!role) {
        return NextResponse.json({ error: 'Role not found' }, { status: 404 });
      }
      
      // Prevent modification of system roles
      if (role.isSystemRole) {
        return NextResponse.json({ error: 'System roles cannot be modified' }, { status: 403 });
      }
      
      // Get the updated role data
      const roleData = await req.json();
      
      // Update the role
      if (roleData.name) role.name = roleData.name;
      if (roleData.description !== undefined) role.description = roleData.description;
      if (roleData.type) role.type = roleData.type;
      if (roleData.permissions) role.permissions = roleData.permissions;
      role.updatedAt = new Date();
      
      await role.save();
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'Role updated successfully',
          role 
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error updating role:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'partner'] // Only owner can update roles
);

// DELETE to remove a role
export const DELETE = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string; roleId: string }> }
  ) => {
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const { companyId, roleId } = params;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId) || !mongoose.Types.ObjectId.isValid(roleId)) {
        return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Only owners can delete roles
      if (user.role !== 'owner' && user.role !== 'partner' && user.userType !== 'superuser') {
        return NextResponse.json({ error: 'Only company owners can delete roles' }, { status: 403 });
      }

      // Find the role
      const role = await Role.findOne({
        _id: new mongoose.Types.ObjectId(roleId),
        companyId: new mongoose.Types.ObjectId(companyId)
      });
      
      if (!role) {
        return NextResponse.json({ error: 'Role not found' }, { status: 404 });
      }
      
      // Prevent deletion of system roles
      if (role.isSystemRole) {
        return NextResponse.json({ error: 'System roles cannot be deleted' }, { status: 403 });
      }
      
      // Delete the role
      await Role.deleteOne({ _id: role._id });
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'Role deleted successfully'
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error deleting role:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  ['owner', 'partner'] // Only owner can delete roles
);
