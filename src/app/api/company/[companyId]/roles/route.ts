// src/app/api/company/[companyId]/roles/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth-helpers';
import connectToDatabase from '@/lib/mongoDb';
import Role from '@/models/Role';
import mongoose from 'mongoose';
import { applyCorsHeaders, handleCorsOptions } from '@/middleware/cors-middleware';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

// GET all roles for a company
export const GET = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning about synchronous access
      const params = await context.params;
      const companyId = params.companyId;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        console.error('Unauthorized access attempt:', { userId: user.id, userCompanyId: user.companyId, requestedCompanyId: companyId });
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Get query parameters
      const { searchParams } = new URL(req.url);
      const includeSystem = searchParams.has('includeSystem') ? searchParams.get('includeSystem') === 'true' : true;
      
      // Build the query
      const query: any = { companyId: new mongoose.Types.ObjectId(companyId) };
      
      // If not including system roles, filter them out
      if (!includeSystem) {
        query.isSystemRole = false;
      }
      
      // Get roles
      const roles = await Role.find(query).sort({ name: 1 }).lean();
      
      return applyCorsHeaders(
        NextResponse.json({ roles }),
        req
      );
    } catch (error: unknown) {
      console.error('Error fetching roles:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  { allowedRoles: ['owner', 'admin', 'partner', 'attorney'], requiredPermissions: ['roles.read'] }
);

// POST to create a new role
export const POST = withAuth(
  async (
    req: NextRequest,
    user: import('@/lib/authUtils').JwtPayload,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await connectToDatabase();
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const companyId = params.companyId;
      
      // Validate the ObjectId format
      if (!mongoose.Types.ObjectId.isValid(companyId)) {
        return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
      }

      // Ensure user has access to this company
      if (user.userType !== 'superuser' && user.companyId?.toString() !== companyId) {
        console.error('Unauthorized access attempt:', { userId: user.id, userCompanyId: user.companyId, requestedCompanyId: companyId });
        return NextResponse.json({ error: 'Unauthorized access to company data' }, { status: 403 });
      }

      // Only owners can create roles
      if (user.role !== 'owner' && user.role !== 'partner' && user.userType !== 'superuser') {
        return NextResponse.json({ error: 'Only company owners can create roles' }, { status: 403 });
      }

      // Get the role data
      const roleData = await req.json();
      
      // Validate required fields
      if (!roleData.name) {
        return NextResponse.json({ error: 'Role name is required' }, { status: 400 });
      }
      
      // Check if role with same name already exists
      const existingRole = await Role.findOne({
        companyId: new mongoose.Types.ObjectId(companyId),
        name: roleData.name
      });
      
      if (existingRole) {
        return NextResponse.json({ error: 'A role with this name already exists' }, { status: 409 });
      }
      
      // Create the new role
      const newRole = new Role({
        companyId: new mongoose.Types.ObjectId(companyId),
        name: roleData.name,
        description: roleData.description || '',
        type: roleData.type || 'hq',
        isSystemRole: false, // Custom roles are never system roles
        permissions: roleData.permissions || []
      });
      
      await newRole.save();
      
      return applyCorsHeaders(
        NextResponse.json({ 
          message: 'Role created successfully',
          role: newRole 
        }),
        req
      );
    } catch (error: unknown) {
      console.error('Error creating role:', error);
      return applyCorsHeaders(
        NextResponse.json(
          { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
          { status: 500 }
        ),
        req
      );
    }
  },
  { allowedRoles: ['owner', 'partner'], requiredPermissions: ['roles.create'] }
);
