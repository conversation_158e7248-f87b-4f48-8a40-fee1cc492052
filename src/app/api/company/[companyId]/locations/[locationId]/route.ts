import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import dbConnect from '@/lib/db';
import Location from '@/models/Location';

// Helper function to verify authentication
const verifyAuth = async () => {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get('session');

  if (!sessionCookie) {
    return { isAuthenticated: false, error: 'No session cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionCookie.value, secret) as {
      userId: string;
      companyId?: string;
    };
    return { isAuthenticated: true, userId: decoded.userId, companyId: decoded.companyId };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    await dbConnect();

    const params = await context.params;
    const { companyId, locationId } = params;
    const headerCompanyId = req.headers.get('company-id');

    console.log('GET Location - Request params:', { companyId, locationId });

    // Verify authentication
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      return Response.json({ error: authResult.error }, { status: 401 });
    }

    // Validate company access
    try {
      await validateCompanyAccess(companyId, headerCompanyId);
    } catch (error: unknown) {
      return Response.json({ error: (error as Error).message }, { status: 403 });
    }

    // Fetch location
    const location = await Location.findOne({
      _id: locationId,
      companyId
    }).lean();

    if (!location) {
      return Response.json({ error: 'Location not found' }, { status: 404 });
    }

    return Response.json(location);
  } catch (error: unknown) {
    console.error('Error in location GET:', error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
