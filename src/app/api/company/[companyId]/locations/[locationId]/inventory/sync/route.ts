import { NextRequest, NextResponse } from 'next/server';
import { isValidCompanyUser } from '@/lib/server-auth';
import dbConnect from '@/lib/db';
import BranchInventory from '@/models/BranchInventory';
import InventoryTransaction from '@/models/InventoryTransaction';
import { Types } from 'mongoose';

interface InventorySyncPayload {
  locationId: string;
  updates: {
    itemId: string;
    currentStock: number;
    transactions: {
      type: 'sale' | 'wastage' | 'adjustment';
      quantity: number;
      menuItemId?: string;
      timestamp: Date;
      reason?: string;
    }[];
  }[];
  syncTimestamp: Date;
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  try {
    // Get companyId and locationId from params
    const { companyId, locationId } = await context.params;

    // Validate user has access to this company
    const validation = await isValidCompanyUser(request, companyId);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.message }, { status: 401 });
    }

    // Parse request body
    const payload: InventorySyncPayload = await request.json();

    // Verify that the locationId in the payload matches the one in the URL
    if (payload.locationId !== locationId) {
      return NextResponse.json(
        { error: 'Location ID in payload does not match URL parameter' },
        { status: 400 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Start a session for transaction
    const session = await BranchInventory.startSession();
    
    try {
      // Map transaction types to our system's types
      const transactionTypeMap: Record<string, string> = {
        'sale': 'SALE',
        'wastage': 'ADJUSTMENT',
        'adjustment': 'ADJUSTMENT'
      };
      
      // Process each item in the payload
      const updatedItems: string[] = [];
      const errors: { itemId: string; error: string }[] = [];
      
      await session.withTransaction(async () => {
        for (const update of payload.updates) {
          try {
            // Find the current inventory item
            const inventoryItem = await BranchInventory.findOne({
              companyId: new Types.ObjectId(companyId),
              locationId: new Types.ObjectId(locationId),
              _id: new Types.ObjectId(update.itemId)
            }).session(session);
            
            if (!inventoryItem) {
              errors.push({
                itemId: update.itemId,
                error: 'Inventory item not found'
              });
              continue;
            }

            // Record each transaction
            for (const txn of update.transactions) {
              // Convert transaction type
              const transactionType = transactionTypeMap[txn.type] || 'ADJUSTMENT';
              
              // Calculate stock change
              // For sales and wastage, stock decreases
              // For adjustments, it can be either positive or negative
              const stockChange = txn.type === 'adjustment' ? txn.quantity : -Math.abs(txn.quantity);
              
              // Previous stock value
              const previousStock = inventoryItem.currentStock;
              
              // New stock value (will be updated at the end)
              const newStock = previousStock + stockChange;
              
              // Create transaction record
              await InventoryTransaction.create([{
                timestamp: txn.timestamp,
                metadata: {
                  companyId: new Types.ObjectId(companyId),
                  locationId: new Types.ObjectId(locationId),
                  itemId: inventoryItem.itemId,
                  itemType: inventoryItem.itemType,
                  transactionType,
                  userId: validation.userId,
                  reference: txn.menuItemId ? `MENU:${txn.menuItemId}` : undefined
                },
                previousStock,
                newStock,
                difference: stockChange,
                notes: txn.reason || `Synced from POS - ${txn.type}`
              }], { session });
            }
            
            // Update the inventory with the final stock level
            await BranchInventory.updateOne(
              { 
                _id: inventoryItem._id 
              },
              { 
                $set: { 
                  currentStock: update.currentStock,
                  lastUpdated: new Date()
                } 
              },
              { session }
            );
            
            updatedItems.push(update.itemId);
          } catch (itemError: any) {
            console.error(`Error updating item ${update.itemId}:`, itemError);
            errors.push({
              itemId: update.itemId,
              error: itemError.message || 'Failed to update inventory item'
            });
          }
        }
      });
      
      // Determine overall status based on errors
      const status = errors.length === 0 ? 'success' : 
        (errors.length < payload.updates.length ? 'partial' : 'error');
      
      return NextResponse.json({
        status,
        updatedItems,
        errors: errors.length > 0 ? errors : undefined,
        lastSyncTimestamp: new Date()
      });
    } finally {
      // End the session
      session.endSession();
    }
  } catch (error: any) {
    console.error('Error syncing inventory:', error);
    return NextResponse.json(
      { 
        status: 'error',
        error: error instanceof Error ? error.message : String(error) || 'Failed to sync inventory',
        lastSyncTimestamp: new Date()
      },
      { status: 500 }
    );
  }
}
