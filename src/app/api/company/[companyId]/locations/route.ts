import { NextRequest } from "next/server";
import { verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import dbConnect from "@/lib/db";
import Location from "@/models/Location";

const verifyAuth = async () => {
  const cookieStore = await cookies();
  const authTokenCookie = cookieStore.get('auth-token');

  if (!authTokenCookie) {
    return { isAuthenticated: false, error: 'No auth-token cookie' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(authTokenCookie.value, secret) as {
      id: string;
      email: string;
      role: string;
      companyId: string;
      userType: string;
    };
    return { isAuthenticated: true, userId: decoded.id, companyId: decoded.companyId };
  } catch (error: unknown) {
    return { isAuthenticated: false, error: 'Invalid auth token' };
  }
};

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    const authResult = await verifyAuth();
    if (!authResult.isAuthenticated) {
      console.error('Authentication failed:', authResult.error);
      return new Response(
        JSON.stringify({ message: "Unauthorized", details: authResult.error }),
        { status: 401 }
      );
    }

    const params = await context.params;
    const companyId = params.companyId;
    const requestCompanyId = request.headers.get("company-id");

    if (!requestCompanyId || requestCompanyId !== companyId) {
      return new Response(
        JSON.stringify({ message: "Company ID mismatch" }),
        { status: 403 }
      );
    }

    await dbConnect();

    const locations = await Location.find({ companyId }).sort({ name: 1 });
    
    return new Response(JSON.stringify(locations), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error: unknown) {
    console.error("Error in locations API:", error);
    return new Response(
      JSON.stringify({ message: "Internal server error" }),
      { status: 500 }
    );
  }
}
