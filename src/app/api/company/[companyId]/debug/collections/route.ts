import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';
import { requireAuth } from '@/lib/auth-helpers';

/**
 * GET /api/company/:companyId/debug/collections
 * 
 * Debug endpoint to list MongoDB collections and sample documents
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    // Check authentication
    let authUser;
    try {
      authUser = await requireAuth(['admin'])(req);
    } catch (error: any) {
      return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 401 });
    }
    // Validate company ID matches
    const { companyId } = await params;
    if (authUser.tenantId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }

    // Connect to the database
    await dbConnect();
    if (!mongoose.connection.db) throw new Error('Database not connected');

    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionResults = [];

    // Get information about specific collections related to inventory
    const collectionsToDetail = [
      'branchinventories',
      'inventorytransactions',
      'items',
      'ingredients'
    ];

    for (const collection of collections) {
      const collectionName = collection.name;
      if (!mongoose.connection.db) throw new Error('Database not connected');
      const count = await mongoose.connection.db.collection(collectionName).countDocuments();
      
      const detail = {
        name: collectionName,
        count: count,
        sample: null
      };
      
      // If this is a collection we want to sample and it has documents
      if (collectionsToDetail.includes(collectionName.toLowerCase()) && count > 0) {
        // Sample a document
        if (!mongoose.connection.db) throw new Error('Database not connected');
        const sampleDoc = await mongoose.connection.db.collection(collectionName)
          .findOne({});
          
        if (sampleDoc) {
          // Convert ObjectId to string for proper JSON serialization
          const stringifySample = JSON.parse(JSON.stringify(sampleDoc, (key, value) => {
            if (value && value._bsontype === 'ObjectID') {
              return value.toString();
            }
            return value;
          }));
          
          detail.sample = stringifySample;
        }
      }
      
      collectionResults.push(detail);
    }

    // Return collections information
    return NextResponse.json({
      success: true,
      collectionsCount: collections.length,
      collections: collectionResults
    });
  } catch (error: unknown) {
    console.error('Error in collections debug:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
