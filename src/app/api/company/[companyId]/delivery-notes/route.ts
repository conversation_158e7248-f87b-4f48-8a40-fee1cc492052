import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import DeliveryNote from '@/models/DeliveryNote';
import { Types } from 'mongoose';

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    const params = await context.params;
    
    // Validate company ID from header matches URL parameter
    const headerCompanyId = request.headers.get('company-id');
    await validateCompanyAccess(params.companyId, headerCompanyId);

    // Connect to database
    await dbConnect();

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;
    const sortField = searchParams.get('sortField') || 'deliveryNoteNumber';
    const sortDirection = searchParams.get('sortDirection') || 'asc';

    // Build query
    const query: {
      companyId: Types.ObjectId;
      status?: string;
      deliveryNoteNumber?: RegExp;
      $or?: Array<any>;
    } = {
      companyId: new Types.ObjectId(params.companyId),
    };

    if (status && status !== 'ALL') {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { deliveryNoteNumber: { $regex: search, $options: 'i' } },
        { 'orderId.orderNumber': { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count for pagination
    const total = await DeliveryNote.countDocuments(query);

    // Get counts for each status
    const counts = await DeliveryNote.aggregate([
      { $match: { companyId: new Types.ObjectId(params.companyId) } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Convert counts array to object
    const statusCounts = {
      draft: 0,
      inProgress: 0,
      completed: 0,
      cancelled: 0
    };

    counts.forEach((item) => {
      switch (item._id) {
        case 'DRAFT':
          statusCounts.draft = item.count;
          break;
        case 'IN_PROGRESS':
          statusCounts.inProgress = item.count;
          break;
        case 'COMPLETED':
          statusCounts.completed = item.count;
          break;
        case 'CANCELLED':
          statusCounts.cancelled = item.count;
          break;
      }
    });

    // Build sort object for MongoDB
    const getSortField = (field: string) => {
      switch (field) {
        case 'deliveryNoteNumber':
          return 'deliveryNoteNumber';
        case 'orderNumber':
          return 'orderId.orderNumber';
        case 'deliveryDate':
          return 'deliveryDate';
        case 'status':
          return 'status';
        case 'createdAt':
          return 'createdAt';
        default:
          return 'deliveryNoteNumber';
      }
    };

    const sortObject: Record<string, 1 | -1> = {};
    const sortOrder = sortDirection === 'asc' ? 1 : -1;
    sortObject[getSortField(sortField)] = sortOrder;
    
    // Get paginated delivery notes
    const deliveryNotes = await DeliveryNote.find(query)
      .populate('orderId', 'orderNumber')
      .sort(sortObject)
      .skip(skip)
      .limit(limit)
      .lean();

    return NextResponse.json({
      deliveryNotes,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      counts: statusCounts
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/company/[companyId]/delivery-notes:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 400 }
    );
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    const params = await context.params;
    
    // Validate company ID from header matches URL parameter
    const headerCompanyId = request.headers.get('company-id');
    await validateCompanyAccess(params.companyId, headerCompanyId);

    // Connect to database
    await dbConnect();

    const data = await request.json();
    const deliveryNoteNumber = await generateDeliveryNoteNumber(params.companyId);

    const deliveryNote = new DeliveryNote({
      ...data,
      companyId: new Types.ObjectId(params.companyId),
      deliveryNoteNumber,
      status: 'DRAFT',
      handoverFlow: [
        { 
          stepType: 'DISPATCH',
          status: 'PENDING',
          required: true,
          confirmedItems: []
        },
        { 
          stepType: 'SHOP',
          status: 'PENDING',
          required: true,
          confirmedItems: []
        }
      ]
    });

    await deliveryNote.save();

    return NextResponse.json(deliveryNote);
  } catch (error: unknown) {
    console.error('Error in POST /api/company/[companyId]/delivery-notes:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 400 }
    );
  }
}

async function generateDeliveryNoteNumber(companyId: string): Promise<string> {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  
  // Get count of delivery notes for today
  const startOfDay = new Date(year, today.getMonth(), today.getDate());
  const endOfDay = new Date(year, today.getMonth(), today.getDate() + 1);
  
  const count = await DeliveryNote.countDocuments({
    companyId: new Types.ObjectId(companyId),
    createdAt: {
      $gte: startOfDay,
      $lt: endOfDay
    }
  });

  const sequence = String(count + 1).padStart(4, '0');
  return `DN${year}${month}${day}${sequence}`;
}
