// src/app/api/company/[companyId]/delivery-notes/pending/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import DeliveryNote from '@/models/DeliveryNote';
import Order from '@/models/Order';
import { verifyToken } from '@/lib/authUtils';
import { Types } from 'mongoose';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  await dbConnect();
  
  try {
    // Validate authentication
    const token = req.cookies.get('session')?.value;
    
    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    const user = await verifyToken(token);
    
    if (!user) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }
    
    // Get companyId from params
    const params = await context.params;
    const { companyId } = params;
    
    // Validate company access
    const headerCompanyId = req.headers.get('company-id');
    
    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }
    
    if (!user.companyId || user.companyId.toString() !== companyId) {
      return NextResponse.json({ error: 'Not authorized for this company' }, { status: 403 });
    }
    
    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'PENDING_REVIEW';
    const locationId = searchParams.get('locationId') || null;
    
    // Build query
    const query: any = {
      companyId,
      status
    };
    
    // Filter by location if provided
    if (locationId) {
      // Find orders for this location first
      const orders = await Order.find({ 
        companyId, 
        sellerLocationId: locationId 
      }).select('_id');
      
      const orderIds = orders.map(o => o._id);
      query.orderId = { $in: orderIds };
    }
    
    // Get total count for pagination
    const total = await DeliveryNote.countDocuments(query);
    
    // Get delivery notes with pagination
    const deliveryNotes = await DeliveryNote.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .populate({
        path: 'orderId',
        select: 'orderNumber status items buyer sellerLocationId'
      });
    
    // Return paginated results
    return NextResponse.json({
      deliveryNotes,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/company/[companyId]/delivery-notes/pending:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// API to finalize a delivery note (transition from PENDING_REVIEW to FINALIZED)
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  await dbConnect();
  
  try {
    // Validate authentication
    const token = req.cookies.get('session')?.value;
    
    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    const user = await verifyToken(token);
    
    if (!user) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }
    
    // Get companyId from params
    const params = await context.params;
    const { companyId } = params;
    
    // Validate company access
    const headerCompanyId = req.headers.get('company-id');
    
    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Company ID mismatch' }, { status: 403 });
    }
    
    // Parse request body
    const body = await req.json();
    const { deliveryNoteId, items, action } = body;
    
    if (!deliveryNoteId || !action) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Find the delivery note
    const deliveryNote = await DeliveryNote.findOne({
      _id: deliveryNoteId,
      companyId
    });
    
    if (!deliveryNote) {
      return NextResponse.json({ error: 'Delivery note not found' }, { status: 404 });
    }
    
    // Update delivery note based on action
    if (action === 'finalize') {
      // Update to FINALIZED status
      deliveryNote.status = 'FINALIZED';
      deliveryNote.reviewed = true;
      
      // Update items if provided
      if (Array.isArray(items) && items.length > 0) {
        // Update items with modified quantities
        const updatedItems = deliveryNote.items.map((existingItem: any) => {
          const updatedItem = items.find((i: any) => 
            i.itemId.toString() === existingItem.itemId.toString()
          );
          
          if (updatedItem && typeof updatedItem.quantityPlanned === 'number') {
            existingItem.quantityPlanned = updatedItem.quantityPlanned;
          }
          
          return existingItem;
        });
        
        deliveryNote.items = updatedItems;
      }
      
      // Save the updated delivery note
      await deliveryNote.save();
      
      // Update dispatch step confirmation
      const dispatchStep = deliveryNote.handoverFlow.find(
        (step: any) => step.stepType === 'DISPATCH'
      );
      
      if (dispatchStep) {
        dispatchStep.status = 'SIGNED';
        dispatchStep.signedByUserId = user.id ? new Types.ObjectId(user.id) : undefined;
        dispatchStep.signedAt = new Date();
        dispatchStep.confirmedItems = deliveryNote.items.map((item: any) => ({
          itemId: item.itemId,
          confirmedQty: item.quantityPlanned
        }));
        
        await deliveryNote.save();
      }
      
      return NextResponse.json({
        message: 'Delivery note finalized successfully',
        deliveryNote
      });
    } else if (action === 'reject') {
      // Cancel the delivery note
      deliveryNote.status = 'CANCELLED';
      deliveryNote.reviewed = true;
      await deliveryNote.save();
      
      return NextResponse.json({
        message: 'Delivery note rejected',
        deliveryNote
      });
    } else if (action === 'markReviewed') {
      // Just mark as reviewed but keep as PENDING_REVIEW
      deliveryNote.reviewed = true;
      await deliveryNote.save();
      
      return NextResponse.json({
        message: 'Delivery note marked as reviewed',
        deliveryNote
      });
    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error: unknown) {
    console.error('Error in PATCH /api/company/[companyId]/delivery-notes/pending:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
