import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';
import dbConnect from '@/lib/db';
import DeliveryNote from '@/models/DeliveryNote';
import Order from '@/models/Order';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';
import { updateOrderDeliveryStatus } from '@/lib/orders';
import UOM from '@/models/UOM';
import { Ingredient } from '@/models/Ingredient';

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// Helper function to verify authentication
const verifyAuth = async (request: NextRequest) => {
  // First, try to get cookies from request object
  const cookieHeader = request.headers.get('cookie');
  let sessionToken = null;
  
  if (cookieHeader) {
    // Parse cookie header manually
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);
    
    sessionToken = cookies['session'];
  }
  
  // If no session from request headers, try cookies() API as fallback
  if (!sessionToken) {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get('session');
    if (sessionCookie) {
      sessionToken = sessionCookie.value;
    }
  }

  if (!sessionToken) {
    console.log('Auth failed: No session token found');
    return { isAuthenticated: false, error: 'No session token found' };
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }

  try {
    const decoded = verify(sessionToken, secret) as {
      userId: string;
      companyId?: string;
    };
    return { isAuthenticated: true, userId: decoded.userId, companyId: decoded.companyId };
  } catch (error: unknown) {
    console.error('JWT verification failed:', error);
    return { isAuthenticated: false, error: 'Invalid session' };
  }
};

// Helper function to get delivery note and validate access
const getAndValidateDeliveryNote = async (
  companyId: string,
  deliveryNoteId: string,
  request: NextRequest
) => {
  // Handle the "new" route
  if (deliveryNoteId === 'new') {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');
    
    if (!orderId) {
      throw new Error('Order ID is required');
    }

    // Verify the order exists and belongs to the company
    const order = await Order.findOne({
      _id: new Types.ObjectId(orderId),
      companyId: new Types.ObjectId(companyId),
    });

    if (!order) {
      throw new Error('Order not found');
    }

    // Generate delivery note number (e.g., ***********-001)
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
    
    // Get the latest delivery note number for today
    const latestDeliveryNote = await DeliveryNote.findOne(
      {
        companyId: new Types.ObjectId(companyId),
        deliveryNoteNumber: new RegExp(`^DN-${dateStr}-`)
      },
      { deliveryNoteNumber: 1 },
      { sort: { deliveryNoteNumber: -1 } }
    );

    let sequenceNumber = '001';
    if (latestDeliveryNote) {
      const currentSequence = parseInt(latestDeliveryNote.deliveryNoteNumber.split('-')[2]);
      sequenceNumber = String(currentSequence + 1).padStart(3, '0');
    }

    const deliveryNoteNumber = `DN-${dateStr}-${sequenceNumber}`;

    // Create a new delivery note
    const deliveryNote = new DeliveryNote({
      companyId: new Types.ObjectId(companyId),
      orderId: new Types.ObjectId(orderId),
      deliveryNoteNumber,
      status: 'DRAFT',
      items: [],
      handoverFlow: [],
      deliveryDate: new Date(),
    });

    // Save the delivery note
    await deliveryNote.save();

    // Return populated delivery note
    return await DeliveryNote.findById(deliveryNote._id)
      .populate('orderId', 'orderNumber')
      .lean();
  }

  // For existing delivery notes
  const deliveryNote = await DeliveryNote.findOne({
    _id: new Types.ObjectId(deliveryNoteId),
    companyId: new Types.ObjectId(companyId),
  })
  .populate('orderId', 'orderNumber')
  .populate({
    path: 'items',
    populate: [
      {
        path: 'itemId',
        model: Ingredient,
        select: 'name description'
      },
      {
        path: 'uomId',
        model: UOM,
        select: 'name shortCode'
      }
    ]
  })
  .lean();

  if (!deliveryNote) {
    throw new Error('Delivery note not found');
  }

  return deliveryNote;
};

// GET /api/company/[companyId]/delivery-notes/[id]
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;
    const headerCompanyId = request.headers.get('company-id');

    await validateCompanyAccess(companyId, headerCompanyId);
    const authResult = await verifyAuth(request);
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error || 'Unauthorized' }, { status: 401 });
    }

    const deliveryNote = await getAndValidateDeliveryNote(companyId, id, request);

    return NextResponse.json(deliveryNote);
  } catch (error: any) {
    console.error('Error in GET /api/company/[companyId]/delivery-notes/[id]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : String(error) || 'Internal Server Error' },
      { status: error instanceof Error && error.message ? 400 : 500 }
    );
  }
}

// PATCH /api/company/[companyId]/delivery-notes/[id]
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;
    const headerCompanyId = request.headers.get('company-id');

    await validateCompanyAccess(companyId, headerCompanyId);
    const authResult = await verifyAuth(request);
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error || 'Unauthorized' }, { status: 401 });
    }

    const updates = await request.json();
    
    // Find and update the delivery note
    const deliveryNote = await DeliveryNote.findOneAndUpdate(
      { _id: id, companyId: new Types.ObjectId(companyId) },
      { $set: updates },
      { new: true }
    );

    if (!deliveryNote) {
      return NextResponse.json({ error: 'Delivery note not found' }, { status: 404 });
    }

    // If status changed to COMPLETED, update order status
    if (updates.status === 'COMPLETED') {
      await updateOrderDeliveryStatus(deliveryNote.orderId.toString());
    }

    return NextResponse.json(deliveryNote);
  } catch (error: any) {
    console.error('Error in PATCH /api/company/[companyId]/delivery-notes/[id]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : String(error) || 'Internal Server Error' },
      { status: error instanceof Error && error.message ? 400 : 500 }
    );
  }
}

// DELETE /api/company/[companyId]/delivery-notes/[id]
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;
    const headerCompanyId = request.headers.get('company-id');

    await validateCompanyAccess(companyId, headerCompanyId);
    const authResult = await verifyAuth(request);
    if (!authResult.isAuthenticated) {
      return NextResponse.json({ error: authResult.error || 'Unauthorized' }, { status: 401 });
    }

    const deliveryNote = await getAndValidateDeliveryNote(companyId, id, request);

    if (!deliveryNote) {
      return NextResponse.json(
        { error: 'Delivery note not found' },
        { status: 404 }
      );
    }

    if (deliveryNote.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Only DRAFT delivery notes can be deleted' },
        { status: 400 }
      );
    }

    await deliveryNote.deleteOne();

    return NextResponse.json({ message: 'Delivery note deleted successfully' });
  } catch (error: any) {
    console.error('Error in DELETE /api/company/[companyId]/delivery-notes/[id]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : String(error) || 'Internal Server Error' },
      { status: error instanceof Error && error.message ? 400 : 500 }
    );
  }
}
