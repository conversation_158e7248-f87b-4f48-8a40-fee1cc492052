import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import DeliveryNote from '@/models/DeliveryNote';
import { Types } from 'mongoose';

// Helper function to validate company ID
const validateCompanyAccess = async (
  companyId: string,
  headerCompanyId: string | null
) => {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
};

// DELETE /api/company/[companyId]/delivery-notes/bulk-delete
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    const params = await context.params;
    const companyId = params?.companyId;
    
    // Validate company ID from header matches URL parameter
    const headerCompanyId = request.headers.get('company-id');
    await validateCompanyAccess(companyId, headerCompanyId);

    // Connect to database
    await dbConnect();

    // Get IDs from request body
    const body = await request.json();
    
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request: No delivery note IDs provided' },
        { status: 400 }
      );
    }

    // Ensure all IDs are valid ObjectIDs
    const validIds = body.ids.filter((id: string) => Types.ObjectId.isValid(id));
    
    if (validIds.length !== body.ids.length) {
      return NextResponse.json(
        { error: 'Invalid request: Some delivery note IDs are invalid' },
        { status: 400 }
      );
    }

    // Find all delivery notes that match the IDs and company ID
    const deliveryNotes = await DeliveryNote.find({
      _id: { $in: validIds },
      companyId: new Types.ObjectId(companyId)
    });

    // Check if all requested notes were found
    if (deliveryNotes.length !== validIds.length) {
      return NextResponse.json(
        { error: 'Some delivery notes were not found or do not belong to this company' },
        { status: 404 }
      );
    }

    // Check if any note is not in DRAFT status
    const nonDraftNotes = deliveryNotes.filter(note => note.status !== 'DRAFT');
    if (nonDraftNotes.length > 0) {
      return NextResponse.json(
        { 
          error: 'Only DRAFT delivery notes can be deleted',
          nonDeletableIds: nonDraftNotes.map(note => note._id)
        },
        { status: 400 }
      );
    }

    // Delete all delivery notes
    const result = await DeliveryNote.deleteMany({
      _id: { $in: validIds },
      companyId: new Types.ObjectId(companyId),
      status: 'DRAFT' // Extra safety check
    });

    return NextResponse.json({
      message: `${result.deletedCount} delivery notes deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (error: unknown) {
    console.error('Error in DELETE /api/company/[companyId]/delivery-notes/bulk-delete:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
