import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { requireAuth } from '@/lib/auth-helpers';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const { companyId } = await params;
    await requireAuth(request);
    await dbConnect();

    const body = await request.json();
    const { menuItemId, components } = body;

    // Update menu item with new mapping
    const result = await fetch(`${process.env.MONGODB_URI}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'API-Key': process.env.MONGODB_API_KEY || '',
      },
      body: JSON.stringify({
        collection: 'menuItems',
        database: 'foodprepai',
        dataSource: 'Cluster0',
        filter: { _id: menuItemId, companyId },
        update: {
          $set: {
            components: components.map((c: any) => ({
              itemId: c.itemId,
              quantity: c.quantity,
              baseUOM: c.baseUOM,
              costPerUnit: c.costPerUnit,
            })),
            inventoryStatus: 'linked',
            updatedAt: new Date(),
          },
        },
      }),
    });

    if (!result.ok) {
      throw new Error('Failed to update menu item mapping');
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Menu mapping error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : String(error) || 'Internal server error' },
      { status: 500 }
    );
  }
}
