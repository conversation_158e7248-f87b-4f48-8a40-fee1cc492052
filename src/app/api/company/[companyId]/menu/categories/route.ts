import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { requireAuth } from '@/lib/auth-helpers';
import { MenuCategory } from '@/models/MenuCategory';
import { MenuItem } from '@/models/MenuItem';
import type { IMenuCategory } from '@/models/MenuCategory';
import { createMenuCategorySchema, updateMenuCategorySchema } from '@/schemas/menu';

// GET /api/company/[companyId]/menu/categories
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAuth(req);
    await dbConnect();
    const { companyId } = await context.params;
    
    // Support pagination and filtering
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';

    // Build query
    const query: any = { companyId: new Types.ObjectId(companyId) };
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }
    if (status) {
      query.status = status;
    }

    const skip = (page - 1) * limit;
    const categories = await MenuCategory.find(query)
      .sort({ order: 1, name: 1 })
      .skip(skip)
      .limit(limit)
      .lean<IMenuCategory[]>();

    const total = await MenuCategory.countDocuments(query);

    // Transform categories to match frontend expectations
    const transformedCategories = categories.map(category => ({
      ...category,
      id: category._id.toString(),
      _id: undefined
    }));

    return NextResponse.json({
      categories: transformedCategories,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error: any) {
    console.error('Error in GET /api/company/[companyId]/menu/categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch menu categories', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// POST /api/company/[companyId]/menu/categories
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAuth(req);
    await dbConnect();
    const { companyId } = await context.params;
    const body = await req.json();

    // Validate request body
    const validatedData = createMenuCategorySchema.parse(body);

    // If order is not provided, set it to the current max order + 1
    if (!validatedData.order) {
      const maxOrderRaw = await MenuCategory.findOne({ companyId: new Types.ObjectId(companyId) })
        .sort({ order: -1 })
        .select('order')
        .lean();
      const maxOrder = maxOrderRaw as { order: number } | null;
      validatedData.order = (maxOrder?.order || 0) + 1;
    }

    // Create new category
    const category = await MenuCategory.create({
      ...validatedData,
      companyId: new Types.ObjectId(companyId),
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST /api/company/[companyId]/menu/categories:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    if (error.code === 11000) { // Duplicate key error
      return NextResponse.json(
        { error: 'Category name must be unique within the company' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/company/[companyId]/menu/categories/[id]
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await requireAuth(req);
    await dbConnect();
    const { companyId, id } = await context.params;
    const body = await req.json();

    // Validate request body
    const validatedData = updateMenuCategorySchema.parse(body);

    // Update category
    const category = await MenuCategory.findOneAndUpdate(
      { 
        _id: new Types.ObjectId(id),
        companyId: new Types.ObjectId(companyId),
      },
      { $set: validatedData },
      { new: true }
    );

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(category);
  } catch (error: any) {
    console.error('Error in PATCH /api/company/[companyId]/menu/categories/[id]:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    if (error.code === 11000) { // Duplicate key error
      return NextResponse.json(
        { error: 'Category name must be unique within the company' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/company/[companyId]/menu/categories/[id]
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await requireAuth(req);
    await dbConnect();
    const { companyId, id } = await context.params;

    // Check if category has any menu items
    const menuItemCount = await MenuItem.countDocuments({
      categoryId: new Types.ObjectId(id),
      companyId: new Types.ObjectId(companyId),
    });

    if (menuItemCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category that contains menu items' },
        { status: 400 }
      );
    }

    // Delete category
    const category = await MenuCategory.findOneAndDelete({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(companyId),
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in DELETE /api/company/[companyId]/menu/categories/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
