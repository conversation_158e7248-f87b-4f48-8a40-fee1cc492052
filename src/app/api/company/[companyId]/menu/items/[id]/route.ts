import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { MenuItem } from '@/models/MenuItem';
import BranchInventory from '@/models/BranchInventory';
import { updateMenuItemSchema, validateAndComputePrice } from '@/schemas/menu';

// GET /api/company/[companyId]/menu/items/[id]
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;

    // Validate ID format
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid menu item ID format' },
        { status: 400 }
      );
    }

    const menuItem = await MenuItem.findOne({
      _id: id,
      companyId: new Types.ObjectId(companyId)
    })
    .populate('categoryId')
    .populate('inventoryItem.itemId')
    .populate('recipeComponents.itemId');

    if (!menuItem) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(menuItem);
  } catch (error: any) {
    console.error('Error in GET /api/company/[companyId]/menu/items/[id]:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

// PATCH /api/company/[companyId]/menu/items/[id]
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;
    
    console.log(`PATCH menu item - id: ${id}, companyId: ${companyId}`);
    
    // Validate ID format
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid menu item ID format' },
        { status: 400 }
      );
    }
    
    const body = await req.json();
    console.log("Request body:", body);

    // Validate request body
    const validatedData = updateMenuItemSchema.parse({
      ...body,
      companyId
    });
    
    console.log("Validated data:", validatedData);

    // Check if we need to update the type to RECIPE
    if (body.type === 'RECIPE' || (body.recipeComponents && body.recipeComponents.length > 0)) {
      validatedData.type = 'RECIPE'; // Ensure type is set to RECIPE for recipe items
    }

    // Validate inventory references if provided
    if (validatedData.type === 'single' && validatedData.inventoryItem) {
      const inventoryItem = await BranchInventory.findById(validatedData.inventoryItem.itemId);
      if (!inventoryItem) {
        return NextResponse.json(
          { error: 'Invalid inventory item reference' },
          { status: 400 }
        );
      }
    } else if ((validatedData.type === 'recipe' || validatedData.type === 'RECIPE') && validatedData.recipeComponents) {
      // Handle case insensitive type check
      const itemIds = validatedData.recipeComponents.map(comp => comp.itemId);
      
      // Skip validation if no item IDs to check
      if (itemIds.length > 0) {
        // Temporarily disable strict validation for recipe components
        // We're allowing any valid ID since we're using a different schema for sellable items
        /*
        const inventoryItems = await BranchInventory.find({
          _id: { $in: itemIds }
        });
        
        if (inventoryItems.length !== itemIds.length) {
          return NextResponse.json(
            { error: 'One or more invalid inventory item references' },
            { status: 400 }
          );
        }
        */
      }
    }

    // Compute VAT and final price if prices are being updated
    let priceData = validatedData.prices;
    if (priceData) {
      priceData = validateAndComputePrice(priceData);
    }

    const updateData: any = { ...validatedData };
    if (priceData) {
      updateData.prices = priceData;
    }
    
    console.log("Update data:", updateData);

    const menuItem = await MenuItem.findOneAndUpdate(
      { _id: id, companyId: new Types.ObjectId(companyId) },
      updateData,
      { new: true }
    );

    if (!menuItem) {
      console.log("Menu item not found");
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }

    console.log("Menu item updated successfully");
    return NextResponse.json(menuItem);
  } catch (error: any) {
    console.error('Error in PATCH /api/company/[companyId]/menu/items/[id]:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : String(error),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// DELETE /api/company/[companyId]/menu/items/[id]
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;
    
    // Validate ID format
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid menu item ID format' },
        { status: 400 }
      );
    }

    const menuItem = await MenuItem.findOneAndDelete({
      _id: id,
      companyId: new Types.ObjectId(companyId)
    });

    if (!menuItem) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Menu item deleted successfully' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Error in DELETE /api/company/[companyId]/menu/items/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}