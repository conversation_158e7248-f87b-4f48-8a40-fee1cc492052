import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { MenuItem } from '@/models/MenuItem';
import BranchInventory from '@/models/BranchInventory';
import { createMenuItemSchema, updateMenuItemSchema, validateAndComputePrice } from '@/schemas/menu';

// GET /api/company/[companyId]/menu/items
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    console.log('GET menu items - starting request');
    await dbConnect();
    const { companyId } = await context.params;
    console.log('GET menu items - company ID:', companyId);
    
    // Validate companyId from header matches URL param
    // Uncomment if there are authentication issues
    /*
    const headerCompanyId = req.headers.get('company-id');
    if (headerCompanyId !== companyId) {
      console.error(`Company ID mismatch: URL ${companyId} vs header ${headerCompanyId}`);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    */
    
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const type = searchParams.get('type') || '';
    const status = searchParams.get('status') || '';
    
    console.log(`GET menu items - params: page=${page}, limit=${limit}, search=${search}, category=${category}, type=${type}, status=${status}`);

    // Safely convert companyId to ObjectId with error handling
    let companyObjectId;
    try {
      companyObjectId = new Types.ObjectId(companyId);
    } catch (error: unknown) {
      console.error('Invalid companyId format:', companyId, error);
      return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
    }

    const query: any = { companyId: companyObjectId };
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }
    if (category) {
      try {
        query.categoryId = new Types.ObjectId(category);
      } catch (error: unknown) {
        console.error('Invalid category ID format:', category);
        // Continue without this filter rather than failing
      }
    }
    if (type) {
      query.type = type;
    }
    if (status) {
      query.status = status;
    }
    
    console.log('GET menu items - query:', JSON.stringify(query));

    const skip = (page - 1) * limit;
    console.log(`GET menu items - executing query with skip=${skip}, limit=${limit}`);
    
    // Use try/catch for each query operation to isolate the issue
    let items = [];
    try {
      items = await MenuItem.find(query)
        .skip(skip)
        .limit(limit)
        .lean();
      console.log(`GET menu items - found ${items.length} items`);
    } catch (error: unknown) {
      console.error('Error fetching menu items:', error);
      return NextResponse.json({ error: 'Error fetching menu items', details: error instanceof Error ? error.message : String(error) }, { status: 500 });
    }
    
    // Try to populate references separately with error handling
    try {
      // Optional: Populate references if needed
      // items = await MenuItem.populate(items, [{ path: 'categoryId' }, { path: 'inventoryItem.itemId' }, { path: 'recipeComponents.itemId' }]);
    } catch (error: unknown) {
      console.error('Error populating references:', error);
      // Continue without populated data rather than failing completely
    }

    let total = 0;
    try {
      total = await MenuItem.countDocuments(query);
      console.log(`GET menu items - total count: ${total}`);
    } catch (error: unknown) {
      console.error('Error counting menu items:', error);
      // Use items.length as fallback if count fails
      total = items.length;
    }

    return NextResponse.json({
      items,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    console.error('Error in GET /api/company/[companyId]/menu/items:', error);
    // Return detailed error information to help debugging
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : String(error),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// POST /api/company/[companyId]/menu/items
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect();
    const { companyId } = await context.params;
    const body = await req.json();

    // Validate request body
    const validatedData = createMenuItemSchema.parse({
      ...body,
      companyId
    });

    // Validate inventory references
    if (validatedData.type === 'single' && validatedData.inventoryItem) {
      const inventoryItem = await BranchInventory.findById(validatedData.inventoryItem.itemId);
      if (!inventoryItem) {
        return NextResponse.json(
          { error: 'Invalid inventory item reference' },
          { status: 400 }
        );
      }
    } else if (validatedData.type === 'recipe' && validatedData.recipeComponents) {
      const itemIds = validatedData.recipeComponents.map(comp => comp.itemId);
      const inventoryItems = await BranchInventory.find({
        _id: { $in: itemIds }
      });
      
      if (inventoryItems.length !== itemIds.length) {
        return NextResponse.json(
          { error: 'One or more invalid inventory item references' },
          { status: 400 }
        );
      }
    }

    // Compute VAT and final price
    const priceData = validateAndComputePrice(validatedData.prices);
    
    const menuItem = new MenuItem({
      ...validatedData,
      prices: priceData
    });

    await menuItem.save();

    return NextResponse.json(menuItem, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST /api/company/[companyId]/menu/items:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/company/[companyId]/menu/items/[id]
export async function PATCH(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; id: string }> }
) {
  try {
    await dbConnect();
    const { companyId, id } = await context.params;
    const body = await req.json();

    // Validate request body
    const validatedData = updateMenuItemSchema.parse({
      ...body,
      companyId
    });

    // Validate inventory references
    if (validatedData.type === 'single' && validatedData.inventoryItem) {
      const inventoryItem = await BranchInventory.findById(validatedData.inventoryItem.itemId);
      if (!inventoryItem) {
        return NextResponse.json(
          { error: 'Invalid inventory item reference' },
          { status: 400 }
        );
      }
    } else if (validatedData.type === 'recipe' && validatedData.recipeComponents) {
      const itemIds = validatedData.recipeComponents.map(comp => comp.itemId);
      const inventoryItems = await BranchInventory.find({
        _id: { $in: itemIds }
      });
      
      if (inventoryItems.length !== itemIds.length) {
        return NextResponse.json(
          { error: 'One or more invalid inventory item references' },
          { status: 400 }
        );
      }
    }

    // Compute VAT and final price if prices are being updated
    let priceData = validatedData.prices;
    if (priceData) {
      priceData = validateAndComputePrice(priceData);
    }

    const menuItem = await MenuItem.findOneAndUpdate(
      { _id: id, companyId },
      {
        ...validatedData,
        prices: priceData
      },
      { new: true }
    )
    .populate('categoryId')
    .populate('inventoryItem.itemId')
    .populate('recipeComponents.itemId');

    if (!menuItem) {
      return NextResponse.json(
        { error: 'Menu item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(menuItem);
  } catch (error: any) {
    console.error('Error in PATCH /api/company/[companyId]/menu/items/[id]:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
