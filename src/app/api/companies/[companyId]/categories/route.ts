import { NextRequest, NextResponse } from 'next/server'
import dbConnect from "@/lib/db"
import { Category } from "@/models/Category"
import { Types } from 'mongoose'

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect()
    const { companyId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }
    
    const searchParams = req.nextUrl.searchParams
    const groupId = searchParams.get('groupId')
    
    const query: any = {
      $or: [
        { companyId },
        { isGlobal: true }
      ]
    }
    
    if (groupId) {
      if (!Types.ObjectId.isValid(groupId)) {
        return NextResponse.json({ error: 'Invalid group ID' }, { status: 400 })
      }
      query['groupId'] = groupId
    }
    
    const categories = await Category.find(query).sort({ name: 1 })
    
    return NextResponse.json(categories)
  } catch (error: unknown) {
    console.error('Error in GET /api/companies/[companyId]/categories:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect()
    const { companyId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }

    const body = await req.json()
    
    if (!body.groupId || !Types.ObjectId.isValid(body.groupId)) {
      return NextResponse.json({ error: 'Valid group ID is required' }, { status: 400 })
    }

    const category = new Category({
      ...body,
      companyId
    })
    
    await category.save()
    return NextResponse.json(category)
  } catch (error: unknown) {
    console.error('Error in POST /api/companies/[companyId]/categories:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 400 }
    )
  }
}
