import { NextRequest, NextResponse } from 'next/server'
import dbConnect from "@/lib/db"
import { Category } from "@/models/Category"
import { Types } from 'mongoose'

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; categoryId: string }> }
) {
  try {
    await dbConnect()
    const { companyId, categoryId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }

    if (!Types.ObjectId.isValid(categoryId)) {
      return NextResponse.json({ error: 'Invalid category ID' }, { status: 400 })
    }

    const body = await req.json()
    const { _id: _, ...updateData } = body

    const category = await Category.findOneAndUpdate(
      { _id: categoryId, companyId },
      updateData,
      { new: true }
    )

    if (!category) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 })
    }

    return NextResponse.json(category)
  } catch (error: unknown) {
    console.error('Error in PUT /api/companies/[companyId]/categories/[categoryId]:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 400 }
    )
  }
}

export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; categoryId: string }> }
) {
  try {
    await dbConnect()
    const { companyId, categoryId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }

    if (!Types.ObjectId.isValid(categoryId)) {
      return NextResponse.json({ error: 'Invalid category ID' }, { status: 400 })
    }

    const category = await Category.findOneAndDelete({ _id: categoryId, companyId })

    if (!category) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 })
    }

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Error in DELETE /api/companies/[companyId]/categories/[categoryId]:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
