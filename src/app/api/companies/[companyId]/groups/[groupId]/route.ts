import { NextRequest, NextResponse } from 'next/server'
import dbConnect from "@/lib/db"
import { Group } from "@/models/Group"
import { Types } from 'mongoose'

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; groupId: string }> }
) {
  try {
    await dbConnect()
    const { companyId, groupId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }

    if (!Types.ObjectId.isValid(groupId)) {
      return NextResponse.json({ error: 'Invalid group ID' }, { status: 400 })
    }

    const body = await req.json()
    const { _id: _, ...updateData } = body

    const group = await Group.findOneAndUpdate(
      { _id: groupId, companyId },
      updateData,
      { new: true }
    )

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    return NextResponse.json(group)
  } catch (error: unknown) {
    console.error('Error in PUT /api/companies/[companyId]/groups/[groupId]:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 400 }
    )
  }
}

export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; groupId: string }> }
) {
  try {
    await dbConnect()
    const { companyId, groupId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }

    if (!Types.ObjectId.isValid(groupId)) {
      return NextResponse.json({ error: 'Invalid group ID' }, { status: 400 })
    }

    const group = await Group.findOneAndDelete({ _id: groupId, companyId })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Error in DELETE /api/companies/[companyId]/groups/[groupId]:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}
