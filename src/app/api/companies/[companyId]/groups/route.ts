import { NextRequest, NextResponse } from 'next/server'
import dbConnect from "@/lib/db"
import { Group } from "@/models/Group"
// Types import removed as it's not used in this file

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect()
    const { companyId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }
    
    const groups = await Group.find({
      $or: [
        { companyId },
        { isGlobal: true }
      ]
    }).sort({ name: 1 })
    
    return NextResponse.json(groups)
  } catch (error: unknown) {
    console.error('Error in GET /api/companies/[companyId]/groups:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    await dbConnect()
    const { companyId } = await context.params
    const headerCompanyId = req.headers.get('company-id')

    if (!headerCompanyId || headerCompanyId !== companyId) {
      return NextResponse.json({ error: 'Invalid company ID' }, { status: 403 })
    }

    const body = await req.json()
    const group = new Group({
      ...body,
      companyId
    })
    
    await group.save()
    return NextResponse.json(group)
  } catch (error: unknown) {
    console.error('Error in POST /api/companies/[companyId]/groups:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 400 }
    )
  }
}
