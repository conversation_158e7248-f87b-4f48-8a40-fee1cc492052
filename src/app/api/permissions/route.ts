import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import Permission from '@/models/Permission';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const companyId = request.headers.get('company-id');
    if (!companyId) return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });

    // Fetch all system-level permissions and any company-specific permissions
    const permissions = await Permission.find({
      $or: [
        { isSystemLevel: true },
        { companyId }
      ]
    });
    
    return NextResponse.json(permissions);
  } catch (error: unknown) {
    console.error('[GET Permissions] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch permissions' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, description, category, isSystemLevel = false } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!companyId && !isSystemLevel) {
      return NextResponse.json({ error: 'Company ID is required for non-system permissions' }, { status: 400 });
    }

    if (!name || !description || !category) {
      return NextResponse.json({ error: 'Name, description, and category are required' }, { status: 400 });
    }

    await dbConnect();
    const permission = await Permission.create({ 
      name, 
      description, 
      category, 
      isSystemLevel,
      ...((!isSystemLevel && companyId) ? { companyId } : {})
    });
    
    return NextResponse.json(permission);
  } catch (error: unknown) {
    console.error('[POST Permission] Error:', error);
    return NextResponse.json({ error: 'Failed to create permission' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { permissionId, name, description, category } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!permissionId) {
      return NextResponse.json({ error: 'Permission ID is required' }, { status: 400 });
    }

    await dbConnect();
    const permission = await Permission.findById(permissionId);
    if (!permission) {
      return NextResponse.json({ error: 'Permission not found' }, { status: 404 });
    }

    // Prevent editing system-level permissions
    if (permission.isSystemLevel) {
      return NextResponse.json({ error: 'System permissions cannot be edited' }, { status: 403 });
    }

    // Verify company-specific match
    if (!permission.isSystemLevel && permission.companyId?.toString() !== companyId) {
      return NextResponse.json({ error: 'Unauthorized to edit this permission' }, { status: 403 });
    }

    // Update allowed fields
    if (name !== undefined) permission.name = name;
    if (description !== undefined) permission.description = description;
    if (category !== undefined) permission.category = category;

    await permission.save();
    return NextResponse.json(permission);
  } catch (error: unknown) {
    console.error('[PATCH Permission] Error:', error);
    return NextResponse.json({ error: 'Failed to update permission' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { permissionId } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!permissionId) {
      return NextResponse.json({ error: 'Permission ID is required' }, { status: 400 });
    }

    await dbConnect();
    
    // First check if it's a system permission
    const permission = await Permission.findById(permissionId);
    
    if (!permission) {
      return NextResponse.json({ error: 'Permission not found' }, { status: 404 });
    }
    
    // Only admin can delete system-level permissions
    if (permission.isSystemLevel) {
      // Here you would check if the user is a system admin
      // For now, we'll just prevent deletion of system permissions
      return NextResponse.json({ error: 'System permissions cannot be deleted' }, { status: 403 });
    }
    
    // For company permissions, verify the company ID matches
    if (!permission.isSystemLevel && permission.companyId?.toString() !== companyId) {
      return NextResponse.json({ error: 'Unauthorized to delete this permission' }, { status: 403 });
    }

    await Permission.findByIdAndDelete(permissionId);
    return NextResponse.json({ message: 'Permission deleted successfully' });
  } catch (error: unknown) {
    console.error('[DELETE Permission] Error:', error);
    return NextResponse.json({ error: 'Failed to delete permission' }, { status: 500 });
  }
}
