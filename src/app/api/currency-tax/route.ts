import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import CurrencyTax from '@/models/CurrencyTax';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const companyId = request.headers.get('company-id');
    if (!companyId) return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });

    const settings = await CurrencyTax.findOne({ companyId });
    return NextResponse.json(settings || {
      baseCurrency: '',
      vatOnSales: false,
      vatOnPurchases: false,
      vatRateOnSales: 20,
      vatRateOnPurchases: 20,
      defaultVatCategory: 'STANDARD'
    });
  } catch (error: unknown) {
    console.error('[GET CurrencyTax] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { 
      vatOnSales, 
      vatOnPurchases, 
      baseCurrency,
      vatRateOnSales,
      vatRateOnPurchases,
      defaultVatCategory 
    } = await request.json();
    const companyId = request.headers.get('company-id');

    if (!companyId || !baseCurrency) {
      return NextResponse.json({ error: 'Company ID and base currency are required' }, { status: 400 });
    }

    // Validate VAT rates
    if (vatRateOnSales < 0 || vatRateOnSales > 100) {
      return NextResponse.json({ error: 'VAT rate on sales must be between 0 and 100' }, { status: 400 });
    }
    if (vatRateOnPurchases < 0 || vatRateOnPurchases > 100) {
      return NextResponse.json({ error: 'VAT rate on purchases must be between 0 and 100' }, { status: 400 });
    }

    await dbConnect();
    const settings = await CurrencyTax.create({
      companyId,
      vatOnSales,
      vatOnPurchases,
      baseCurrency,
      vatRateOnSales,
      vatRateOnPurchases,
      defaultVatCategory
    });

    return NextResponse.json(settings);
  } catch (error: unknown) {
    console.error('[POST CurrencyTax] Error:', error);
    return NextResponse.json({ error: 'Failed to add settings' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { 
      vatOnSales, 
      vatOnPurchases, 
      baseCurrency,
      vatRateOnSales,
      vatRateOnPurchases,
      defaultVatCategory 
    } = await request.json();
    const companyId = request.headers.get('company-id');

    console.log('Received PUT request with data:', {
      vatOnSales,
      vatOnPurchases,
      baseCurrency,
      vatRateOnSales,
      vatRateOnPurchases,
      defaultVatCategory
    });

    if (!companyId || !baseCurrency) {
      return NextResponse.json({ error: 'Company ID and base currency are required' }, { status: 400 });
    }

    // Validate VAT rates
    if (vatRateOnSales < 0 || vatRateOnSales > 100) {
      return NextResponse.json({ error: 'VAT rate on sales must be between 0 and 100' }, { status: 400 });
    }
    if (vatRateOnPurchases < 0 || vatRateOnPurchases > 100) {
      return NextResponse.json({ error: 'VAT rate on purchases must be between 0 and 100' }, { status: 400 });
    }

    await dbConnect();
    const settings = await CurrencyTax.findOneAndUpdate(
      { companyId },
      { 
        vatOnSales, 
        vatOnPurchases, 
        baseCurrency,
        vatRateOnSales,
        vatRateOnPurchases,
        defaultVatCategory 
      },
      { new: true, upsert: true }
    );

    console.log('Updated settings:', settings.toObject());
    return NextResponse.json(settings);
  } catch (error: unknown) {
    console.error('[PUT CurrencyTax] Error:', error);
    return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const companyId = request.headers.get('company-id');

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    await dbConnect();
    await CurrencyTax.findOneAndDelete({ companyId });
    return NextResponse.json({ message: 'Tax settings deleted successfully' });
  } catch (error: unknown) {
    console.error('[DELETE CurrencyTax] Error:', error);
    return NextResponse.json({ error: 'Failed to delete settings' }, { status: 500 });
  }
}
