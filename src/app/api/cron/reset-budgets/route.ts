import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import BranchInventory, { IBranchInventory } from '@/models/BranchInventory'; 
import Company, { CompanyDocument } from '@/models/Company'; 
import { Types } from 'mongoose';

// Simple protection using a secret key from environment variables

// GET handler - Cron jobs typically use GET requests
export async function GET(req: NextRequest) {
  // Read CRON_SECRET inside the function
  const CRON_SECRET = process.env.CRON_SECRET;

  // --- Security Check --- 
  const authHeader = req.headers.get('authorization');
  if (!CRON_SECRET) {
    console.warn('CRON_SECRET environment variable not set. Endpoint is disabled.');
    return NextResponse.json({ success: false, message: 'Endpoint disabled: CRON_SECRET not set.' }, { status: 503 }); // 503 Service Unavailable
  }
  if (authHeader !== `Bearer ${CRON_SECRET}`) {
    return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
  }

  console.log('--- Running Cron Job: reset-budgets ---');
  console.log(`Cron job 'reset-budgets' started at ${new Date().toISOString()}`);

  try {
    await dbConnect();

    // --- Core Logic --- 

    const now = new Date(); // Use UTC by default
    const todayStartUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));

    // 1. Fetch all BranchInventory items with orderBudgetPeriod set (including monthly)
    const inventoryItems = await BranchInventory.find({
      orderBudgetPeriod: { $in: ['daily', 'weekly', 'monthly'] }
    }).select('_id companyId locationId orderBudgetPeriod orderBudgetLastReset').lean() as any[];

    if (!inventoryItems || inventoryItems.length === 0) {
      const message = "Cron job 'reset-budgets': No inventory items found with daily/weekly/monthly budget periods.";
      console.log(message);
      // Return empty details array when no resets
      return NextResponse.json({ success: true, message: message, details: [], timestamp: new Date().toISOString() });
    }

    // 2. Get unique company IDs
    const companyIds = [...new Set(inventoryItems.map(item => item.companyId.toString()))]
        .map(id => new Types.ObjectId(id));

    // 3. Fetch relevant Company documents
    const companies = await Company.find({ _id: { $in: companyIds } }).select('_id weekStartDay').lean() as Pick<CompanyDocument, '_id' | 'weekStartDay'>[];
    const companySettings = new Map<string, { weekStartDay: number }>();
    companies.forEach(comp => {
      companySettings.set((comp._id as any).toString(), { weekStartDay: comp.weekStartDay ?? 1 }); // Default to Monday if not set
    });

    // Helper to get the start of the week in UTC
    const getStartOfWeekUTC = (date: Date, weekStartDay: number): Date => {
        const currentDay = date.getUTCDay(); // 0=Sunday, 1=Monday...
        const diff = currentDay - weekStartDay;
        const daysToSubtract = diff < 0 ? diff + 7 : diff;
        const startOfWeek = new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate() - daysToSubtract));
        return startOfWeek;
    };

    // 6. Perform resets per company and period, collecting details
    const details: Array<{ companyId: string; daily: { updatedCount: number }; weekly: { updatedCount: number }; monthly: { updatedCount: number } }> = [];
    const monthStartUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1));
    for (const comp of companies) {
        const compIdStr = (comp._id as any).toString();
        const compItems = inventoryItems.filter(item => item.companyId.toString() === compIdStr);
        // Daily
        let dailyCount = 0;
        const dailyIds = compItems.filter(item => item.orderBudgetPeriod === 'daily' && (!item.orderBudgetLastReset || item.orderBudgetLastReset < todayStartUTC)).map(item => item._id);
        if (dailyIds.length) {
            const res = await BranchInventory.updateMany({ _id: { $in: dailyIds } }, { $set: { orderBudgetUsed: 0, orderBudgetLastReset: todayStartUTC } });
            dailyCount = (res as any).modifiedCount ?? 0;
        }
        // Weekly
        let weeklyCount = 0;
        const weekStartUTC = getStartOfWeekUTC(now, companySettings.get(compIdStr)!.weekStartDay);
        const weeklyIds = compItems.filter(item => item.orderBudgetPeriod === 'weekly' && (!item.orderBudgetLastReset || item.orderBudgetLastReset < weekStartUTC)).map(item => item._id);
        if (weeklyIds.length) {
            const res = await BranchInventory.updateMany({ _id: { $in: weeklyIds } }, { $set: { orderBudgetUsed: 0, orderBudgetLastReset: weekStartUTC } });
            weeklyCount = (res as any).modifiedCount ?? 0;
        }
        // Monthly
        let monthlyCount = 0;
        const monthlyIds = compItems.filter(item => item.orderBudgetPeriod === 'monthly' && (!item.orderBudgetLastReset || item.orderBudgetLastReset < monthStartUTC)).map(item => item._id);
        if (monthlyIds.length) {
            const res = await BranchInventory.updateMany({ _id: { $in: monthlyIds } }, { $set: { orderBudgetUsed: 0, orderBudgetLastReset: monthStartUTC } });
            monthlyCount = (res as any).modifiedCount ?? 0;
        }
        details.push({ companyId: compIdStr, daily: { updatedCount: dailyCount }, weekly: { updatedCount: weeklyCount }, monthly: { updatedCount: monthlyCount } });
    }
    const message = `Cron job 'reset-budgets' completed. Items checked: ${inventoryItems.length}. Items reset: ${details.reduce((sum, d) => sum + d.daily.updatedCount + d.weekly.updatedCount + d.monthly.updatedCount, 0)}.`;
    console.log(message);
    // --- Response --- 
    return NextResponse.json({ success: true, message: message, timestamp: new Date().toISOString(), details });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    console.error(`Error in 'reset-budgets' cron job: ${errorMessage}`, error);
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
