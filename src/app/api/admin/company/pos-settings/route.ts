// src/app/api/admin/company/pos-settings/route.ts
import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/db";
import Company from "@/models/Company";
import { withAuth } from "@/lib/auth-helpers";
import { applyCorsHeaders } from "@/middleware/cors-middleware";

/**
 * Get POS settings for a company
 * This endpoint is for administrators to get POS settings for companies
 */
export const GET = withAuth(
  async (req: NextRequest, _user: any) => {
    try {
      // The withAuth middleware already verified the user is authenticated
      // and has the correct role
      
      // Now we can focus on the business logic

      // Get companyId from query params
      const url = new URL(req.url);
      const companyId = url.searchParams.get('companyId');

      // Validate request parameters
      if (!companyId) {
        const response = NextResponse.json(
          { message: "Company ID is required" },
          { status: 400 }
        );
        return applyCorsHeaders(response, req);
      }

      // Connect to database
      await dbConnect();

      // Find company by ID
      const company = await Company.findById(companyId);
      
      if (!company) {
        const response = NextResponse.json(
          { message: "Company not found" },
          { status: 404 }
        );
        return applyCorsHeaders(response, req);
      }

      // Create response with company POS settings
      const response = NextResponse.json(
        {
          companyId: company._id,
          companyName: company.name,
          companyCode: company.companyCode || null,
          hasPassword: !!company.posPassword
        },
        { status: 200 }
      );
      
      // Apply CORS headers
      return applyCorsHeaders(response, req);
    } catch (error: unknown) {
      console.error("Error in POS settings API:", error);
      const response = NextResponse.json(
        { message: "Internal server error" },
        { status: 500 }
      );
      return applyCorsHeaders(response, req);
    }
  },
  ["admin", "superuser", "owner"] // Allowed roles
);
