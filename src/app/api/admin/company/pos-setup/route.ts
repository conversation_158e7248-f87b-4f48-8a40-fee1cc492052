// src/app/api/admin/company/pos-setup/route.ts
import { NextRequest, NextResponse } from "next/server";
import bcrypt from 'bcryptjs';
import dbConnect from "@/lib/db";
import Company from "@/models/Company";
import { withAuth } from "@/lib/auth-helpers";
import { applyCorsHeaders } from "@/middleware/cors-middleware";

/**
 * Set up POS credentials for a company
 * This endpoint is for administrators to set up POS access for companies
 */
export const POST = withAuth(
  async (req: NextRequest, _user: any) => {
    try {

      // Parse request body
      const body = await req.json();
      const { companyId, companyCode, posPassword } = body;

      // Validate request parameters
      if (!companyId || !companyCode) {
        const response = NextResponse.json(
          { message: "Company ID and code are required" },
          { status: 400 }
        );
        return applyCorsHeaders(response, req);
      }

      // Validate password if provided
      if (posPassword && posPassword.length < 6) {
        const response = NextResponse.json(
          { message: "Password must be at least 6 characters long" },
          { status: 400 }
        );
        return applyCorsHeaders(response, req);
      }

      // Validate company code format (e.g., alphanumeric, no spaces)
      if (!/^[a-zA-Z0-9_-]+$/.test(companyCode)) {
        const response = NextResponse.json(
          { message: "Company code must contain only letters, numbers, underscores, and hyphens" },
          { status: 400 }
        );
        return applyCorsHeaders(response, req);
      }

      // Connect to database
      await dbConnect();

      // Check if company code is already in use by another company
      const existingCompany = await Company.findOne({ 
        companyCode,
        _id: { $ne: companyId } // Exclude current company
      });
      
      if (existingCompany) {
        const response = NextResponse.json(
          { message: "Company code is already in use" },
          { status: 409 }
        );
        return applyCorsHeaders(response, req);
      }

      // Find company by ID
      const company = await Company.findById(companyId);
      
      if (!company) {
        const response = NextResponse.json(
          { message: "Company not found" },
          { status: 404 }
        );
        return applyCorsHeaders(response, req);
      }

      // Update company with POS credentials
      company.companyCode = companyCode;
      
      // Only update password if provided
      if (posPassword) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(posPassword, salt);
        company.posPassword = hashedPassword;
      }
      
      await company.save();

      // Prepare success response
      const response = NextResponse.json(
        { 
          message: "POS credentials set up successfully",
          data: {
            companyId: company._id,
            companyName: company.name,
            companyCode: company.companyCode
          }
        },
        { status: 200 }
      );
      
      // Apply CORS headers
      return applyCorsHeaders(response, req);
    } catch (error: unknown) {
      console.error("Error in POS setup API:", error);
      const response = NextResponse.json(
        { message: "Internal server error" },
        { status: 500 }
      );
      return applyCorsHeaders(response, req);
    }
  },
  ["admin", "superuser", "owner"] // Allowed roles
);
