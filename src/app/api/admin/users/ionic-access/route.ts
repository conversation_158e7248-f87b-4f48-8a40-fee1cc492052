import { NextRequest, NextResponse } from "next/server";
import { withAuth } from "@/lib/auth-helpers";
import User from "@/models/User";
import dbConnect from "@/lib/db";

/**
 * API endpoint to toggle IonicPOS access for users
 * Accessible to admins and owners only
 */
import { requireAuth } from "@/lib/auth-helpers";

async function handler(req: NextRequest) {
  try {
    await dbConnect();

    // Authenticate and authorize user
    const auth = await requireAuth(["admin", "owner"])(req);
    if (!auth) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 });
    }

    // Extract request data
    const { userId, canUseIonicApp } = await req.json();
    if (!userId || typeof canUseIonicApp !== "boolean") {
      return NextResponse.json(
        { success: false, message: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Find and update the user, enforce tenant isolation
    const user = await User.findOneAndUpdate(
      { _id: userId, companyId: auth.tenantId },
      { $set: { canUseIonicApp, modifiedFields: ["canUseIonicApp"] } },
      { new: true }
    ).select("-passwordHash -pin");

    if (!user) {
      return NextResponse.json(
        { success: false, message: "User not found or does not belong to company" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        success: true, 
        message: `User IonicPOS access ${canUseIonicApp ? 'granted' : 'revoked'}`,
        user
      },
      { status: 200 }
    );
  } catch (error: unknown) {
    console.error("Error updating IonicPOS access:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Only allow admins and owners to manage IonicPOS access
export const POST = handler;
