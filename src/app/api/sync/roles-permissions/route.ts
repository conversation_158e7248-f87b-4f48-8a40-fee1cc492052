import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import { withAuth } from '@/lib/auth-helpers';
import { serializeRolesAndPermissions, getCompanyUsersWithPermissions } from '@/lib/syncUtils';

/**
 * Endpoint for Ionic app to sync roles and permissions data
 */
export const GET = withAuth(
  async (request: NextRequest, user: any) => {
    try {
      const companyId = request.headers.get('company-id');
      if (!companyId) {
        return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
      }

      await dbConnect();

      // Get serialized roles and permissions data
      const rolesAndPermissions = await serializeRolesAndPermissions(companyId);
      
      // Get all users with their permissions
      const users = await getCompanyUsersWithPermissions(companyId);

      // Return complete sync data
      return NextResponse.json({
        ...rolesAndPermissions,
        users,
        syncTimestamp: new Date().toISOString()
      });
    } catch (error: unknown) {
      console.error('[GET Sync Roles Permissions] Error:', error);
      return NextResponse.json({ error: 'Failed to sync roles and permissions' }, { status: 500 });
    }
  },
  { allowedRoles: ['owner','admin','partner'], requiredPermissions: ['roles.read','permissions.read','users.read'] }
);

/**
 * Handle sync status updates from the Ionic app
 */
export const POST = withAuth(
  async (request: NextRequest, user: any) => {
    try {
      const companyId = request.headers.get('company-id');
      if (!companyId) {
        return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
      }

      const { lastSyncTimestamp, deviceId } = await request.json();
      
      if (!lastSyncTimestamp || !deviceId) {
        return NextResponse.json({ error: 'Last sync timestamp and device ID are required' }, { status: 400 });
      }

      // Here you could log sync events or handle device-specific data
      console.log(`Device ${deviceId} synced roles/permissions at ${lastSyncTimestamp}`);

      return NextResponse.json({
        status: 'success',
        message: 'Sync status updated successfully',
        serverTime: new Date().toISOString()
      });
    } catch (error: unknown) {
      console.error('[POST Sync Roles Permissions] Error:', error);
      return NextResponse.json({ error: 'Failed to update sync status' }, { status: 500 });
    }
  },
  { allowedRoles: ['owner','admin','partner'], requiredPermissions: ['roles.sync','permissions.sync','users.sync'] }
);
