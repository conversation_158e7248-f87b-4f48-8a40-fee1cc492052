// src/app/api/pos/company/authenticate/route.ts
import { NextRequest, NextResponse } from "next/server";
import bcrypt from 'bcryptjs';
import dbConnect from "@/lib/db";
import Company from "@/models/Company";
import { applyCorsHeaders, handleCorsOptions } from "@/middleware/cors-middleware";
import { rateLimiter } from "@/lib/rate-limiter";
import { generateCompanyToken } from "@/lib/authUtils";

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

/**
 * Authenticate company using company code and password
 * This endpoint is specifically for the IonicPOS app
 */
export async function POST(req: NextRequest) {
  try {
    // Check API key
    const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
    if (apiKey !== "test_api_key_1") {
      console.error("Invalid API key");
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid API key",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Apply rate limiting (10 requests per minute per IP)
    const ip = req.headers.get("x-forwarded-for") || "127.0.0.1";
    const rateLimitResult = await rateLimiter(ip, "pos-company-auth", 10, 60);
    
    if (!rateLimitResult.success) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Too many requests, please try again later",
            timestamp: new Date().toISOString()
          },
          { status: 429 }
        ),
        req
      );
    }

    // Parse request body
    const body = await req.json();
    const { companyCode, password } = body;

    // Validate request parameters
    if (!companyCode || !password) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Company code and password are required",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Connect to database
    await dbConnect();

    // Find company by code
    const company = await Company.findOne({ companyCode });
    
    if (!company) {
      // Use vague error message to prevent enumeration attacks
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid company code or password",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Check if posPassword exists
    if (!company.posPassword) {
      console.error("Company does not have POS password set up", { companyId: company._id });
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Company not configured for POS access",
            timestamp: new Date().toISOString()
          },
          { status: 403 }
        ),
        req
      );
    }

    // Compare password
    const isPasswordValid = await bcrypt.compare(password, company.posPassword);
    
    if (!isPasswordValid) {
      // Log authentication failure but return vague error message
      console.error("Invalid password attempt for company", { companyId: company._id });
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid company code or password",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Generate company token with limited scope and short lifespan
    const companyToken = generateCompanyToken(
      company._id.toString(),
      company.name
    );

    // Authentication successful
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: true,
          data: {
            companyId: company._id.toString(),
            companyName: company.name
          },
          companyToken: companyToken, // Include the company token
          timestamp: new Date().toISOString()
        },
        { status: 200 }
      ),
      req
    );
  } catch (error: unknown) {
    console.error("Error in company authentication API:", error);
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: false,
          message: "Internal server error",
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      ),
      req
    );
  }
}
