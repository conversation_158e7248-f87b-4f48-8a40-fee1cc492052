// src/app/api/pos/company/[companyId]/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { applyCorsHeaders, handleCorsOptions } from '@/middleware/cors-middleware';
import { verifyCompanyToken } from '@/lib/authUtils';
import mongoose from 'mongoose';

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

/**
 * Get users for a specific company and location
 * This endpoint is specifically for the IonicPOS app using company token
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  try {
    // Check API key
    const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
    if (apiKey !== "test_api_key_1") {
      console.error("Invalid API key");
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid API key",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Authenticate using company token
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Authentication required",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    const token = authHeader.substring(7);
    const decodedToken = await verifyCompanyToken(token);

    if (!decodedToken) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid or expired token",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Validate company ID
    const params = await context.params;
    const urlCompanyId = params.companyId;
    const tokenCompanyId = decodedToken.companyId;

    if (urlCompanyId !== tokenCompanyId) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Company ID mismatch",
            timestamp: new Date().toISOString()
          },
          { status: 403 }
        ),
        req
      );
    }

    await dbConnect();

    // Get location ID from query parameters
    const { searchParams } = new URL(req.url);
    const locationId = searchParams.get('locationId');

    // Build query
    const query: any = {
      companyId: new mongoose.Types.ObjectId(urlCompanyId),
      canUseIonicApp: true // Filter for users who can use the Ionic app
    };

    // Filter by location if provided
    if (locationId && mongoose.Types.ObjectId.isValid(locationId)) {
      query.locationIds = new mongoose.Types.ObjectId(locationId);
    }
    
    console.log('User query:', JSON.stringify(query));

    // Fetch users
    const users = await User.find(query)
      .select('_id email displayName role locationIds pin posSettings canUseIonicApp')
      .lean();
      
    console.log(`Found ${users.length} users matching the query`);

    // Transform data for POS app
    const transformedUsers = users.map(user => ({
      id: user._id ? user._id.toString() : user.id?.toString(),
      name: user.displayName || user.email.split('@')[0], // Use displayName instead of name
      email: user.email,
      role: user.role,
      hasPin: !!user.pin,
      locationIds: Array.isArray(user.locationIds) ? user.locationIds.map((id: any) => id.toString()) : [], // Array of location IDs
      posSettings: user.posSettings || {}
    }));
    
    console.log(`Returning ${transformedUsers.length} transformed users`);

    return applyCorsHeaders(
      NextResponse.json({
        success: true,
        data: transformedUsers,
        timestamp: new Date().toISOString()
      }),
      req
    );
  } catch (error: unknown) {
    console.error('Error in POS users endpoint:', error);
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: false,
          message: "Internal server error",
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      ),
      req
    );
  }
}
