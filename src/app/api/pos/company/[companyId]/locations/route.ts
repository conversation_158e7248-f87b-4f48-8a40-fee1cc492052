// src/app/api/pos/company/[companyId]/locations/route.ts
import { NextRequest, NextResponse } from "next/server";
import { withAuth } from "@/lib/auth-helpers";
import dbConnect from "@/lib/db";
import Location from "@/models/Location";
import { applyCorsHeaders, handleCorsOptions } from "@/middleware/cors-middleware";

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

// GET locations for a company
export const GET = withAuth(
  async (
    req: NextRequest,
    user: any,
    context: { params: Promise<{ companyId: string }> }
  ) => {
    try {
      await dbConnect();
      
      // Await params to fix Next.js warning
      const params = await context.params;
      const companyId = params.companyId;
      
      // Check for API key authentication
      const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
      const headerCompanyId = 
        req.headers.get("X-Company-Id") || 
        req.headers.get("x-company-id") || 
        req.headers.get("company-id");
      
      // Validate company ID from headers matches URL parameter
      if (!headerCompanyId || headerCompanyId !== companyId) {
        console.error("Company ID mismatch:", { headerCompanyId, urlCompanyId: companyId });
        return applyCorsHeaders(
          NextResponse.json(
            { 
              success: false, 
              message: "Company ID mismatch", 
              timestamp: new Date().toISOString() 
            }, 
            { status: 403 }
          ),
          req
        );
      }
      
      // Check for company token authentication
      const isCompanyToken = user.isCompanyToken && user.companyId === companyId;
      
      // API key validation (for production, this should check against stored keys)
      if (apiKey === "test_api_key_1" || user.userType === "superuser" || user.companyId?.toString() === companyId || isCompanyToken) {
        // Fetch locations
        const locations = await Location.find({ companyId }).sort({ name: 1 });
        
        // Transform data to match expected POS format
        const formattedLocations = locations.map(location => ({
          id: location._id.toString(),
          name: location.name,
          type: location.locationType,
          address: location.address || "",
          contactInfo: {
            phone: location.contactInfo?.phone || "",
            email: location.contactInfo?.email || ""
          }
        }));
        
        // Return formatted response
        return applyCorsHeaders(
          NextResponse.json(
            { 
              success: true, 
              data: formattedLocations,
              timestamp: new Date().toISOString() 
            }, 
            { status: 200 }
          ),
          req
        );
      } else {
        // Invalid API key
        console.error("Invalid API key or unauthorized access");
        return applyCorsHeaders(
          NextResponse.json(
            { 
              success: false, 
              message: "Unauthorized access", 
              timestamp: new Date().toISOString() 
            }, 
            { status: 401 }
          ),
          req
        );
      }
    } catch (error: unknown) {
      console.error("Error in POS locations API:", error);
      return applyCorsHeaders(
        NextResponse.json(
          { 
            success: false, 
            message: "Internal server error", 
            timestamp: new Date().toISOString() 
          }, 
          { status: 500 }
        ),
        req
      );
    }
  },
  { allowCompanyToken: true }
);
