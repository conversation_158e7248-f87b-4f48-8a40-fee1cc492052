import { NextRequest, NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { validateIonicAuth } from '@/lib/ionic-auth'; // Assuming this helper exists and works for POS
import { validateLocationAccess } from '@/lib/location-validation'; // Assuming this helper exists
import { applyCorsHeaders, handleCorsOptions } from '@/lib/cors';
import BranchInventory from '@/models/BranchInventory';
import { MenuItem, IMenuItem } from '@/models/MenuItem'; // Import MenuItem model AND IMenuItem interface

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

// Interface for the expected request body
interface OrderPayloadItem {
  menuItemId: string;
  quantity: number;
  // Add other relevant fields if needed, e.g., timestamp, orderId
}

interface OrderPayload {
  items: OrderPayloadItem[];
  // Add other top-level fields if needed, e.g., totalAmount, paymentMethod
}

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ companyId: string; locationId: string }> }
) {
  let response: NextResponse;
  try {
    await dbConnect();

    const { companyId, locationId } = await context.params;
    const headerCompanyId = req.headers.get('company-id');

    // --- Authentication & Authorization --- 
    const authResult = await validateIonicAuth(req);
    if (!authResult.isAuthenticated) {
      response = NextResponse.json({ success: false, message: authResult.error }, { status: 401 });
      return applyCorsHeaders(response, req);
    }

    // ---> ADDED CHECK: Validate company ID match <--- 
    if (headerCompanyId !== companyId) {
        response = NextResponse.json({ success: false, message: 'Unauthorized: Company ID mismatch' }, { status: 401 });
        return applyCorsHeaders(response, req);
    }
    // ---> END ADDED CHECK <--- 

    // Validate company and location access
    try {
      await validateLocationAccess(companyId, locationId, headerCompanyId);
    } catch (error: unknown) {
      response = NextResponse.json({ success: false, message: (error as Error).message }, { status: 403 });
      return applyCorsHeaders(response, req);
    }

    // --- Parse and Validate Body --- 
    const payload: OrderPayload = await req.json();

    if (!payload || !Array.isArray(payload.items) || payload.items.length === 0) {
      response = NextResponse.json({ success: false, message: 'Invalid payload. Expected { items: [...] } with at least one item.' }, { status: 400 });
      return applyCorsHeaders(response, req);
    }

    // --- Core Logic - Process Order Items and Update Budgets --- 
    const inventoryUpdates: Map<string, number> = new Map(); // Map<BranchInventoryId, IncrementValue>
    const errors: string[] = [];

    for (const orderItem of payload.items) {
      if (!Types.ObjectId.isValid(orderItem.menuItemId) || orderItem.quantity <= 0) {
        errors.push(`Invalid data for menu item: ${orderItem.menuItemId}, quantity: ${orderItem.quantity}`);
        continue;
      }

      // Fetch the menu item as a plain object and assert its type
      const menuItem = await MenuItem.findById(orderItem.menuItemId).lean() as IMenuItem | null;

      if (!menuItem) {
        errors.push(`Menu item not found: ${orderItem.menuItemId}`);
        continue;
      }

      if (menuItem.companyId.toString() !== companyId) {
        errors.push(`Menu item ${orderItem.menuItemId} does not belong to company ${companyId}`);
        continue;
      }

      if (menuItem.type === 'single') {
        if (menuItem.inventoryItem?.itemId) {
          const invItemIdStr = menuItem.inventoryItem.itemId.toString();
          const currentInc = inventoryUpdates.get(invItemIdStr) || 0;
          // Assuming 1 menu item consumes 1 unit of the inventory item
          inventoryUpdates.set(invItemIdStr, currentInc + orderItem.quantity);
        } else {
          errors.push(`Menu item ${menuItem.name} (${menuItem._id}) is type 'single' but not linked to inventory.`);
        }
      } else if (menuItem.type === 'recipe') {
        if (menuItem.recipeComponents && menuItem.recipeComponents.length > 0) {
          for (const component of menuItem.recipeComponents) {
            const invItemIdStr = component.itemId.toString();
            const currentInc = inventoryUpdates.get(invItemIdStr) || 0;
            const increment = orderItem.quantity * component.quantity;
            inventoryUpdates.set(invItemIdStr, currentInc + increment);
          }
        } else {
          errors.push(`Menu item ${menuItem.name} (${menuItem._id}) is type 'recipe' but has no recipe components defined.`);
        }
      } else {
        errors.push(`Menu item ${menuItem.name} (${menuItem._id}) has an invalid type: ${menuItem.type}`);
      }
    }

    // --- Perform Bulk Update --- 
    if (inventoryUpdates.size > 0 && errors.length === 0) {
      const bulkOperations = Array.from(inventoryUpdates.entries()).map(([itemId, incrementValue]) => ({
        updateOne: {
          filter: { _id: new Types.ObjectId(itemId), companyId: new Types.ObjectId(companyId), locationId: new Types.ObjectId(locationId) },
          update: { $inc: { orderBudgetUsed: incrementValue } }
        }
      }));

      try {
        const bulkResult = await BranchInventory.bulkWrite(bulkOperations);
        // TODO: Optionally check bulkResult for errors or specific counts
      } catch (dbError: unknown) {
        throw dbError;
      }
    } else if (inventoryUpdates.size === 0 && errors.length === 0) {
      // No inventory items were linked to the ordered menu items
      // Consider if this should be an error or just a notification
    }

    // --- Response --- 
    if (errors.length > 0) {
      response = NextResponse.json({
        success: false,
        message: `Order processed with errors: ${errors.join(', ')}`,
        timestamp: new Date().toISOString(),
        errors: errors
      }, { status: 400 }); // Or 207 Multi-Status if partial success is meaningful
    } else {
      response = NextResponse.json({
          success: true,
          message: 'Order processed successfully and budget usage updated.',
          timestamp: new Date().toISOString(),
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    response = NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }

  return applyCorsHeaders(response, req);
}
