import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Discount, { IDiscount } from '@/models/Discount';
import { applyCorsHeaders, handleCorsOptions } from '@/lib/cors';
import mongoose from 'mongoose';

// Define a type for the lean discount object (plain JS object)
type LeanDiscount = Omit<IDiscount, keyof mongoose.Document> & {
  _id: mongoose.Types.ObjectId; // Ensure _id is ObjectId before conversion
  companyId: mongoose.Types.ObjectId;
  locationId?: mongoose.Types.ObjectId;
  applicableItems?: mongoose.Types.ObjectId[];
  applicableCategories?: mongoose.Types.ObjectId[];
  createdAt: Date; // lean() preserves Date objects
  updatedAt: Date;
  comboRequirements?: {
    requiredItems: Array<{ itemId: mongoose.Types.ObjectId; quantity: number }>;
    discountedItems: Array<{ itemId: mongoose.Types.ObjectId; discountValue: number; discountType: 'percentage' | 'fixed' }>;
  }
};

// Define types for combo requirement items
type LeanComboRequiredItem = { itemId: mongoose.Types.ObjectId; quantity: number };
type LeanComboDiscountedItem = { itemId: mongoose.Types.ObjectId; discountValue: number; discountType: 'percentage' | 'fixed' };

// Handle CORS preflight requests
export async function OPTIONS(req: NextRequest) {
  return handleCorsOptions(req);
}

export async function GET(req: NextRequest, context: { params: { companyId: string } }) {
  let response: NextResponse;
  try {
    // --- Authentication & Authorization --- 
    const apiKey = req.headers.get('x-api-key');
    const headerCompanyId = req.headers.get('company-id') || req.headers.get('x-company-id');
    const { companyId } = context.params;

    // 1. API Key Check (Basic check as per memory)
    if (apiKey !== 'test_api_key_1') {
      response = NextResponse.json({ success: false, message: 'Unauthorized: Invalid API Key' }, { status: 401 });
      return applyCorsHeaders(response, req);
    }

    // 2. Company ID header check
    if (!headerCompanyId) {
        response = NextResponse.json({ success: false, message: 'Missing company-id header' }, { status: 400 });
        return applyCorsHeaders(response, req);
    }
    if (headerCompanyId !== companyId) {
        response = NextResponse.json({ success: false, message: 'Company ID mismatch between header and URL' }, { status: 400 });
        return applyCorsHeaders(response, req);
    }
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
        response = NextResponse.json({ success: false, message: 'Invalid Company ID format' }, { status: 400 });
        return applyCorsHeaders(response, req);
    }

    await dbConnect();

    // --- Query Parameters --- 
    const { searchParams } = new URL(req.url);
    const locationId = searchParams.get('locationId');
    const lastSyncTime = searchParams.get('lastSyncTime');

    // --- Build Query --- 
    const query: any = {
        companyId: new mongoose.Types.ObjectId(companyId),
        isActive: true,
        // Include discounts that are either global (no locationId) or match the specific locationId
        $or: [
            { locationId: { $exists: false } },
            { locationId: null }
        ]
    };

    if (locationId && mongoose.Types.ObjectId.isValid(locationId)) {
        query.$or.push({ locationId: new mongoose.Types.ObjectId(locationId) });
    } else if (locationId) {
        // Handle invalid locationId format if provided
        response = NextResponse.json({ success: false, message: 'Invalid Location ID format' }, { status: 400 });
        return applyCorsHeaders(response, req);
    }

    // Time-bound check (optional)
    const now = new Date();
    query.$and = [
        { $or: [{ startDate: { $exists: false } }, { startDate: null }, { startDate: { $lte: now } }] },
        { $or: [{ endDate: { $exists: false } }, { endDate: null }, { endDate: { $gte: now } }] }
    ];

    // Sync check
    if (lastSyncTime) {
        const syncDate = new Date(lastSyncTime);
        if (!isNaN(syncDate.getTime())) {
            query.updatedAt = { $gt: syncDate };
        } else {
             console.warn('Invalid lastSyncTime format received:', lastSyncTime);
             // Decide if you want to error out or just ignore the invalid timestamp
             // response = NextResponse.json({ success: false, message: 'Invalid lastSyncTime format' }, { status: 400 });
             // return applyCorsHeaders(response, req);
        }
    }

    // --- Execute Query --- 
    const discounts = await Discount.find(query).lean();

    // --- Format Response --- 
    // Convert ObjectIds to strings if needed by the POS app
    const formattedDiscounts = discounts.map((d: any) => {
      // Basic assertion for required fields (optional, but good practice with any)
      if (!d._id || !d.companyId) {
        console.error('Lean object missing expected properties:', d);
        return null; // or throw an error, or return a default structure
      }

      return {
          ...d, // Spread the lean object
          _id: d._id.toString(),
          companyId: d.companyId.toString(),
          locationId: d.locationId?.toString(),
          applicableItems: d.applicableItems?.map((id: mongoose.Types.ObjectId) => id.toString()),
          applicableCategories: d.applicableCategories?.map((id: mongoose.Types.ObjectId) => id.toString()),
          // Ensure dates are ISO strings if needed
          startDate: d.startDate instanceof Date ? d.startDate.toISOString() : d.startDate, // Check if it's a Date object
          endDate: d.endDate instanceof Date ? d.endDate.toISOString() : d.endDate,
          createdAt: d.createdAt instanceof Date ? d.createdAt.toISOString() : d.createdAt,
          updatedAt: d.updatedAt instanceof Date ? d.updatedAt.toISOString() : d.updatedAt,
          // Format combo requirements if they exist
          comboRequirements: d.comboRequirements ? {
              requiredItems: d.comboRequirements.requiredItems.map((item: LeanComboRequiredItem) => ({
                  ...item,
                  itemId: item.itemId.toString()
              })),
              discountedItems: d.comboRequirements.discountedItems.map((item: LeanComboDiscountedItem) => ({
                  ...item,
                  itemId: item.itemId.toString()
              }))
          } : undefined
      };
    }).filter(d => d !== null); // Filter out any nulls introduced by errors/checks

    response = NextResponse.json({
        success: true,
        data: formattedDiscounts,
        timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching discounts:', error);
    response = NextResponse.json({ success: false, message: 'Internal Server Error' }, { status: 500 });
  }

  return applyCorsHeaders(response, req);
}
