import { NextRequest, NextResponse } from "next/server";
import { hashPin } from "@/lib/auth-helpers";
import User from "@/models/User";
import dbConnect from "@/lib/db";
import { applyCorsHeaders } from "@/middleware/cors-middleware";

export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await dbConnect();

    // Validate API key (same as company authentication)
    const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
    if (apiKey !== "test_api_key_1") {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid API key",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Parse request body
    const body = await req.json();
    const { userId, pin, companyId } = body;

    // Validate input
    if (!userId || !pin || !companyId) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User ID, Company ID, and PIN are required",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Find the user and verify they belong to the company and can use IonicPOS
    const user = await User.findOne({ 
      _id: userId, 
      companyId: companyId,
      canUseIonicApp: true
    });

    if (!user) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User not found or does not belong to the company",
            timestamp: new Date().toISOString()
          },
          { status: 404 }
        ),
        req
      );
    }

    // Check if PIN is already set
    if (user.pin) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "PIN is already set. Use reset PIN instead.",
            timestamp: new Date().toISOString()
          },
          { status: 403 }
        ),
        req
      );
    }

    // Validate PIN complexity (optional, but recommended)
    if (pin.length < 4 || pin.length > 6) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "PIN must be between 4 and 6 digits",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Hash the PIN
    const hashedPin = await hashPin(pin);

    // Update user with hashed PIN
    user.pin = hashedPin;
    await user.save();

    // Return success response
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: true,
          message: "PIN set successfully",
          timestamp: new Date().toISOString()
        },
        { status: 200 }
      ),
      req
    );

  } catch (error: unknown) {
    console.error("Error setting PIN:", error);
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: false,
          message: "Internal server error",
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      ),
      req
    );
  }
}
