import { NextRequest, NextResponse } from "next/server";
import { hashPin, validateUserPin } from "@/lib/auth-helpers";
import User from "@/models/User";
import dbConnect from "@/lib/db";
import { applyCorsHeaders } from "@/middleware/cors-middleware";

export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await dbConnect();

    // Validate API key (same as company authentication)
    const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
    if (apiKey !== "test_api_key_1") {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid API key",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Parse request body
    const body = await req.json();
    const { userId, companyId, oldPin, newPin } = body;

    // Validate input
    if (!userId || !companyId || !oldPin || !newPin) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User ID, Company ID, Old PIN, and New PIN are required",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Find the user and verify they belong to the company and can use IonicPOS
    const user = await User.findOne({ 
      _id: userId, 
      companyId: companyId,
      canUseIonicApp: true
    });

    if (!user) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User not found or does not belong to the company",
            timestamp: new Date().toISOString()
          },
          { status: 404 }
        ),
        req
      );
    }

    // Verify the old PIN
    if (!user.pin) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "No existing PIN. Use set-pin endpoint first.",
            timestamp: new Date().toISOString()
          },
          { status: 403 }
        ),
        req
      );
    }

    // Validate old PIN
    const isOldPinValid = await validateUserPin(userId, oldPin);
    if (!isOldPinValid) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Current PIN is incorrect",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Validate new PIN complexity
    if (newPin.length < 4 || newPin.length > 6) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "New PIN must be between 4 and 6 digits",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Prevent reusing the same PIN
    const isNewPinSameAsOld = await validateUserPin(userId, newPin);
    if (isNewPinSameAsOld) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "New PIN cannot be the same as the current PIN",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update user with new hashed PIN
    user.pin = hashedNewPin;
    await user.save();

    // Return success response
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: true,
          message: "PIN reset successfully",
          timestamp: new Date().toISOString()
        },
        { status: 200 }
      ),
      req
    );

  } catch (error: unknown) {
    console.error("Error resetting PIN:", error);
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: false,
          message: "Internal server error",
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      ),
      req
    );
  }
}
