import { NextRequest, NextResponse } from "next/server";
import { validateUserPin } from "@/lib/auth-helpers";
import User from "@/models/User";
import dbConnect from "@/lib/db";
import { applyCorsHeaders } from "@/middleware/cors-middleware";

export async function POST(req: NextRequest) {
  try {
    // Connect to database
    await dbConnect();

    // Validate API key (same as company authentication)
    const apiKey = req.headers.get("X-API-Key") || req.headers.get("x-api-key");
    if (apiKey !== "test_api_key_1") {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "Invalid API key",
            timestamp: new Date().toISOString()
          },
          { status: 401 }
        ),
        req
      );
    }

    // Parse request body
    const body = await req.json();
    const { userId, pin, companyId } = body;

    // Validate input
    if (!userId || !pin || !companyId) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User ID, Company ID, and PIN are required",
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        ),
        req
      );
    }

    // Find the user and verify they belong to the company and can use IonicPOS
    const user = await User.findOne({ 
      _id: userId, 
      companyId: companyId,
      canUseIonicApp: true
    });

    if (!user) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User not found or does not have access to Ionic POS",
            timestamp: new Date().toISOString()
          },
          { status: 404 }
        ),
        req
      );
    }

    // Check if user has a PIN set
    if (!user.pin) {
      return applyCorsHeaders(
        NextResponse.json(
          {
            success: false,
            message: "User has not set a PIN yet",
            timestamp: new Date().toISOString()
          },
          { status: 403 }
        ),
        req
      );
    }

    // Validate the PIN
    const isPinValid = await validateUserPin(userId, pin);
    
    // Return PIN verification result
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: true,
          pinValid: isPinValid,
          message: isPinValid ? "PIN is valid" : "Invalid PIN",
          timestamp: new Date().toISOString()
        },
        { status: 200 }
      ),
      req
    );

  } catch (error: unknown) {
    console.error("Error in PIN verification:", error);
    return applyCorsHeaders(
      NextResponse.json(
        {
          success: false,
          message: "Internal server error",
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      ),
      req
    );
  }
}
