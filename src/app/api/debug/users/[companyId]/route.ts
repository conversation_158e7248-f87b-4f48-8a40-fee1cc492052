// src/app/api/debug/users/[companyId]/route.ts
// WARNING: This is a development-only endpoint and should NOT be used in production!
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import mongoose from 'mongoose';
import { applyCorsHeaders } from '@/middleware/cors-middleware';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ companyId: string }> }
) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Debug endpoints are not available in production' }, { status: 403 });
  }

  try {
    await dbConnect();
    
    const { companyId } = await context.params;
    
    // Validate the ObjectId format
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return NextResponse.json({ error: 'Invalid company ID format' }, { status: 400 });
    }

    // Get query parameters for optional filtering
    const { searchParams } = new URL(req.url);
    const includeDeleted = searchParams.has('includeDeleted');
    
    // Build the query
    const query: any = {
      companyId: new mongoose.Types.ObjectId(companyId),
      userType: 'company_user'
    };
    
    if (!includeDeleted) {
      query.isDeleted = { $ne: true };
    }

    console.log('[DEBUG] Fetching users with query:', JSON.stringify(query));
    
    // Execute the query
    const users = await User.find(query)
      .select('-passwordHash -pin')  // Exclude sensitive fields
      .lean();                       // Convert to plain JavaScript objects
    
    console.log(`[DEBUG] Found ${users.length} users`);
    
    return applyCorsHeaders(
      NextResponse.json({
        users,
        _debug: {
          query,
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV
        }
      }, { status: 200 }),
      req
    );
  } catch (error: unknown) {
    console.error('[DEBUG] Error fetching users:', error);
    return applyCorsHeaders(
      NextResponse.json(
        { 
          error: 'Internal server error', 
          details: (error as Error).message,
          stack: (error as Error).stack 
        },
        { status: 500 }
      ),
      req
    );
  }
}