// src/app/api/debug/auth/route.ts
// FOR DEVELOPMENT USE ONLY
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth-helpers';
import { applyCorsHeaders } from '@/middleware/cors-middleware';

export const GET = withAuth(
  async (req: NextRequest, user: any, context: { params: Promise<Record<string, never>> }) => {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Debug endpoints are not available in production' }, { status: 403 });
    }
    
    // Return detailed user info for debugging
    const userInfo = {
      id: user.id,
      userId: user.userId,
      email: user.email,
      userType: user.userType,
      role: user.role,
      companyId: user.companyId,
      isValid: !!user.id || !!user.userId,
      tokenType: user.tokenType || 'unknown',
      headers: Object.fromEntries(req.headers),
      cookies: req.cookies.getAll().map(c => ({ name: c.name, valuePreview: c.value.substring(0, 20) + '...' }))
    };
    
    return applyCorsHeaders(
      NextResponse.json({ 
        message: 'Debug auth info', 
        user: userInfo 
      }),
      req
    );
  },
  [] // Allow any authenticated user to access this debug endpoint
);
