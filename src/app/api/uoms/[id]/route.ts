import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { UOM } from '@/models/UOM';

// PUT /api/uoms/[id]
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const companyId = req.headers.get('company-id');
    const { id } = await params;
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    if (!id) {
      return NextResponse.json({ error: 'UOM ID is required' }, { status: 400 });
    }

    // Find the UOM first
    const existingUom = await UOM.findById(id);
    if (!existingUom) {
      return NextResponse.json({ error: 'UOM not found' }, { status: 404 });
    }

    // Prevent editing global UOMs
    if (!existingUom.companyId) {
      return NextResponse.json(
        { error: 'Cannot modify global UOMs' },
        { status: 403 }
      );
    }

    // Ensure the UOM belongs to the company
    if (existingUom.companyId.toString() !== companyId) {
      return NextResponse.json(
        { error: 'UOM does not belong to this company' },
        { status: 403 }
      );
    }

    const data = await req.json();

    // Check if name is being changed and if it already exists
    if (data.name && data.name !== existingUom.name) {
      const nameExists = await UOM.findOne({
        _id: { $ne: id }, // exclude current UOM
        $or: [
          { name: data.name, companyId },
          { name: data.name, companyId: null }
        ]
      });

      if (nameExists) {
        return NextResponse.json(
          { error: 'A UOM with this name already exists' },
          { status: 400 }
        );
      }
    }

    const updatedUom = await UOM.findByIdAndUpdate(
      id,
      { $set: { ...data, companyId } },
      { new: true, runValidators: true }
    );

    return NextResponse.json(updatedUom);
  } catch (error: any) {
    console.error('[PUT UOM] Error:', error);
    if (error.code === 11000) {
      // Check which field caused the duplicate key error
      const field = Object.keys(error.keyPattern)[1]; // [1] because [0] is companyId
      return NextResponse.json(
        { error: `A UOM with this ${field} already exists` },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update UOM' },
      { status: 500 }
    );
  }
}

// DELETE /api/uoms/[id]
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const companyId = req.headers.get('company-id');
    const { id } = await params;
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    if (!id) {
      return NextResponse.json({ error: 'UOM ID is required' }, { status: 400 });
    }

    // Find the UOM first
    const uom = await UOM.findById(id);
    if (!uom) {
      return NextResponse.json({ error: 'UOM not found' }, { status: 404 });
    }

    // Prevent deleting global UOMs
    if (!uom.companyId) {
      return NextResponse.json(
        { error: 'Cannot delete global UOMs' },
        { status: 403 }
      );
    }

    // Ensure the UOM belongs to the company
    if (uom.companyId.toString() !== companyId) {
      return NextResponse.json(
        { error: 'UOM does not belong to this company' },
        { status: 403 }
      );
    }

    await UOM.findByIdAndDelete(id);

    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error('[DELETE UOM] Error:', error);
    return NextResponse.json(
      { error: 'Failed to delete UOM' },
      { status: 500 }
    );
  }
}
