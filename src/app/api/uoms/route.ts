import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-helpers';
import dbConnect from '@/lib/db';
import { UOM } from '@/models/UOM';
import { Types } from 'mongoose';

// GET /api/uoms
export async function GET(req: NextRequest) {
  try {
    // Authentication
    const auth = await requireAuth()(req);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = auth.tenantId; // Use tenantId from auth

    await dbConnect();

    // Query for both global UOMs (companyId is null) and company-specific UOMs
    const query = { $or: [{ companyId: new Types.ObjectId(companyId) }, { companyId: null }] }; // Use ObjectId
    const uoms = await UOM.find(query).sort({ name: 1 });

    return NextResponse.json(uoms || []);
  } catch (error: unknown) {
    console.error('[GET UOMs] Error:', error);
    // Handle potential errors from requireAuth or dbConnect
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json(
      { error: 'Failed to fetch UOMs' },
      { status: 500 }
    );
  }
}

// POST /api/uoms
export async function POST(req: NextRequest) {
  try {
    // Authentication
    const auth = await requireAuth()(req);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = auth.tenantId; // Use tenantId from auth

    await dbConnect();

    const data = await req.json();

    // Check if a UOM with this name already exists for this company or globally
    const existingUom = await UOM.findOne({
      name: data.name, // Case-sensitive check
      $or: [
        { companyId: new Types.ObjectId(companyId) },
        { companyId: null }
      ]
    });

    if (existingUom) {
      const scope = existingUom.companyId ? 'in your company' : 'globally';
      return NextResponse.json(
        { error: `A UOM with the name '${data.name}' already exists ${scope}.` },
        { status: 409 } // Use 409 Conflict for existing resource
      );
    }

    // Create the UOM with the company ID
    const uom = await UOM.create({
      ...data,
      companyId: new Types.ObjectId(companyId), // Use ObjectId
    });

    return NextResponse.json(uom, { status: 201 });
  } catch (error: any) {
    console.error('[POST UOM] Error:', error);
    // Handle potential errors from requireAuth or dbConnect
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    if (error.code === 11000) {
      // More specific duplicate key error (e.g., for shortCode if unique index exists)
      const field = Object.keys(error.keyPattern).filter(k => k !== 'companyId')[0] || 'identifier'; 
      return NextResponse.json(
        { error: `A UOM with this ${field} already exists for this company.` },
        { status: 409 } // Use 409 Conflict
      );
    }
    return NextResponse.json(
      { error: 'Failed to create UOM' },
      { status: 500 }
    );
  }
}
