import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongoDb';
import User from '@/models/User';
import Role from '@/models/Role';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const companyId = request.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    await dbConnect();
    
    // Find the user and ensure they belong to the company
    const user = await User.findOne({ 
      _id: userId,
      companyId
    });
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Get all permissions assigned to the user
    let permissions = [...(user.permissions || [])];
    
    // If the user has a role, get additional permissions from that role
    if (user.role) {
      // Find the role in our roles collection
      const roleDocument = await Role.findOne({ 
        companyId, 
        name: user.role 
      });
      
      if (roleDocument) {
        // Add role permissions to user permissions (avoid duplicates)
        permissions = [...new Set([...permissions, ...roleDocument.permissions])];
      }
    }
    
    return NextResponse.json({ 
      userId: user._id,
      role: user.role,
      permissions
    });
  } catch (error: unknown) {
    console.error('[GET User Permissions] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch user permissions' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    const { permissions } = await request.json();
    const companyId = request.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }
    
    if (!Array.isArray(permissions)) {
      return NextResponse.json({ error: 'Permissions must be an array' }, { status: 400 });
    }

    await dbConnect();
    
    // Find the user and ensure they belong to the company
    const user = await User.findOne({ 
      _id: userId,
      companyId
    });
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Update the user's permissions
    user.permissions = permissions;
    await user.save();
    
    return NextResponse.json({ 
      userId: user._id,
      role: user.role,
      permissions: user.permissions
    });
  } catch (error: unknown) {
    console.error('[PUT User Permissions] Error:', error);
    return NextResponse.json({ error: 'Failed to update user permissions' }, { status: 500 });
  }
}
