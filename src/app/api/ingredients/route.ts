import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Ingredient, IIngredient } from '@/models/Ingredient';
import { Types } from 'mongoose';
import { requireAuth } from '@/lib/auth-helpers';

// Define interface for bulk update payload item
interface IngredientBulkUpdatePayload extends Omit<Partial<IIngredient>, '_id' | 'companyId' | 'createdAt' | 'updatedAt'> {
  _id: string; // Expect _id as a string in the payload
}

// GET /api/ingredients
export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    const companyIdHeader = req.headers.get('company-id');
    if (!companyIdHeader) {
      return NextResponse.json({ error: 'Company ID header is required' }, { status: 400 });
    }
    // --- Authentication --- 
    const user = await requireAuth(req);
    if (companyIdHeader !== user.tenantId) {
      return NextResponse.json({ error: 'Unauthorized company ID' }, { status: 403 });
    }
    const companyId = user.tenantId; // Use validated companyId from auth
    // ---------------------
    
    // Support pagination
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';

    // Build query
    const query: any = { companyId: new Types.ObjectId(companyId) };
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { SKU: { $regex: search, $options: 'i' } },
      ];
    }
    if (category) {
      query.category = category;
    }

    // Get total count for pagination
    const total = await Ingredient.countDocuments(query);

    // Get paginated results with populated references
    const ingredients = await Ingredient.find(query)
      .populate('baseUomId', 'name shortCode')
      .populate('supplierDetails.supplierId', 'name')
      .populate('supplierDetails.unitsOfOrdering.unitOfMeasure', 'name shortCode')
      .sort({ name: 1 })
      .skip((page - 1) * limit)
      .limit(limit);

    // Get unique categories for filtering
    const categories = await Ingredient.distinct('category', { companyId: new Types.ObjectId(companyId) });

    return NextResponse.json({
      ingredients,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit
      },
      categories
    });
  } catch (error: unknown) {
    console.error('Error fetching ingredients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ingredients' },
      { status: 500 }
    );
  }
}

// POST /api/ingredients
export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    const companyIdHeader = req.headers.get('company-id');
    if (!companyIdHeader) {
      return NextResponse.json({ error: 'Company ID header is required' }, { status: 400 });
    }
    // --- Authentication --- 
    const user = await requireAuth(req);
    if (companyIdHeader !== user.tenantId) {
      return NextResponse.json({ error: 'Unauthorized company ID' }, { status: 403 });
    }
    const companyId = user.tenantId; // Use validated companyId from auth
    // ---------------------
    
    const data: Partial<IIngredient> = await req.json(); // Type the payload

    // Validate baseUomId if present
    if (data.baseUomId && !Types.ObjectId.isValid(data.baseUomId)) {
      return NextResponse.json({ error: 'Invalid UOM ID' }, { status: 400 });
    }

    // Validate suppliers array if provided
    if (data.supplierDetails) {
      if (!Array.isArray(data.supplierDetails)) {
        return NextResponse.json({ error: 'Supplier details must be an array' }, { status: 400 });
      }
      
      // Validate each supplier detail
      for (const supplier of data.supplierDetails) {
        if (!Types.ObjectId.isValid(supplier.supplierId)) {
          return NextResponse.json({ error: 'Invalid supplier ID' }, { status: 400 });
        }
        
        if (!Array.isArray(supplier.unitsOfOrdering)) {
          return NextResponse.json({ error: 'Supplier units of ordering must be an array' }, { status: 400 });
        }
        
        // Validate each unit
        for (const unit of supplier.unitsOfOrdering) {
          if (!unit.unitOfMeasure || typeof unit.unitOfMeasure !== 'string') {
            return NextResponse.json({ error: 'Unit of measure is required and must be a string' }, { status: 400 });
          }
          if (!unit.quantityInBaseUom || typeof unit.quantityInBaseUom !== 'number' || unit.quantityInBaseUom <= 0) {
            return NextResponse.json({ error: 'Quantity in base UOM must be a positive number' }, { status: 400 });
          }
          if (!unit.price || typeof unit.price !== 'number' || unit.price < 0) {
            return NextResponse.json({ error: 'Price must be a non-negative number' }, { status: 400 });
          }
          if (!unit.pricePerBaseUom || typeof unit.pricePerBaseUom !== 'number' || unit.pricePerBaseUom < 0) {
            return NextResponse.json({ error: 'Price per base UOM must be a non-negative number' }, { status: 400 });
          }
        }
      }
    }

    const ingredient = await Ingredient.create({
      ...data,
      companyId: new Types.ObjectId(companyId),
    });

    // Populate references for the response
    await ingredient.populate([
      { path: 'baseUomId', select: 'name shortCode' },
      { path: 'supplierDetails.supplierId', select: 'name' },
      { path: 'supplierDetails.unitsOfOrdering.unitOfMeasure', select: 'name shortCode' },
    ]);

    return NextResponse.json(ingredient, { status: 201 });
  } catch (error: any) {
    console.error('[POST Ingredient] Error:', error);
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[1]; // [1] because [0] is companyId
      return NextResponse.json(
        { error: `An ingredient with this ${field} already exists` },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create ingredient' },
      { status: 500 }
    );
  }
}

// PUT /api/ingredients/bulk
export async function PUT(req: NextRequest) {
  try {
    await dbConnect();
    // --- Authentication & Tenant ID --- 
    const auth = await requireAuth()(req);
    if (!auth) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const companyId = auth.tenantId; // Use validated companyId from auth
    // ------------------------------

    // Use the new interface for payload typing
    const { ingredients }: { ingredients: IngredientBulkUpdatePayload[] } = await req.json(); 

    if (!Array.isArray(ingredients)) {
      return NextResponse.json({ error: 'Ingredients must be an array' }, { status: 400 });
    }

    // Validate ownership and update each ingredient
    const updates = await Promise.all(
      ingredients.map(async (ing) => {
        // --- Validate _id within the map --- 
        if (!ing._id || !Types.ObjectId.isValid(ing._id)) { // Validate the string _id
          return {
            _id: ing._id || 'INVALID_OR_MISSING',
            error: 'Invalid or missing ingredient ID',
          };
        }
        const ingredientId = new Types.ObjectId(ing._id); // Create ObjectId
        // ------------------------------------

        const existing = await Ingredient.findOne({ // Use ObjectId
          _id: ingredientId, 
          companyId: new Types.ObjectId(companyId),
        });

        if (!existing) {
          return {
            _id: ing._id, // Return original string ID in error
            error: 'Ingredient not found or does not belong to company',
          };
        }

        try {
          // Create updateData, ensuring companyId is correct and string _id is removed
          const updateData = { ...ing, companyId: new Types.ObjectId(companyId) };
          delete (updateData as any)._id; 

          const updated = await Ingredient.findByIdAndUpdate( // Use ObjectId
            ingredientId, 
            { $set: updateData }, // Use updateData 
            { new: true, runValidators: true }
          ).populate([
            { path: 'baseUomId', select: 'name shortCode' },
            { path: 'supplierDetails.supplierId', select: 'name' },
            { path: 'supplierDetails.unitsOfOrdering.unitOfMeasure', select: 'name shortCode' },
          ]);

          return {
            _id: ing._id, // Return original string ID on success
            success: true,
            ingredient: updated,
          };
        } catch (error: any) {
          return {
            _id: ing._id, // Return original string ID on error
            error: error.code === 11000 ? 'Duplicate entry' : 'Update failed',
          };
        }
      })
    );

    const hasErrors = updates.some(update => 'error' in update);
    return NextResponse.json(
      { results: updates },
      { status: hasErrors ? 400 : 200 }
    );
  } catch (error: unknown) {
    console.error('[PUT Bulk Ingredients] Error:', error);
    // Handle potential errors from requireAuth or dbConnect
    if ((error as any)?.message?.includes('Unauthorized') || (error as any)?.message?.includes('Forbidden')) {
      return NextResponse.json({ error: (error as Error).message }, { status: 401 });
    }
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update ingredients' },
      { status: 500 }
    );
  }
}
