// src/app/api/ingredients/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Ingredient } from '@/models/Ingredient';
import mongoose, { Types } from 'mongoose';
// Import auth utilities from auth helpers
import { requireAuth } from '@/lib/auth-helpers';

// GET /api/ingredients/[id]
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const params = await context.params;
    const { id } = params;
    const companyId = request.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    if (!id || !Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Valid ingredient ID is required' }, { status: 400 });
    }

    const ingredient = await Ingredient.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(companyId)
    })
    .populate('baseUomId', 'name shortCode')
    .populate('defaultSupplierId', 'name')
    .populate('supplierDetails.supplierId', 'name')
    .populate('supplierDetails.unitsOfOrdering.unitOfMeasure', 'name shortCode')
    .populate('sellingDetails.unitOfSelling', 'name shortCode')
    .populate('sellingDetails.visibility.locations')
    .lean();

    if (!ingredient) {
      return NextResponse.json({ error: 'Ingredient not found' }, { status: 404 });
    }

    // Transform ObjectIds to strings for the frontend
    const transformedIngredient = {
      ...ingredient,
      _id: ingredient._id.toString(),
      companyId: ingredient.companyId.toString(),
      // Type assertion is used here because populated fields may be plain objects
      baseUomId: ingredient.baseUomId ? {
        _id: (ingredient.baseUomId as any)._id?.toString(),
        name: (ingredient.baseUomId as any).name,
        shortCode: (ingredient.baseUomId as any).shortCode
      } : null,
      // Type assertion is used here because populated fields may be plain objects
      defaultSupplierId: ingredient.defaultSupplierId ? {
        _id: (ingredient.defaultSupplierId as any)._id?.toString(),
        name: (ingredient.defaultSupplierId as any).name
      } : null,
      supplierDetails: ingredient.supplierDetails?.map((supplier: any) => ({
        ...supplier,
        supplierId: {
          _id: supplier.supplierId._id.toString(),
          name: (supplier.supplierId as any).name
        },
        unitsOfOrdering: supplier.unitsOfOrdering.map((unit: any) => ({
          ...unit,
          unitOfMeasure: {
            _id: unit.unitOfMeasure._id.toString(),
            name: (unit.unitOfMeasure as any).name,
            shortCode: (unit.unitOfMeasure as any).shortCode
          }
        }))
      })),
      sellingDetails: ingredient.sellingDetails?.map((option: any) => ({
        ...option,
        unitOfSelling: {
          _id: option.unitOfSelling._id.toString(),
          name: (option.unitOfSelling as any).name,
          shortCode: (option.unitOfSelling as any).shortCode
        },
        visibility: {
          ...option.visibility,
          locations: option.visibility.locations?.map((location: any) => ({
            _id: location._id.toString(),
            name: (location as any).name
          }))
        }
      }))
    };

    return NextResponse.json(transformedIngredient);
  } catch (error: unknown) {
    console.error('[GET Ingredient] Error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch ingredient' },
      { status: 500 }
    );
  }
}

// PUT /api/ingredients/[id]
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const params = await context.params;
    const { id } = params;
    const companyId = request.headers.get('company-id');
    
    console.log('PUT /api/ingredients/[id] - Request received');
    console.log('ID:', id);
    console.log('Company ID:', companyId);
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    if (!id || !Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Valid ingredient ID is required' }, { status: 400 });
    }

    const data = await request.json();
    console.log('Received update data:', JSON.stringify(data, null, 2));

    // Clean and validate the data
    const cleanData = {
      ...data,
      companyId: new Types.ObjectId(companyId),
      defaultSupplierId: data.defaultSupplierId ? new Types.ObjectId(data.defaultSupplierId) : null,
      baseUomId: new Types.ObjectId(data.baseUomId),
      canBeSold: Boolean(data.canBeSold),
      // Format supplier details
      supplierDetails: data.supplierDetails?.map((supplier: any) => ({
        supplierId: new Types.ObjectId(supplier.supplierId._id),
        unitsOfOrdering: supplier.unitsOfOrdering.map((unit: any) => ({
          unitOfMeasure: new Types.ObjectId(unit.unitOfMeasure._id),
          quantityInBaseUom: Number(unit.quantityInBaseUom),
          price: Number(unit.price),
          pricePerBaseUom: Number(unit.price / unit.quantityInBaseUom)
        }))
      })),
      // Format selling details - always transform if they exist
      sellingDetails: data.sellingDetails?.map((option: any) => ({
        unitOfSelling: new Types.ObjectId(option.unitOfSelling._id),
        priceWithoutTax: Number(option.priceWithoutTax),
        priceWithTax: Number(option.priceWithTax),
        taxRate: Number(option.taxRate),
        taxCategory: option.taxCategory,
        conversionFactor: Number(option.conversionFactor),
        visibility: {
          type: option.visibility.type,
          locations: option.visibility.locations?.map((loc: any) => 
            // Handle both formats: when loc is an ID string and when it's an object with _id
            new Types.ObjectId(typeof loc === 'string' ? loc : loc._id)
          ),
          externalAccess: Boolean(option.visibility.externalAccess)
        },
        sourceType: option.sourceType,
        markupType: option.markupType,
        markupPercentage: option.markupPercentage ? Number(option.markupPercentage) : undefined
      }))
    };

    console.log('Cleaned update data:', JSON.stringify(cleanData, null, 2));

    // Log the query we're about to execute
    console.log('Finding ingredient with:', {
      _id: id,
      companyId: new Types.ObjectId(companyId)
    });

    // Update the ingredient
    const updatedIngredient = await Ingredient.findOneAndUpdate(
      { _id: id, companyId: new Types.ObjectId(companyId) },
      cleanData,
      { 
        new: true,
        runValidators: true 
      }
    )
    .populate('baseUomId', 'name shortCode')
    .populate('defaultSupplierId', 'name')
    .populate('supplierDetails.supplierId', 'name')
    .populate('supplierDetails.unitsOfOrdering.unitOfMeasure', 'name shortCode')
    .populate('sellingDetails.unitOfSelling', 'name shortCode')
    .populate('sellingDetails.visibility.locations');

    console.log('Update result:', updatedIngredient ? 'Document updated' : 'No document found');
    
    if (!updatedIngredient) {
      console.log('Ingredient not found');
      return NextResponse.json({ error: 'Ingredient not found' }, { status: 404 });
    }

    // Transform the response data
    const transformedIngredient = {
      ...updatedIngredient.toObject(),
      _id: updatedIngredient._id as string,
      companyId: updatedIngredient.companyId.toString(),
      // Type assertion is used here because populated fields may be plain objects
      baseUomId: updatedIngredient.baseUomId ? {
        _id: (updatedIngredient.baseUomId as any)._id?.toString(),
        name: (updatedIngredient.baseUomId as any).name,
        shortCode: (updatedIngredient.baseUomId as any).shortCode
      } : null,
      // Type assertion is used here because populated fields may be plain objects
      defaultSupplierId: updatedIngredient.defaultSupplierId ? {
        _id: (updatedIngredient.defaultSupplierId as any)._id?.toString(),
        name: (updatedIngredient.defaultSupplierId as any).name
      } : null,
      supplierDetails: updatedIngredient.supplierDetails?.map((supplier: any) => ({
        ...supplier,
        supplierId: {
          _id: supplier.supplierId._id.toString(),
          name: (supplier.supplierId as any).name
        },
        unitsOfOrdering: supplier.unitsOfOrdering.map((unit: any) => ({
          ...unit,
          unitOfMeasure: {
            _id: unit.unitOfMeasure._id.toString(),
            name: (unit.unitOfMeasure as any).name,
            shortCode: (unit.unitOfMeasure as any).shortCode
          }
        }))
      })),
      sellingDetails: updatedIngredient.sellingDetails?.map((option: any) => ({
        ...option,
        unitOfSelling: {
          _id: option.unitOfSelling._id.toString(),
          name: (option.unitOfSelling as any).name,
          shortCode: (option.unitOfSelling as any).shortCode
        },
        visibility: {
          ...option.visibility,
          locations: option.visibility.locations?.map((location: any) => ({
            _id: location._id.toString(),
            name: (location as any).name
          }))
        }
      }))
    };

    console.log('Transformed response:', JSON.stringify({
      id: transformedIngredient._id,
      canBeSold: transformedIngredient.canBeSold,
      sellingDetails: transformedIngredient.sellingDetails?.length || 0
    }, null, 2));

    return NextResponse.json(transformedIngredient);
  } catch (error: unknown) {
    console.error('[PUT Ingredient] Error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update ingredient' },
      { status: 500 }
    );
  }
}

// DELETE /api/ingredients/[id]
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();
    const params = await context.params;
    const { id } = params;
    const companyId = request.headers.get('company-id');
    
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    if (!id || !Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Valid ingredient ID is required' }, { status: 400 });
    }

    const ingredient = await Ingredient.findOneAndDelete({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(companyId)
    });

    if (!ingredient) {
      return NextResponse.json({ error: 'Ingredient not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Ingredient deleted successfully' });
  } catch (error: unknown) {
    console.error('[DELETE Ingredient] Error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete ingredient' },
      { status: 500 }
    );
  }
}
