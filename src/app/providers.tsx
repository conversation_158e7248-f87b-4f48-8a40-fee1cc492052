// src/app/providers.tsx
'use client';

import { useEffect, useState } from 'react';
import { getUserCompany } from '@/lib/services/companyService';
import { CompanyUserData, SuperUserData, AuthContext } from '@/lib/auth';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

export interface User {
  _id: string;
  email: string;
  displayName?: string;
  userType: 'superuser' | 'company_user';
  lastLogin?: Date;
  createdAt: Date;
  role?: string;
  permissions?: string[];
}

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      refetchOnWindowFocus: false,
    },
  },
});

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>{children}</AuthProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState<CompanyUserData | SuperUserData | null>(null);
  const [isSuperUser, setIsSuperUser] = useState(false);

  // Fetch user data when auth state changes
  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/auth/verify');
      if (!response.ok) {
        throw new Error('Failed to verify session');
      }

      const data = await response.json();
      if (!data.uid) {
        setUser(null);
        setUserData(null);
        setIsSuperUser(false);
        return;
      }

      // Try to get user's company role
      console.log('Checking for company association...');
      const companyData = await getUserCompany(data.uid);
      console.log('Company data for user:', companyData);

      if (companyData) {
        const userData = {
          uid: data.uid,
          email: data.email,
          userType: 'company_user' as const,
          lastLogin: new Date(),
          createdAt: new Date(),
          companyId: companyData.companyId,
          role: data.role,  // Use the role from verify endpoint
          permissions: data.permissions || []  // Set default empty permissions
        };
        setUser(data);
        setUserData(userData);
        setIsSuperUser(false);
      } else {
        // Check if user is a superuser
        const response = await fetch(`/api/users/${data.uid}`);
        if (response.ok) {
          const userData = await response.json();
          if (userData.userType === 'superuser') {
            setUser(data);
            setUserData({
              uid: data.uid,
              email: data.email,
              userType: 'superuser',
              lastLogin: new Date(),
              createdAt: userData.createdAt,
              role: userData.role,
              permissions: userData.permissions
            });
            setIsSuperUser(true);
            return;
          }
        }
        setUser(null);
        setUserData(null);
        setIsSuperUser(false);
      }
    } catch (error) {
      console.error('Error in fetchUserData:', error);
      setUser(null);
      setUserData(null);
      setIsSuperUser(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      console.log('Attempting sign in...');
      const response = await fetch('/api/auth/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to sign in');
      }

      await fetchUserData();
    } catch (error: any) {
      console.error('Sign in error:', error.message);
      throw new Error(error.message);
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      console.log('Attempting sign up...');
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to sign up');
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Sign up error:', error.message);
      throw new Error(error.message);
    }
  };

  const signOut = async () => {
    try {
      await fetch('/api/auth/signout', { method: 'POST' });
      setUser(null);
      setUserData(null);
      setIsSuperUser(false);
    } catch (error: any) {
      console.error('Sign out error:', error.message);
      throw new Error(error.message);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      userData,
      isSuperUser,
      signIn,
      signUp,
      signOut
    }}>
      {children}
    </AuthContext.Provider>
  );
}
