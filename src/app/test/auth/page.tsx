'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function TestAuthPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncResult, setSyncResult] = useState<any>(null);
  const [companyId, setCompanyId] = useState('67682466d436c5f697693330');
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Authentication failed');
      }

      const data = await response.json();
      setToken(data.token || 'Session cookie set');
      
      // Get sync data after successful login
      await fetchSyncData();
    } catch (error) {
      console.error('Login error:', error);
      setError(error instanceof Error ? error.message : 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const fetchSyncData = async () => {
    setLoading(true);
    setSyncResult(null);
    setError(null);

    try {
      const response = await fetch(`/api/company/${companyId}/users/sync`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        credentials: 'include', // Include cookies for session authentication
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Sync failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      setSyncResult(data);
    } catch (error) {
      console.error('Sync error:', error);
      setError(error instanceof Error ? error.message : 'Sync failed');
    } finally {
      setLoading(false);
    }
  };

  const handleTestWithToken = async () => {
    if (!token) {
      setError('Please login first or provide a token');
      return;
    }

    setLoading(true);
    setSyncResult(null);
    setError(null);

    try {
      const response = await fetch(`/api/company/${companyId}/users/sync`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'company-id': companyId,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Test failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      setSyncResult(data);
    } catch (error) {
      console.error('Test error:', error);
      setError(error instanceof Error ? error.message : 'Test failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Test Authentication for Sync</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Login</h2>
          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Email:</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full border rounded px-3 py-2"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Password:</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full border rounded px-3 py-2"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Company ID:</label>
              <input
                type="text"
                value={companyId}
                onChange={(e) => setCompanyId(e.target.value)}
                className="w-full border rounded px-3 py-2"
                required
              />
            </div>
            <button
              type="submit"
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Login & Sync'}
            </button>
          </form>

          {token && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-sm font-medium text-green-800">Authenticated!</p>
              <p className="text-xs text-green-600 break-all">{token}</p>
            </div>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Test with Token</h2>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">JWT Token:</label>
            <textarea
              value={token}
              onChange={(e) => setToken(e.target.value)}
              className="w-full border rounded px-3 py-2 h-32"
              placeholder="Paste JWT token here to test"
            />
          </div>
          <button
            onClick={handleTestWithToken}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-2"
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Test with Token'}
          </button>
          <button
            onClick={() => router.push('/test/users')}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
          >
            View Users Page
          </button>
        </div>
      </div>

      {error && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <h3 className="font-bold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {syncResult && (
        <div className="mt-4 bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2">Sync Result</h2>
          <p className="text-sm text-gray-500 mb-4">Retrieved {syncResult.users?.length || 0} users</p>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {syncResult.users?.map((user: any) => (
                  <tr key={user._id}>
                    <td className="px-3 py-2 text-sm text-gray-500">{user._id}</td>
                    <td className="px-3 py-2 whitespace-nowrap">{user.email}</td>
                    <td className="px-3 py-2 whitespace-nowrap">{user.displayName || 'N/A'}</td>
                    <td className="px-3 py-2 whitespace-nowrap">{user.role || 'N/A'}</td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.isDeleted ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                        {user.isDeleted ? 'Deleted' : user.syncStatus || 'Active'}
                      </span>
                    </td>
                  </tr>
                ))}
                
                {(!syncResult.users || syncResult.users.length === 0) && (
                  <tr>
                    <td colSpan={5} className="px-3 py-2 text-center text-sm text-gray-500">
                      No users found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Raw Response:</h3>
            <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto">
              {JSON.stringify(syncResult, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}