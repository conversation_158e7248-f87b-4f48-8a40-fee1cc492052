'use client';

import React, { useState, useEffect } from 'react';

export default function TestUsersPage() {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [companyId, setCompanyId] = useState('67682466d436c5f697693330');

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        // First test if basic API functionality works
        console.log('Testing basic API functionality...');
        const testResponse = await fetch('/api/test');
        if (!testResponse.ok) {
          throw new Error('Basic API test failed');
        }
        const testData = await testResponse.json();
        console.log('API test successful:', testData);
        
        // Now try to fetch users with authentication
        console.log(`Fetching users for company: ${companyId}`);
        
        // Use the sync endpoint with cookies for authentication
        const response = await fetch(`/api/company/${companyId}/users/sync`, {
          credentials: 'include', // Include cookies for authentication
          headers: {
            'company-id': companyId // Include company ID in headers
          }
        });
        
        if (!response.ok) {
          // If authentication fails, show a helpful message
          if (response.status === 401) {
            throw new Error('Authentication required. Please log in at /test/auth first');
          }
          
          const errorText = await response.text();
          throw new Error(`Failed to fetch users: ${response.status} ${response.statusText} - ${errorText}`);
        }
        
        const data = await response.json();
        console.log('Users data:', data);
        
        setUsers(data.users || []);
        setError(null);
      } catch (error) {
        console.error('Error fetching users:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [companyId]);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Test User Synchronization</h1>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Company ID:</label>
        <div className="flex gap-2">
          <input 
            type="text" 
            value={companyId} 
            onChange={(e) => setCompanyId(e.target.value)}
            className="border rounded px-2 py-1 flex-grow"
          />
          <button 
            onClick={() => setCompanyId('67682466d436c5f697693330')}
            className="bg-gray-200 px-2 py-1 rounded"
          >
            Reset
          </button>
        </div>
      </div>
      
      <div className="mb-4">
        <button 
          onClick={() => window.location.href = `/test/users?${Date.now()}`}
          className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
        >
          Refresh
        </button>
        <button 
          onClick={() => window.location.href = '/test/auth'}
          className="bg-green-500 text-white px-4 py-2 rounded mr-2"
        >
          Log In
        </button>
        <button 
          onClick={() => fetch(`/api/test`).then(r => r.json()).then(console.log)}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Test API
        </button>
      </div>
      
      {loading && <div className="text-center py-4">Loading...</div>}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {!loading && !error && (
        <div className="bg-white shadow rounded-lg p-4">
          <div className="mb-4">
            <h2 className="text-xl font-semibold">Users ({users.length})</h2>
            <p className="text-sm text-gray-500">From Company ID: {companyId}</p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Modified</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user._id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user._id}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{user.email}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{user.displayName || 'N/A'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{user.role || 'N/A'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{new Date(user.lastModified).toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.isDeleted ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                        {user.isDeleted ? 'Deleted' : user.syncStatus || 'Active'}
                      </span>
                    </td>
                  </tr>
                ))}
                
                {users.length === 0 && (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No users found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}