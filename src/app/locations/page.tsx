// src/app/locations/page.tsx
'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { Location } from '@/lib/types/location';

export default function LocationsPage() {
  const { userData } = useRequireCompanyUser();
  const { setSelectedLocation } = useLocation();
  const [locations, setLocations] = React.useState<Location[]>([]);

  React.useEffect(() => {
    async function fetchLocations() {
      try {
        const response = await fetch('/api/locations', {
          headers: {
            'company-id': userData?.companyId || '',
          },
        });
        if (response.ok) {
          const data = await response.json();
          setLocations(data);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    }

    if (userData?.companyId) {
      fetchLocations();
    }
  }, [userData?.companyId]);

  const handleLocationClick = (location: Location) => {
    setSelectedLocation(location);
    if (location.locationType === 'RETAIL_SHOP') {
      window.location.href = `/retail/${location._id}/dashboard`;
    }
  };

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'CENTRAL_KITCHEN':
        return 'Central Kitchen';
      case 'RETAIL_SHOP':
        return 'Retail Shop';
      case 'SINGLE_LOCATION':
        return 'Single Location';
      default:
        return type;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Locations</h1>
          <p className="mt-2 text-sm text-gray-700">
            A list of all locations in your company. Click on a retail location to access its specific dashboard.
          </p>
        </div>
      </div>

      <div className="mt-8 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {locations.map((location) => (
          <div
            key={location._id}
            onClick={() => handleLocationClick(location)}
            className={`bg-white overflow-hidden shadow rounded-lg cursor-pointer transition-transform hover:scale-105 ${
              location.locationType === 'RETAIL_SHOP' ? 'hover:shadow-lg' : ''
            }`}
          >
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {location.locationType === 'RETAIL_SHOP' ? (
                    <svg className="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  ) : (
                    <svg className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  )}
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">{location.name}</h3>
                  <p className="text-sm text-gray-500">{getLocationTypeLabel(location.locationType)}</p>
                </div>
              </div>
              {location.locationType === 'RETAIL_SHOP' && (
                <div className="mt-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Click to view dashboard
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
