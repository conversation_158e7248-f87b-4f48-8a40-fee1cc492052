'use client';

import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';
import { ObjectId } from 'mongodb';

// Define types for our components
type SystemRole = 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper';
type UserRole = SystemRole | string; // Can be a system role or a custom role ID

type User = {
  _id: string;
  email: string;
  displayName: string;
  userType: string;
  role?: SystemRole;
  customRoleId?: string;
  companyId: string;
  isActive?: boolean;
  canUseIonicApp?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  locationIds?: string[];
};

type CustomRole = {
  _id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  permissions: string[];
  type: 'hq' | 'branch';
};

type Location = {
  _id: string;
  name: string;
  companyId: string;
  locationType?: string;
};

type NewUserData = {
  displayName: string;
  email: string;
  role?: SystemRole;
  customRoleId?: string;
  password: string;
  locationId?: string;
};

type UserFormData = {
  displayName: string;
  email: string;
  role?: SystemRole;
  customRoleId?: string;
  roleSelection: string; // This field is used for the dropdown selection (either system role or custom role ID)
  password: string;
  locationIds?: string[]; // Array of location IDs
};

type UserModalProps = {
  isOpen: boolean;
  mode: 'add' | 'edit';
  onClose: () => void;
  onSubmit: (data: UserFormData, mode: 'add' | 'edit', userId?: string) => Promise<void>;
  userData: any; // The current user's data
  companyId: string;
  userToEdit?: User | null; // User to edit when in edit mode
  locations: Location[]; // Array of locations
};

// Modal component for adding or editing users
function UserModal({ isOpen, mode, onClose, onSubmit, userData, companyId, userToEdit, locations }: UserModalProps) {
  const [formData, setFormData] = useState<UserFormData>({
    displayName: '',
    email: '',
    roleSelection: 'user', // Default to 'user' system role
    password: '',
    locationIds: []
  });
  
  // Update form data when userToEdit changes or mode changes
  useEffect(() => {
    console.log('UserModal effect - mode:', mode, 'userToEdit:', userToEdit);
    if (mode === 'edit' && userToEdit) {
      console.log('Setting form data for edit:', userToEdit);
      console.log('User locations:', userToEdit.locationIds);
      
      // Ensure locationIds is an array of strings
      const locationIds = Array.isArray(userToEdit.locationIds) 
        ? userToEdit.locationIds.map(id => String(id)) 
        : [];
        
      console.log('Normalized locationIds:', locationIds);
      
      setFormData({
        displayName: userToEdit.displayName || '',
        email: userToEdit.email || '',
        roleSelection: userToEdit.role || userToEdit.customRoleId || 'user',
        password: '', // Empty password for edit mode
        locationIds: locationIds
      });
    } else if (mode === 'add') {
      // Reset form for add mode
      setFormData({
        displayName: '',
        email: '',
        roleSelection: 'user',
        password: '',
        locationIds: []
      });
    }
  }, [mode, userToEdit]);
  
  // This effect runs after form data changes to ensure select options are properly selected
  useEffect(() => {
    if (isOpen && mode === 'edit') {
      console.log('Setting selected options in location select:', formData.locationIds);
      
      // Get the select element
      const selectElement = document.querySelector('select[name="locationIds"]') as HTMLSelectElement;
      if (selectElement) {
        // Convert locationIds to strings to ensure consistent comparison
        const locationIdStrings = (formData.locationIds || []).map(String);
        console.log('LocationIds as strings for comparison:', locationIdStrings);
        
        // For each option in the select
        Array.from(selectElement.options).forEach(option => {
          // Check if the option's value is in the locationIds array
          const isSelected = locationIdStrings.includes(option.value);
          option.selected = isSelected;
          console.log(`Option ${option.value} (${option.text}) selected:`, isSelected);
        });
      }
    }
  }, [isOpen, formData.locationIds, mode]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [customRoles, setCustomRoles] = useState<CustomRole[]>([]);

  // Fetch custom roles on component mount
  useEffect(() => {
    const fetchCustomRoles = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/roles`, {
          headers: {
            'Content-Type': 'application/json',
            'company-id': companyId
          }
        });
        if (!response.ok) {
          throw new Error('Failed to fetch roles');
        }
        
        const data = await response.json();
        setCustomRoles(data.roles.filter((role: CustomRole) => !role.isSystemRole));
      } catch (err) {
        console.error('Error loading custom roles:', err);
      }
    };

    if (isOpen) {
      fetchCustomRoles();
    }
  }, [companyId, isOpen]);

  // Fetch locations for the company
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/locations`, {
          headers: {
            'company-id': companyId
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch locations');
        }
        
        const data = await response.json();
        console.log('Locations data from API:', data);
        
        // The locations API returns an array directly, not inside a locations property
        if (Array.isArray(data) && data.length > 0) {
          console.log(`Found ${data.length} locations:`, data.map(loc => loc.name).join(', '));
        } else {
          console.log('No locations found or invalid data format');
        }
        
        // Make sure we're using the locations prop passed from the parent
        // We don't need to maintain a separate state here
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    // Only fetch locations as a debug check if the modal is open
    if (isOpen) {
      fetchLocations();
    }
  }, [isOpen, companyId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'roleSelection') {
      // Determine if this is a system role or custom role
      const isSystemRole = ['owner', 'admin', 'manager', 'user', 'storekeeper'].includes(value);

      if (isSystemRole) {
        setFormData(prev => ({
          ...prev,
          roleSelection: value,
          role: value as SystemRole,
          customRoleId: undefined
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          roleSelection: value,
          role: undefined,
          customRoleId: value
        }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value as string }));
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Prepare the data for submission
      const submissionData = {
        displayName: formData.displayName,
        email: formData.email,
        password: formData.password,
        role: formData.role,
        customRoleId: formData.customRoleId,
        // Ensure locationIds is always an array of strings
        locationIds: Array.isArray(formData.locationIds) ? formData.locationIds.map(String) : []
      };
      
      console.log('Submitting user data:', submissionData);
      
      // Pass the mode and userId if in edit mode
      await onSubmit(submissionData as UserFormData, mode, userToEdit?._id);
      onClose();
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : `Failed to ${mode === 'add' ? 'create' : 'update'} user`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold mb-4">{mode === 'add' ? 'Add New User' : 'Edit User'}</h2>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Name
              <input
                type="text"
                name="displayName"
                value={formData.displayName}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mt-1"
                required
              />
            </label>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mt-1"
                required
              />
            </label>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Role
              <select
                name="roleSelection"
                value={formData.roleSelection}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mt-1"
                required
              >
                <optgroup label="System Roles">
                  <option value="user">User</option>
                  <option value="storekeeper">Storekeeper</option>
                  <option value="manager">Manager</option>
                  {userData.role === 'owner' && (
                    <option value="admin">Admin</option>
                  )}
                </optgroup>
                
                {customRoles.length > 0 && (
                  <optgroup label="Custom Roles">
                    {customRoles.map(role => (
                      <option key={role._id} value={role._id}>
                        {role.name}
                      </option>
                    ))}
                  </optgroup>
                )}
              </select>
            </label>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Locations
              <select
                multiple
                name="locationIds"
                value={formData.locationIds || []}
                onChange={(e) => {
                  // Convert selected options to array of values
                  const selectedLocations = Array.from(e.target.selectedOptions).map(option => option.value);
                  console.log('Selected locations in dropdown:', selectedLocations);
                  console.log('Previous locationIds:', formData.locationIds);
                  
                  // Ensure we're always dealing with strings
                  const normalizedLocations = selectedLocations.map(String);
                  console.log('Normalized location IDs:', normalizedLocations);
                  
                  setFormData(prev => {
                    const updated = {
                      ...prev,
                      locationIds: normalizedLocations
                    };
                    console.log('Updated form data:', updated);
                    return updated;
                  });
                }}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mt-1"
                required
              >
                {/* Debug: Show how many locations are available */}
                {/* {console.log('Rendering location options from props, count:', locations.length, locations)} */}
                {locations.map(location => (
                  <option 
                    key={location._id} 
                    value={location._id}
                  >
                    {location.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Hold Ctrl (Windows) or Cmd (Mac) to select multiple locations
              </p>
            </label>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password {mode === 'edit' && <span className="text-xs text-gray-500 font-normal ml-1">(leave empty to keep current)</span>}
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mt-1"
                required={mode === 'add'} 
                minLength={mode === 'add' ? 6 : 0}
                placeholder={mode === 'edit' ? '••••••••' : 'Enter password'}
              />
            </label>
          </div>
          
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-2 bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              disabled={isSubmitting}
            >
              {isSubmitting 
                ? (mode === 'add' ? 'Creating...' : 'Updating...') 
                : (mode === 'add' ? 'Create User' : 'Update User')
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function UsersClient() {
  const { userData, loading } = useRequireCompanyUser();
  const [users, setUsers] = useState<User[]>([]);
  const [locations, setLocations] = useState<Location[]>([]); // Add state for locations
  const [isLoading, setIsLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [updatingAccess, setUpdatingAccess] = useState<string | null>(null); // For tracking which user's access is being updated
  const [userToEdit, setUserToEdit] = useState<User | null>(null); // For storing the user being edited
  const [newUser, setNewUser] = useState<UserFormData>({
    displayName: '',
    email: '',
    roleSelection: 'user',
    role: 'user', // Default system role
    password: ''
  });
  const params = useParams();
  // Add null check to fix TypeScript error
  const companyId = params?.companyId as string;
  
  // Function to toggle IonicPOS access for a user
  const toggleIonicAccess = async (userId: string, newStatus: boolean) => {
    if (updatingAccess === userId) return; // Prevent double-clicks
    
    setUpdatingAccess(userId);
    try {
      const response = await fetch(`/api/admin/users/ionic-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify({
          userId,
          canUseIonicApp: newStatus
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update IonicPOS access: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Update the users list with the updated user
      setUsers(users.map(user => 
        user._id === userId ? { ...user, canUseIonicApp: newStatus } : user
      ));
      
    } catch (error) {
      console.error('Error updating IonicPOS access:', error);
      alert('Failed to update IonicPOS access. Please try again.');
    } finally {
      setUpdatingAccess(null);
    }
  };

  const handleUserSubmit = async (userData: UserFormData, mode: 'add' | 'edit', userId?: string) => {
    setIsLoading(true);
    console.log(`${mode === 'add' ? 'Creating' : 'Updating'} user with data:`, userData);
    console.log('Location IDs being submitted:', userData.locationIds);
    try {
      if (mode === 'add') {
        // Create a new user
        const response = await fetch(`/api/company/${companyId}/users`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'company-id': companyId
          },
          body: JSON.stringify({
            ...userData,
            companyId,
            userType: 'company_user'
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create user');
        }
      } else if (mode === 'edit' && userId) {
        // Edit existing user
        const response = await fetch(`/api/company/${companyId}/users/${userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'company-id': companyId
          },
          body: JSON.stringify({
            ...userData,
            // Only include password if it's not empty (don't update password otherwise)
            ...(userData.password ? { password: userData.password } : {}),
            // Explicitly include locationIds to ensure it's sent properly
            locationIds: userData.locationIds || []
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update user');
        }
      }

      // Refresh the user list
      fetchUsers();
      // Reset edit state
      setUserToEdit(null);
    } catch (error) {
      console.error(`Error ${mode === 'add' ? 'creating' : 'updating'} user:`, error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      console.log('Fetching users...');
      // First test if basic API functionality works
      console.log('Testing basic API functionality...');
      const testResponse = await fetch('/api/test', {
        credentials: 'include' // Include cookies for authentication
      });
      if (!testResponse.ok) {
        throw new Error('Basic API test failed');
      }
      const testData = await testResponse.json();
      console.log('API test successful:', testData);
      
      // Now try to fetch users
      console.log(`Fetching users for company: ${companyId}`);
      const response = await fetch(`/api/company/${companyId}/users/sync`, {
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        }
      });
      
      if (!response.ok) {
        console.error('Response not OK:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Users data:', data);
      
      // For the sync endpoint, users are in the 'users' property
      if (data && data.users) {
        // Process users to ensure locationIds is always an array of strings
        const processedUsers = (data.users || []).map((user: any) => ({
          ...user,
          locationIds: Array.isArray(user.locationIds) 
            ? user.locationIds.map((id: any) => String(id))
            : []
        }));
        console.log('Processed users with normalized locationIds:', processedUsers);
        setUsers(processedUsers);
      } else {
        // Fallback - try direct fetch (manual users, not from sync)
        // This will hopefully give us more information
        console.log('Trying manual fetch...');
        const manualResponse = await fetch(`/api/debug/users/${companyId}`, {
          headers: {
            'company-id': companyId
          },
          credentials: 'include' // Include cookies for authentication
        });
        const manualData = await manualResponse.json();
        console.log('Manual fetch result:', manualData);
        
        // Process the manually fetched users to ensure locationIds is consistent
        const usersFromFetch = manualData.users || [];
        const processedUsers = usersFromFetch.map((user: any) => ({
          ...user,
          locationIds: Array.isArray(user.locationIds) 
            ? user.locationIds.map((id: any) => String(id))
            : []
        }));
        console.log('Processed manual users with normalized locationIds:', processedUsers);
        setUsers(processedUsers);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch locations
  const fetchLocations = async () => {
    try {
      console.log('Fetching locations for company:', companyId);
      const response = await fetch(`/api/company/${companyId}/locations`, {
        headers: {
          'company-id': companyId
        }
      });
      
      if (!response.ok) {
        console.error('Locations API response not OK:', response.status);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch locations: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Locations data from main component:', data);
      
      // Location API returns an array directly
      if (Array.isArray(data)) {
        console.log(`Found ${data.length} locations in main component`);
        setLocations(data);
      } else {
        console.warn('Unexpected locations data format, expected array but got:', typeof data);
        setLocations([]);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      setLocations([]);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchLocations(); // Fetch locations when component mounts
  }, [companyId]);

  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Users</h1>
      
      {/* User Modal for Add/Edit */}
      <UserModal 
        isOpen={showAddModal || !!userToEdit}
        mode={userToEdit ? 'edit' : 'add'}
        onClose={() => {
          setShowAddModal(false);
          setUserToEdit(null);
        }}
        onSubmit={handleUserSubmit}
        userData={userData}
        companyId={companyId}
        userToEdit={userToEdit}
        locations={locations} // Pass the fetched locations array
      />
      
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex justify-between mb-4">
          <h2 className="text-xl font-semibold">User Management</h2>
          <button 
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            onClick={() => setShowAddModal(true)}
          >
            Add User
          </button>
        </div>
        
        {users.length === 0 ? (
          <p>No users found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">IonicPOS Access</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user: any) => (
                  <tr key={user._id}>
                    <td className="px-6 py-4 whitespace-nowrap w-1/5">
                      <div className="text-sm text-gray-900">{user.displayName}</div>
                      {/* Show multiple locations */}
                      {user.locationIds && user.locationIds.length > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          Locations: {Array.isArray(user.locationIds) && user.locationIds.length > 0 ? 
                            user.locationIds.map((locationId: string) => {
                              const location = locations.find(loc => loc._id === locationId);
                              return location ? location.name : locationId;
                            }).join(', ') : 'None'}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap w-1/5">{user.email}</td>
                    <td className="px-6 py-4 whitespace-nowrap w-1/6">{user.role || (user.customRoleId ? 'Custom Role' : 'N/A')}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.isDeleted ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                        {user.isDeleted ? 'Inactive' : 'Active'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <button 
                          onClick={() => toggleIonicAccess(user._id, !user.canUseIonicApp)}
                          className={`px-3 py-1 rounded-md text-white text-xs ${user.canUseIonicApp ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-400 hover:bg-gray-500'}`}
                          disabled={updatingAccess === user._id}
                        >
                          {updatingAccess === user._id ? 'Updating...' : (user.canUseIonicApp ? 'Enabled' : 'Disabled')}
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button 
                        className="text-indigo-600 hover:text-indigo-900 mr-2 px-2 py-1 border border-indigo-600 rounded"
                        onClick={() => setUserToEdit(user)}
                      >
                        Edit
                      </button>
                      <button className="text-red-600 hover:text-red-900 px-2 py-1 border border-red-600 rounded">
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}