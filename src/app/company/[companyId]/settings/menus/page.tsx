import { Metadata } from "next"
import { GroupsManagement } from "@/components/menus/groups-management"
import { CategoriesManagement } from "@/components/menus/categories-management"
import { MenuItemsManagement } from "@/components/menus/menu-items-management"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "Menu Management",
  description: "Manage menu items, groups, and categories",
}

export default function MenuManagementPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Menu Management</h1>
        <p className="text-muted-foreground">
          Manage your menu structure, items, and categories
        </p>
      </div>

      <Tabs defaultValue="items" className="space-y-4">
        <TabsList>
          <TabsTrigger value="items">Menu Items</TabsTrigger>
          <TabsTrigger value="groups">Groups</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>
        <TabsContent value="items">
          <Card>
            <CardHeader>
              <CardTitle>Menu Items</CardTitle>
              <CardDescription>
                Create and manage menu items including single items and recipes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MenuItemsManagement />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="groups">
          <Card>
            <CardHeader>
              <CardTitle>Menu Groups</CardTitle>
              <CardDescription>
                Create and manage menu groups like Beverages, Main Courses, etc.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GroupsManagement />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="categories">
          <Card>
            <CardHeader>
              <CardTitle>Menu Categories</CardTitle>
              <CardDescription>
                Create and manage menu categories within groups
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CategoriesManagement />
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>
    </div>
  )
}
