'use client';

import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd';
import { 
  GripVertical,
  Plus,
  X,
  Save,
  AlertCircle
} from 'lucide-react';
import { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Available step types
const STEP_TYPES = [
  { id: 'DISPATCH', label: 'Dispatch' },
  { id: 'QUALITY_CHECK', label: 'Quality Check' },
  { id: 'DRIVER', label: 'Driver' },
  { id: 'SECURITY', label: 'Security' },
  { id: 'SHOP', label: 'Shop' },
  { id: 'CUSTOMER', label: 'Customer' },
] as const;

interface HandoverStep {
  stepType: typeof STEP_TYPES[number]['id'];
  required: boolean;
}

export default function DeliveryFlowSettingsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  // State for steps
  const [steps, setSteps] = useState<HandoverStep[]>([]);
  const [isEditing, setIsEditing] = useState(false);

  // Fetch current settings
  const { data: settings, isLoading } = useQuery({
    queryKey: ['deliveryFlowSettings', companyId],
    queryFn: async () => {
      const response = await axios.get(
        `/api/company/${companyId}/settings/delivery-flow`,
        { headers: { 'company-id': companyId } }
      );
      setSteps(response.data.handoverSteps || []);
      return response.data;
    },
  });

  // Save settings mutation
  const saveMutation = useMutation({
    mutationFn: async (newSteps: HandoverStep[]) => {
      await axios.patch(
        `/api/company/${companyId}/settings/delivery-flow`,
        { handoverSteps: newSteps },
        { headers: { 'company-id': companyId } }
      );
    },
    onSuccess: () => {
      toast.success('Delivery flow settings saved successfully');
      setIsEditing(false);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to save settings');
    },
  });

  // Handle drag and drop
  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(steps);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSteps(items);
  };

  // Add new step
  const addStep = () => {
    const unusedSteps = STEP_TYPES.filter(
      type => !steps.some(step => step.stepType === type.id)
    );

    if (unusedSteps.length === 0) {
      toast.error('All available step types have been added');
      return;
    }

    setSteps([...steps, { stepType: unusedSteps[0].id, required: true }]);
    setIsEditing(true);
  };

  // Remove step
  const removeStep = (index: number) => {
    const newSteps = steps.filter((_, i) => i !== index);
    setSteps(newSteps);
    setIsEditing(true);
  };

  // Update step
  const updateStep = (index: number, updates: Partial<HandoverStep>) => {
    const newSteps = steps.map((step, i) => 
      i === index ? { ...step, ...updates } : step
    );
    setSteps(newSteps);
    setIsEditing(true);
  };

  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Delivery Flow Settings</h1>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={addStep}
            disabled={steps.length >= STEP_TYPES.length}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Step
          </Button>
          {isEditing && (
            <Button 
              onClick={() => saveMutation.mutate(steps)}
              disabled={saveMutation.isLoading}
              className="min-w-[130px]"
            >
              {saveMutation.isLoading ? (
                <div className="flex items-center">
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Saving...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Configure the sequence of handover steps that occur after an order is converted into a delivery note. 
          Each delivery note will follow these steps in order, from preparation to final delivery:
          <ul className="mt-2 ml-4 list-disc space-y-1">
            <li><span className="font-medium">Dispatch:</span> Items are prepared and checked for delivery</li>
            <li><span className="font-medium">Quality Check:</span> Optional verification of items and quantities</li>
            <li><span className="font-medium">Driver:</span> Driver picks up and confirms the delivery items</li>
            <li><span className="font-medium">Security:</span> Optional security checkpoint verification</li>
            <li><span className="font-medium">Shop/Location:</span> Delivery arrives at destination</li>
            <li><span className="font-medium">Customer:</span> Final recipient confirms delivery</li>
          </ul>
          Required steps cannot be skipped, while optional steps can be bypassed if needed.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Handover Flow Steps</CardTitle>
        </CardHeader>
        <CardContent>
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="steps">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-2"
                >
                  {steps.map((step, index) => (
                    <Draggable
                      key={index}
                      draggableId={`step-${index}`}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className="flex items-center space-x-4 p-4 bg-white border rounded-lg"
                        >
                          <div {...provided.dragHandleProps}>
                            <GripVertical className="h-5 w-5 text-gray-400" />
                          </div>
                          
                          <Select
                            value={step.stepType}
                            onValueChange={(value) => 
                              updateStep(index, { 
                                stepType: value as typeof STEP_TYPES[number]['id'] 
                              })
                            }
                          >
                            <SelectTrigger className="w-[200px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {STEP_TYPES.map((type) => (
                                <SelectItem
                                  key={type.id}
                                  value={type.id}
                                  disabled={steps.some(
                                    (s, i) => i !== index && s.stepType === type.id
                                  )}
                                >
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          <Select
                            value={step.required ? 'required' : 'optional'}
                            onValueChange={(value) => 
                              updateStep(index, { required: value === 'required' })
                            }
                          >
                            <SelectTrigger className="w-[150px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="required">Required</SelectItem>
                              <SelectItem value="optional">Optional</SelectItem>
                            </SelectContent>
                          </Select>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeStep(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          {steps.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No handover steps configured. Click &quot;Add Step&quot; to get started.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
