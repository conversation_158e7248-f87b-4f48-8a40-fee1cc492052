"use client"

import { MenuItemRecipesManagement } from "@/components/menus/menu-item-recipes-management"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function MenuItemRecipesPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Menu Item Recipes</h1>
        <p className="text-muted-foreground">
          Link menu items to inventory components and manage recipe ingredients
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recipe Components</CardTitle>
          <CardDescription>
            Link menu items to inventory components for accurate stock deduction
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MenuItemRecipesManagement />
        </CardContent>
      </Card>
    </div>
  )
}
