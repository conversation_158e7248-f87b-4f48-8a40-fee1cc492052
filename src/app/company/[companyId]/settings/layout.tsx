'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';
import { Settings, Users, Truck, Building2, Scale, ListChecks, UtensilsCrossed, Utensils, ChevronDown, ChevronRight } from 'lucide-react';

interface SettingsLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ElementType;
  roles: Array<'admin' | 'owner' | 'manager' | 'storekeeper'>;
  subItems?: Array<{
    title: string;
    href: string;
    icon: React.ElementType;
    roles: Array<'admin' | 'owner' | 'manager' | 'storekeeper'>;
  }>;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpand = (title: string) => {
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title) 
        : [...prev, title]
    );
  };

  const settingsNavItems: NavItem[] = [
    {
      title: "General",
      href: "./general",
      icon: Settings,
      roles: ['admin', 'owner']
    },
    {
      title: "Company Profile",
      href: "./profile",
      icon: Building2,
      roles: ['admin', 'owner']
    },
    {
      title: "Users",
      href: "./users",
      icon: Users,
      roles: ['admin', 'owner']
    },
    {
      title: "Menu Management",
      href: "./menus",
      icon: UtensilsCrossed,
      roles: ['admin', 'owner', 'manager']
    },
    {
      title: "Recipe Management",
      href: "./menu-recipes",
      icon: Utensils,
      roles: ['admin', 'owner', 'manager']
    },

    {
      title: "Supplier Settings",
      href: "./suppliers",
      icon: Truck,
      roles: ['admin', 'owner']
    },
    {
      title: "Units of Measure",
      href: "./uoms",
      icon: Scale,
      roles: ['admin', 'owner']
    },
    {
      title: "Delivery Flow",
      href: "./delivery-flow",
      icon: ListChecks,
      roles: ['admin', 'owner']
    }
  ];

  return (
    <div className="space-y-6 p-10 pb-16">
      <div className="space-y-0.5">
        <h2 className="text-2xl font-bold tracking-tight">Settings</h2>
        <p className="text-muted-foreground">
          Manage your company settings and preferences.
        </p>
      </div>
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
        <aside className="-mx-4 lg:w-1/5">
          <nav className="flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1">
            {settingsNavItems.map((item) => (
              <div key={item.title} className="space-y-1">
                {item.subItems ? (
                  <>
                    <div 
                      className={cn(
                        buttonVariants({ variant: "ghost" }),
                        pathname === item.href
                          ? "bg-muted hover:bg-muted"
                          : "hover:bg-transparent hover:underline",
                        "justify-start cursor-pointer flex items-center w-full"
                      )}
                      onClick={() => {
                        toggleExpand(item.title);
                      }}
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      <span>{item.title}</span>
                      <div className="ml-auto">
                        {expandedItems.includes(item.title) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                    {expandedItems.includes(item.title) && item.subItems.map((subItem) => (
                      <Link
                        key={subItem.href}
                        href={subItem.href}
                        className={cn(
                          buttonVariants({ variant: "ghost" }),
                          pathname === subItem.href
                            ? "bg-muted hover:bg-muted"
                            : "hover:bg-transparent hover:underline",
                          "justify-start pl-9"
                        )}
                      >
                        <subItem.icon className="mr-2 h-4 w-4" />
                        {subItem.title}
                      </Link>
                    ))}
                  </>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      buttonVariants({ variant: "ghost" }),
                      pathname === item.href
                        ? "bg-muted hover:bg-muted"
                        : "hover:bg-transparent hover:underline",
                      "justify-start"
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    {item.title}
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </aside>
        <div className="flex-1 lg:max-w-2xl">{children}</div>
      </div>
    </div>
  );
}
