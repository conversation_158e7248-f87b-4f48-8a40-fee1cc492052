'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function GeneralSettingsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId || !['admin', 'owner'].includes(userData.role)) {
    return null; // or redirect to unauthorized
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">General Settings</h2>
        <p className="text-muted-foreground">
          Manage your general company settings and preferences.
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Company Settings</CardTitle>
          <CardDescription>Configure your company-wide settings.</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Add general settings content here */}
          <p>General settings content will be added here.</p>
        </CardContent>
      </Card>
    </div>
  );
}
