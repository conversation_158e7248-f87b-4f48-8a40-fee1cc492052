"use client"

import { MenuItemRecipesManagement } from "@/components/menus/menu-item-recipes-management"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function RecipeManagementPage() {
  return (
    <div className="container max-w-7xl mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Recipe Management</h1>
        <p className="text-muted-foreground">
          Link menu items to their inventory components
        </p>
      </div>

      <MenuItemRecipesManagement />
    </div>
  )
}
