'use client';

import React from 'react';
import { SupplierSettings } from '@/components/admin/SupplierSettings';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';

export default function SupplierSettingsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId || !['admin', 'owner'].includes(userData.role)) {
    return null; // or redirect to unauthorized
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Supplier Settings</h2>
        <p className="text-muted-foreground">
          Configure how supplier information is collected and managed in your company.
        </p>
      </div>
      <SupplierSettings />
    </div>
  );
}
