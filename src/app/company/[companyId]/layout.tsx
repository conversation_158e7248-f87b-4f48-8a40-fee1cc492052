'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { CompanyProvider } from '@/lib/contexts/CompanyContext';
import { getCompanyById } from '@/lib/services/companyService';
import { CompanyData } from '@/lib/types/company';
import { Loader2 } from 'lucide-react';
import { CompanyNav } from '@/components/CompanyNav';
import { LocationSelector } from '@/components/LocationSelector';

interface CompanyLayoutProps {
  children: React.ReactNode;
  params: {
    companyId: string;
  };
}

export default function CompanyLayout({ children, params }: CompanyLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { userData, loading: authLoading } = useRequireCompanyUser();
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [loading, setLoading] = useState(true);
  const resolvedParams = React.use(params);
  const companyId = resolvedParams.companyId;

  // Check if we're in a location route
  const isLocationRoute = pathname.includes('/location/');

  useEffect(() => {
    let mounted = true;

    const initializeCompany = async () => {
      try {
        if (!authLoading && userData) {
          if (userData.companyId !== companyId) {
            console.error('User does not have access to this company');
            router.push('/login');
            return;
          }

          const company = await getCompanyById(companyId, userData);
          if (!company) {
            console.error('Company not found');
            router.push('/login');
            return;
          }

          if (mounted) {
            setCompanyData(company);
          }
        }
      } catch (error) {
        console.error('Error initializing company:', error);
        router.push('/login');
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    initializeCompany();
    return () => {
      mounted = false;
    };
  }, [authLoading, userData, companyId, router]);

  if (!companyData) {
    return null;
  }

  return (
    <CompanyProvider value={companyData}>
      <div className="flex min-h-screen bg-gray-50">
        {!isLocationRoute && <CompanyNav />}
        <div className="flex-1">
          {!isLocationRoute && <LocationSelector />}
          {loading || authLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            children
          )}
        </div>
      </div>
    </CompanyProvider>
  );
}
