'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { CompanyNav } from '@/components/company/CompanyNav';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { Loader2, Plus, Filter, Search, Calendar, CheckCircle, AlertCircle, FileDown } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { NewMovementForm } from './components/NewMovementForm';

// Type definitions
interface Location {
  _id: string;
  name: string;
}

interface InventoryMovement {
  _id: string;
  timestamp: string;
  metadata: {
    companyId: string;
    locationId: string;
    locationName?: string;
    itemId: string;
    itemName?: string;
    itemType: 'RECIPE' | 'INGREDIENT';
    transactionType: 'COUNT' | 'ADJUSTMENT' | 'SALE' | 'RECEIVED' | 'DISPATCHED';
    userId: string;
    userName?: string;
    reference?: string;
  };
  previousStock: number;
  newStock: number;
  difference: number;
  notes?: string;
  uomName?: string;
}

// Helper for transaction type badges
const TransactionTypeBadge = ({ type }: { type: string }) => {
  const variant = {
    'COUNT': 'secondary',
    'ADJUSTMENT': 'default',
    'SALE': 'outline',
    'RECEIVED': 'success',
    'DISPATCHED': 'destructive'
  }[type] || 'default';

  const label = {
    'COUNT': 'Inventory Count',
    'ADJUSTMENT': 'Manual Adjustment',
    'SALE': 'Sale',
    'RECEIVED': 'Stock Received',
    'DISPATCHED': 'Stock Dispatched'
  }[type] || type;

  return <Badge variant={variant as any}>{label}</Badge>;
};

export default function InventoryMovementsPage() {
  useRequireCompanyUser();
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [movements, setMovements] = useState<InventoryMovement[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [newMovementDialogOpen, setNewMovementDialogOpen] = useState(false);
  
  // Filters
  const [locationFilter, setLocationFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch data
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/locations`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setLocations(data.locations || []);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    fetchLocations();
    fetchMovements();
  }, [companyId, page, locationFilter, typeFilter, searchQuery, dateRange]);

  const fetchMovements = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let url = `/api/company/${companyId}/inventory/movement?page=${page}&limit=${itemsPerPage}`;
      
      if (locationFilter) {
        url += `&locationId=${locationFilter}`;
      }
      
      if (typeFilter) {
        url += `&transactionType=${typeFilter}`;
      }
      
      if (searchQuery) {
        url += `&search=${encodeURIComponent(searchQuery)}`;
      }
      
      if (dateRange.from) {
        url += `&startDate=${dateRange.from.toISOString()}`;
      }
      
      if (dateRange.to) {
        url += `&endDate=${dateRange.to.toISOString()}`;
      }
      
      const response = await fetch(url, {
        headers: { 'company-id': companyId }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch inventory movements');
      }
      
      const data = await response.json();
      setMovements(data.transactions || []);
      setTotalPages(Math.ceil(data.total / itemsPerPage));
    } catch (error: any) {
      console.error('Error fetching movements:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNewMovementSuccess = () => {
    setNewMovementDialogOpen(false);
    fetchMovements();
  };

  const exportToCSV = async () => {
    try {
      let url = `/api/company/${companyId}/inventory/movement/export?format=csv`;
      
      if (locationFilter) url += `&locationId=${locationFilter}`;
      if (typeFilter) url += `&transactionType=${typeFilter}`;
      if (searchQuery) url += `&search=${encodeURIComponent(searchQuery)}`;
      if (dateRange.from) url += `&startDate=${dateRange.from.toISOString()}`;
      if (dateRange.to) url += `&endDate=${dateRange.to.toISOString()}`;
      
      // Redirect to export URL
      window.location.href = url;
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <CompanyNav />
      
      <main className="flex-1 container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Inventory Movements</h1>
          <div className="flex gap-2">
            <Button onClick={() => setNewMovementDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              New Movement
            </Button>
            <Button variant="outline" onClick={exportToCSV}>
              <FileDown className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="w-full sm:w-auto">
                <Select value={locationFilter} onValueChange={setLocationFilter}>
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="All Locations" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Locations</SelectItem>
                    {locations.map((location) => (
                      <SelectItem key={location._id} value={location._id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="w-full sm:w-auto">
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="All Transaction Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Types</SelectItem>
                    <SelectItem value="COUNT">Inventory Count</SelectItem>
                    <SelectItem value="ADJUSTMENT">Manual Adjustment</SelectItem>
                    <SelectItem value="SALE">Sale</SelectItem>
                    <SelectItem value="RECEIVED">Stock Received</SelectItem>
                    <SelectItem value="DISPATCHED">Stock Dispatched</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full sm:w-[240px] justify-start text-left font-normal">
                    <Calendar className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Date Range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="center">
                  <CalendarComponent
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={setDateRange}
                    numberOfMonths={2}
                  />
                  <div className="flex justify-end gap-2 p-3 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDateRange({ from: undefined, to: undefined })}
                    >
                      Clear
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
              
              <div className="flex-1 relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by item name or reference..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Movements Table */}
        <Card>
          <CardHeader>
            <CardTitle>Movement History</CardTitle>
            <CardDescription>
              Record of all inventory changes
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : error ? (
              <div className="bg-destructive/10 border border-destructive p-4 rounded-md text-destructive flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                {error}
              </div>
            ) : movements.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No inventory movements found matching your criteria
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date & Time</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Item</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Previous</TableHead>
                      <TableHead className="text-right">Change</TableHead>
                      <TableHead className="text-right">New Stock</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Reference</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {movements.map((movement) => (
                      <TableRow key={movement._id}>
                        <TableCell>
                          {format(new Date(movement.timestamp), 'MMM d, yyyy h:mm a')}
                        </TableCell>
                        <TableCell>{movement.metadata.locationName}</TableCell>
                        <TableCell className="font-medium">
                          {movement.metadata.itemName}
                          <div className="text-xs text-muted-foreground">
                            {movement.metadata.itemType === 'RECIPE' ? 'Recipe' : 'Ingredient'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <TransactionTypeBadge type={movement.metadata.transactionType} />
                        </TableCell>
                        <TableCell className="text-right">
                          {movement.previousStock} {movement.uomName}
                        </TableCell>
                        <TableCell className="text-right">
                          <span className={
                            movement.difference > 0 
                              ? "text-green-600" 
                              : movement.difference < 0 
                              ? "text-red-600" 
                              : ""
                          }>
                            {movement.difference > 0 ? '+' : ''}{movement.difference} {movement.uomName}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {movement.newStock} {movement.uomName}
                        </TableCell>
                        <TableCell>{movement.metadata.userName}</TableCell>
                        <TableCell>
                          {movement.metadata.reference || (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              Page {page} of {totalPages}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setPage(p => p + 1)}
                disabled={page === totalPages}
              >
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>
      </main>

      {/* New Movement Dialog */}
      <Dialog open={newMovementDialogOpen} onOpenChange={setNewMovementDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>New Inventory Movement</DialogTitle>
            <DialogDescription>
              Record a new inventory transaction such as receipt or adjustment
            </DialogDescription>
          </DialogHeader>
          <NewMovementForm 
            companyId={companyId} 
            locations={locations}
            onSuccess={handleNewMovementSuccess}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
