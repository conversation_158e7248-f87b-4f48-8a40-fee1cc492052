'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, AlertCircle } from "lucide-react";
import { DialogFooter } from "@/components/ui/dialog";

// Type definitions
interface Location {
  _id: string;
  name: string;
}

interface InventoryItem {
  _id: string;
  itemName: string;
  itemType: 'RECIPE' | 'INGREDIENT';
  currentStock: number;
  baseUomId: string;
  uomName?: string;
  itemCategory?: string;
}

interface UOM {
  _id: string;
  name: string;
  shortCode: string;
}

interface NewMovementFormProps {
  companyId: string;
  locations: Location[];
  onSuccess: () => void;
}

export function NewMovementForm({ companyId, locations, onSuccess }: NewMovementFormProps) {
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    locationId: '',
    itemId: '',
    transactionType: '',
    quantity: '',
    notes: '',
    reference: ''
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [uoms, setUoms] = useState<UOM[]>([]);
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Load items when location changes
  useEffect(() => {
    if (!formData.locationId) {
      setItems([]);
      setFilteredItems([]);
      return;
    }

    const fetchInventoryItems = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/inventory/current?locationId=${formData.locationId}`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setItems(data.items || []);
          setFilteredItems(data.items || []);
        }
      } catch (error) {
        console.error('Error fetching inventory items:', error);
      }
    };

    const fetchUOMs = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/uoms`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setUoms(data.uoms || []);
        }
      } catch (error) {
        console.error('Error fetching UOMs:', error);
      }
    };

    fetchInventoryItems();
    fetchUOMs();
  }, [companyId, formData.locationId]);

  // Filter items when search query changes
  useEffect(() => {
    if (!searchQuery) {
      setFilteredItems(items);
      return;
    }
    
    const filtered = items.filter(item => 
      item.itemName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.itemCategory && item.itemCategory.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    
    setFilteredItems(filtered);
  }, [searchQuery, items]);

  // Get UOM name
  const getUomName = (uomId: string) => {
    const uom = uoms.find(u => u._id === uomId);
    return uom ? uom.shortCode || uom.name : '';
  };

  // Update form data
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // If item is selected, find and set the selected item
    if (name === 'itemId') {
      const item = items.find(i => i._id === value);
      setSelectedItem(item || null);
    }
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validate form
    if (!formData.locationId || !formData.itemId || !formData.transactionType || !formData.quantity) {
      setError('Please fill in all required fields');
      setLoading(false);
      return;
    }

    // Validate quantity
    const quantity = parseFloat(formData.quantity);
    if (isNaN(quantity) || quantity <= 0) {
      setError('Quantity must be a positive number');
      setLoading(false);
      return;
    }

    // For DISPATCHED type, make sure quantity doesn't exceed current stock
    if (formData.transactionType === 'DISPATCHED' && selectedItem && quantity > selectedItem.currentStock) {
      setError(`Cannot dispatch more than current stock (${selectedItem.currentStock})`);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/company/${companyId}/inventory/movement`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify({
          locationId: formData.locationId,
          itemId: formData.itemId,
          transactionType: formData.transactionType,
          quantity: quantity,
          notes: formData.notes,
          reference: formData.reference
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create inventory movement');
      }

      // Reset form and notify success
      setFormData({
        locationId: '',
        itemId: '',
        transactionType: '',
        quantity: '',
        notes: '',
        reference: ''
      });
      
      onSuccess();
    } catch (error: any) {
      console.error('Error creating movement:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <div className="bg-destructive/10 border border-destructive p-3 rounded-md text-destructive flex items-center mb-4">
          <AlertCircle className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}
      
      <div className="grid gap-4 py-4">
        {/* Location */}
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="locationId" className="text-right">
            Location <span className="text-destructive">*</span>
          </Label>
          <div className="col-span-3">
            <Select
              value={formData.locationId}
              onValueChange={value => handleSelectChange('locationId', value)}
            >
              <SelectTrigger id="locationId">
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location._id} value={location._id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Transaction Type */}
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="transactionType" className="text-right">
            Movement Type <span className="text-destructive">*</span>
          </Label>
          <div className="col-span-3">
            <Select
              value={formData.transactionType}
              onValueChange={value => handleSelectChange('transactionType', value)}
            >
              <SelectTrigger id="transactionType">
                <SelectValue placeholder="Select movement type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="RECEIVED">Stock Received</SelectItem>
                <SelectItem value="DISPATCHED">Stock Dispatched</SelectItem>
                <SelectItem value="ADJUSTMENT">Manual Adjustment</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Item Search */}
        {formData.locationId && (
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="itemSearch" className="text-right">
              Search Items
            </Label>
            <div className="col-span-3">
              <Input
                id="itemSearch"
                placeholder="Search by name or category"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        )}
        
        {/* Item Selection */}
        {formData.locationId && (
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="itemId" className="text-right">
              Item <span className="text-destructive">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                value={formData.itemId}
                onValueChange={value => handleSelectChange('itemId', value)}
              >
                <SelectTrigger id="itemId">
                  <SelectValue placeholder="Select inventory item" />
                </SelectTrigger>
                <SelectContent>
                  {filteredItems.map((item) => (
                    <SelectItem key={item._id} value={item._id}>
                      {item.itemName} ({item.itemType === 'RECIPE' ? 'Recipe' : 'Ingredient'}) - {item.currentStock} {getUomName(item.baseUomId)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
        
        {/* Quantity */}
        {formData.itemId && (
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="quantity" className="text-right">
              Quantity <span className="text-destructive">*</span>
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                type="number"
                id="quantity"
                name="quantity"
                placeholder="Enter quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                step="0.01"
                min={0}
              />
              <span className="text-muted-foreground">
                {selectedItem && getUomName(selectedItem.baseUomId)}
              </span>
            </div>
          </div>
        )}
        
        {/* Reference */}
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="reference" className="text-right">
            Reference
          </Label>
          <div className="col-span-3">
            <Input
              id="reference"
              name="reference"
              placeholder="Invoice #, PO #, etc."
              value={formData.reference}
              onChange={handleInputChange}
            />
          </div>
        </div>
        
        {/* Notes */}
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="notes" className="text-right">
            Notes
          </Label>
          <div className="col-span-3">
            <Textarea
              id="notes"
              name="notes"
              placeholder="Add notes about this transaction"
              value={formData.notes}
              onChange={handleInputChange}
              className="min-h-[80px]"
            />
          </div>
        </div>
      </div>
      
      <DialogFooter>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            'Create Movement'
          )}
        </Button>
      </DialogFooter>
    </form>
  );
}
