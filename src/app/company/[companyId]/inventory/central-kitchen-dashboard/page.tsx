'use client';

import React, { use<PERSON>tate, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { CompanyNav } from '@/components/company/CompanyNav';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Loader2, AlertCircle, TrendingDown, TrendingUp, Search, Package, Clock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend
} from "recharts";

// Define types
interface Location {
  _id: string;
  name: string;
}

interface ParLevelItem {
  inventoryId: string;
  itemId: string;
  itemName: string;
  locationId: string;
  locationName: string;
  itemType: 'RECIPE' | 'INGREDIENT';
  category: string;
  currentStock: number;
  branchParLevel: number;
  centralKitchenParLevel: number;
  centralKitchenStock: number;
  baseUOM: string;
  belowBranchPar: boolean;
  belowCentralPar: boolean;
  reorderRecommended: boolean;
  leadTimeDays?: number;
  minOrderQuantity?: number;
  maxOrderQuantity?: number;
  isOrderable?: boolean;
}

interface CentralKitchenSummary {
  totalItems: number;
  itemsBelowBranchPar: number;
  itemsBelowCentralPar: number;
  reorderRecommendations: number;
  orderableItems: number;
  outOfStockCentral: number;
  locationBreakdown: {
    location: string;
    belowPar: number;
    reorderRecommended: number;
    totalItems: number;
  }[];
  categoryBreakdown: {
    category: string;
    belowPar: number;
    totalItems: number;
  }[];
}

export default function CentralKitchenDashboardPage() {
  useRequireCompanyUser();
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [locations, setLocations] = useState<Location[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [parLevelItems, setParLevelItems] = useState<ParLevelItem[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [itemTypeFilter, setItemTypeFilter] = useState<string>('ALL');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [summary, setSummary] = useState<CentralKitchenSummary | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Fetch data
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/locations`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setLocations(data.locations || []);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    fetchLocations();
  }, [companyId]);

  // Fetch par level data
  useEffect(() => {
    const fetchParLevels = async () => {
      setLoading(true);
      setError(null);
      
      try {
        let url = `/api/company/${companyId}/par-levels`;
        
        if (selectedLocation) {
          url += `?locationId=${selectedLocation}`;
        }
        
        const response = await fetch(url, {
          headers: { 'company-id': companyId }
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch par level data');
        }
        
        const data = await response.json();
        setParLevelItems(data.data || []);
        
        // Generate summary statistics
        generateSummary(data.data || [], data.summary);
      } catch (error: any) {
        console.error('Error fetching par levels:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchParLevels();
    }
  }, [companyId, selectedLocation]);

  // Generate summary statistics
  const generateSummary = (items: ParLevelItem[], apiSummary?: any) => {
    const totalItems = items.length;
    const itemsBelowBranchPar = items.filter(item => item.belowBranchPar).length;
    const itemsBelowCentralPar = items.filter(item => item.belowCentralPar).length;
    const reorderRecommendations = items.filter(item => item.reorderRecommended).length;
    const orderableItems = items.filter(item => item.isOrderable !== false).length;
    const outOfStockCentral = items.filter(item => item.centralKitchenStock <= 0).length;
    
    // Group by location
    const locationMap = new Map<string, {
      belowPar: number;
      reorderRecommended: number;
      totalItems: number;
    }>();
    
    items.forEach(item => {
      const locationName = item.locationName || 'Unknown';
      const current = locationMap.get(locationName) || {
        belowPar: 0,
        reorderRecommended: 0,
        totalItems: 0
      };
      
      if (item.belowBranchPar) current.belowPar++;
      if (item.reorderRecommended) current.reorderRecommended++;
      current.totalItems++;
      
      locationMap.set(locationName, current);
    });
    
    const locationBreakdown = Array.from(locationMap.entries()).map(([location, data]) => ({
      location,
      ...data
    }));
    
    // Group by category
    const categoryMap = new Map<string, {
      belowPar: number;
      totalItems: number;
    }>();
    
    items.forEach(item => {
      const category = item.category || 'Uncategorized';
      const current = categoryMap.get(category) || {
        belowPar: 0,
        totalItems: 0
      };
      
      if (item.belowBranchPar) current.belowPar++;
      current.totalItems++;
      
      categoryMap.set(category, current);
    });
    
    const categoryBreakdown = Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      ...data
    }));
    
    setSummary({
      totalItems,
      itemsBelowBranchPar,
      itemsBelowCentralPar,
      reorderRecommendations,
      orderableItems,
      outOfStockCentral,
      locationBreakdown,
      categoryBreakdown
    });
  };

  // Filter items based on search and filters
  const filteredItems = parLevelItems.filter(item => {
    // Search filter
    if (searchQuery && !item.itemName.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Item type filter
    if (itemTypeFilter !== 'ALL' && item.itemType !== itemTypeFilter) {
      return false;
    }
    
    // Status filter
    if (statusFilter === 'BELOW_PAR' && !item.belowBranchPar) return false;
    if (statusFilter === 'REORDER_RECOMMENDED' && !item.reorderRecommended) return false;
    if (statusFilter === 'OUT_OF_STOCK_CENTRAL' && item.centralKitchenStock > 0) return false;
    
    return true;
  });

  // Handle bulk par level update
  const handleBulkParLevelUpdate = async (locationId: string, branchParLevel?: number, centralParLevel?: number) => {
    try {
      const response = await fetch(`/api/company/${companyId}/par-levels`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify({
          locationId,
          defaultBranchParLevel: branchParLevel,
          defaultCentralParLevel: centralParLevel
        })
      });

      if (response.ok) {
        // Refresh data
        window.location.reload();
      } else {
        const errorData = await response.json();
        alert('Error updating par levels: ' + errorData.error);
      }
    } catch (error) {
      console.error('Error updating par levels:', error);
      alert('Error updating par levels');
    }
  };

  // Render par level status badge
  const ParLevelStatusBadge = ({ item }: { item: ParLevelItem }) => {
    if (item.centralKitchenStock <= 0) {
      return <Badge variant="destructive">Out of Stock (Central)</Badge>;
    }
    if (item.reorderRecommended) {
      return <Badge variant="default">Reorder Recommended</Badge>;
    }
    if (item.belowBranchPar) {
      return <Badge variant="secondary">Below Branch Par</Badge>;
    }
    if (item.belowCentralPar) {
      return <Badge variant="outline">Below Central Par</Badge>;
    }
    return <Badge variant="success">Healthy</Badge>;
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <CompanyNav />
      
      <main className="flex-1 container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Central Kitchen Dashboard</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push(`/company/${companyId}/inventory/dashboard`)}>
              Standard Dashboard
            </Button>
            <Button variant="outline" onClick={() => router.push(`/company/${companyId}/delivery-notes`)}>
              Delivery Notes
            </Button>
          </div>
        </div>

        {/* Location Selector */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger className="w-[220px]">
                  <SelectValue placeholder="All Locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Locations</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location._id} value={location._id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <div className="flex-1 relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search inventory items..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dashboard Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="par-levels">Par Level Management</TabsTrigger>
            <TabsTrigger value="reorder">Reorder Recommendations</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="bg-destructive/10 border border-destructive p-4 rounded-md text-destructive flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          ) : (
            <>
              {/* Overview Tab */}
              <TabsContent value="overview">
                {summary && (
                  <>
                    {/* Summary Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center">
                            <Package className="mr-2 h-5 w-5" />
                            Total Items
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.totalItems}</div>
                          <div className="text-sm text-muted-foreground">
                            {summary.orderableItems} orderable
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center text-amber-600">
                            <TrendingDown className="mr-2 h-5 w-5" />
                            Below Branch Par
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.itemsBelowBranchPar}</div>
                          <div className="text-sm text-muted-foreground">
                            {summary.totalItems > 0 
                              ? `${Math.round((summary.itemsBelowBranchPar / summary.totalItems) * 100)}% of inventory`
                              : '0% of inventory'}
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center text-red-600">
                            <AlertCircle className="mr-2 h-5 w-5" />
                            Central Kitchen Low
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.itemsBelowCentralPar}</div>
                          <div className="text-sm text-muted-foreground">
                            {summary.outOfStockCentral} out of stock
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center text-blue-600">
                            <Clock className="mr-2 h-5 w-5" />
                            Reorder Recommended
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.reorderRecommendations}</div>
                          <div className="text-sm text-muted-foreground">
                            Items needing orders
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* Charts */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Par Level Status Distribution</CardTitle>
                          <CardDescription>
                            Overview of inventory health vs par levels
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={[
                                    { name: 'Below Branch Par', value: summary.itemsBelowBranchPar },
                                    { name: 'Below Central Par', value: summary.itemsBelowCentralPar },
                                    { name: 'Reorder Needed', value: summary.reorderRecommendations },
                                    { name: 'Healthy', value: summary.totalItems - summary.itemsBelowBranchPar - summary.itemsBelowCentralPar }
                                  ]}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={false}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={80}
                                  fill="#8884d8"
                                  dataKey="value"
                                >
                                  <Cell fill="#fb923c" /> {/* Below Branch Par */}
                                  <Cell fill="#f43f5e" /> {/* Below Central Par */}
                                  <Cell fill="#8b5cf6" /> {/* Reorder Needed */}
                                  <Cell fill="#22c55e" /> {/* Healthy */}
                                </Pie>
                                <Tooltip />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <CardTitle>Par Level Issues by Location</CardTitle>
                          <CardDescription>
                            Items below par level by location
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={summary.locationBreakdown}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="location" />
                                <YAxis />
                                <Tooltip />
                                <Legend />
                                <Bar dataKey="belowPar" fill="#fb923c" name="Below Par" />
                                <Bar dataKey="reorderRecommended" fill="#8b5cf6" name="Reorder Recommended" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </>
                )}
              </TabsContent>
              
              {/* Par Levels Tab */}
              <TabsContent value="par-levels">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Par Level Management</CardTitle>
                      <CardDescription>
                        Manage branch and central kitchen par levels
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Select value={itemTypeFilter} onValueChange={setItemTypeFilter}>
                        <SelectTrigger className="w-[160px]">
                          <SelectValue placeholder="All Types" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Types</SelectItem>
                          <SelectItem value="INGREDIENT">Ingredients</SelectItem>
                          <SelectItem value="RECIPE">Recipes</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[200px]">
                          <SelectValue placeholder="All Items" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Items</SelectItem>
                          <SelectItem value="BELOW_PAR">Below Branch Par</SelectItem>
                          <SelectItem value="REORDER_RECOMMENDED">Reorder Recommended</SelectItem>
                          <SelectItem value="OUT_OF_STOCK_CENTRAL">Central Out of Stock</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Item</TableHead>
                            <TableHead>Location</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Current Stock</TableHead>
                            <TableHead>Branch Par</TableHead>
                            <TableHead>Central Stock</TableHead>
                            <TableHead>Central Par</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredItems.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                                No items found matching your criteria
                              </TableCell>
                            </TableRow>
                          ) : (
                            filteredItems.map((item) => (
                              <TableRow key={item.inventoryId}>
                                <TableCell className="font-medium">{item.itemName}</TableCell>
                                <TableCell>{item.locationName}</TableCell>
                                <TableCell>{item.itemType === 'RECIPE' ? 'Recipe' : 'Ingredient'}</TableCell>
                                <TableCell className="text-right">
                                  {item.currentStock} {item.baseUOM}
                                </TableCell>
                                <TableCell className="text-right">
                                  {item.branchParLevel} {item.baseUOM}
                                </TableCell>
                                <TableCell className="text-right">
                                  {item.centralKitchenStock} {item.baseUOM}
                                </TableCell>
                                <TableCell className="text-right">
                                  {item.centralKitchenParLevel} {item.baseUOM}
                                </TableCell>
                                <TableCell>
                                  <ParLevelStatusBadge item={item} />
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              {/* Reorder Tab */}
              <TabsContent value="reorder">
                <Card>
                  <CardHeader>
                    <CardTitle>Reorder Recommendations</CardTitle>
                    <CardDescription>
                      Items that need to be ordered from central kitchen
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Item</TableHead>
                            <TableHead>Location</TableHead>
                            <TableHead>Current Stock</TableHead>
                            <TableHead>Par Level</TableHead>
                            <TableHead>Central Available</TableHead>
                            <TableHead>Suggested Order</TableHead>
                            <TableHead>Lead Time</TableHead>
                            <TableHead>Action</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {parLevelItems
                            .filter(item => item.reorderRecommended)
                            .map((item) => {
                              const suggestedOrder = Math.max(
                                item.branchParLevel - item.currentStock,
                                item.minOrderQuantity || 0
                              );
                              
                              return (
                                <TableRow key={item.inventoryId}>
                                  <TableCell className="font-medium">{item.itemName}</TableCell>
                                  <TableCell>{item.locationName}</TableCell>
                                  <TableCell className="text-right">
                                    {item.currentStock} {item.baseUOM}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    {item.branchParLevel} {item.baseUOM}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    {item.centralKitchenStock} {item.baseUOM}
                                  </TableCell>
                                  <TableCell className="text-right font-semibold">
                                    {suggestedOrder} {item.baseUOM}
                                  </TableCell>
                                  <TableCell>
                                    {item.leadTimeDays ? `${item.leadTimeDays} days` : 'N/A'}
                                  </TableCell>
                                  <TableCell>
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      onClick={() => router.push(`/company/${companyId}/branches/${item.locationId}/ordering-catalog`)}
                                    >
                                      Create Order
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          {parLevelItems.filter(item => item.reorderRecommended).length === 0 && (
                            <TableRow>
                              <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                                No reorder recommendations at this time
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              {/* Analytics Tab */}
              <TabsContent value="analytics">
                <Card>
                  <CardHeader>
                    <CardTitle>Par Level Analytics</CardTitle>
                    <CardDescription>
                      Analysis of inventory health by category
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-96">
                      {summary && (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={summary.categoryBreakdown}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="category" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="totalItems" fill="#3b82f6" name="Total Items" />
                            <Bar dataKey="belowPar" fill="#fb923c" name="Below Par" />
                          </BarChart>
                        </ResponsiveContainer>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </>
          )}
        </Tabs>
      </main>
    </div>
  );
}