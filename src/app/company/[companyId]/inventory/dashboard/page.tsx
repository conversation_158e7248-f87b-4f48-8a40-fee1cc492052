'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { CompanyNav } from '@/components/company/CompanyNav';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Loader2, AlertCircle, TrendingDown, ArrowUpDown, TrendingUp, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from "recharts";

// Define types
interface Location {
  _id: string;
  name: string;
}

interface InventoryItem {
  _id: string;
  itemId: string;
  locationId: string;
  locationName?: string;
  itemName: string;
  itemType: 'RECIPE' | 'INGREDIENT';
  currentStock: number;
  lastUpdated: string;
  baseUomId: string;
  uomName?: string;
  itemCategory?: string;
  stockStatus: 'LOW' | 'NORMAL' | 'EXCESS' | 'OUT';
  nearExpiry?: boolean;
  expiryDate?: string;
  daysOfSupply?: number;
}

interface InventorySummary {
  totalItems: number;
  outOfStock: number;
  lowStock: number;
  normalStock: number;
  excessStock: number;
  nearExpiry: number;
  categoryBreakdown: {
    category: string;
    count: number;
    value: number;
  }[];
  locationBreakdown: {
    location: string;
    outOfStock: number;
    lowStock: number;
    normalStock: number;
    excessStock: number;
  }[];
}

export default function InventoryDashboardPage() {
  useRequireCompanyUser();
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [locations, setLocations] = useState<Location[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [itemTypeFilter, setItemTypeFilter] = useState<string>('ALL');
  const [stockStatusFilter, setStockStatusFilter] = useState<string>('ALL');
  const [summary, setSummary] = useState<InventorySummary | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Fetch data
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/locations`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setLocations(data.locations || []);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    fetchLocations();
  }, [companyId]);

  // Fetch inventory data when location changes
  useEffect(() => {
    const fetchInventory = async () => {
      setLoading(true);
      setError(null);
      
      try {
        let url = `/api/company/${companyId}/inventory/current`;
        
        if (selectedLocation) {
          url += `?locationId=${selectedLocation}`;
        }
        
        const response = await fetch(url, {
          headers: { 'company-id': companyId }
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch inventory data');
        }
        
        const data = await response.json();
        
        // Add derived fields
        const enhancedItems = data.items.map((item: any) => ({
          ...item,
          stockStatus: determineStockStatus(item)
        }));
        
        setInventoryItems(enhancedItems);
        
        // Generate summary statistics
        generateSummary(enhancedItems);
      } catch (error: any) {
        console.error('Error fetching inventory:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchInventory();
    }
  }, [companyId, selectedLocation]);

  // Determine stock status based on current stock
  const determineStockStatus = (item: any): 'LOW' | 'NORMAL' | 'EXCESS' | 'OUT' => {
    // In a real app, this would use par levels, min/max settings, etc.
    // For now, using simple rules
    if (item.currentStock <= 0) return 'OUT';
    if (item.currentStock < 5) return 'LOW';
    if (item.currentStock > 50) return 'EXCESS';
    return 'NORMAL';
  };

  // Generate summary statistics
  const generateSummary = (items: InventoryItem[]) => {
    const totalItems = items.length;
    const outOfStock = items.filter(item => item.stockStatus === 'OUT').length;
    const lowStock = items.filter(item => item.stockStatus === 'LOW').length;
    const normalStock = items.filter(item => item.stockStatus === 'NORMAL').length;
    const excessStock = items.filter(item => item.stockStatus === 'EXCESS').length;
    
    // Group by category
    const categoryMap = new Map<string, { count: number; value: number }>();
    items.forEach(item => {
      const category = item.itemCategory || 'Uncategorized';
      const current = categoryMap.get(category) || { count: 0, value: 0 };
      categoryMap.set(category, {
        count: current.count + 1,
        // In a real app, we'd use actual value based on cost
        value: current.value + item.currentStock
      });
    });
    
    const categoryBreakdown = Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      count: data.count,
      value: data.value
    }));
    
    // Group by location
    const locationMap = new Map<string, {
      outOfStock: number;
      lowStock: number;
      normalStock: number;
      excessStock: number;
    }>();
    
    items.forEach(item => {
      const locationName = item.locationName || 'Unknown';
      const current = locationMap.get(locationName) || {
        outOfStock: 0,
        lowStock: 0,
        normalStock: 0,
        excessStock: 0
      };
      
      if (item.stockStatus === 'OUT') current.outOfStock++;
      else if (item.stockStatus === 'LOW') current.lowStock++;
      else if (item.stockStatus === 'NORMAL') current.normalStock++;
      else if (item.stockStatus === 'EXCESS') current.excessStock++;
      
      locationMap.set(locationName, current);
    });
    
    const locationBreakdown = Array.from(locationMap.entries()).map(([location, data]) => ({
      location,
      ...data
    }));
    
    setSummary({
      totalItems,
      outOfStock,
      lowStock,
      normalStock,
      excessStock,
      nearExpiry: 0, // We'd calculate this in a real app
      categoryBreakdown,
      locationBreakdown
    });
  };

  // Filter inventory items based on search and filters
  const filteredItems = inventoryItems.filter(item => {
    // Search filter
    if (searchQuery && !item.itemName.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Item type filter
    if (itemTypeFilter !== 'ALL' && item.itemType !== itemTypeFilter) {
      return false;
    }
    
    // Stock status filter
    if (stockStatusFilter !== 'ALL' && item.stockStatus !== stockStatusFilter) {
      return false;
    }
    
    return true;
  });

  // Render stock status badge
  const StockStatusBadge = ({ status }: { status: string }) => {
    const variant = {
      'OUT': 'destructive',
      'LOW': 'warning',
      'NORMAL': 'success',
      'EXCESS': 'secondary'
    }[status] || 'default';

    return <Badge variant={variant as any}>{status}</Badge>;
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <CompanyNav />
      
      <main className="flex-1 container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Inventory Dashboard</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push(`/company/${companyId}/inventory-count`)}>
              Inventory Counts
            </Button>
            <Button variant="outline" onClick={() => router.push(`/company/${companyId}/inventory/movements`)}>
              Inventory Movements
            </Button>
          </div>
        </div>

        {/* Location Selector */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger className="w-[220px]">
                  <SelectValue placeholder="All Locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Locations</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location._id} value={location._id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <div className="flex-1 relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search inventory items..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dashboard Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="items">Inventory Items</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="bg-destructive/10 border border-destructive p-4 rounded-md text-destructive flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          ) : (
            <>
              {/* Overview Tab */}
              <TabsContent value="overview">
                {summary && (
                  <>
                    {/* Summary Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl">Total Items</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.totalItems}</div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center text-amber-600">
                            <TrendingDown className="mr-2 h-5 w-5" />
                            Low Stock
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.lowStock}</div>
                          <div className="text-sm text-muted-foreground">
                            {summary.totalItems > 0 
                              ? `${Math.round((summary.lowStock / summary.totalItems) * 100)}% of inventory`
                              : '0% of inventory'}
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center text-red-600">
                            <AlertCircle className="mr-2 h-5 w-5" />
                            Out of Stock
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.outOfStock}</div>
                          <div className="text-sm text-muted-foreground">
                            {summary.totalItems > 0 
                              ? `${Math.round((summary.outOfStock / summary.totalItems) * 100)}% of inventory`
                              : '0% of inventory'}
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center text-blue-600">
                            <TrendingUp className="mr-2 h-5 w-5" />
                            Excess Stock
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold">{summary.excessStock}</div>
                          <div className="text-sm text-muted-foreground">
                            {summary.totalItems > 0 
                              ? `${Math.round((summary.excessStock / summary.totalItems) * 100)}% of inventory`
                              : '0% of inventory'}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* Charts */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Inventory Status Distribution</CardTitle>
                          <CardDescription>
                            Overview of inventory health by status
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={[
                                    { name: 'Out of Stock', value: summary.outOfStock },
                                    { name: 'Low Stock', value: summary.lowStock },
                                    { name: 'Normal', value: summary.normalStock },
                                    { name: 'Excess', value: summary.excessStock }
                                  ]}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={false}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={80}
                                  fill="#8884d8"
                                  dataKey="value"
                                >
                                  <Cell fill="#f43f5e" /> {/* Out of Stock */}
                                  <Cell fill="#fb923c" /> {/* Low Stock */}
                                  <Cell fill="#22c55e" /> {/* Normal */}
                                  <Cell fill="#3b82f6" /> {/* Excess */}
                                </Pie>
                                <Tooltip />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <CardTitle>Inventory by Category</CardTitle>
                          <CardDescription>
                            Distribution of inventory across categories
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={summary.categoryBreakdown}
                                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="category" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="count" fill="#8884d8" name="Number of Items" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    {/* Top Items Needing Attention */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Items Needing Attention</CardTitle>
                        <CardDescription>
                          Items that are out of stock or low in inventory
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="rounded-md border">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Item</TableHead>
                                <TableHead>Location</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Current Stock</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {inventoryItems
                                .filter(item => item.stockStatus === 'OUT' || item.stockStatus === 'LOW')
                                .slice(0, 5)
                                .map((item) => (
                                  <TableRow key={item._id}>
                                    <TableCell className="font-medium">{item.itemName}</TableCell>
                                    <TableCell>{item.locationName}</TableCell>
                                    <TableCell>{item.itemCategory || 'N/A'}</TableCell>
                                    <TableCell>{item.itemType === 'RECIPE' ? 'Recipe' : 'Ingredient'}</TableCell>
                                    <TableCell>
                                      <StockStatusBadge status={item.stockStatus} />
                                    </TableCell>
                                    <TableCell className="text-right">
                                      {item.currentStock} {item.uomName}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              {inventoryItems.filter(item => item.stockStatus === 'OUT' || item.stockStatus === 'LOW').length === 0 && (
                                <TableRow>
                                  <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                                    All items are in stock and at healthy levels
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </div>
                        
                        <div className="mt-4 text-right">
                          <Button 
                            variant="link"
                            onClick={() => {
                              setActiveTab('items');
                              setStockStatusFilter('LOW');
                            }}
                          >
                            View all low stock items
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}
              </TabsContent>
              
              {/* Items Tab */}
              <TabsContent value="items">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Inventory Items</CardTitle>
                      <CardDescription>
                        All inventory items across locations
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Select value={itemTypeFilter} onValueChange={setItemTypeFilter}>
                        <SelectTrigger className="w-[160px]">
                          <SelectValue placeholder="All Types" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Types</SelectItem>
                          <SelectItem value="INGREDIENT">Ingredients</SelectItem>
                          <SelectItem value="RECIPE">Recipes</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Select value={stockStatusFilter} onValueChange={setStockStatusFilter}>
                        <SelectTrigger className="w-[160px]">
                          <SelectValue placeholder="All Statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Statuses</SelectItem>
                          <SelectItem value="OUT">Out of Stock</SelectItem>
                          <SelectItem value="LOW">Low Stock</SelectItem>
                          <SelectItem value="NORMAL">Normal</SelectItem>
                          <SelectItem value="EXCESS">Excess</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Item</TableHead>
                            <TableHead>Location</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Current Stock</TableHead>
                            <TableHead>Last Updated</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredItems.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                                No inventory items found matching your criteria
                              </TableCell>
                            </TableRow>
                          ) : (
                            filteredItems.map((item) => (
                              <TableRow key={item._id}>
                                <TableCell className="font-medium">{item.itemName}</TableCell>
                                <TableCell>{item.locationName}</TableCell>
                                <TableCell>{item.itemCategory || 'N/A'}</TableCell>
                                <TableCell>{item.itemType === 'RECIPE' ? 'Recipe' : 'Ingredient'}</TableCell>
                                <TableCell>
                                  <StockStatusBadge status={item.stockStatus} />
                                </TableCell>
                                <TableCell className="text-right">
                                  {item.currentStock} {item.uomName}
                                </TableCell>
                                <TableCell>
                                  {new Date(item.lastUpdated).toLocaleDateString()}
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <div className="text-sm text-muted-foreground">
                      Showing {filteredItems.length} of {inventoryItems.length} items
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>
              
              {/* Analytics Tab */}
              <TabsContent value="analytics">
                <Card>
                  <CardHeader>
                    <CardTitle>Stock Health by Location</CardTitle>
                    <CardDescription>
                      Comparison of inventory health across locations
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-96">
                      {summary && (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={summary.locationBreakdown}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="location" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="outOfStock" stackId="a" fill="#f43f5e" name="Out of Stock" />
                            <Bar dataKey="lowStock" stackId="a" fill="#fb923c" name="Low Stock" />
                            <Bar dataKey="normalStock" stackId="a" fill="#22c55e" name="Normal" />
                            <Bar dataKey="excessStock" stackId="a" fill="#3b82f6" name="Excess" />
                          </BarChart>
                        </ResponsiveContainer>
                      )}
                    </div>
                  </CardContent>
                </Card>
                
                <div className="mt-6 text-center text-muted-foreground">
                  <p>More analytics features coming soon, including:</p>
                  <ul className="mt-2 list-disc list-inside text-left max-w-md mx-auto">
                    <li>Stock turnover rates</li>
                    <li>Inventory valuation</li>
                    <li>Consumption trends</li>
                    <li>Variance analysis</li>
                    <li>Forecasting and demand prediction</li>
                  </ul>
                </div>
              </TabsContent>
            </>
          )}
        </Tabs>
      </main>
    </div>
  );
}
