'use client';

import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Plus, Minus, Trash2, ShoppingCart, Calendar, AlertCircle } from 'lucide-react';

interface CartItem {
  id: string;
  itemId: string;
  itemType: string;
  name: string;
  description?: string;
  category?: string;
  cartQuantity: number;
  minOrderQuantity: number;
  maxOrderQuantity: number;
  orderingUOM: string;
  leadTimeDays: number;
  estimatedCost: number;
  baseUom: {
    name: string;
    shortCode: string;
  };
}

export default function ShoppingCartPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const companyId = params.companyId as string;

  const [cart, setCart] = useState<CartItem[]>([]);
  const [orderNotes, setOrderNotes] = useState('');
  const [requestedDeliveryDate, setRequestedDeliveryDate] = useState('');
  const [priority, setPriority] = useState<'NORMAL' | 'URGENT' | 'LOW'>('NORMAL');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem(`shop-cart-${companyId}`);
    if (savedCart) {
      try {
        const cartData = JSON.parse(savedCart);
        setCart(Array.isArray(cartData) ? cartData : []);
      } catch (error) {
        console.error('Failed to parse cart data:', error);
        setCart([]);
      }
    }
  }, [companyId]);

  // Save cart to localStorage when cart changes
  useEffect(() => {
    if (cart.length > 0) {
      localStorage.setItem(`shop-cart-${companyId}`, JSON.stringify(cart));
    } else {
      localStorage.removeItem(`shop-cart-${companyId}`);
    }
  }, [cart, companyId]);

  const updateQuantity = (itemId: string, newQuantity: number) => {
    setCart(prevCart => 
      prevCart.map(item => {
        if (item.id === itemId) {
          const clampedQuantity = Math.max(
            item.minOrderQuantity,
            Math.min(newQuantity, item.maxOrderQuantity)
          );
          return { ...item, cartQuantity: clampedQuantity };
        }
        return item;
      })
    );
  };

  const removeItem = (itemId: string) => {
    setCart(prevCart => prevCart.filter(item => item.id !== itemId));
    toast({
      title: 'Item Removed',
      description: 'Item removed from cart',
    });
  };

  const clearCart = () => {
    setCart([]);
    toast({
      title: 'Cart Cleared',
      description: 'All items removed from cart',
    });
  };

  const submitOrder = async () => {
    if (cart.length === 0) {
      toast({
        title: 'Empty Cart',
        description: 'Please add items to your cart before placing an order',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const orderItems = cart.map(item => ({
        itemId: item.itemId,
        branchInventoryId: item.id,
        quantity: item.cartQuantity,
        unitPrice: item.estimatedCost,
        notes: ''
      }));

      const response = await fetch(`/api/company/${companyId}/shop-portal/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': userData?.locationId || 'temp-location-id'
        },
        body: JSON.stringify({
          items: orderItems,
          requestedDeliveryDate: requestedDeliveryDate || undefined,
          notes: orderNotes,
          priority
        })
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Order Placed Successfully',
          description: `Order ${data.data.orderNumber} has been submitted`,
        });
        
        // Clear cart and redirect
        setCart([]);
        router.push(`/company/${companyId}/shop-portal/orders`);
      } else {
        toast({
          title: 'Order Failed',
          description: data.message || 'Failed to place order',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Order Failed',
        description: 'An error occurred while placing the order',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalItems = cart.reduce((sum, item) => sum + item.cartQuantity, 0);
  const estimatedTotal = cart.reduce((sum, item) => sum + (item.cartQuantity * item.estimatedCost), 0);
  const maxLeadTime = Math.max(...cart.map(item => item.leadTimeDays), 0);

  // Set minimum delivery date based on lead time
  const minDeliveryDate = new Date();
  minDeliveryDate.setDate(minDeliveryDate.getDate() + maxLeadTime);

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Shopping Cart</h1>
          <p className="text-muted-foreground">Review your order before submitting</p>
        </div>
        <Button 
          variant="outline"
          onClick={() => router.push(`/company/${companyId}/shop-portal/catalog`)}
        >
          Continue Shopping
        </Button>
      </div>

      {cart.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Your cart is empty</h3>
            <p className="text-muted-foreground mb-4">Add items from the catalog to get started</p>
            <Button onClick={() => router.push(`/company/${companyId}/shop-portal/catalog`)}>
              Browse Catalog
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Cart Items ({totalItems})</CardTitle>
                <Button variant="outline" size="sm" onClick={clearCart}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Cart
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {cart.map(item => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{item.name}</h4>
                      {item.description && (
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      )}
                      <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
                        <span>Category: {item.category || 'N/A'}</span>
                        <span>Lead time: {item.leadTimeDays} days</span>
                        <span>Unit: {item.orderingUOM}</span>
                      </div>
                      {item.estimatedCost > 0 && (
                        <p className="text-sm font-medium mt-1">
                          ${item.estimatedCost.toFixed(2)} per {item.orderingUOM}
                        </p>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, item.cartQuantity - 1)}
                          disabled={item.cartQuantity <= item.minOrderQuantity}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="font-medium min-w-[3rem] text-center">
                          {item.cartQuantity}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, item.cartQuantity + 1)}
                          disabled={item.cartQuantity >= item.maxOrderQuantity}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="text-right min-w-[80px]">
                        {item.estimatedCost > 0 && (
                          <p className="font-medium">
                            ${(item.cartQuantity * item.estimatedCost).toFixed(2)}
                          </p>
                        )}
                      </div>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeItem(item.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Items:</span>
                  <span className="font-medium">{totalItems}</span>
                </div>
                {estimatedTotal > 0 && (
                  <>
                    <div className="flex justify-between">
                      <span>Estimated Total:</span>
                      <span className="font-medium">${estimatedTotal.toFixed(2)}</span>
                    </div>
                    <Separator />
                  </>
                )}
                <div className="flex justify-between text-lg font-semibold">
                  <span>Status:</span>
                  <Badge variant="outline">Ready to Order</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Order Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Priority</label>
                  <select 
                    value={priority}
                    onChange={(e) => setPriority(e.target.value as any)}
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    <option value="LOW">Low Priority</option>
                    <option value="NORMAL">Normal Priority</option>
                    <option value="URGENT">Urgent</option>
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    <Calendar className="h-4 w-4 inline mr-1" />
                    Requested Delivery Date
                  </label>
                  <Input
                    type="date"
                    value={requestedDeliveryDate}
                    onChange={(e) => setRequestedDeliveryDate(e.target.value)}
                    min={minDeliveryDate.toISOString().split('T')[0]}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Minimum {maxLeadTime} days lead time required
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Order Notes</label>
                  <Textarea
                    placeholder="Special instructions or notes..."
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    rows={3}
                  />
                </div>

                {maxLeadTime > 1 && (
                  <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-amber-800">Lead Time Notice</p>
                      <p className="text-amber-700">
                        Some items require up to {maxLeadTime} days preparation time
                      </p>
                    </div>
                  </div>
                )}

                <Button 
                  className="w-full" 
                  onClick={submitOrder}
                  disabled={isSubmitting || cart.length === 0}
                >
                  {isSubmitting ? 'Placing Order...' : 'Place Order'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}