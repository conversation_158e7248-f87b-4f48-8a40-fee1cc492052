'use client';

import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Package, 
  Search, 
  Eye,
  RefreshCw 
} from 'lucide-react';

interface ShopOrder {
  _id: string;
  orderNumber: string;
  status: string;
  approvalStatus?: string;
  priority?: string;
  totalAmount?: number;
  requestedDeliveryDate?: string;
  estimatedReadyTime?: string;
  items: Array<{
    itemId: {
      name: string;
      description?: string;
    };
    quantity: number;
    deliveredQuantity: number;
    uomId: {
      shortCode: string;
      name: string;
    };
    unitPrice: number;
    lineTotal: number;
  }>;
  seller: {
    sellerName: string;
  };
  orderNotes?: string;
  createdAt: string;
  updatedAt: string;
}

const statusConfig = {
  'DRAFT': { label: 'Draft', color: 'bg-gray-500', icon: Clock },
  'INCOMING': { label: 'Pending Review', color: 'bg-yellow-500', icon: Clock },
  'CONFIRMED': { label: 'Confirmed', color: 'bg-blue-500', icon: CheckCircle },
  'NOT_DELIVERED': { label: 'Not Delivered', color: 'bg-red-500', icon: XCircle },
  'PARTIALLY_DELIVERED': { label: 'Partially Delivered', color: 'bg-orange-500', icon: Package },
  'DELIVERED': { label: 'Delivered', color: 'bg-green-500', icon: CheckCircle },
  'CANCELLED': { label: 'Cancelled', color: 'bg-gray-400', icon: XCircle },
  'APPROVED': { label: 'Approved', color: 'bg-green-500', icon: CheckCircle },
};

export default function ShopOrdersPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const { toast } = useToast();
  const companyId = params.companyId as string;

  const [orders, setOrders] = useState<ShopOrder[]>([]);
  const [loading1, setLoading1] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);

  const userLocationId = userData?.locationId || 'temp-location-id';

  useEffect(() => {
    if (userData && companyId) {
      fetchOrders();
    }
  }, [userData, companyId, selectedStatus]);

  const fetchOrders = async () => {
    try {
      setLoading1(true);
      const response = await fetch(
        `/api/company/${companyId}/shop-portal/orders?locationId=${userLocationId}&status=${selectedStatus}`
      );
      const data = await response.json();
      
      if (data.success) {
        setOrders(data.data);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to load orders',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load orders',
        variant: 'destructive',
      });
    } finally {
      setLoading1(false);
    }
  };

  const filteredOrders = orders.filter(order =>
    order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.items.some(item => 
      item.itemId.name.toLowerCase().includes(searchTerm.toLowerCase())
    ) ||
    order.seller.sellerName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string, approvalStatus?: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    const StatusIcon = config?.icon || Clock;
    
    return (
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="flex items-center gap-1">
          <StatusIcon className="h-3 w-3" />
          {config?.label || status}
        </Badge>
        {approvalStatus && approvalStatus !== 'AUTO_APPROVED' && (
          <Badge variant="outline">
            {approvalStatus === 'PENDING' ? 'Awaiting Approval' : approvalStatus}
          </Badge>
        )}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading || loading1) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Order History</h1>
          <p className="text-muted-foreground">Track your orders from central kitchen</p>
        </div>
        <Button onClick={fetchOrders} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search orders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select 
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Statuses</option>
          <option value="INCOMING">Pending Review</option>
          <option value="CONFIRMED">Confirmed</option>
          <option value="PARTIALLY_DELIVERED">In Progress</option>
          <option value="DELIVERED">Delivered</option>
          <option value="CANCELLED">Cancelled</option>
        </select>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No orders found</h3>
              <p className="text-muted-foreground">
                {searchTerm || selectedStatus ? 'Try adjusting your filters' : 'You haven\'t placed any orders yet'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredOrders.map(order => (
            <Card key={order._id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-3">
                      {order.orderNumber}
                      {getStatusBadge(order.status, order.approvalStatus)}
                    </CardTitle>
                    <div className="flex gap-4 text-sm text-muted-foreground mt-2">
                      <span>From: {order.seller.sellerName}</span>
                      <span>Items: {order.items.length}</span>
                      <span>Ordered: {formatDate(order.createdAt)}</span>
                      {order.totalAmount && (
                        <span>Total: ${order.totalAmount.toFixed(2)}</span>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setExpandedOrder(
                      expandedOrder === order._id ? null : order._id
                    )}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {expandedOrder === order._id ? 'Hide' : 'View'} Details
                  </Button>
                </div>
              </CardHeader>

              {expandedOrder === order._id && (
                <CardContent className="border-t">
                  <div className="space-y-4">
                    {/* Order Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      {order.priority && (
                        <div>
                          <p className="font-medium text-muted-foreground">Priority</p>
                          <Badge variant={order.priority === 'URGENT' ? 'destructive' : 'secondary'}>
                            {order.priority}
                          </Badge>
                        </div>
                      )}
                      {order.requestedDeliveryDate && (
                        <div>
                          <p className="font-medium text-muted-foreground">Requested Delivery</p>
                          <p>{formatDate(order.requestedDeliveryDate)}</p>
                        </div>
                      )}
                      {order.estimatedReadyTime && (
                        <div>
                          <p className="font-medium text-muted-foreground">Estimated Ready</p>
                          <p>{formatDate(order.estimatedReadyTime)}</p>
                        </div>
                      )}
                    </div>

                    {/* Order Notes */}
                    {order.orderNotes && (
                      <div>
                        <p className="font-medium text-muted-foreground mb-2">Order Notes</p>
                        <p className="text-sm bg-gray-50 p-3 rounded-lg">{order.orderNotes}</p>
                      </div>
                    )}

                    {/* Items */}
                    <div>
                      <p className="font-medium text-muted-foreground mb-3">Order Items</p>
                      <div className="space-y-2">
                        {order.items.map((item, index) => (
                          <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                              <p className="font-medium">{item.itemId.name}</p>
                              {item.itemId.description && (
                                <p className="text-sm text-muted-foreground">{item.itemId.description}</p>
                              )}
                            </div>
                            <div className="text-right">
                              <p className="font-medium">
                                {item.quantity} {item.uomId.shortCode}
                              </p>
                              {item.deliveredQuantity > 0 && (
                                <p className="text-sm text-green-600">
                                  Delivered: {item.deliveredQuantity} {item.uomId.shortCode}
                                </p>
                              )}
                              {item.unitPrice > 0 && (
                                <p className="text-sm text-muted-foreground">
                                  ${item.lineTotal.toFixed(2)}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Timeline */}
                    <div>
                      <p className="font-medium text-muted-foreground mb-3">Order Timeline</p>
                      <div className="space-y-2">
                        <div className="flex items-center gap-3 text-sm">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>Order placed: {formatDate(order.createdAt)}</span>
                        </div>
                        {order.updatedAt !== order.createdAt && (
                          <div className="flex items-center gap-3 text-sm">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>Last updated: {formatDate(order.updatedAt)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))
        )}
      </div>
    </div>
  );
}