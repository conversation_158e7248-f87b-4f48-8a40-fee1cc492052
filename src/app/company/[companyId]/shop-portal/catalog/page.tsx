'use client';

import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Plus, Minus, Package, Search, Filter } from 'lucide-react';

interface OrderableItem {
  id: string;
  itemId: string;
  itemType: string;
  name: string;
  description?: string;
  category?: string;
  currentStock: number;
  centralKitchenStock: number;
  minOrderQuantity: number;
  maxOrderQuantity: number;
  orderingUOM: string;
  leadTimeDays: number;
  estimatedCost: number;
  isAvailable: boolean;
  baseUom: {
    name: string;
    shortCode: string;
  };
}

interface CartItem extends OrderableItem {
  cartQuantity: number;
}

export default function ShopCatalogPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const { toast } = useToast();
  const companyId = params.companyId as string;

  const [items, setItems] = useState<OrderableItem[]>([]);
  const [cart, setCart] = useState<Map<string, CartItem>>(new Map());
  const [loading1, setLoading1] = useState(true);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  // Get user's location - in real implementation, this would come from user profile
  const userLocationId = userData?.locationId || 'temp-location-id';

  useEffect(() => {
    if (userData && companyId) {
      fetchOrderableItems();
    }
  }, [userData, companyId]);

  const fetchOrderableItems = async () => {
    try {
      const response = await fetch(
        `/api/company/${companyId}/shop-portal/orderable-items?locationId=${userLocationId}&search=${search}&category=${selectedCategory}`
      );
      const data = await response.json();
      
      if (data.success) {
        setItems(data.data);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to load orderable items',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load orderable items',
        variant: 'destructive',
      });
    } finally {
      setLoading1(false);
    }
  };

  const addToCart = (item: OrderableItem, quantity: number = 1) => {
    const newCart = new Map(cart);
    const existingItem = newCart.get(item.id);
    
    if (existingItem) {
      const newQuantity = existingItem.cartQuantity + quantity;
      if (newQuantity <= item.maxOrderQuantity) {
        existingItem.cartQuantity = newQuantity;
        newCart.set(item.id, existingItem);
      } else {
        toast({
          title: 'Quantity Limit',
          description: `Maximum order quantity is ${item.maxOrderQuantity} ${item.orderingUOM}`,
          variant: 'destructive',
        });
        return;
      }
    } else {
      if (quantity >= item.minOrderQuantity) {
        newCart.set(item.id, { ...item, cartQuantity: quantity });
      } else {
        toast({
          title: 'Minimum Quantity',
          description: `Minimum order quantity is ${item.minOrderQuantity} ${item.orderingUOM}`,
          variant: 'destructive',
        });
        return;
      }
    }
    
    setCart(newCart);
    toast({
      title: 'Added to Cart',
      description: `${item.name} added to cart`,
    });
  };

  const removeFromCart = (itemId: string) => {
    const newCart = new Map(cart);
    newCart.delete(itemId);
    setCart(newCart);
  };

  const updateCartQuantity = (itemId: string, quantity: number) => {
    const newCart = new Map(cart);
    const item = newCart.get(itemId);
    if (item) {
      if (quantity === 0) {
        newCart.delete(itemId);
      } else if (quantity >= item.minOrderQuantity && quantity <= item.maxOrderQuantity) {
        item.cartQuantity = quantity;
        newCart.set(itemId, item);
      }
      setCart(newCart);
    }
  };

  const categories = [...new Set(items.map(item => item.category).filter(Boolean))];
  const cartCount = Array.from(cart.values()).reduce((sum, item) => sum + item.cartQuantity, 0);

  if (loading || loading1) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Order from Central Kitchen</h1>
          <p className="text-muted-foreground">Browse available items and build your order</p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="secondary">
            Cart: {cartCount} items
          </Badge>
          <Button 
            onClick={() => window.location.href = `/company/${companyId}/shop-portal/cart`}
            disabled={cartCount === 0}
          >
            View Cart ({cartCount})
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search items..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <select 
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
        <Button onClick={fetchOrderableItems} variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          Apply Filters
        </Button>
      </div>

      {/* Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map(item => {
          const cartItem = cart.get(item.id);
          const inCart = !!cartItem;
          
          return (
            <Card key={item.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{item.name}</CardTitle>
                    {item.description && (
                      <CardDescription>{item.description}</CardDescription>
                    )}
                  </div>
                  <Badge variant={item.isAvailable ? "default" : "secondary"}>
                    {item.isAvailable ? "Available" : "Low Stock"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">In Stock</p>
                    <p className="font-medium">{item.centralKitchenStock} {item.baseUom.shortCode}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Min Order</p>
                    <p className="font-medium">{item.minOrderQuantity} {item.orderingUOM}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Lead Time</p>
                    <p className="font-medium">{item.leadTimeDays} days</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Category</p>
                    <p className="font-medium">{item.category || 'N/A'}</p>
                  </div>
                </div>

                {inCart ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateCartQuantity(item.id, cartItem.cartQuantity - 1)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="font-medium min-w-[3rem] text-center">
                        {cartItem.cartQuantity}
                      </span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateCartQuantity(item.id, cartItem.cartQuantity + 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => removeFromCart(item.id)}
                    >
                      Remove
                    </Button>
                  </div>
                ) : (
                  <Button 
                    className="w-full"
                    onClick={() => addToCart(item, item.minOrderQuantity)}
                    disabled={!item.isAvailable}
                  >
                    <Package className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {items.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No items available</h3>
          <p className="text-muted-foreground">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
}