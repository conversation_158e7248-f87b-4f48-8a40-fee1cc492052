// src/app/company/[companyId]/locations/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Location } from '@/lib/types/location';
import { useLocation } from '@/contexts/LocationContext';
import { Warehouse, Store } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

export default function LocationsPage() {
  const { userData } = useRequireCompanyUser();
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;
  const { setSelectedLocation } = useLocation();
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchLocations() {
      try {
        console.log('Debug - Fetching locations for company:', companyId);
        const response = await fetch(`/api/company/${companyId}/locations`, {
          headers: {
            'company-id': companyId,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch locations');
        }

        const data = await response.json();
        console.log('Debug - Fetched locations:', data);
        setLocations(data);
      } catch (error) {
        console.error('Error fetching locations:', error);
      } finally {
        setLoading(false);
      }
    }

    if (companyId) {
      fetchLocations();
    }
  }, [companyId]);

  const handleLocationClick = (location: Location) => {
    console.log('Debug - Location clicked:', {
      location,
      companyId,
      targetUrl: `/company/${companyId}/location/${location._id}/dashboard`
    });
    
    // Store location in both context and localStorage with consistent key
    setSelectedLocation(location);
    localStorage.setItem(`location_${location._id}`, JSON.stringify(location));
    localStorage.setItem('lastSelectedLocation', JSON.stringify(location));
    console.log('Debug - Selected location set in context and localStorage');
    
    const dashboardUrl = `/company/${companyId}/location/${location._id}/dashboard`;
    console.log('Debug - Navigating to:', dashboardUrl);
    router.push(dashboardUrl);
  };

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'CENTRAL_KITCHEN':
        return 'Central Kitchen';
      case 'RETAIL_SHOP':
        return 'Retail Shop';
      case 'SINGLE_LOCATION':
        return 'Single Location';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-8">Locations</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {locations.map((location) => (
          <div
            key={location._id}
            onClick={() => handleLocationClick(location)}
            className="p-6 rounded-lg border shadow-sm hover:shadow-md cursor-pointer transition-all duration-200"
          >
            <div className="flex items-start space-x-4">
              <div className="p-2 rounded-full bg-primary/10">
                {location.locationType === 'RETAIL_SHOP' ? (
                  <Store className="h-6 w-6 text-primary" />
                ) : (
                  <Warehouse className="h-6 w-6 text-primary" />
                )}
              </div>
              <div>
                <h3 className="font-semibold text-lg">{location.name}</h3>
                <p className="text-sm text-gray-600">
                  {getLocationTypeLabel(location.locationType)}
                </p>
                {location.address && (
                  <p className="text-sm text-gray-500 mt-1">{location.address}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
