// src/app/company/[companyId]/location/[id]/items-to-order/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

type VisibilityType = 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';

interface Location {
  _id: string;
  name: string;
}

interface SellingOption {
  id: string;
  name: string;
  sourceType: 'recipe' | 'ingredient';
  sourceId: string;
  unitOfSellingShortCode: string;
  priceWithoutTax: number;
  priceWithTax: number;
  visibility: {
    type: VisibilityType;
    locations?: string[];
    externalAccess?: boolean;
  };
}

export default function ItemsToOrderPage() {
  const { userData } = useRequireCompanyUser();
  const params = useParams();
  const { selectedLocation, setSelectedLocation } = useLocation();
  
  const companyId = params.companyId as string;
  const locationId = params.id as string;
  const [location, setLocation] = useState<Location | null>(null);
  const [sellingOptions, setSellingOptions] = useState<SellingOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getLocation = async () => {
      try {
        // First try context
        if (selectedLocation) {
          console.log('Debug - Using location from context:', selectedLocation);
          setLocation(selectedLocation);
          setIsLoading(false);
          return;
        }

        // Then try localStorage
        const storedLocation = localStorage.getItem('selectedLocation');
        if (storedLocation) {
          const parsedLocation = JSON.parse(storedLocation);
          console.log('Debug - Using location from localStorage:', parsedLocation);
          setSelectedLocation(parsedLocation); // Restore context
          setLocation(parsedLocation);
          setIsLoading(false);
          return;
        }

        // If no stored location, fetch from API
        console.log('Debug - Fetching location from API:', locationId);
        const response = await fetch(`/api/locations/${locationId}`, {
          headers: {
            'company-id': companyId,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch location');
        }

        const locationData = await response.json();
        console.log('Debug - Fetched location:', locationData);
        setLocation(locationData);
        setSelectedLocation(locationData);
        localStorage.setItem('selectedLocation', JSON.stringify(locationData));
      } catch (err) {
        console.error('Error getting location:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    getLocation();
  }, [companyId, locationId, selectedLocation, setSelectedLocation]);

  // Then fetch the selling options for this location
  useEffect(() => {
    const fetchSellingOptions = async () => {
      if (!location?._id) return;

      try {
        console.log('Debug - Fetching selling options for location:', location._id);
        const queryParams = new URLSearchParams();
        queryParams.set('locations', location._id);

        const url = `/api/company/${companyId}/selling-options?${queryParams.toString()}`;
        console.log('Debug - Selling options URL:', url);
        
        const response = await fetch(url, {
          headers: {
            'company-id': companyId,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch selling options');
        }

        const data = await response.json();
        console.log('Debug - Fetched selling options:', data);
        setSellingOptions(data);
      } catch (err) {
        console.error('Error fetching selling options:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      }
    };

    fetchSellingOptions();
  }, [companyId, location?._id]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[300px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">
        Error loading items: {error}
      </div>
    );
  }

  if (sellingOptions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Available Items to Order</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No items available for ordering at this location.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Available Items to Order</CardTitle>
        <Link href={`/company/${companyId}/location/${location?._id}/place-order`}>
          <Button>
            Place Order
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Unit</TableHead>
              <TableHead className="text-right">Price (excl. tax)</TableHead>
              <TableHead className="text-right">Price (incl. tax)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sellingOptions.map((option) => (
              <TableRow key={option.id}>
                <TableCell>{option.name}</TableCell>
                <TableCell>
                  {option.sourceType === 'recipe' ? 'Recipe' : 'Ingredient'}
                </TableCell>
                <TableCell>{option.unitOfSellingShortCode}</TableCell>
                <TableCell className="text-right">
                  ${option.priceWithoutTax.toFixed(2)}
                </TableCell>
                <TableCell className="text-right">
                  ${option.priceWithTax.toFixed(2)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
