// src/app/company/[companyId]/location/[id]/inventory-count/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format } from 'date-fns';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { MultiUOMInput } from '@/components/MultiUOMInput';

interface InventoryItem {
  _id: string;
  itemId: {
    _id: string;
    name: string;
  };
  currentStock: number;
  baseUomId: {
    name: string;
    shortCode: string;
  };
  itemType: string;
  countedQuantity?: number;
  sellingOptions: Array<{
    _id: string;
    unitOfSelling: {
      _id: string;
      name: string;
      shortCode: string;
      conversionFactor: number;
    };
    countedQuantity?: number;
  }>;
  totalCountedQuantity?: number;
}

interface CountItem {
  itemId: string;
  countedQuantity: number;
}

export default function InventoryCountPage() {
  const params = useParams();
  const { user } = useRequireCompanyUser();
  const { selectedLocation } = useLocation();
  
  const [allInventoryItems, setAllInventoryItems] = useState<InventoryItem[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [countItems, setCountItems] = useState<CountItem[]>([]);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [recentCounts, setRecentCounts] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 100,
    total: 0,
    pages: 0
  });

  // Fetch all inventory items for counting
  const fetchAllInventoryItems = async () => {
    if (!selectedLocation?._id) return;
    
    try {
      setLoading(true);
      const allItems: InventoryItem[] = [];
      let currentPage = 1;
      let hasMore = true;
      
      while (hasMore) {
        const response = await fetch(
          `/api/company/${params.companyId}/location/${selectedLocation._id}/inventory?page=${currentPage}&limit=100&includeSellingOptions=true`,
          {
            headers: {
              'company-id': params.companyId
            }
          }
        );

        if (!response.ok) {
          throw new Error('Failed to fetch inventory items');
        }

        const data = await response.json();
        const enhancedItems = data.data.map((item: any) => ({
          ...item,
          sellingOptions: item.itemDetails?.sellingDetails?.map((detail: any) => ({
            _id: detail._id,
            unitOfSelling: {
              _id: detail.unitOfSelling._id,
              name: detail.unitOfSelling.name,
              shortCode: detail.unitOfSelling.shortCode,
              conversionFactor: detail.conversionFactor || 1
            }
          })) || [],
          totalCountedQuantity: item.countedQuantity
        }));
        
        allItems.push(...enhancedItems);
        hasMore = currentPage * 100 < data.pagination.total;
        currentPage++;
      }
      
      setAllInventoryItems(allItems);
      setInventoryItems(allItems.slice(0, pagination.limit));
      setPagination(prev => ({
        ...prev,
        total: allItems.length,
        pages: Math.ceil(allItems.length / prev.limit)
      }));
    } catch (error) {
      console.error('Error fetching all inventory items:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch inventory items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedLocation?._id) {
      fetchAllInventoryItems();
      fetchRecentCounts();
    }
  }, [selectedLocation?._id, fetchAllInventoryItems, fetchRecentCounts]);

  // Update paginated view when page changes
  useEffect(() => {
    const start = (pagination.page - 1) * pagination.limit;
    const end = start + pagination.limit;
    setInventoryItems(allInventoryItems.slice(start, end));
  }, [pagination.page, pagination.limit, allInventoryItems]);

  const fetchRecentCounts = async () => {
    if (!selectedLocation?._id) return;

    try {
      console.log('Fetching recent counts with params:', {
        companyId: params.companyId,
        locationId: selectedLocation._id
      });

      const response = await fetch(
        `/api/company/${params.companyId}/location/${selectedLocation._id}/inventory-count`,
        {
          headers: {
            'company-id': params.companyId
          }
        }
      );

      console.log('Recent counts response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to fetch recent counts');
      }

      const data = await response.json();
      console.log('Recent counts response data:', data);
      setRecentCounts(data.transactions || []);
    } catch (error) {
      console.error('Error fetching recent counts:', error);
    }
  };

  const handleQuantityChange = (itemId: string, totalQuantity: number) => {
    setAllInventoryItems(prev =>
      prev.map(item =>
        item.itemId._id === itemId
          ? { ...item, totalCountedQuantity: totalQuantity, countedQuantity: totalQuantity }
          : item
      )
    );
    setInventoryItems(prev =>
      prev.map(item =>
        item.itemId._id === itemId
          ? { ...item, totalCountedQuantity: totalQuantity, countedQuantity: totalQuantity }
          : item
      )
    );
  };

  const handleSubmit = async () => {
    if (!selectedLocation?._id) return;

    try {
      setSubmitting(true);
      // Include all items in the count, using 0 for uncounted items
      const countItems = allInventoryItems.map(item => ({
        itemId: item.itemId._id,
        countedQuantity: Number(item.countedQuantity ?? 0)
      }));

      console.log('Inventory count details:', {
        totalItems: allInventoryItems.length,
        itemsWithCount: allInventoryItems.filter(i => i.countedQuantity !== undefined).length,
        sampleItems: allInventoryItems.slice(0, 3).map(i => ({
          id: i.itemId._id,
          name: i.itemId.name,
          count: i.countedQuantity
        }))
      });

      const payload = {
        items: countItems,
        notes,
        countBatch: new Date().toISOString()
      };

      console.log('Submitting count payload:', JSON.stringify(payload, null, 2));

      const response = await fetch(
        `/api/company/${params.companyId}/location/${selectedLocation._id}/inventory-count`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'company-id': params.companyId
          },
          body: JSON.stringify(payload),
        }
      );

      console.log('Submit count response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to submit count');
      }

      const data = await response.json();
      console.log('Submit count response:', data);

      // Show success message with status
      setError(null);
      const message = data.status === 'APPROVED' 
        ? 'Stock count completed and levels updated!'
        : 'Stock count submitted for approval';
      alert(message);

      // Refresh data
      await fetchAllInventoryItems();
      await fetchRecentCounts();
      setNotes('');
    } catch (error) {
      console.error('Error submitting count:', error);
      setError(error instanceof Error ? error.message : 'Failed to submit count');
    } finally {
      setSubmitting(false);
    }
  };

  const handleApproveCount = async (countId: string) => {
    if (!selectedLocation?._id) return;

    try {
      const response = await fetch(
        `/api/company/${params.companyId}/location/${selectedLocation._id}/inventory-count`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'company-id': params.companyId
          },
          body: JSON.stringify({ countId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve count');
      }

      const data = await response.json();
      alert('Stock count approved and levels updated!');

      // Refresh data
      await fetchAllInventoryItems();
      await fetchRecentCounts();
    } catch (error) {
      console.error('Error approving count:', error);
      setError(error instanceof Error ? error.message : 'Failed to approve count');
    }
  };

  return (
    <>
      <div className="mb-8">
        <h1 className="text-2xl font-bold">New Stock Count</h1>
      </div>

      {error && (
        <div className="mb-4 p-4 text-red-600 bg-red-50 rounded-lg">
          {error}
        </div>
      )}

      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Count Details</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading inventory items...</div>
            ) : inventoryItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No inventory items found.
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Input
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Enter count notes..."
                  />
                </div>

                <div className="text-sm text-gray-500 mb-4">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} items
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Current Stock</TableHead>
                      <TableHead className="w-96">Count</TableHead>
                      <TableHead>Difference</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inventoryItems.map((item) => {
                      const difference = (item.totalCountedQuantity || 0) - item.currentStock;
                      return (
                        <TableRow key={item._id}>
                          <TableCell>{item.itemId.name}</TableCell>
                          <TableCell>{item.currentStock} {item.baseUomId.shortCode}</TableCell>
                          <TableCell>
                            <MultiUOMInput
                              item={item}
                              onChange={(total) => handleQuantityChange(item.itemId._id, total)}
                            />
                          </TableCell>
                          <TableCell
                            className={
                              difference === 0
                                ? ''
                                : difference > 0
                                ? 'text-green-600'
                                : 'text-red-600'
                            }
                          >
                            {difference > 0 ? '+' : ''}
                            {difference} {item.baseUomId.shortCode}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}
                      disabled={pagination.page === 1 || loading}
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                      disabled={pagination.page === 1 || loading}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <span className="text-sm">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                      disabled={pagination.page === pagination.pages || loading}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.pages }))}
                      disabled={pagination.page === pagination.pages || loading}
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <Button
                    onClick={handleSubmit}
                    disabled={submitting}
                    className="w-auto"
                  >
                    {submitting ? 'Submitting...' : 'Submit Count'}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Counts</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentCounts.map((count: any) => (
                  <TableRow key={count._id}>
                    <TableCell>
                      {format(new Date(count.timestamp), 'MMM d, yyyy h:mm a')}
                    </TableCell>
                    <TableCell>{count.countStatus}</TableCell>
                    <TableCell>{count.items.length}</TableCell>
                    <TableCell>{count.notes}</TableCell>
                    <TableCell>
                      {count.countStatus === 'SUBMITTED' && (
                        <Button
                          size="sm"
                          onClick={() => handleApproveCount(count._id)}
                        >
                          Approve
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
