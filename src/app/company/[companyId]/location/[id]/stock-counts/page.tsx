'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format } from 'date-fns';
import Link from 'next/link';

interface StockCount {
  _id: string;
  timestamp: string;
  countBatch: string;
  countStatus: string;
  itemsCount?: number;
  notes?: string;
}

export default function StockCountsPage() {
  const params = useParams();
  const router = useRouter();
  const { userData } = useRequireCompanyUser();
  const { selectedLocation } = useLocation();
  const [counts, setCounts] = useState<StockCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Ensure URL matches selected location
    if (selectedLocation && params.id !== selectedLocation._id) {
      console.log('Debug - Redirecting to correct location URL');
      router.replace(`/company/${params.companyId}/location/${selectedLocation._id}/stock-counts`);
      return;
    }

    // Only fetch if we have a valid location
    if (selectedLocation) {
      fetchCounts();
    }
  }, [params.companyId, params.id, selectedLocation, router]);

  const fetchCounts = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Debug - Fetching stock counts:', {
        companyId: params.companyId,
        locationId: selectedLocation?._id
      });

      const response = await fetch(
        `/api/company/${params.companyId}/location/${selectedLocation?._id}/inventory-count`,
        {
          headers: {
            'company-id': params.companyId,
          },
          credentials: 'same-origin'  // Add this to ensure cookies are sent
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch stock counts');
      }

      const data = await response.json();
      console.log('Debug - Fetched stock counts:', data);
      setCounts(data.transactions || []);
    } catch (error) {
      console.error('Error fetching stock counts:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch stock counts');
    } finally {
      setLoading(false);
    }
  };

  if (!selectedLocation) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            No location selected
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Stock Counts</h1>
        <Link
          href={`/company/${params.companyId}/location/${selectedLocation._id}/inventory-count`}
        >
          <Button>New Count</Button>
        </Link>
      </div>

      <Card>
        <CardContent className="pt-6">
          {counts.length === 0 ? (
            <div className="text-center text-gray-500">
              No stock counts found
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Batch</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {counts.map((count) => (
                  <TableRow key={count._id}>
                    <TableCell>
                      {format(new Date(count.timestamp), 'MMM d, yyyy h:mm a')}
                    </TableCell>
                    <TableCell>{count.countBatch}</TableCell>
                    <TableCell>{count.countStatus}</TableCell>
                    <TableCell>{count.itemsCount || 0}</TableCell>
                    <TableCell>{count.notes || '-'}</TableCell>
                    <TableCell>
                      <Link
                        href={`/company/${params.companyId}/location/${selectedLocation._id}/inventory-count?batch=${count.countBatch}`}
                      >
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
