'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { LocationNav } from '@/components/LocationNav';

interface Location {
  _id: string;
  name: string;
  locationType: string;
  companyId: string;
}

export default function LocationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const router = useRouter();
  const { userData } = useRequireCompanyUser();
  const { selectedLocation, setSelectedLocation } = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const companyId = params.companyId as string;
  const locationId = params.id as string;

  useEffect(() => {
    const initializeLocation = async () => {
      try {
        // Skip if we're already on the dashboard
        if (locationId === 'dashboard') {
          setIsLoading(false);
          return;
        }

        // First check if we have the correct location in context
        if (selectedLocation && selectedLocation._id === locationId) {
          console.log('Using location from context:', selectedLocation);
          setIsLoading(false);
          return;
        }

        // Then check localStorage
        const storedLocation = localStorage.getItem(`location_${locationId}`);
        if (storedLocation) {
          const parsedLocation = JSON.parse(storedLocation);
          if (parsedLocation._id === locationId && parsedLocation.companyId === companyId) {
            console.log('Using location from localStorage:', parsedLocation);
            setSelectedLocation(parsedLocation);
            setIsLoading(false);
            return;
          }
        }

        // If no valid stored location, fetch from API
        console.log('Fetching location from API:', locationId);
        const response = await fetch(`/api/company/${companyId}/locations/${locationId}`, {
          headers: {
            'company-id': companyId,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch location');
        }

        const locationData = await response.json();
        console.log('Fetched location:', locationData);

        // Validate location belongs to company
        if (locationData.companyId !== companyId) {
          throw new Error('Location does not belong to this company');
        }

        // Store in context and localStorage
        setSelectedLocation(locationData);
        localStorage.setItem(`location_${locationId}`, JSON.stringify(locationData));
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing location:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize location');
        setIsLoading(false);
        
        // Redirect to company dashboard on error
        router.push(`/company/${companyId}/dashboard`);
      }
    };

    if (companyId && locationId) {
      initializeLocation();
    }
  }, [companyId, locationId, selectedLocation, setSelectedLocation, router, setIsLoading, setError]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2">Loading location...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <div className="w-64 flex-none">
        <LocationNav />
      </div>
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto p-8">
          {children}
        </div>
      </main>
    </div>
  );
}
