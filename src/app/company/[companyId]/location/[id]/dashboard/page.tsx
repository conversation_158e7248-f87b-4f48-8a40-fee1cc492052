// src/app/company/[companyId]/location/[id]/dashboard/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Location } from '@/lib/types/location';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function LocationDashboardPage() {
  const { userData } = useRequireCompanyUser();
  const params = useParams();
  const { selectedLocation, setSelectedLocation } = useLocation();
  
  const companyId = params.companyId as string;
  const locationId = params.id as string;
  const [location, setLocation] = useState<Location | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getLocation = async () => {
      try {
        // First try context
        if (selectedLocation) {
          console.log('Debug - Using location from context:', selectedLocation);
          setLocation(selectedLocation);
          setIsLoading(false);
          return;
        }

        // Then try localStorage
        const storedLocation = localStorage.getItem('selectedLocation');
        if (storedLocation) {
          const parsedLocation = JSON.parse(storedLocation);
          console.log('Debug - Using location from localStorage:', parsedLocation);
          setSelectedLocation(parsedLocation); // Restore context
          setLocation(parsedLocation);
          setIsLoading(false);
          return;
        }

        // If no stored location, fetch from API
        console.log('Debug - Fetching location from API:', locationId);
        const response = await fetch(`/api/locations/${locationId}`, {
          headers: {
            'company-id': companyId,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch location');
        }

        const locationData = await response.json();
        console.log('Debug - Fetched location:', locationData);
        setLocation(locationData);
        setSelectedLocation(locationData);
        localStorage.setItem('selectedLocation', JSON.stringify(locationData));
      } catch (err) {
        console.error('Error getting location:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    getLocation();
  }, [companyId, locationId, selectedLocation, setSelectedLocation]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[300px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">
        Error loading location details: {error}
      </div>
    );
  }

  if (!location) {
    return <div>No location found</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{location.name} Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            {location.address && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Address</h3>
                <p className="mt-1">{location.address}</p>
              </div>
            )}
            {location.locationType && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Location Type</h3>
                <p className="mt-1">{location.locationType}</p>
              </div>
            )}
            <div className="flex gap-4">
              <Link 
                href={`/company/${companyId}/location/${location._id}/items-to-order`}
                passHref
                onClick={() => {
                  console.log('Debug - Items link clicked:', {
                    companyId,
                    locationId,
                    url: `/company/${companyId}/location/${location._id}/items-to-order`
                  });
                }}
              >
                <Button>
                  View Available Items
                </Button>
              </Link>
              <Link 
                href={`/company/${companyId}/location/${location._id}/place-order`}
                passHref
              >
                <Button variant="outline">
                  Place Order
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
