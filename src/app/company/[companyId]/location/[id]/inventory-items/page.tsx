// src/app/company/[companyId]/location/[id]/inventory-items/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface InventoryItem {
  _id: string;
  itemId: {
    _id: string;
    name: string;
  };
  itemType: 'RECIPE' | 'INGREDIENT';
  currentStock: number;
  parLevel: number;
  reorderPoint: number;
  baseUomId: {
    _id: string;
    name: string;
    shortCode: string;
  };
  lastUpdated: string;
  category: string;
  name: string;
  description: string;
  belowReorderPoint: boolean;
  isActive: boolean;
  isLocked: boolean;
  itemDetails: {
    _id: string;
    name: string;
    description: string;
    category?: string;
    Category?: string;
    canBeSold: boolean;
    sellingDetails: any[];
  };
}

interface ApiResponse {
  data: InventoryItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

type ItemTypeFilter = 'ALL' | 'RECIPE' | 'INGREDIENT';

interface PaginationControlsProps {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  loading: boolean;
  setPagination: (value: any) => void;
}

function PaginationControls({ pagination, loading, setPagination }: PaginationControlsProps) {
  return (
    <div className="flex items-center justify-between mt-4">
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}
          disabled={pagination.page === 1 || loading}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
          disabled={pagination.page === 1 || loading}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <span className="text-sm">
          Page {pagination.page} of {pagination.pages}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
          disabled={pagination.page === pagination.pages || loading}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPagination(prev => ({ ...prev, page: prev.pages }))}
          disabled={pagination.page === pagination.pages || loading}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
      <div className="text-sm text-gray-500">
        Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} items
      </div>
    </div>
  );
}

export default function InventoryItemsPage() {
  const { userData } = useRequireCompanyUser();
  const params = useParams();
  const { selectedLocation } = useLocation();
  const [loading, setLoading] = useState(true);
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [search, setSearch] = useState('');
  const [itemType, setItemType] = useState<ItemTypeFilter>('ALL');
  const [category, setCategory] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);
  const [belowReorder, setBelowReorder] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 100,
    total: 0,
    pages: 0,
  });

  useEffect(() => {
    if (items.length > 0) {
      const uniqueCategories = Array.from(new Set(
        items.map(item => item.category).filter(cat => cat && cat.trim() !== '')
      )).sort();
      setCategories(uniqueCategories);
    }
  }, [items]);

  useEffect(() => {
    async function fetchInventory() {
      if (!userData?.companyId) return;

      setLoading(true);
      setError(null);
      try {
        const searchParams = new URLSearchParams();
        if (search) searchParams.set('search', search);
        if (itemType !== 'ALL') searchParams.set('itemType', itemType);
        if (category && category !== '_all') searchParams.set('category', category);
        if (belowReorder) searchParams.set('belowReorder', 'true');
        searchParams.set('page', pagination.page.toString());
        searchParams.set('limit', pagination.limit.toString());

        const locationId = selectedLocation?._id;
        if (!locationId) {
          throw new Error('Location ID not found');
        }

        const url = `/api/company/${params.companyId}/location/${locationId}/inventory?${searchParams.toString()}`;
        console.log('Fetching inventory from:', url);

        const response = await fetch(url, {
          headers: {
            'company-id': params.companyId,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error response:', errorData);
          throw new Error(errorData.message || 'Failed to fetch inventory');
        }

        const data = await response.json() as ApiResponse;
        setItems(data.data);
        setPagination(data.pagination);
      } catch (error) {
        console.error('Error fetching inventory:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch inventory');
      } finally {
        setLoading(false);
      }
    }

    if (selectedLocation) {
      fetchInventory();
    }
  }, [userData?.companyId, params.companyId, selectedLocation, search, itemType, category, belowReorder, pagination.page]);

  if (!selectedLocation) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Loading location...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="mb-8 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Inventory Items - {selectedLocation.name}</h1>
        <div className="space-x-4">
          <Button asChild>
            <Link href={`/company/${params.companyId}/location/${params.id}/inventory-count`}>
              Stock Count
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex flex-col gap-2">
              <Label htmlFor="search">Search</Label>
              <Input
                id="search"
                placeholder="Search by name..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="itemType">Item Type</Label>
              <Select value={itemType} onValueChange={(value) => setItemType(value as ItemTypeFilter)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select item type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="RECIPE">Recipes</SelectItem>
                  <SelectItem value="INGREDIENT">Ingredients</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={category}
                onValueChange={setCategory}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="_all">All Categories</SelectItem>
                  {categories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="belowReorder"
                  checked={belowReorder}
                  onCheckedChange={(checked) => setBelowReorder(checked as boolean)}
                />
                <Label htmlFor="belowReorder">Below Reorder Point</Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Inventory Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="text-center py-8">Loading inventory items...</div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">{error}</div>
          ) : items.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No inventory items found
            </div>
          ) : (
            <div className="space-y-4">
              <PaginationControls
                pagination={pagination}
                loading={loading}
                setPagination={setPagination}
              />

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Current Stock</TableHead>
                    <TableHead>Par Level</TableHead>
                    <TableHead>Reorder Point</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Last Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    Array.from({ length: 5 }).map((_, i) => (
                      <TableRow key={i}>
                        {Array.from({ length: 7 }).map((_, j) => (
                          <TableCell key={j}>
                            <Skeleton className="h-4 w-[100px]" />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : items.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        No inventory items found
                      </TableCell>
                    </TableRow>
                  ) : (
                    items.map((item) => (
                      <TableRow key={item._id}>
                        <TableCell>{item.itemId.name}</TableCell>
                        <TableCell>{item.itemType}</TableCell>
                        <TableCell>{item.currentStock}</TableCell>
                        <TableCell>{item.parLevel}</TableCell>
                        <TableCell>{item.reorderPoint}</TableCell>
                        <TableCell>{item.baseUomId.shortCode}</TableCell>
                        <TableCell>
                          {new Date(item.lastUpdated).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              <PaginationControls
                pagination={pagination}
                loading={loading}
                setPagination={setPagination}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
