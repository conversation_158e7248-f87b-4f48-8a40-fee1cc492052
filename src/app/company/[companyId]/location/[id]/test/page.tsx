'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestPage() {
  const params = useParams();
  const router = useRouter();
  const { userData } = useRequireCompanyUser();
  const { selectedLocation } = useLocation();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const debugInfo = {
      companyId: params.companyId,
      locationId: params.id,
      user: userData,
      selectedLocation
    };
    console.log('Test Page Debug Info:', debugInfo);

    // Redirect to the correct URL if needed
    if (selectedLocation && params.id !== selectedLocation._id) {
      console.log('Debug - Redirecting to correct location URL');
      router.replace(`/company/${params.companyId}/location/${selectedLocation._id}/test`);
    } else {
      setLoading(false);
    }
  }, [params.companyId, params.id, userData, selectedLocation, router]);

  // Helper function to determine location access status
  const getLocationAccessStatus = () => {
    if (!selectedLocation) {
      return {
        isValid: false,
        message: 'No location selected'
      };
    }

    // Check if the selected location matches the URL parameter
    const isValid = selectedLocation._id === params.id;
    return {
      isValid,
      message: isValid ? 'Valid' : 'Invalid (Location ID mismatch)'
    };
  };

  const locationStatus = getLocationAccessStatus();

  if (loading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardContent>
            <div className="text-center py-4">Loading...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Authentication and Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">URL Parameters:</h3>
              <pre className="bg-gray-100 p-4 rounded-lg">
                {JSON.stringify({ companyId: params.companyId, locationId: params.id }, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold mb-2">User Data:</h3>
              <pre className="bg-gray-100 p-4 rounded-lg">
                {JSON.stringify(userData, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Selected Location:</h3>
              <pre className="bg-gray-100 p-4 rounded-lg">
                {JSON.stringify(selectedLocation, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Authentication Status:</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <p>
                  <span className="font-medium">User Authenticated:</span>{' '}
                  <span className={userData ? 'text-green-600' : 'text-red-600'}>
                    {userData ? 'Yes' : 'No'}
                  </span>
                </p>
                <p>
                  <span className="font-medium">User Role:</span>{' '}
                  {userData?.role || 'N/A'}
                </p>
                <p>
                  <span className="font-medium">Company Access:</span>{' '}
                  <span className={userData?.companyId === params.companyId ? 'text-green-600' : 'text-red-600'}>
                    {userData?.companyId === params.companyId ? 'Valid' : 'Invalid'}
                  </span>
                </p>
                <p>
                  <span className="font-medium">Location Access:</span>{' '}
                  <span className={locationStatus.isValid ? 'text-green-600' : 'text-red-600'}>
                    {locationStatus.message}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
