'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { CompanyNav } from '@/components/CompanyNav';
import { format } from 'date-fns';

interface DeliveryNote {
  _id: string;
  deliveryNoteNumber: string;
  status: string;
  source: string;
  reviewed: boolean;
  deliveryDate: string;
  items: Array<{
    itemId: string;
    quantityPlanned: number;
    uomId: string;
  }>;
  orderId: {
    _id: string;
    orderNumber: string;
    status: string;
    items: Array<{
      itemType: string;
      itemId: string;
      description: string;
      quantity: number;
      uomId: string;
    }>;
  };
  createdAt: string;
}

export default function PendingOrdersPage() {
  const params = useParams() || {};
  const router = useRouter();
  const companyId = params.companyId as string;
  const { userData } = useRequireCompanyUser();

  const [deliveryNotes, setDeliveryNotes] = useState<DeliveryNote[]>([]);
  const [selectedNote, setSelectedNote] = useState<DeliveryNote | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [editedItems, setEditedItems] = useState<Record<string, number>>({});
  const [processing, setProcessing] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 1
  });

  // Fetch delivery notes
  useEffect(() => {
    if (!userData) return;
    
    const fetchPendingDeliveryNotes = async () => {
      setIsLoading(true);
      try {
        const status = activeTab === 'reviewed' ? 'PENDING_REVIEW&reviewed=true' : 
                      activeTab === 'unreviewed' ? 'PENDING_REVIEW&reviewed=false' : 
                      'PENDING_REVIEW';
        
        const response = await fetch(
          `/api/company/${companyId}/delivery-notes/pending?page=${pagination.page}&limit=${pagination.limit}&status=${status}`,
          {
            headers: {
              'company-id': companyId
            }
          }
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch pending delivery notes');
        }
        
        const data = await response.json();
        setDeliveryNotes(data.deliveryNotes);
        setPagination(data.pagination);
      } catch (error) {
        console.error('Error fetching delivery notes:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load pending delivery notes."
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPendingDeliveryNotes();
  }, [userData, companyId, activeTab, pagination.page, pagination.limit]);

  // Handle selecting a delivery note for review
  const handleSelectNote = (note: DeliveryNote) => {
    setSelectedNote(note);
    // Initialize edited quantities with current values
    const items: Record<string, number> = {};
    note.items.forEach(item => {
      items[item.itemId] = item.quantityPlanned;
    });
    setEditedItems(items);
  };

  // Handle quantity change
  const handleQuantityChange = (itemId: string, value: number) => {
    setEditedItems(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // Process the delivery note (finalize or reject)
  const handleProcessNote = async (action: 'finalize' | 'reject' | 'markReviewed') => {
    if (!selectedNote) return;
    
    setProcessing(true);
    try {
      // Prepare items with updated quantities
      const items = Object.entries(editedItems).map(([itemId, quantityPlanned]) => ({
        itemId,
        quantityPlanned
      }));
      
      const response = await fetch(`/api/company/${companyId}/delivery-notes/pending`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify({
          deliveryNoteId: selectedNote._id,
          items,
          action
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to process delivery note');
      }
      
      const result = await response.json();
      
      // Success message based on action
      let message = '';
      if (action === 'finalize') {
        message = 'Delivery note finalized successfully';
      } else if (action === 'reject') {
        message = 'Delivery note rejected';
      } else {
        message = 'Delivery note marked as reviewed';
      }
      
      toast({
        title: "Success",
        description: message
      });
      
      // Refresh the list
      const updatedNotes = deliveryNotes.filter(note => note._id !== selectedNote._id);
      setDeliveryNotes(updatedNotes);
      setSelectedNote(null);
      
      // If finalized, navigate to the delivery note details
      if (action === 'finalize') {
        router.push(`/company/${companyId}/delivery-notes/${selectedNote._id}`);
      }
    } catch (error) {
      console.error('Error processing delivery note:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to process the delivery note."
      });
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-100">
      <CompanyNav />
      
      <div className="flex-1 p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Branch Orders Queue</h1>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/company/${companyId}/dashboard`)}
            >
              Dashboard
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push(`/company/${companyId}/delivery-notes`)}
            >
              All Delivery Notes
            </Button>
          </div>
        </div>
        
        {/* Tabs for filtering */}
        <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">All Pending ({pagination.total})</TabsTrigger>
            <TabsTrigger value="unreviewed">Unreviewed</TabsTrigger>
            <TabsTrigger value="reviewed">Reviewed</TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column: List of pending delivery notes */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Pending Branch Orders</CardTitle>
                <CardDescription>
                  Orders from branches requiring dispatch review
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center p-4">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : deliveryNotes.length === 0 ? (
                  <p className="text-center text-gray-500 p-4">No pending orders found</p>
                ) : (
                  <div className="space-y-2">
                    {deliveryNotes.map((note) => (
                      <div
                        key={note._id}
                        className={`p-3 border rounded-md cursor-pointer hover:bg-gray-50 ${
                          selectedNote?._id === note._id ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                        onClick={() => handleSelectNote(note)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{note.deliveryNoteNumber}</p>
                            <p className="text-sm text-gray-500">
                              Order: {note.orderId?.orderNumber}
                            </p>
                            <p className="text-xs text-gray-400">
                              {format(new Date(note.createdAt), 'MMM d, yyyy • h:mm a')}
                            </p>
                          </div>
                          <div className="flex flex-col items-end">
                            <Badge className={note.source === 'IONIC' ? 'bg-orange-100 text-orange-800' : ''}>
                              {note.source}
                            </Badge>
                            {note.reviewed && (
                              <Badge variant="outline" className="mt-1 border-green-500 text-green-700">
                                Reviewed
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button 
                  variant="outline" 
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                  disabled={pagination.page <= 1 || isLoading}
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-500">
                  Page {pagination.page} of {pagination.pages}
                </span>
                <Button 
                  variant="outline" 
                  onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                  disabled={pagination.page >= pagination.pages || isLoading}
                >
                  Next
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          {/* Right column: Selected delivery note details and actions */}
          <div className="md:col-span-2">
            {selectedNote ? (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Review Order {selectedNote.deliveryNoteNumber}</CardTitle>
                      <CardDescription>
                        From {selectedNote.orderId?.orderNumber} • {format(new Date(selectedNote.createdAt), 'MMMM d, yyyy')}
                      </CardDescription>
                    </div>
                    <Badge className={selectedNote.source === 'IONIC' ? 'bg-orange-100 text-orange-800' : ''}>
                      {selectedNote.source}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Order Items</h3>
                      <p className="text-sm text-gray-500 mb-2">
                        Review and adjust quantities as needed before finalizing
                      </p>
                      
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Item</TableHead>
                            <TableHead>Requested Qty</TableHead>
                            <TableHead>Dispatch Qty</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedNote.orderId?.items.map((item) => {
                            // Find corresponding item in the delivery note
                            const deliveryItem = selectedNote.items.find(
                              (di) => di.itemId === item.itemId
                            );
                            
                            const originalQty = deliveryItem?.quantityPlanned || item.quantity;
                            const currentQty = editedItems[item.itemId] ?? originalQty;
                            
                            return (
                              <TableRow key={item.itemId}>
                                <TableCell className="font-medium">
                                  {item.description}
                                </TableCell>
                                <TableCell>{item.quantity}</TableCell>
                                <TableCell>
                                  <Input
                                    type="number"
                                    min="0"
                                    value={currentQty}
                                    onChange={(e) => handleQuantityChange(
                                      item.itemId, 
                                      parseFloat(e.target.value) || 0
                                    )}
                                    className="w-24"
                                  />
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline"
                      onClick={() => handleProcessNote('markReviewed')}
                      disabled={processing}
                    >
                      {processing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <CheckCircle className="h-4 w-4 mr-2" />}
                      Mark Reviewed
                    </Button>
                    <Button 
                      variant="destructive"
                      onClick={() => handleProcessNote('reject')}
                      disabled={processing}
                    >
                      {processing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <AlertCircle className="h-4 w-4 mr-2" />}
                      Reject
                    </Button>
                  </div>
                  <Button 
                    onClick={() => handleProcessNote('finalize')}
                    disabled={processing}
                  >
                    {processing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <CheckCircle className="h-4 w-4 mr-2" />}
                    Finalize & Process
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center p-12">
                  <div className="text-center">
                    <h3 className="text-lg font-medium mb-2">Select an order to review</h3>
                    <p className="text-gray-500">
                      Choose an order from the list to review its details and make adjustments
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
