'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { CompanyNav } from '@/components/company/CompanyNav';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from 'date-fns';
import { CheckCircle, ClipboardList, Loader2, Save, SendHorizontal, AlertCircle, Filter, Search } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Status badge helper
const StatusBadge = ({ status }: { status: string }) => {
  const variant = {
    'IN_PROGRESS': 'default',
    'PENDING_APPROVAL': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'destructive',
    'CANCELLED': 'outline'
  }[status] || 'default';

  const label = {
    'IN_PROGRESS': 'In Progress',
    'PENDING_APPROVAL': 'Pending Approval',
    'APPROVED': 'Approved',
    'REJECTED': 'Rejected',
    'CANCELLED': 'Cancelled'
  }[status] || status;

  return <Badge variant={variant as any}>{label}</Badge>;
};

interface CountItem {
  itemId: string;
  itemType: 'RECIPE' | 'INGREDIENT';
  itemName: string;
  itemCategory?: string;
  sellingOptionId: string;
  systemStock: number;
  countedStock: number;
  difference: number;
  baseUomId: string;
  uomName?: string;
  notes?: string;
}

interface InventoryCount {
  _id: string;
  companyId: string;
  locationId: string;
  locationName?: string;
  status: 'IN_PROGRESS' | 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
  countDate: string;
  startedBy: string;
  startedByName?: string;
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  notes?: string;
  items: CountItem[];
  isLocked: boolean;
  lockedAt?: string;
  unlockedAt?: string;
}

export default function InventoryCountDetailPage() {
  useRequireCompanyUser();
  const router = useRouter();
  const { companyId, countId } = useParams() as { companyId: string; countId: string };

  // State
  const [count, setCount] = useState<InventoryCount | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [uoms, setUoms] = useState<any[]>([]);
  const [editedItems, setEditedItems] = useState<Record<string, { countedStock: number; notes?: string }>>({});
  const [filter, setFilter] = useState<string>('ALL');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [approvalData, setApprovalData] = useState({ approved: true, notes: '' });
  const [processingApproval, setProcessingApproval] = useState(false);

  // Fetch inventory count data
  useEffect(() => {
    const fetchInventoryCount = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/company/${companyId}/inventory-count/${countId}`, {
          headers: { 'company-id': companyId }
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch inventory count data');
        }
        
        const data = await response.json();
        
        // Initialize edited items from the fetched data
        const initialEdited: Record<string, { countedStock: number; notes?: string }> = {};
        data.items.forEach((item: CountItem) => {
          initialEdited[item.itemId] = {
            countedStock: item.countedStock,
            notes: item.notes
          };
        });
        
        setCount(data);
        setEditedItems(initialEdited);
      } catch (error: any) {
        console.error('Error fetching inventory count:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    // Fetch UOMs for display
    const fetchUOMs = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/uoms`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setUoms(data.uoms || []);
        }
      } catch (error) {
        console.error('Error fetching UOMs:', error);
      }
    };

    fetchInventoryCount();
    fetchUOMs();
  }, [companyId, countId]);

  // Get UOM name by ID
  const getUomName = (uomId: string) => {
    const uom = uoms.find(u => u._id === uomId);
    return uom ? uom.shortCode || uom.name : '';
  };

  // Handle item count input change
  const handleCountChange = (itemId: string, value: string) => {
    const numValue = parseFloat(value);
    
    if (isNaN(numValue)) return;
    
    setEditedItems(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        countedStock: numValue
      }
    }));
  };

  // Handle item notes change
  const handleNotesChange = (itemId: string, notes: string) => {
    setEditedItems(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        notes
      }
    }));
  };

  // Calculate difference for an item
  const calculateDifference = (itemId: string, systemStock: number) => {
    const countedStock = editedItems[itemId]?.countedStock || 0;
    return countedStock - systemStock;
  };

  // Save current count progress
  const saveCount = async () => {
    if (!count) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Convert edited items to array format expected by API
      const itemsToUpdate = Object.keys(editedItems).map(itemId => ({
        itemId,
        countedStock: editedItems[itemId].countedStock,
        notes: editedItems[itemId].notes
      }));
      
      const response = await fetch(`/api/company/${companyId}/inventory-count/${countId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify({ items: itemsToUpdate })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save inventory count');
      }
      
      setSuccess('Inventory count saved successfully');
      
      // Refresh the data
      const updatedResponse = await fetch(`/api/company/${companyId}/inventory-count/${countId}`, {
        headers: { 'company-id': companyId }
      });
      
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        setCount(updatedData);
      }
    } catch (error: any) {
      console.error('Error saving inventory count:', error);
      setError(error.message);
    } finally {
      setSaving(false);
      
      // Clear success message after 3 seconds
      if (success) {
        setTimeout(() => setSuccess(null), 3000);
      }
    }
  };

  // Submit count for approval
  const submitCount = async () => {
    if (!count) return;
    
    // First save the current state
    await saveCount();
    
    setSubmitting(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await fetch(`/api/company/${companyId}/inventory-count/${countId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit inventory count');
      }
      
      setSuccess('Inventory count submitted for approval');
      
      // Refresh the data
      const updatedResponse = await fetch(`/api/company/${companyId}/inventory-count/${countId}`, {
        headers: { 'company-id': companyId }
      });
      
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        setCount(updatedData);
      }
    } catch (error: any) {
      console.error('Error submitting inventory count:', error);
      setError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle approval or rejection
  const handleApproval = async () => {
    if (!count) return;
    
    setProcessingApproval(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await fetch(`/api/company/${companyId}/inventory-count/${countId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify(approvalData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process approval');
      }
      
      setSuccess(`Inventory count ${approvalData.approved ? 'approved' : 'rejected'} successfully`);
      
      // Refresh the data
      const updatedResponse = await fetch(`/api/company/${companyId}/inventory-count/${countId}`, {
        headers: { 'company-id': companyId }
      });
      
      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        setCount(updatedData);
      }
      
      // Close the dialog
      setApprovalDialogOpen(false);
    } catch (error: any) {
      console.error('Error processing approval:', error);
      setError(error.message);
    } finally {
      setProcessingApproval(false);
    }
  };

  // Filter items based on category and search query
  const filteredItems = useMemo(() => {
    if (!count) return [];
    
    return count.items.filter(item => {
      // Category filter
      if (filter !== 'ALL' && item.itemType !== filter) {
        return false;
      }
      
      // Search query filter
      if (searchQuery && !item.itemName.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  }, [count, filter, searchQuery]);

  // Calculate count statistics
  const countStats = useMemo(() => {
    if (!count) return { total: 0, counted: 0, remaining: 0, withVariance: 0 };
    
    const total = count.items.length;
    const counted = count.items.filter(item => item.countedStock > 0 || editedItems[item.itemId]?.countedStock > 0).length;
    const remaining = total - counted;
    
    // Items with variance (using edited values)
    const withVariance = count.items.filter(item => {
      const countedStock = editedItems[item.itemId]?.countedStock || item.countedStock;
      return countedStock > 0 && countedStock !== item.systemStock;
    }).length;
    
    return { total, counted, remaining, withVariance };
  }, [count, editedItems]);

  // Check if the current user can edit the count
  const canEdit = useMemo(() => {
    if (!count) return false;
    return count.status === 'IN_PROGRESS';
  }, [count]);

  // Check if the current user can submit the count
  const canSubmit = useMemo(() => {
    if (!count) return false;
    if (count.status !== 'IN_PROGRESS') return false;
    
    // Require all items to be counted
    return countStats.counted === countStats.total;
  }, [count, countStats]);

  // Check if the current user can approve/reject the count
  const canApprove = useMemo(() => {
    if (!count) return false;
    return count.status === 'PENDING_APPROVAL';
  }, [count]);

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <CompanyNav />
      
      <main className="flex-1 container py-6">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive flex items-center">
                <AlertCircle className="mr-2 h-5 w-5" />
                Error
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>{error}</p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" onClick={() => router.push(`/company/${companyId}/inventory-count`)}>
                Back to Inventory Counts
              </Button>
            </CardFooter>
          </Card>
        ) : count ? (
          <>
            {/* Header section with count details */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-3xl font-bold flex items-center">
                  Inventory Count
                  <StatusBadge status={count.status} className="ml-3" />
                </h1>
                <p className="text-muted-foreground mt-1">
                  {count.locationName} • {format(new Date(count.countDate), 'PPP')}
                </p>
              </div>
              <div className="flex gap-2">
                {canEdit && (
                  <>
                    <Button variant="outline" onClick={saveCount} disabled={saving}>
                      {saving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Progress
                        </>
                      )}
                    </Button>
                    <Button onClick={submitCount} disabled={!canSubmit || submitting}>
                      {submitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <SendHorizontal className="mr-2 h-4 w-4" />
                          Submit for Approval
                        </>
                      )}
                    </Button>
                  </>
                )}
                {canApprove && (
                  <Button onClick={() => setApprovalDialogOpen(true)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Review & Approve
                  </Button>
                )}
                <Button variant="outline" onClick={() => router.push(`/company/${companyId}/inventory-count`)}>
                  Back to Counts
                </Button>
              </div>
            </div>

            {/* Success message */}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                {success}
              </div>
            )}

            {/* Count summary */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Count Summary</CardTitle>
                <CardDescription>
                  Overview of the current inventory count progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-sm text-muted-foreground">Total Items</div>
                    <div className="text-2xl font-bold mt-1">{countStats.total}</div>
                  </div>
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-sm text-muted-foreground">Counted</div>
                    <div className="text-2xl font-bold mt-1 text-green-600">{countStats.counted}</div>
                  </div>
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-sm text-muted-foreground">Remaining</div>
                    <div className="text-2xl font-bold mt-1 text-amber-600">{countStats.remaining}</div>
                  </div>
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-sm text-muted-foreground">With Variance</div>
                    <div className="text-2xl font-bold mt-1 text-blue-600">{countStats.withVariance}</div>
                  </div>
                </div>

                {/* Count metadata */}
                <Accordion type="single" collapsible className="mt-4">
                  <AccordionItem value="details">
                    <AccordionTrigger>Count Details</AccordionTrigger>
                    <AccordionContent>
                      <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                        <div className="grid grid-cols-2">
                          <dt className="font-medium text-muted-foreground">Started By</dt>
                          <dd>{count.startedByName}</dd>
                        </div>
                        <div className="grid grid-cols-2">
                          <dt className="font-medium text-muted-foreground">Started On</dt>
                          <dd>{format(new Date(count.countDate), 'PPP p')}</dd>
                        </div>
                        {count.approvedBy && (
                          <>
                            <div className="grid grid-cols-2">
                              <dt className="font-medium text-muted-foreground">Approved By</dt>
                              <dd>{count.approvedByName}</dd>
                            </div>
                            <div className="grid grid-cols-2">
                              <dt className="font-medium text-muted-foreground">Approved On</dt>
                              <dd>{count.approvedAt ? format(new Date(count.approvedAt), 'PPP p') : 'N/A'}</dd>
                            </div>
                          </>
                        )}
                        {count.notes && (
                          <div className="grid grid-cols-2 md:col-span-2">
                            <dt className="font-medium text-muted-foreground">Notes</dt>
                            <dd>{count.notes}</dd>
                          </div>
                        )}
                      </dl>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>

            {/* Count items */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Inventory Items</CardTitle>
                  <CardDescription>
                    Count physical inventory and record variances
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative w-60">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search items..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Select value={filter} onValueChange={setFilter}>
                    <SelectTrigger className="w-40">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Filter" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Items</SelectItem>
                      <SelectItem value="INGREDIENT">Ingredients</SelectItem>
                      <SelectItem value="RECIPE">Recipes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead className="text-right">System Stock</TableHead>
                        <TableHead className="text-right">Counted Stock</TableHead>
                        <TableHead className="text-right">Variance</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            No items found matching your criteria
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredItems.map((item) => {
                          const difference = calculateDifference(item.itemId, item.systemStock);
                          const uomName = getUomName(item.baseUomId);
                          
                          return (
                            <TableRow key={item.itemId}>
                              <TableCell className="font-medium">{item.itemName}</TableCell>
                              <TableCell>{item.itemCategory || 'N/A'}</TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {item.itemType === 'RECIPE' ? 'Recipe' : 'Ingredient'}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                {item.systemStock} {uomName}
                              </TableCell>
                              <TableCell className="text-right">
                                {canEdit ? (
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    value={editedItems[item.itemId]?.countedStock || ''}
                                    onChange={(e) => handleCountChange(item.itemId, e.target.value)}
                                    className="w-24 ml-auto text-right"
                                  />
                                ) : (
                                  <span>{item.countedStock} {uomName}</span>
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <span className={
                                  difference > 0 
                                    ? "text-green-600" 
                                    : difference < 0 
                                    ? "text-red-600" 
                                    : ""
                                }>
                                  {difference > 0 ? '+' : ''}{difference} {uomName}
                                </span>
                              </TableCell>
                              <TableCell>
                                {canEdit ? (
                                  <Input
                                    value={editedItems[item.itemId]?.notes || ''}
                                    onChange={(e) => handleNotesChange(item.itemId, e.target.value)}
                                    placeholder="Add notes..."
                                    className="w-full"
                                  />
                                ) : (
                                  <span>{item.notes || '-'}</span>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredItems.length} of {count.items.length} items
                </div>
                {canEdit && (
                  <Button onClick={saveCount} disabled={saving}>
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Progress
                      </>
                    )}
                  </Button>
                )}
              </CardFooter>
            </Card>
          </>
        ) : null}
      </main>

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Review Inventory Count</DialogTitle>
            <DialogDescription>
              Review the count details and approve or reject as needed
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="mb-4">
              <Label className="text-muted-foreground">Count Status</Label>
              <div className="font-medium">PENDING APPROVAL</div>
            </div>
            
            <div className="mb-4">
              <Label className="text-muted-foreground">Items with Variance</Label>
              <div className="font-medium">{countStats.withVariance} of {countStats.total} items</div>
            </div>
            
            <div className="mb-4">
              <Label htmlFor="approvalNotes">Notes</Label>
              <Input
                id="approvalNotes"
                placeholder="Add approval notes..."
                value={approvalData.notes}
                onChange={(e) => setApprovalData({...approvalData, notes: e.target.value})}
              />
            </div>
            
            <div className="flex items-center space-x-2 mt-6">
              <Label htmlFor="approvalAction">Approval Decision</Label>
              <Select 
                value={approvalData.approved ? "approve" : "reject"}
                onValueChange={(value) => setApprovalData({
                  ...approvalData, 
                  approved: value === "approve"
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="approve">Approve Count</SelectItem>
                  <SelectItem value="reject">Reject Count</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)} disabled={processingApproval}>
              Cancel
            </Button>
            <Button 
              onClick={handleApproval} 
              disabled={processingApproval}
              variant={approvalData.approved ? "default" : "destructive"}
            >
              {processingApproval ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : approvalData.approved ? (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve Count
                </>
              ) : (
                <>
                  <AlertCircle className="mr-2 h-4 w-4" />
                  Reject Count
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
