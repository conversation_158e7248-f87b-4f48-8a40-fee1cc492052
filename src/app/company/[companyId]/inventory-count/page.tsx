'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { CompanyNav } from '@/components/company/CompanyNav';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, FileText, Loader2, RefreshCcw, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Status badge helper 
const StatusBadge = ({ status }: { status: string }) => {
  const variant = {
    'IN_PROGRESS': 'default',
    'PENDING_APPROVAL': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'destructive',
    'CANCELLED': 'outline'
  }[status] || 'default';

  const label = {
    'IN_PROGRESS': 'In Progress',
    'PENDING_APPROVAL': 'Pending Approval',
    'APPROVED': 'Approved',
    'REJECTED': 'Rejected',
    'CANCELLED': 'Cancelled'
  }[status] || status;

  return <Badge variant={variant as any}>{label}</Badge>;
};

export default function InventoryCountPage() {
  useRequireCompanyUser();
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };

  // State
  const [counts, setCounts] = useState<any[]>([]);
  const [locations, setLocations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({ page: 1, limit: 10, total: 0, pages: 0 });

  // Filter state
  const [locationId, setLocationId] = useState<string>('');
  const [status, setStatus] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // New count dialog
  const [showNewCountDialog, setShowNewCountDialog] = useState(false);
  const [newCountData, setNewCountData] = useState({
    locationId: '',
    notes: '',
    categoryFilter: '',
    itemTypeFilter: 'ALL'
  });
  const [startingCount, setStartingCount] = useState(false);

  // Load locations
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await fetch(`/api/company/${companyId}/locations`, {
          headers: { 'company-id': companyId }
        });
        
        if (response.ok) {
          const data = await response.json();
          setLocations(data.locations || []);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    };

    fetchLocations();
  }, [companyId]);

  // Load inventory counts
  const fetchCounts = async (page = 1) => {
    setLoading(true);
    try {
      // Build query string
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', pagination.limit.toString());
      
      if (locationId) queryParams.append('locationId', locationId);
      if (status) queryParams.append('status', status);
      if (startDate) queryParams.append('startDate', startDate.toISOString().split('T')[0]);
      if (endDate) queryParams.append('endDate', endDate.toISOString().split('T')[0]);

      const response = await fetch(`/api/company/${companyId}/inventory-count/history?${queryParams.toString()}`, {
        headers: { 'company-id': companyId }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCounts(data.counts || []);
        setPagination({
          page: data.pagination.page,
          limit: data.pagination.limit,
          total: data.pagination.total,
          pages: data.pagination.pages
        });
      } else {
        console.error('Failed to fetch counts:', await response.text());
      }
    } catch (error) {
      console.error('Error fetching inventory counts:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCounts(1);
  }, [companyId, locationId, status, startDate, endDate]);

  // Handle new count creation
  const handleStartNewCount = async () => {
    setStartingCount(true);
    try {
      const response = await fetch(`/api/company/${companyId}/inventory-count/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify(newCountData)
      });

      if (response.ok) {
        const data = await response.json();
        // Navigate to the count page
        router.push(`/company/${companyId}/inventory-count/${data.countId}`);
      } else {
        const errorData = await response.json();
        if (errorData.existingCountId) {
          // If there's already a count in progress, navigate to it
          alert('There is already a count in progress for this location. Redirecting to it.');
          router.push(`/company/${companyId}/inventory-count/${errorData.existingCountId}`);
        } else {
          alert(`Error starting count: ${errorData.error}`);
        }
      }
    } catch (error) {
      console.error('Error starting inventory count:', error);
      alert('Failed to start inventory count. Please try again.');
    } finally {
      setStartingCount(false);
      setShowNewCountDialog(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <CompanyNav />
      
      <main className="flex-1 container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Inventory Count</h1>
          <Button onClick={() => setShowNewCountDialog(true)}>Start New Count</Button>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter inventory counts by location, status, and date</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Location filter */}
              <div>
                <Label htmlFor="location">Location</Label>
                <Select value={locationId} onValueChange={setLocationId}>
                  <SelectTrigger>
                    <SelectValue placeholder="All locations" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All locations</SelectItem>
                    {locations.map(location => (
                      <SelectItem key={location._id} value={location._id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status filter */}
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                    <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
                    <SelectItem value="APPROVED">Approved</SelectItem>
                    <SelectItem value="REJECTED">Rejected</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Start date filter */}
              <div>
                <Label>Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      {startDate ? (
                        format(startDate, "PPP")
                      ) : (
                        <span className="text-muted-foreground">Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* End date filter */}
              <div>
                <Label>End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      {endDate ? (
                        format(endDate, "PPP")
                      ) : (
                        <span className="text-muted-foreground">Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Inventory Count History</CardTitle>
              <CardDescription>
                View all inventory counts and their status
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => fetchCounts(pagination.page)}>
              <RefreshCcw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : counts.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No inventory counts found. Start a new count to begin.</p>
              </div>
            ) : (
              <>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Items</TableHead>
                        <TableHead>Variance</TableHead>
                        <TableHead>Started By</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {counts.map((count) => (
                        <TableRow key={count._id}>
                          <TableCell>
                            {format(new Date(count.countDate), 'PPP')}
                          </TableCell>
                          <TableCell>{count.locationName}</TableCell>
                          <TableCell>
                            <StatusBadge status={count.status} />
                          </TableCell>
                          <TableCell>{count.itemCount}</TableCell>
                          <TableCell>
                            {count.totalDifference !== 0 ? (
                              <span className={
                                count.totalDifference > 0 
                                  ? "text-green-600" 
                                  : "text-red-600"
                              }>
                                {count.totalDifference > 0 ? '+' : ''}
                                {count.totalDifference.toFixed(2)}
                                {' '}
                                ({count.varianceItems} items)
                              </span>
                            ) : (
                              <span className="text-gray-500">No variance</span>
                            )}
                          </TableCell>
                          <TableCell>{count.startedByName}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/company/${companyId}/inventory-count/${count._id}`)}
                            >
                              <Search className="h-4 w-4 mr-2" />
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="flex justify-center items-center gap-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchCounts(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchCounts(pagination.page + 1)}
                      disabled={pagination.page >= pagination.pages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </main>

      {/* New Count Dialog */}
      <Dialog open={showNewCountDialog} onOpenChange={setShowNewCountDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Start New Inventory Count</DialogTitle>
            <DialogDescription>
              Select a location and inventory filters to begin a new count
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            {/* Location */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newCountLocation" className="text-right">Location</Label>
              <Select 
                value={newCountData.locationId} 
                onValueChange={(value) => setNewCountData({...newCountData, locationId: value})}
                disabled={startingCount}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map(location => (
                    <SelectItem key={location._id} value={location._id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Item Type Filter */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="itemTypeFilter" className="text-right">Item Type</Label>
              <Select 
                value={newCountData.itemTypeFilter} 
                onValueChange={(value) => setNewCountData({...newCountData, itemTypeFilter: value})}
                disabled={startingCount}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select item type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Items</SelectItem>
                  <SelectItem value="INGREDIENT">Ingredients Only</SelectItem>
                  <SelectItem value="RECIPE">Recipes Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Category Filter - Optional */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="categoryFilter" className="text-right">Category</Label>
              <Input
                id="categoryFilter"
                placeholder="Optional category filter"
                className="col-span-3"
                value={newCountData.categoryFilter}
                onChange={(e) => setNewCountData({...newCountData, categoryFilter: e.target.value})}
                disabled={startingCount}
              />
            </div>

            {/* Notes */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">Notes</Label>
              <Input
                id="notes"
                placeholder="Optional notes"
                className="col-span-3"
                value={newCountData.notes}
                onChange={(e) => setNewCountData({...newCountData, notes: e.target.value})}
                disabled={startingCount}
              />
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowNewCountDialog(false)}
              disabled={startingCount}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleStartNewCount} 
              disabled={!newCountData.locationId || startingCount}
            >
              {startingCount ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Starting...
                </>
              ) : (
                'Start Count'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
