// src/app/company/[companyId]/admin/dashboard/page.tsx

"use client";

import React from "react";
import { useRequireCompanyUser, CompanyUserData } from "@/lib/auth";
import { useRouter } from "next/navigation";
import { LocationManagement } from "@/components/admin/LocationManagement";
import { EnhancedRoleManagement } from "@/components/admin/EnhancedRoleManagement";
import { PermissionManagement } from "@/components/admin/PermissionManagement";
import { CurrencyTaxManagement } from "@/components/admin/CurrencyTaxManagement";
import { PosSettings } from "@/components/admin/PosSettings";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

export default function AdminDashboardPage() {
  const { userData, loading } = useRequireCompanyUser("admin");
  const router = useRouter();
  
  // Get the company ID from the URL since it's available in the route parameter
  const companyId = typeof window !== 'undefined' ? 
    window.location.pathname.split('/')[2] : '';

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!userData || userData.userType !== 'company_user') {
    router.replace("/login");
    return null;
  }

  // At this point, TypeScript knows userData is CompanyUserData
  const companyUser = userData as CompanyUserData;
  
  if (companyUser.role !== "admin" && companyUser.role !== "owner") {
    router.replace("/unauthorized");
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="w-full bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage your company&apos;s locations, roles, and settings
              </p>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
              <span className="text-sm text-gray-500">
                Company ID: <span className="font-medium text-gray-900">{companyUser.companyId}</span>
              </span>
              <span className="text-sm text-gray-500">
                Role: <span className="font-medium text-gray-900">{companyUser.role}</span>
              </span>
            </div>
          </div>

          <div className="w-full">
            <Tabs defaultValue="locations" className="w-full">
              <TabsList>
                <TabsTrigger 
                  value="locations"
                  className="flex-1 px-4 py-2 text-sm font-medium transition-colors data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  Locations
                </TabsTrigger>
                <TabsTrigger 
                  value="roles"
                  className="flex-1 px-4 py-2 text-sm font-medium transition-colors data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  Roles
                </TabsTrigger>
                <TabsTrigger 
                  value="permissions"
                  className="flex-1 px-4 py-2 text-sm font-medium transition-colors data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  Permissions
                </TabsTrigger>
                <TabsTrigger 
                  value="currency"
                  className="flex-1 px-4 py-2 text-sm font-medium transition-colors data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  Currency & Tax
                </TabsTrigger>
                <TabsTrigger 
                  value="pos"
                  className="flex-1 px-4 py-2 text-sm font-medium transition-colors data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm rounded-md"
                >
                  POS App
                </TabsTrigger>
              </TabsList>

              <div className="w-full mt-6">
                <TabsContent value="locations" className="w-full">
                  <LocationManagement />
                </TabsContent>
                <TabsContent value="roles" className="w-full">
                  <EnhancedRoleManagement companyId={companyUser.companyId} />
                </TabsContent>
                <TabsContent value="permissions" className="w-full">
                  <PermissionManagement companyId={companyUser.companyId} />
                </TabsContent>
                <TabsContent value="currency" className="w-full">
                  <CurrencyTaxManagement companyId={companyUser.companyId} />
                </TabsContent>
                <TabsContent value="pos" className="w-full">
                  <PosSettings companyId={companyUser.companyId} />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
