'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Hi<PERSON><PERSON>, HiP<PERSON>cil, HiTrash } from 'react-icons/hi';
import { Dialog } from '@headlessui/react';
import { 
  <PERSON>, 
  CardContent, 
  <PERSON>Footer, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

type Permission = {
  _id: string;
  name: string;
  description: string;
  category: string;
  isSystemLevel: boolean;
};

const PermissionsClient = () => {
  const params = useParams() || {};
  const companyId = params.companyId as string;
  
  // State for storing permissions - used inside fetchPermissions
  const [_permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsByCategory, setPermissionsByCategory] = useState<Record<string, Permission[]>>({});
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Form state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [form, setForm] = useState({
    name: '',
    description: '',
    category: ''
  });
  
  // New category state
  const [newCategory, setNewCategory] = useState('');
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  
  // Delete confirmation state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [permissionToDelete, setPermissionToDelete] = useState<Permission | null>(null);

  const fetchPermissions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/company/${companyId}/permissions`, {
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      
      const data = await response.json();
      setPermissions(data.permissions);
      
      if (data.groupedPermissions) {
        setPermissionsByCategory(data.groupedPermissions);
        setCategories(Object.keys(data.groupedPermissions));
      }
    } catch (err) {
      setError('Error loading permissions');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [companyId]);

  const handleCreatePermission = () => {
    setEditingPermission(null);
    setForm({
      name: '',
      description: '',
      category: categories[0] || ''
    });
    setIsModalOpen(true);
  };

  const handleEditPermission = (permission: Permission) => {
    setEditingPermission(permission);
    setForm({
      name: permission.name,
      description: permission.description,
      category: permission.category
    });
    setIsModalOpen(true);
  };

  const handleDeletePermission = (permission: Permission) => {
    setPermissionToDelete(permission);
    setIsDeleteModalOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // If creating a new category
    let category = form.category;
    if (showNewCategoryInput && newCategory) {
      category = newCategory;
    }
    
    try {
      const method = editingPermission ? 'PUT' : 'POST';
      const url = editingPermission 
        ? `/api/company/${companyId}/permissions/${editingPermission._id}` 
        : `/api/company/${companyId}/permissions`;
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...form,
          category
        })
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save permission');
      }
      
      // Refresh permissions list
      fetchPermissions();
      setIsModalOpen(false);
      
      // Reset new category state
      setShowNewCategoryInput(false);
      setNewCategory('');
    } catch (err) {
      console.error('Error saving permission:', err);
      alert(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleConfirmDelete = async () => {
    if (!permissionToDelete) return;
    
    try {
      const response = await fetch(`/api/company/${companyId}/permissions/${permissionToDelete._id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete permission');
      }
      
      // Refresh permissions list
      fetchPermissions();
      setIsDeleteModalOpen(false);
    } catch (err) {
      console.error('Error deleting permission:', err);
      alert(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const renderPermissionCards = (perms: Permission[]) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {perms.map(permission => (
          <Card key={permission._id} className={permission.isSystemLevel ? 'border-blue-300' : ''}>
            <CardHeader>
              <CardTitle className="flex justify-between items-center text-base">
                <span>{permission.name}</span>
                {permission.isSystemLevel && (
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">System</span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">{permission.description}</p>
              <div className="mt-2">
                <span className="text-xs font-semibold">Category:</span>
                <span className="text-xs text-gray-500 ml-1">{permission.category}</span>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleEditPermission(permission)}
                disabled={permission.isSystemLevel}
              >
                <HiPencil className="mr-1" />
                Edit
              </Button>
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => handleDeletePermission(permission)}
                disabled={permission.isSystemLevel}
              >
                <HiTrash className="mr-1" />
                Delete
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Permission Management</h2>
        <Button onClick={handleCreatePermission}>
          <HiPlus className="mr-2" />
          Add Permission
        </Button>
      </div>
      
      {error && <div className="bg-red-100 text-red-800 p-4 rounded-md">{error}</div>}
      
      {isLoading ? (
        <div className="text-center py-8">Loading permissions...</div>
      ) : (
        <div className="space-y-6">
          {categories.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No permissions found</div>
          ) : (
            categories.map(category => (
              <div key={category} className="space-y-2">
                <h3 className="text-lg font-semibold">{category}</h3>
                {renderPermissionCards(permissionsByCategory[category] || [])}
              </div>
            ))
          )}
        </div>
      )}
      
      {/* Permission Create/Edit Modal */}
      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="fixed inset-0 z-10 overflow-y-auto"
        >
          <div className="min-h-screen px-4 text-center">
            <div className="fixed inset-0 bg-black opacity-30" />
            
            <span className="inline-block h-screen align-middle" aria-hidden="true">&#8203;</span>
            
            <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
              <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                {editingPermission ? 'Edit Permission' : 'Create New Permission'}
              </Dialog.Title>
              
              <form onSubmit={handleSubmit}>
                <div className="mt-4 space-y-4">
                  <div>
                    <Label htmlFor="name">Permission Name</Label>
                    <Input
                      id="name"
                      value={form.name}
                      onChange={e => setForm({...form, name: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={form.description}
                      onChange={e => setForm({...form, description: e.target.value})}
                      rows={3}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="category">Category</Label>
                    {showNewCategoryInput ? (
                      <div className="flex space-x-2">
                        <Input
                          id="new-category"
                          value={newCategory}
                          onChange={e => setNewCategory(e.target.value)}
                          placeholder="Enter new category"
                          className="flex-1"
                          required
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setShowNewCategoryInput(false);
                            setNewCategory('');
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Select
                          value={form.category}
                          onValueChange={value => setForm({...form, category: value})}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map(category => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setShowNewCategoryInput(true)}
                        >
                          New
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsModalOpen(false);
                      setShowNewCategoryInput(false);
                      setNewCategory('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingPermission ? 'Update Permission' : 'Create Permission'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </Dialog>
      )}
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <Dialog
          open={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          className="fixed inset-0 z-10 overflow-y-auto"
        >
          <div className="min-h-screen px-4 text-center">
            <div className="fixed inset-0 bg-black opacity-30" />
            
            <span className="inline-block h-screen align-middle" aria-hidden="true">&#8203;</span>
            
            <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
              <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                Delete Permission
              </Dialog.Title>
              
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete the permission &quot;{permissionToDelete?.name}&quot;? This action cannot be undone.
                </p>
              </div>
              
              <div className="mt-4 flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDeleteModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button" 
                  variant="destructive"
                  onClick={handleConfirmDelete}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </Dialog>
      )}
    </div>
  );
};

export default PermissionsClient;
