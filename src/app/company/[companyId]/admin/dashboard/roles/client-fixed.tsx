'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Hi<PERSON><PERSON>, HiPencil, HiTrash } from 'react-icons/hi';
import { Dialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react';
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';

type Role = {
  _id: string;
  name: string;
  description: string;
  isSystemRole: boolean;
  permissions: string[];
  type: 'hq' | 'branch';
};

type Permission = {
  _id: string;
  name: string;
  description: string;
  category: string;
  isSystemLevel: boolean;
};

const RolesClient = () => {
  const params = useParams() || {};
  const companyId = params.companyId as string;
  
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsByCategory, setPermissionsByCategory] = useState<Record<string, Permission[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Role form state
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    type: 'hq',
    permissions: [] as string[]
  });
  
  // Delete confirmation state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);

  const fetchRoles = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/company/${companyId}/roles`);
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      
      const data = await response.json();
      setRoles(data.roles);
    } catch (err) {
      setError('Error loading roles');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await fetch(`/api/company/${companyId}/permissions`);
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      
      const data = await response.json();
      setPermissions(data.permissions);
      setPermissionsByCategory(data.groupedPermissions || {});
    } catch (err) {
      console.error('Error loading permissions:', err);
    }
  };

  // Fetch roles and permissions on component mount
  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, [companyId]);

  const handleCreateRole = () => {
    setEditingRole(null);
    setRoleForm({
      name: '',
      description: '',
      type: 'hq',
      permissions: []
    });
    setIsRoleModalOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setRoleForm({
      name: role.name,
      description: role.description,
      type: role.type,
      permissions: role.permissions
    });
    setIsRoleModalOpen(true);
  };

  const handleDeleteRole = (role: Role) => {
    setRoleToDelete(role);
    setIsDeleteModalOpen(true);
  };

  const handlePermissionToggle = (permissionId: string) => {
    setRoleForm(prev => {
      if (prev.permissions.includes(permissionId)) {
        return {
          ...prev,
          permissions: prev.permissions.filter(id => id !== permissionId)
        };
      } else {
        return {
          ...prev,
          permissions: [...prev.permissions, permissionId]
        };
      }
    });
  };

  const handleSubmitRole = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const method = editingRole ? 'PUT' : 'POST';
      const url = editingRole 
        ? `/api/company/${companyId}/roles/${editingRole._id}` 
        : `/api/company/${companyId}/roles`;
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(roleForm)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save role');
      }
      
      // Refresh the roles list
      fetchRoles();
      setIsRoleModalOpen(false);
    } catch (err) {
      console.error('Error saving role:', err);
      alert(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleConfirmDelete = async () => {
    if (!roleToDelete) return;
    
    try {
      const response = await fetch(`/api/company/${companyId}/roles/${roleToDelete._id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete role');
      }
      
      // Refresh the roles list
      fetchRoles();
      setIsDeleteModalOpen(false);
    } catch (err) {
      console.error('Error deleting role:', err);
      alert(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Role Management</h2>
        <Button onClick={handleCreateRole}>
          <HiPlus className="mr-2" />
          Add Role
        </Button>
      </div>
      
      {error && <div className="bg-red-100 text-red-800 p-4 rounded-md">{error}</div>}
      
      {isLoading ? (
        <div className="text-center py-8">Loading roles...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {roles.map(role => (
            <Card key={role._id} className={role.isSystemRole ? 'border-blue-300' : ''}>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <span>{role.name}</span>
                  {role.isSystemRole && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">System</span>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500">{role.description}</p>
                <div className="mt-2">
                  <span className="text-xs font-semibold">Permissions:</span>
                  <p className="text-xs text-gray-500">
                    {role.permissions.length} permissions granted
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleEditRole(role)}
                  disabled={role.isSystemRole}
                >
                  <HiPencil className="mr-1" />
                  Edit
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={() => handleDeleteRole(role)}
                  disabled={role.isSystemRole}
                >
                  <HiTrash className="mr-1" />
                  Delete
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
      
      {/* Role Create/Edit Modal */}
      {isRoleModalOpen && (
        <Dialog
          open={isRoleModalOpen}
          onClose={() => setIsRoleModalOpen(false)}
          className="relative z-10"
        >
          <DialogBackdrop className="fixed inset-0 bg-black/30" />
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <DialogPanel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" className="text-lg font-medium leading-6 text-gray-900">
                  {editingRole ? 'Edit Role' : 'Create New Role'}
                </DialogTitle>
              
              <form onSubmit={handleSubmitRole}>
                <div className="mt-4 space-y-4">
                  <div>
                    <Label htmlFor="name">Role Name</Label>
                    <Input
                      id="name"
                      value={roleForm.name}
                      onChange={e => setRoleForm({...roleForm, name: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={roleForm.description}
                      onChange={e => setRoleForm({...roleForm, description: e.target.value})}
                      rows={3}
                    />
                  </div>
                  
                  <div>
                    <Label>Type</Label>
                    <div className="flex space-x-4 mt-1">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="type-hq"
                          name="type"
                          value="hq"
                          checked={roleForm.type === 'hq'}
                          onChange={() => setRoleForm({...roleForm, type: 'hq'})}
                          className="mr-2"
                        />
                        <Label htmlFor="type-hq" className="cursor-pointer">Headquarters</Label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="type-branch"
                          name="type"
                          value="branch"
                          checked={roleForm.type === 'branch'}
                          onChange={() => setRoleForm({...roleForm, type: 'branch'})}
                          className="mr-2"
                        />
                        <Label htmlFor="type-branch" className="cursor-pointer">Branch</Label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <Label>Permissions</Label>
                    <div className="mt-2 border rounded-md p-4 max-h-60 overflow-y-auto">
                      <Tabs defaultValue="all">
                        <TabsList className="mb-4">
                          <TabsTrigger value="all">All</TabsTrigger>
                          {Object.keys(permissionsByCategory).map(category => (
                            <TabsTrigger key={category} value={category}>
                              {category}
                            </TabsTrigger>
                          ))}
                        </TabsList>
                        
                        <TabsContent value="all">
                          <div className="space-y-2">
                            {permissions.map(permission => (
                              <div key={permission._id} className="flex items-start space-x-2">
                                <Checkbox
                                  id={`perm-${permission._id}`}
                                  checked={roleForm.permissions.includes(permission.name)}
                                  onCheckedChange={() => handlePermissionToggle(permission.name)}
                                />
                                <div>
                                  <Label
                                    htmlFor={`perm-${permission._id}`}
                                    className="font-medium cursor-pointer"
                                  >
                                    {permission.name}
                                  </Label>
                                  <p className="text-xs text-gray-500">{permission.description}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </TabsContent>
                        
                        {Object.entries(permissionsByCategory).map(([category, perms]) => (
                          <TabsContent key={category} value={category}>
                            <div className="space-y-2">
                              {perms.map(permission => (
                                <div key={permission._id} className="flex items-start space-x-2">
                                  <Checkbox
                                    id={`perm-cat-${permission._id}`}
                                    checked={roleForm.permissions.includes(permission.name)}
                                    onCheckedChange={() => handlePermissionToggle(permission.name)}
                                  />
                                  <div>
                                    <Label
                                      htmlFor={`perm-cat-${permission._id}`}
                                      className="font-medium cursor-pointer"
                                    >
                                      {permission.name}
                                    </Label>
                                    <p className="text-xs text-gray-500">{permission.description}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </TabsContent>
                        ))}
                      </Tabs>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsRoleModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingRole ? 'Update Role' : 'Create Role'}
                  </Button>
                </div>
              </form>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <Dialog
          open={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          className="relative z-10"
        >
          <DialogBackdrop className="fixed inset-0 bg-black/30" />
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" className="text-lg font-medium leading-6 text-gray-900">
                  Delete Role
                </DialogTitle>
              
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete the role &quot;{roleToDelete?.name}&quot;? This action cannot be undone.
                </p>
              </div>
              
              <div className="mt-4 flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDeleteModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button" 
                  variant="destructive"
                  onClick={handleConfirmDelete}
                >
                  Delete
                </Button>
              </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}
    </div>
  );
};

export default RolesClient;
