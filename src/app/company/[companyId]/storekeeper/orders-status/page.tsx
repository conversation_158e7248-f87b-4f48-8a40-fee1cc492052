import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default function StorekeeperPage() {
  const { userData, loading } = useRequireCompanyUser();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (userData?.role !== 'storekeeper') {
    redirect('/');
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Orders Status</h1>
      {/* TODO: Implement storekeeper orders status page content */}
    </div>
  );
}
