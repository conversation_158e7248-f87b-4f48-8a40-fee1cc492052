import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default function StorekeeperInventoryCountPage() {
  const { userData, loading } = useRequireCompanyUser();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (userData?.role !== 'storekeeper') {
    redirect('/');
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Inventory Count</h1>
      {/* TODO: Implement storekeeper inventory count page content */}
    </div>
  );
}
