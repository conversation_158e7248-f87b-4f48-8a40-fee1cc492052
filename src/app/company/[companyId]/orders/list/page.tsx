'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams, useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Plus } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

export default function OrdersListPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const searchParams = useSearchParams();
  const companyId = params?.companyId as string;
  const status = searchParams?.get('status');

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['orders', companyId, status],
    queryFn: async () => {
      const response = await axios.get('/api/orders', {
        params: status ? { status } : undefined,
        headers: { 'company-id': companyId },
        withCredentials: true
      });
      console.log('API Response:', response.data); // Debug log
      return response.data;
    },
    enabled: !loading && !!companyId
  });

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-orange-600" />
      </div>
    );
  }
  
  // Debug missing order
  const missingOrderNumber = 'ORD-20250321-0004';
  const hasMissingOrder = data?.orders?.some((order: any) => order.orderNumber === missingOrderNumber);

  let title = 'All Orders';
  let description = 'View and manage all orders';

  if (status === 'DRAFT') {
    title = 'Draft Orders';
    description = 'Manage your draft orders and templates';
  } else if (status === 'INCOMING') {
    title = 'Incoming Orders';
    description = 'View and process incoming orders';
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
        {(!status || status === 'DRAFT') && (
          <Link href={`/company/${companyId}/orders/new`}>
            <Button className="bg-orange-600 hover:bg-orange-700">
              <Plus className="mr-2 h-4 w-4" /> New Order
            </Button>
          </Link>
        )}
      </div>

      <Card>
        <div className="p-4 flex justify-between items-center">
          <div className="flex flex-col gap-1">
            {hasMissingOrder ? (
              <p className="text-green-600">Order {missingOrderNumber} is present in the list.</p>
            ) : (
              <p className="text-red-600">Order {missingOrderNumber} is missing from the list.</p>
            )}
            {data?.debug && (
              <div className="text-sm text-gray-500">
                <span>Debug: Searched for order {data.debug.searchedFor}</span>
                <span className="mx-2">•</span>
                <span>Found: {data.debug.found ? 'Yes' : 'No'}</span>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => refetch()}
              className="ml-auto"
            >
              Refresh Orders
            </Button>
          </div>
        </div>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order Number</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Seller</TableHead>
                <TableHead>Buyer</TableHead>
                <TableHead className="text-right">Items</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.orders.map((order: any) => (
                <TableRow key={order._id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Link 
                        href={`/company/${companyId}/orders/${order._id}`}
                        className="text-orange-600 hover:text-orange-700"
                      >
                        {order.orderNumber}
                      </Link>
                      {order.orderSource === 'IONIC' && (
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                          IONIC
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(order.status || 'INCOMING')}>{order.status || 'INCOMING'}</Badge>
                  </TableCell>
                  <TableCell>{format(new Date(order.createdAt), 'MMM d, yyyy')}</TableCell>
                  <TableCell>{order.sellerLocationId?.name || '-'}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{order.buyer?.buyerType || 'CUSTOMER'}</Badge>
                      <span>{order.buyer?.name || order.buyer?.buyerName || 'Unknown'}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">{order.items?.length || 0}</TableCell>
                </TableRow>
              ))}
              {(!data?.orders || data.orders.length === 0) && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">
                    No orders found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

function getStatusColor(status: string) {
  switch (status) {
    case 'DRAFT':
      return 'bg-gray-100 text-gray-700';
    case 'INCOMING':
      return 'bg-blue-100 text-blue-700';
    case 'CONFIRMED':
      return 'bg-green-100 text-green-700';
    case 'NOT_DELIVERED':
      return 'bg-red-100 text-red-700';
    case 'PARTIALLY_DELIVERED':
      return 'bg-yellow-100 text-yellow-700';
    case 'DELIVERED':
      return 'bg-emerald-100 text-emerald-700';
    case 'CANCELLED':
      return 'bg-red-100 text-red-700';
    case 'APPROVED':
      return 'bg-purple-100 text-purple-700';
    case 'SYNCED':
      return 'bg-indigo-100 text-indigo-700';
    default:
      return 'bg-gray-100 text-gray-700';
  }
}
