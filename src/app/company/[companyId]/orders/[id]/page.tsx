// src/app/company/[companyId]/orders/[id]/page.tsx
'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { 
  Loader2, 
  TruckIcon, 
  PenIcon, 
  XIcon,
  PrinterIcon,
  FileDownIcon,
  MoreVerticalIcon,
  CheckIcon
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { closeOrder } from '@/lib/orders';
import axios from 'axios';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface OrderItem {
  itemType: 'INGREDIENT' | 'RECIPE';
  itemId: {
    _id: string;
    name: string;
    description: string;
  };
  description: string;
  quantity: number;
  uomId: {
    _id: string;
    name: string;
    shortCode: string;
  };
  unitPrice: number;
  lineTotal: number;
  deliveredQuantity: number;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: 'DRAFT' | 'INCOMING' | 'CONFIRMED' | 'NOT_DELIVERED' | 'PARTIALLY_DELIVERED' | 'DELIVERED' | 'CANCELLED' | 'APPROVED';
  sellerLocationId: {
    _id: string;
    name: string;
  };
  buyer: {
    buyerType: string;
    name: string;
  };
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

export default function OrderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const companyId = params?.companyId as string;
  const orderId = params?.id as string;
  const { userData, loading: authLoading } = useRequireCompanyUser();
  const queryClient = useQueryClient();
  const [isCreatingDeliveryNote, setIsCreatingDeliveryNote] = useState(false);

  // Debug auth state
  useEffect(() => {
    console.log('Auth state:', { userData, authLoading });
  }, [userData, authLoading]);

  // Debug order ID
  useEffect(() => {
    console.log('Order ID:', orderId);
  }, [orderId]);

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Close order mutation
  const closeMutation = useMutation({
    mutationFn: async () => {
      await closeOrder(orderId);
      queryClient.invalidateQueries({ queryKey: ['order', orderId] });
    },
    onSuccess: () => {
      toast.success('Order closed successfully');
      // Refresh order data
      fetchOrder();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to close order');
    },
  });

  const handleCloseOrder = useCallback(() => {
    if (window.confirm('Are you sure you want to close this order? This will mark it as fully delivered.')) {
      closeMutation.mutate();
    }
  }, [closeMutation]);

  const fetchOrder = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/orders/${orderId}`, {
        headers: { 'company-id': companyId },
      });
      setOrder(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch order');
    } finally {
      setLoading(false);
    }
  }, [companyId, orderId]);

  useEffect(() => {
    if (!authLoading && userData) {
      fetchOrder();
    }
  }, [authLoading, userData, fetchOrder]);

  const handleCreateDeliveryNote = async () => {
    if (isCreatingDeliveryNote || !userData) return; // Prevent multiple clicks and ensure auth
    
    try {
      console.log('Creating delivery note for order:', orderId);
      setIsCreatingDeliveryNote(true);

      const response = await axios.post(
        `/api/company/${companyId}/delivery-notes`,
        {
          orderId,
          companyId,
          orderNumber: order?.orderNumber,
        },
        {
          headers: { 'company-id': companyId },
          withCredentials: true,
        }
      );

      console.log('Created delivery note:', response.data);

      // Navigate to the edit page of the newly created delivery note
      router.push(`/company/${companyId}/delivery-notes/${response.data._id}/edit?orderId=${orderId}`);
    } catch (error) {
      console.error('Error creating delivery note:', error);
      if (axios.isAxiosError(error)) {
        toast.error(error.response?.data?.message || 'Failed to create delivery note');
      } else {
        toast.error('Failed to create delivery note');
      }
    } finally {
      setIsCreatingDeliveryNote(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-4">
        <p className="text-red-500">{error}</p>
        <Link href={`/company/${companyId}/orders`}>
          <Button variant="outline">Back to Orders</Button>
        </Link>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-4">
        <p>Order not found</p>
        <Link href={`/company/${companyId}/orders`}>
          <Button variant="outline">Back to Orders</Button>
        </Link>
      </div>
    );
  }

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-500';
      case 'INCOMING':
        return 'bg-blue-500';
      case 'CONFIRMED':
        return 'bg-green-500';
      case 'DELIVERED':
        return 'text-green-700 bg-green-50';
      case 'NOT_DELIVERED':
        return 'text-red-700 bg-red-50';
      case 'PARTIALLY_DELIVERED':
        return 'text-yellow-700 bg-yellow-50';
      case 'CANCELLED':
        return 'bg-red-500';
      case 'APPROVED':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const totalAmount = order.items.reduce((sum, item) => sum + item.lineTotal, 0);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Order Details</h1>
          <p className="text-muted-foreground">
            View and manage order {order.orderNumber}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreVerticalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => {
                // TODO: Implement print functionality
                toast.info('Print functionality coming soon');
              }}>
                <PrinterIcon className="mr-2 h-4 w-4" />
                Print
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                // TODO: Implement PDF download
                toast.info('PDF download coming soon');
              }}>
                <FileDownIcon className="mr-2 h-4 w-4" />
                Download PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          {order.status !== 'CANCELLED' && (
            <>
              {order.status === 'APPROVED' && (
                <Button 
                  className="bg-green-600 hover:bg-green-700"
                  onClick={handleCreateDeliveryNote}
                  disabled={isCreatingDeliveryNote}
                >
                  {isCreatingDeliveryNote ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <TruckIcon className="mr-2 h-4 w-4" />
                      Create Delivery Note
                    </>
                  )}
                </Button>
              )}
              {order.status === 'DRAFT' && (
                <Button 
                  variant="default"
                  onClick={async () => {
                    try {
                      const response = await fetch(`/api/orders/${order._id}`, {
                        method: 'PATCH',
                        headers: {
                          'Content-Type': 'application/json',
                          'company-id': companyId,
                        },
                        body: JSON.stringify({ status: 'APPROVED' }),
                      });
                      
                      if (!response.ok) {
                        throw new Error('Failed to approve order');
                      }
                      
                      // Refresh the page to show updated status
                      window.location.reload();
                      toast.success('Order approved successfully');
                    } catch (error) {
                      console.error('Error approving order:', error);
                      toast.error('Failed to approve order');
                    }
                  }}
                >
                  <CheckIcon className="mr-2 h-4 w-4" />
                  Approve
                </Button>
              )}
              {['DRAFT', 'INCOMING'].includes(order.status) && (
                <Link href={`/company/${companyId}/orders/${order._id}/edit`}>
                  <Button variant="outline">
                    <PenIcon className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </Link>
              )}
              <Button 
                variant="destructive" 
                onClick={async () => {
                  try {
                    const response = await fetch(`/api/orders/${order._id}`, {
                      method: 'PATCH',
                      headers: {
                        'Content-Type': 'application/json',
                        'company-id': companyId,
                      },
                      body: JSON.stringify({ status: 'CANCELLED' }),
                    });
                    
                    if (!response.ok) {
                      throw new Error('Failed to cancel order');
                    }
                    
                    // Refresh the page to show updated status
                    window.location.reload();
                    toast.success('Order cancelled successfully');
                  } catch (error) {
                    console.error('Error cancelling order:', error);
                    toast.error('Failed to cancel order');
                  }
                }}
              >
                <XIcon className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              {order.status === 'PARTIALLY_DELIVERED' && (
                <Button
                  variant="outline"
                  onClick={handleCloseOrder}
                  disabled={closeMutation.isPending}
                >
                  {closeMutation.isPending ? 'Closing...' : 'Close Order'}
                </Button>
              )}
            </>
          )}
          <Link href={`/company/${companyId}/orders`}>
            <Button variant="outline">Back to Orders</Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-semibold">Order Number:</span>
              <span>{order.orderNumber}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-semibold">Status:</span>
              <Badge className={getStatusColor(order.status)}>{order.status}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-semibold">Buyer:</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{order.buyer?.buyerType || '-'}</Badge>
                <span>{order.buyer?.name || 'Unknown'}</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-semibold">Seller:</span>
              <span>{order.sellerLocationId.name}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-semibold">Created:</span>
              <span>{format(new Date(order.createdAt), 'PPpp')}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-semibold">Updated:</span>
              <span>{format(new Date(order.updatedAt), 'PPpp')}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-semibold">Total Items:</span>
              <span>{order.items.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-semibold">Total Amount:</span>
              <span className="text-lg font-bold">{formatCurrency(totalAmount)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-4">Order Items</h3>
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Delivered
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {order.items.map((item, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.itemId.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.quantity} {item.uomId?.shortCode || ''}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.deliveredQuantity} {item.uomId?.shortCode || ''}
                    {item.deliveredQuantity < item.quantity && (
                      <span className="ml-2 text-yellow-600">
                        ({((item.deliveredQuantity / item.quantity) * 100).toFixed(0)}%)
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${item.unitPrice.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${item.lineTotal.toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
