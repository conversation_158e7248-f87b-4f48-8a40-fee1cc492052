// src/app/company/[companyId]/orders/[id]/edit/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Loader2, PlusIcon, TrashIcon } from 'lucide-react';
import { toast } from 'sonner';
import { OrderItemCombobox } from '@/components/OrderItemCombobox';

interface OrderItem {
  itemType: 'INGREDIENT' | 'RECIPE';
  itemId: string;
  description: string;
  quantity: number;
  uomId: string;
  uomShortCode: string;
  unitPrice: number;
  lineTotal: number;
}

interface UOM {
  _id: string;
  name: string;
  symbol: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: 'DRAFT' | 'INCOMING' | 'CONFIRMED' | 'NOT_DELIVERED' | 'PARTIALLY_DELIVERED' | 'DELIVERED' | 'CANCELLED' | 'APPROVED';
  items: OrderItem[];
}

export default function EditOrderPage() {
  const { companyId, id } = useParams<{ companyId: string; id: string }>();
  const router = useRouter();
  const { isLoading: isAuthLoading } = useRequireCompanyUser();
  
  const [isLoading, setIsLoading] = useState(true);
  const [order, setOrder] = useState<Order | null>(null);
  const [uoms, setUOMs] = useState<UOM[]>([]);
  const [items, setItems] = useState<OrderItem[]>([]);

  // Fetch order details
  useEffect(() => {
    const fetchOrder = async () => {
      try {
        const response = await fetch(`/api/orders/${id}`, {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch order');
        const data = await response.json();
        console.log('Raw order data:', data);
        setOrder(data);
        
        // Map items to include UOM information from the ingredient/recipe
        const mappedItems = await Promise.all(data.items.map(async (item: any) => {
          console.log('Processing item:', item);
          
          // If the item already has UOM info, use it
          if (item.uomId?._id) {
            return {
              ...item,
              description: item.itemId?.name || item.description,
              uomId: item.uomId._id,
              uomShortCode: item.uomId.shortCode,
              quantity: item.quantity || 0,
              unitPrice: item.unitPrice || 0,
              lineTotal: (item.quantity || 0) * (item.unitPrice || 0)
            };
          }

          if (!item.itemId?._id) return item;

          // Fetch the ingredient/recipe to get its UOM info
          const type = item.itemType === 'INGREDIENT' ? 'ingredients' : 'recipes';
          const itemResponse = await fetch(`/api/${type}/${item.itemId._id}`, {
            headers: {
              'company-id': companyId,
            },
          });
          
          if (!itemResponse.ok) return item;
          
          const itemData = await itemResponse.json();
          console.log('Item data:', itemData);
          const uomId = item.itemType === 'INGREDIENT' ? itemData.baseUomId : itemData.baseYieldUomId;

          // Fetch UOM details if we have a UOM ID
          let uomShortCode = '';
          if (uomId) {
            const uomResponse = await fetch(`/api/uoms/${uomId}`, {
              headers: {
                'company-id': companyId,
              },
            });
            if (uomResponse.ok) {
              const uomData = await uomResponse.json();
              uomShortCode = uomData.shortCode;
            }
          }

          return {
            ...item,
            description: item.itemId.name || item.description,
            uomId: uomId,
            uomShortCode: uomShortCode,
            quantity: item.quantity || 0,
            unitPrice: item.unitPrice || 0,
            lineTotal: (item.quantity || 0) * (item.unitPrice || 0)
          };
        }));

        console.log('Mapped items with UOM:', mappedItems);
        setItems(mappedItems);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching order:', error);
        toast.error('Failed to fetch order details');
        setIsLoading(false);
      }
    };

    if (companyId && id) {
      fetchOrder();
    }
  }, [companyId, id]);

  const handleQuantityChange = (index: number, value: number) => {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      quantity: value,
      lineTotal: value * newItems[index].unitPrice
    };
    setItems(newItems);
  };

  const handleUnitPriceChange = (index: number, value: number) => {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      unitPrice: value,
      lineTotal: newItems[index].quantity * value
    };
    setItems(newItems);
  };

  const handleIngredientSelect = (index: number, value: { 
    ingredientName: string; 
    baseUomId: string;
    baseUomShortCode: string;
    isSubRecipe: boolean;
    itemId: string;
  }) => {
    const searchResponse = async () => {
      try {
        const newItems = [...items];
        newItems[index] = {
          ...newItems[index],
          itemType: value.isSubRecipe ? 'RECIPE' : 'INGREDIENT',
          itemId: value.itemId,
          description: value.ingredientName,
          uomId: value.baseUomId,
          uomShortCode: value.baseUomShortCode,
          quantity: newItems[index].quantity || 1,
          unitPrice: newItems[index].unitPrice || 0,
          lineTotal: (newItems[index].quantity || 1) * (newItems[index].unitPrice || 0)
        };
        setItems(newItems);
      } catch (error) {
        console.error('Error getting item details:', error);
        toast.error('Failed to get item details');
      }
    };

    searchResponse();
  };

  const addItem = () => {
    setItems([
      ...items,
      {
        itemType: 'INGREDIENT',
        itemId: '',
        description: '',
        quantity: 0,
        uomId: '',
        uomShortCode: '',
        unitPrice: 0,
        lineTotal: 0,
      },
    ]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof OrderItem, value: any) => {
    const newItems = [...items];
    const item = { ...newItems[index], [field]: value };

    // Update line total when quantity or unit price changes
    if (field === 'quantity' || field === 'unitPrice') {
      item.lineTotal = item.quantity * item.unitPrice;
    }

    newItems[index] = item;
    setItems(newItems);
  };

  const saveOrder = async () => {
    try {
      // Prepare items for update by extracting UOM ID if it's an object
      const preparedItems = items.map(item => ({
        ...item,
        uomId: typeof item.uomId === 'object' ? (item.uomId as any)?._id : item.uomId,
        itemId: typeof item.itemId === 'object' ? (item.itemId as any)?._id : item.itemId,
        lineTotal: item.quantity * item.unitPrice
      }));

      const response = await fetch(`/api/orders/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify({
          ...order,
          items: preparedItems
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update order');
      }

      toast.success('Order updated successfully');
      router.push(`/company/${companyId}/orders/${id}`);
    } catch (error) {
      console.error('Error updating order:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update order');
    }
  };

  // Fetch UOMs
  useEffect(() => {
    const fetchUOMs = async () => {
      try {
        const response = await fetch('/api/uoms', {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch UOMs');
        const data = await response.json();
        setUOMs(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching UOMs:', error);
        toast.error('Failed to fetch UOMs');
        setIsLoading(false);
      }
    };

    if (companyId) {
      fetchUOMs();
    }
  }, [companyId]);

  if (isAuthLoading || isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Edit Order</h1>
          <p className="text-muted-foreground">
            Order {order?.orderNumber}
          </p>
        </div>
        <div className="space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button onClick={saveOrder}>
            Save Changes
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Order Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">Item</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>UOM</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Line Total</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="w-[200px]">
                      <OrderItemCombobox
                        value={item.description}
                        onChange={(value) => handleIngredientSelect(index, value)}
                        companyId={companyId}
                      />
                    </TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell className="w-[100px]">
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) =>
                          handleQuantityChange(index, parseFloat(e.target.value))
                        }
                      />
                    </TableCell>
                    <TableCell className="w-[80px]">
                      {item.uomShortCode || '-'}
                    </TableCell>
                    <TableCell className="w-[120px]">
                      <Input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) =>
                          handleUnitPriceChange(index, parseFloat(e.target.value))
                        }
                      />
                    </TableCell>
                    <TableCell className="text-right">
                      {item.lineTotal.toFixed(2)}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(index)}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <Button variant="outline" onClick={addItem}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Item
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
