'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Card, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FileEdit, ShoppingCart, Truck, ListFilter } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

// Configure axios defaults
axios.defaults.withCredentials = true;

export default function OrdersPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  // Fetch counts for orders
  const { data: orderCounts, isLoading: isLoadingCounts } = useQuery({
    queryKey: ['orderCounts', companyId],
    queryFn: async () => {
      try {
        const [drafts, incoming, all] = await Promise.all([
          axios.get('/api/orders', {
            params: { status: 'DRAFT' },
            headers: { 'company-id': companyId },
            withCredentials: true
          }),
          axios.get('/api/orders', {
            params: { status: 'INCOMING' },
            headers: { 'company-id': companyId },
            withCredentials: true
          }),
          axios.get('/api/orders', {
            headers: { 'company-id': companyId },
            withCredentials: true
          })
        ]);

        // Get today's counts
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString();

        const [draftToday, incomingToday] = await Promise.all([
          axios.get('/api/orders', {
            params: { 
              status: 'DRAFT',
              'createdAt[gte]': todayStr 
            },
            headers: { 'company-id': companyId },
            withCredentials: true
          }),
          axios.get('/api/orders', {
            params: { 
              status: 'INCOMING',
              'createdAt[gte]': todayStr 
            },
            headers: { 'company-id': companyId },
            withCredentials: true
          })
        ]);

        return {
          drafts: draftToday.data.pagination.total,
          incoming: incomingToday.data.pagination.total,
          all: all.data.pagination.total,
          deliveryNotes: 0 // TODO: Implement delivery notes count
        };
      } catch (error) {
        console.error('Error fetching order counts:', error);
        throw error;
      }
    },
    enabled: !loading && !!companyId,
    refetchInterval: 30000 // Refetch every 30 seconds
  });

  if (loading || isLoadingCounts) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold mb-4">Orders Management</h1>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* All Orders */}
        <Link href={`/company/${companyId}/orders/list`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>All Orders</CardTitle>
                <CardDescription>View and manage all orders</CardDescription>
              </div>
              <div className="flex items-center space-x-4">
                {orderCounts?.all > 0 && (
                  <span className="bg-orange-100 text-orange-600 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                    {orderCounts.all}
                  </span>
                )}
                <ListFilter className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
              </div>
            </CardHeader>
          </Card>
        </Link>

        {/* Draft Orders */}
        <Link href={`/company/${companyId}/orders/list?status=DRAFT`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>Draft Orders</CardTitle>
                <CardDescription>Manage order drafts and templates</CardDescription>
              </div>
              <div className="flex items-center space-x-4">
                {orderCounts?.drafts > 0 && (
                  <span className="bg-orange-100 text-orange-600 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                    +{orderCounts.drafts}
                  </span>
                )}
                <FileEdit className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
              </div>
            </CardHeader>
          </Card>
        </Link>

        {/* Incoming Orders */}
        <Link href={`/company/${companyId}/orders/list?status=INCOMING`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>Incoming Orders</CardTitle>
                <CardDescription>View and process new orders</CardDescription>
              </div>
              <div className="flex items-center space-x-4">
                {orderCounts?.incoming > 0 && (
                  <span className="bg-orange-100 text-orange-600 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                    +{orderCounts.incoming}
                  </span>
                )}
                <ShoppingCart className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
              </div>
            </CardHeader>
          </Card>
        </Link>

        {/* Delivery Notes */}
        <Link href={`/company/${companyId}/delivery-notes`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>Delivery Notes</CardTitle>
                <CardDescription>Manage delivery documentation</CardDescription>
              </div>
              <div className="flex items-center space-x-4">
                {orderCounts?.deliveryNotes > 0 && (
                  <span className="bg-orange-100 text-orange-600 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                    +{orderCounts.deliveryNotes}
                  </span>
                )}
                <Truck className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
              </div>
            </CardHeader>
          </Card>
        </Link>
      </div>
    </div>
  );
}
