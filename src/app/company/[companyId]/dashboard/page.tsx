// src/app/company/[companyId]/dashboard/page.tsx
'use client';

import { useAuth } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { CompanyUserData } from '@/lib/auth';

export default function DashboardPage() {
  const { userData, signOut } = useAuth();
  const params = useParams();
  const companyId = params?.companyId as string;

  console.log('Dashboard rendering with userData:', userData);

  const handleLogout = async () => {
    await signOut();
    // Navigation will be handled by auth state change in AuthProvider
  };
  
  const getAllLinks = (companyId: string) => {
    return [
      {
        href: `/company/${companyId}/admin/dashboard`,
        text: 'Go to Admin Dashboard',
      },
      {
        href: `/company/${companyId}/manager/dashboard`,
        text: 'Go to Manager Dashboard',
      },
      {
        href: `/company/${companyId}/storekeeper/dashboard`,
        text: 'Go to Storekeeper Dashboard',
      },
    ];
  };

  const getRoleSpecificDashboardLink = () => {
    if (!userData || userData.userType !== 'company_user') return null;
    
    const companyUser = userData as CompanyUserData;
    if (!companyUser.role) return null;

    const links = {
      owner: {
        href: `/company/${companyId}/admin/dashboard`,
        text: 'Go to Admin Dashboard'
      },
      admin: {
        href: `/company/${companyId}/admin/dashboard`,
        text: 'Go to Admin Dashboard'
      },
      manager: {
        href: `/company/${companyId}/manager/dashboard`,
        text: 'Go to Manager Dashboard'
      },
      storekeeper: {
        href: `/company/${companyId}/storekeeper/dashboard`,
        text: 'Go to Storekeeper Dashboard'
      }
    };

    return links[companyUser.role as keyof typeof links];
  };

  const roleLink = getRoleSpecificDashboardLink();
  const allLinks = getAllLinks(companyId);

  return (
    <div className="max-w-screen-xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-2">Welcome!</h2>
        <p className="mb-4">You are logged in as: {userData?.email}</p>

        {roleLink ? (
          <Link
            href={roleLink.href}
            className="block mb-4 text-blue-600 hover:text-blue-800 underline"
          >
            {roleLink.text}
          </Link>
        ) : (
          <>
            <p className="text-yellow-500 mb-4">
              No role retrieved, so we are showing all links:
            </p>
            {allLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="block mb-2 text-blue-600 hover:text-blue-800 underline"
              >
                {link.text}
              </Link>
            ))}
          </>
        )}

        <Button
          onClick={handleLogout}
          variant="destructive"
          className="bg-red-500 text-white hover:bg-red-600 block w-full sm:w-auto"
        >
          Sign Out
        </Button>
      </div>
    </div>
  );
}