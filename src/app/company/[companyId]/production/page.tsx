'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Card, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { <PERSON>lipboardList, Calculator, BarChart } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function ProductionPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold mb-4">Production Planning</h1>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Production Costing */}
        <Link href={`/company/${companyId}/production/costing`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>Production Costing</CardTitle>
                <CardDescription>Calculate and manage production costs</CardDescription>
              </div>
              <Calculator className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
            </CardHeader>
          </Card>
        </Link>

        {/* Production Orders */}
        <Link href={`/company/${companyId}/production/orders`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>Production Orders</CardTitle>
                <CardDescription>Manage production orders and schedules</CardDescription>
              </div>
              <ClipboardList className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
            </CardHeader>
          </Card>
        </Link>

        {/* Production Reports */}
        <Link href={`/company/${companyId}/production/reports`}>
          <Card className="hover:bg-accent/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle>Production Reports</CardTitle>
                <CardDescription>View and analyze production metrics</CardDescription>
              </div>
              <BarChart className="h-6 w-6 text-orange-600 group-hover:text-orange-700" />
            </CardHeader>
          </Card>
        </Link>
      </div>
    </div>
  );
}
