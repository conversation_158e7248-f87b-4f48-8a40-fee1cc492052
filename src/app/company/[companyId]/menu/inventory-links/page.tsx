'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { CompanyNav } from '@/components/CompanyNav';
import { useRequireCompanyUser } from '@/lib/auth';
import { MenuItem, MenuCategory } from '@/types';
import { InventoryLinkModal } from '@/components/menu/InventoryLinkModal';

interface MenuItemWithCategory extends MenuItem {
  category: MenuCategory;
}

export default function MenuInventoryLinks() {
  const params = useParams();
  const companyId = params.companyId as string;
  const { user } = useRequireCompanyUser(companyId);

  const [menuItems, setMenuItems] = useState<MenuItemWithCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<MenuItemWithCategory | null>(null);
  const [filter, setFilter] = useState({
    category: '',
    inventoryStatus: '',
    type: ''
  });

  const fetchMenuItems = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/company/${companyId}/menu/items?include=category`);
      if (!response.ok) throw new Error('Failed to fetch menu items');
      const data = await response.json();
      setMenuItems(data.items);
    } catch (error) {
      console.error('Error fetching menu items:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMenuItems();
  }, [companyId]);

  const handleSave = async () => {
    await fetchMenuItems();
    setSelectedItem(null);
  };

  const filteredItems = menuItems.filter(item => {
    if (filter.category && item.category._id !== filter.category) return false;
    if (filter.inventoryStatus && item.inventoryStatus !== filter.inventoryStatus) return false;
    if (filter.type && item.type !== filter.type) return false;
    return true;
  });

  const categories = [...new Set(menuItems.map(item => item.category))];

  return (
    <div className="min-h-screen bg-gray-100">
      <CompanyNav />
      
      <main className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Menu Inventory Links</h1>
            <div className="text-sm text-gray-500">
              {menuItems.filter(i => i.inventoryStatus === 'linked').length} of {menuItems.length} items linked
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <select
              className="form-select"
              value={filter.category}
              onChange={e => setFilter(f => ({ ...f, category: e.target.value }))}
            >
              <option value="">All Categories</option>
              {categories.map(cat => (
                <option key={cat._id} value={cat._id}>{cat.name}</option>
              ))}
            </select>

            <select
              className="form-select"
              value={filter.inventoryStatus}
              onChange={e => setFilter(f => ({ ...f, inventoryStatus: e.target.value }))}
            >
              <option value="">All Inventory Status</option>
              <option value="pending">Pending</option>
              <option value="linked">Linked</option>
            </select>

            <select
              className="form-select"
              value={filter.type}
              onChange={e => setFilter(f => ({ ...f, type: e.target.value }))}
            >
              <option value="">All Types</option>
              <option value="single">Single Item</option>
              <option value="recipe">Recipe</option>
            </select>
          </div>

          {/* Menu Items Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">
                      Loading...
                    </td>
                  </tr>
                ) : filteredItems.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">
                      No menu items found
                    </td>
                  </tr>
                ) : (
                  filteredItems.map(item => (
                    <tr key={item._id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {item.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {item.category.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.type === 'recipe'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {item.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.inventoryStatus === 'linked' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.inventoryStatus}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => setSelectedItem(item)}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          {item.inventoryStatus === 'linked' ? 'Edit Link' : 'Link to Inventory'}
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      {/* Inventory Link Modal */}
      {selectedItem && (
        <InventoryLinkModal
          item={selectedItem}
          onClose={() => setSelectedItem(null)}
          onSave={handleSave}
        />
      )}
    </div>
  );
}
