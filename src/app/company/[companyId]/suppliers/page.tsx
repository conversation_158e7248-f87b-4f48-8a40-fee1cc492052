// src/app/company/[companyId]/suppliers/page.tsx
'use client';

import React from 'react';
import { SupplierManagement } from '@/components/admin/SupplierManagement';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';

export default function SuppliersPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null; // or redirect to unauthorized
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <SupplierManagement />
    </div>
  );
}
