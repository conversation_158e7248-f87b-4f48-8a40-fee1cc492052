import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default function ManagerInventoryPage() {
  const { userData, loading } = useRequireCompanyUser();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (userData?.role !== 'manager') {
    redirect('/');
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Manager Inventory</h1>
      {/* TODO: Implement manager inventory page content */}
    </div>
  );
}
