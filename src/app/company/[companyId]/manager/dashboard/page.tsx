import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { ManagerDashboard } from '@/components/dashboards/ManagerDashboard';

export default function ManagerDashboardPage() {
  const { userData, loading } = useRequireCompanyUser();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (userData?.role !== 'manager') {
    redirect('/');
  }

  return <ManagerDashboard />;
}
