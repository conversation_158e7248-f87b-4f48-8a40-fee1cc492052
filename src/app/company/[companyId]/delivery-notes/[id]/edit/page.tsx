// src/app/company/[companyId]/delivery-notes/[id]/edit/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { 
  Loader2, 
  PlusIcon, 
  TrashIcon,
  GripVertical,
  AlertCircle,
  CheckSquare,
  Square
} from 'lucide-react';
import { toast } from 'sonner';
import { useQuery, useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { format } from 'date-fns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd';
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { DeliveryNotesOrderCombobox } from '@/components/DeliveryNotesOrderCombobox';
import { Checkbox } from "@/components/ui/checkbox";

// Types
interface DeliveryItem {
  itemId: {
    _id: string;
    name: string;
    description: string;
  };
  quantityPlanned: number;
  uomId: {
    _id: string;
    name: string;
    shortCode: string;
  };
}

interface HandoverStep {
  stepType: string;
  status: 'PENDING' | 'SIGNED' | 'SKIPPED';
  required: boolean;
}

interface DeliveryNote {
  _id: string;
  deliveryNoteNumber: string;
  status: 'DRAFT' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  order: {
    _id: string;
  };
  orderId: string;
  deliveryDate: string;
  items: DeliveryItem[];
  handoverFlow: HandoverStep[];
}

interface OrderItem {
  itemType: 'INGREDIENT' | 'RECIPE';
  itemId: {
    _id: string;
    name: string;
    description: string;
  };
  quantity: number;
  uomId: {
    _id: string;
    name: string;
    shortCode: string;
  };
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
}

export default function EditDeliveryNotePage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const searchParams = useSearchParams();
  const orderId = searchParams?.get('orderId');
  const router = useRouter();
  const companyId = params?.companyId as string;
  const deliveryNoteId = params?.id as string;

  // State
  const [deliveryNote, setDeliveryNote] = useState<DeliveryNote | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [itemQuantities, setItemQuantities] = useState<Record<string, number>>({});
  const [isLoadingOrder, setIsLoadingOrder] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [flowSettings] = useState({
    handoverSteps: [
      { 
        stepType: 'DISPATCH',
        required: true,
      },
      { 
        stepType: 'SHOP',
        required: true,
      }
    ]
  });

  // Fetch delivery note data
  const { data: deliveryNoteData, isLoading: isLoadingDeliveryNote } = useQuery({
    queryKey: ['deliveryNote', deliveryNoteId],
    queryFn: async () => {
      const response = await axios.get(
        `/api/company/${companyId}/delivery-notes/${deliveryNoteId}`,
        { headers: { 'company-id': companyId } }
      );
      return response.data;
    },
    enabled: !!deliveryNoteId && deliveryNoteId !== 'new',
  });

  // Effect to handle initial order data
  useEffect(() => {
    const fetchOrderData = async () => {
      // Get orderId from either URL params or delivery note
      const targetOrderId = orderId || deliveryNoteData?.order?._id;
      if (!targetOrderId || !companyId) return;

      console.log('Fetching order data for:', targetOrderId);
      setIsLoadingOrder(true);
      try {
        const response = await axios.get(`/api/orders/${targetOrderId}`, {
          headers: { 'company-id': companyId },
        });
        const orderData = response.data;
        console.log('Fetched order data:', orderData);

        setSelectedOrder(orderData);

        // Pre-select all items from the order
        if (orderData.items && Array.isArray(orderData.items)) {
          const orderItemIds = orderData.items.map((item: OrderItem) => item.itemId._id);
          setSelectedItems(new Set(orderItemIds));

          // Initialize quantities from order
          const quantities: Record<string, number> = {};
          orderData.items.forEach((item: OrderItem) => {
            quantities[item.itemId._id] = item.quantity;
          });
          setItemQuantities(quantities);
        }

      } catch (error) {
        console.error('Error fetching order:', error);
        toast.error('Failed to fetch order details');
      } finally {
        setIsLoadingOrder(false);
      }
    };

    fetchOrderData();
  }, [companyId, orderId, deliveryNoteData]);

  // Effect to update delivery note state
  useEffect(() => {
    if (deliveryNoteData) {
      setDeliveryNote(deliveryNoteData);
      if (deliveryNoteData.deliveryDate) {
        setSelectedDate(new Date(deliveryNoteData.deliveryDate));
      }
    }
  }, [deliveryNoteData]);

  // Handle order selection from combobox
  const handleOrderSelect = async (orderId: string | null) => {
    console.log('Order selection triggered with orderId:', orderId);
    
    if (!orderId || !companyId) {
      console.log('Clearing order selection state');
      setSelectedOrder(null);
      setSelectedItems(new Set());
      setItemQuantities({});
      setDeliveryNote(null);
      return;
    }

    setIsLoadingOrder(true);
    try {
      const response = await axios.get(`/api/orders/${orderId}`, {
        headers: { 'company-id': companyId },
      });
      const orderData = response.data;
      console.log('Fetched order data:', orderData);

      setSelectedOrder(orderData);
      
      // Create or update delivery note with new order
      setDeliveryNote(prev => {
        const base = prev || {
          _id: deliveryNoteId || '',
          deliveryNoteNumber: '',
          status: 'DRAFT' as const,
          deliveryDate: '',
          handoverFlow: [
            { 
              stepType: 'DISPATCH' as const,
              status: 'PENDING' as const,
              required: true,
              confirmedItems: []
            },
            { 
              stepType: 'SHOP' as const,
              status: 'PENDING' as const,
              required: true,
              confirmedItems: []
            }
          ],
        };
        
        const updatedNote: DeliveryNote = {
          ...base,
          order: { _id: orderData._id },
          orderId: orderData._id,
          items: []
        };
        console.log('Setting delivery note state:', updatedNote);
        return updatedNote;
      });

      if (orderData.items && Array.isArray(orderData.items)) {
        // Pre-select all items from the order
        const orderItemIds = orderData.items.map((item: OrderItem) => item.itemId._id);
        setSelectedItems(new Set(orderItemIds));
        
        // Initialize quantities from order
        const quantities: Record<string, number> = {};
        orderData.items.forEach((item: OrderItem) => {
          quantities[item.itemId._id] = item.quantity;
        });
        setItemQuantities(quantities);
      }
    } catch (error) {
      console.error('Error fetching order:', error);
      toast.error('Failed to fetch order details');
      setSelectedOrder(null);
      setSelectedItems(new Set());
      setItemQuantities({});
      setDeliveryNote(null);
    } finally {
      setIsLoadingOrder(false);
    }
  };

  // Effect to update delivery note when items change
  useEffect(() => {
    if (!selectedOrder || !deliveryNote) return;

    console.log('Updating items in deliveryNote. Current state:', {
      selectedItems,
      itemQuantities,
      selectedOrder,
      deliveryNote
    });

    const selectedItemsArray = Array.from(selectedItems);
    const items = selectedItemsArray.map(itemId => {
      const orderItem = selectedOrder.items.find(item => item.itemId._id === itemId);
      if (!orderItem) {
        console.log('Warning: Could not find order item for id:', itemId);
        return null;
      }

      return {
        itemId: orderItem.itemId,
        quantityPlanned: itemQuantities[itemId] || orderItem.quantity,
        uomId: orderItem.uomId
      };
    }).filter(Boolean);

    console.log('Setting new items in deliveryNote:', items);

    setDeliveryNote(prev => {
      if (!prev) {
        return null;
      }
      return {
        ...prev,
        items
      };
    });
  }, [selectedItems, itemQuantities, selectedOrder, deliveryNote]);

  // Log state changes for debugging
  useEffect(() => {
    console.log('State updated:', {
      selectedOrder,
      itemsCount: selectedOrder?.items?.length,
      selectedItems: Array.from(selectedItems),
      itemQuantities,
    });
  }, [selectedOrder, selectedItems, itemQuantities]);

  // Debug effect to monitor state changes
  useEffect(() => {
    console.log('State updated:', {
      selectedOrder,
      itemsCount: selectedOrder?.items?.length,
      selectedItems: selectedItems ? Array.from(selectedItems) : [],
      itemQuantities
    });
  }, [selectedOrder, selectedItems, itemQuantities]);

  // Debug logging for render
  console.log('Render state:', {
    selectedOrder,
    selectedItems: selectedItems ? Array.from(selectedItems) : [],
    itemQuantities,
    isLoadingOrder
  });

  // Create new delivery note
  const createDeliveryNote = async (orderId: string) => {
    if (isCreating) return; // Prevent double submission
    setIsCreating(true);

    try {
      const response = await axios.get(
        `/api/company/${companyId}/delivery-notes/new`,
        {
          params: { orderId },
          headers: { 'company-id': companyId },
        }
      );
      
      const newDeliveryNote = response.data;
      console.log('Created delivery note:', newDeliveryNote);
      
      // Redirect to the edit page for the new delivery note
      router.push(`/company/${companyId}/delivery-notes/${newDeliveryNote._id}/edit`);
    } catch (error) {
      console.error('Error creating delivery note:', error);
      toast.error('Failed to create delivery note');
    } finally {
      setIsCreating(false);
    }
  };

  // Initialize form with data
  useEffect(() => {
    if (deliveryNoteId === 'new' && orderId && !isCreating) {
      createDeliveryNote(orderId);
    } else if (deliveryNoteData) {
      setDeliveryNote(deliveryNoteData);
      setSelectedDate(new Date(deliveryNoteData.deliveryDate));
      
      // Initialize selected items and quantities
      const items = new Set(deliveryNoteData.items.map(item => item.itemId._id));
      setSelectedItems(items);
      
      const quantities: { [key: string]: number } = {};
      deliveryNoteData.items.forEach(item => {
        quantities[item.itemId._id] = item.quantityPlanned;
      });
      setItemQuantities(quantities);
    }
  }, [deliveryNoteId, deliveryNoteData, orderId, isCreating, createDeliveryNote]);

  // Handle item selection
  const handleItemSelect = (itemId: string, checked: boolean) => {
    const newSelectedItems = new Set(selectedItems);
    if (checked) {
      newSelectedItems.add(itemId);
    } else {
      newSelectedItems.delete(itemId);
    }
    setSelectedItems(newSelectedItems);

    if (!deliveryNote || !selectedOrder) return;

    // Update delivery note items based on selection
    const updatedItems = selectedOrder.items
      .filter((item: OrderItem) => newSelectedItems.has(item.itemId._id))
      .map((item: OrderItem) => ({
        itemId: item.itemId,
        quantityPlanned: itemQuantities[item.itemId._id] || item.quantity,
        uomId: item.uomId,
      }));

    setDeliveryNote({
      ...deliveryNote,
      items: updatedItems,
    });
  };

  // Handle quantity change
  const handleQuantityChange = (itemId: string, quantity: number) => {
    if (!deliveryNote || !selectedOrder) return;

    const newQuantities = { ...itemQuantities, [itemId]: quantity };
    setItemQuantities(newQuantities);

    // Update delivery note items with new quantity
    const updatedItems = deliveryNote.items.map(item => 
      item.itemId._id === itemId
        ? { ...item, quantityPlanned: quantity }
        : item
    );

    setDeliveryNote({
      ...deliveryNote,
      items: updatedItems,
    });
  };

  // Handle adding a new handover step
  const addStep = () => {
    if (!deliveryNote) return;

    setDeliveryNote({
      ...deliveryNote,
      handoverFlow: [
        ...deliveryNote.handoverFlow,
        {
          stepType: 'DISPATCH',
          status: 'PENDING',
          required: true,
        },
      ],
    });
  };

  // Handle removing a handover step
  const removeStep = (index: number) => {
    if (!deliveryNote) return;

    const newFlow = [...deliveryNote.handoverFlow];
    newFlow.splice(index, 1);

    setDeliveryNote({
      ...deliveryNote,
      handoverFlow: newFlow,
    });
  };

  // Handle updating a handover step
  const updateStep = (index: number, updates: Partial<HandoverStep>) => {
    if (!deliveryNote) return;

    const newFlow = [...deliveryNote.handoverFlow];
    newFlow[index] = {
      ...newFlow[index],
      ...updates,
    };

    setDeliveryNote({
      ...deliveryNote,
      handoverFlow: newFlow,
    });
  };

  // Handle reordering handover steps
  const onDragEnd = (result: DropResult) => {
    if (!deliveryNote || !result.destination) return;

    const { source, destination } = result;
    const steps = Array.from(deliveryNote.handoverFlow);
    const [removed] = steps.splice(source.index, 1);
    steps.splice(destination.index, 0, removed);

    setDeliveryNote({
      ...deliveryNote,
      handoverFlow: steps,
    });
  };

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: async (data: Partial<DeliveryNote>) => {
      if (deliveryNoteId === 'new') {
        const response = await axios.post(
          `/api/company/${companyId}/delivery-notes`,
          data,
          { headers: { 'company-id': companyId } }
        );
        return response.data;
      } else {
        const response = await axios.patch(
          `/api/company/${companyId}/delivery-notes/${deliveryNoteId}`,
          data,
          { headers: { 'company-id': companyId } }
        );
        return response.data;
      }
    },
    onSuccess: (data) => {
      toast.success('Delivery note saved successfully');
      router.push(`/company/${companyId}/delivery-notes/${data._id}`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to save delivery note');
    },
  });

  // Handle save
  const handleSave = () => {
    console.log('Save triggered with full deliveryNote:', JSON.stringify(deliveryNote, null, 2));
    console.log('Selected date:', selectedDate);
    console.log('Selected order:', selectedOrder);
    console.log('Selected items:', selectedItems);

    if (!deliveryNote || !selectedDate) {
      console.log('Validation failed: Missing deliveryNote or selectedDate');
      toast.error('Please fill in all required fields');
      return;
    }

    if (!deliveryNote.order || !deliveryNote.order._id) {
      console.log('Validation failed: Missing order in deliveryNote:', deliveryNote);
      
      // If we have orderId but not order, fix it
      if (deliveryNote.orderId && selectedOrder) {
        const updatedDeliveryNote = {
          ...deliveryNote,
          order: { _id: selectedOrder._id }
        };
        console.log('Fixed delivery note with order:', updatedDeliveryNote);
        setDeliveryNote(updatedDeliveryNote);
        return; // Return and let the effect trigger save again
      }
      
      toast.error('Please select an order');
      return;
    }

    if (deliveryNote.items.length === 0) {
      console.log('Validation failed: No items selected');
      toast.error('Please select at least one item');
      return;
    }

    // Validate required steps
    const missingRequiredSteps = flowSettings.handoverSteps
      .filter(step => step.required)
      .filter(requiredStep => 
        !deliveryNote.handoverFlow.some(step => step.stepType === requiredStep.stepType)
      );

    if (missingRequiredSteps.length > 0) {
      console.log('Validation failed: Missing required steps:', missingRequiredSteps);
      toast.error(`Missing required steps: ${missingRequiredSteps.map(step => step.stepType).join(', ')}`);
      return;
    }

    const saveData = {
      ...deliveryNote,
      deliveryDate: selectedDate.toISOString(),
    };
    console.log('Attempting to save with data:', saveData);

    saveMutation.mutate(saveData);
  };

  if (loading || isLoadingDeliveryNote) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-5xl">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold">
            {deliveryNoteId === 'new' ? 'Create Delivery Note' : 'Edit Delivery Note'}
          </h1>
        </div>

        <div className="grid gap-4">
          {/* Order and Delivery Info */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Order</Label>
              <DeliveryNotesOrderCombobox
                value={selectedOrder?._id}
                onChange={handleOrderSelect}
                companyId={companyId}
                placeholder="Select order..."
              />
            </div>
            
            <div className="space-y-2">
              <Label>Delivery Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !selectedDate && "text-muted-foreground"
                    )}
                  >
                    {selectedDate ? (
                      format(selectedDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Buyer and Seller Info */}
          {selectedOrder && (
            <Card>
              <CardHeader>
                <CardTitle>Delivery Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-2">From (Seller)</h3>
                    <div className="text-sm space-y-1">
                      <p>{selectedOrder.sellerLocationId?.name}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">
                      To ({selectedOrder.buyer?.buyerType === 'CUSTOMER' ? 'Customer' : 'Supplier'})
                    </h3>
                    <div className="text-sm space-y-1">
                      <p>{selectedOrder.buyer?.name}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Order Items */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Order Items</CardTitle>
                {selectedOrder?.items?.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newSelectedItems = new Set(selectedOrder.items.map(item => item.itemId._id));
                      setSelectedItems(newSelectedItems);
                      const newQuantities = selectedOrder.items.reduce((acc, item) => ({
                        ...acc,
                        [item.itemId._id]: item.quantity
                      }), {});
                      setItemQuantities(newQuantities);
                    }}
                  >
                    Select All Items
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingOrder ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : selectedOrder?.items?.length > 0 ? (
                <div className="space-y-4">
                  {selectedOrder.items.map((item) => (
                    <div key={item._id} className="flex items-start space-x-4 p-4 rounded-lg border">
                      <Checkbox
                        checked={selectedItems.has(item.itemId._id)}
                        onCheckedChange={(checked) => {
                          const newSelectedItems = new Set(selectedItems);
                          if (checked) {
                            newSelectedItems.add(item.itemId._id);
                            setItemQuantities(prev => ({
                              ...prev,
                              [item.itemId._id]: item.quantity
                            }));
                          } else {
                            newSelectedItems.delete(item.itemId._id);
                            const newQuantities = { ...itemQuantities };
                            delete newQuantities[item.itemId._id];
                            setItemQuantities(newQuantities);
                          }
                          setSelectedItems(newSelectedItems);
                        }}
                      />
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{item.itemId.name}</p>
                            <p className="text-sm text-muted-foreground">{item.description || 'No description'}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Input
                              type="number"
                              value={itemQuantities[item.itemId._id] || ''}
                              onChange={(e) => {
                                const value = parseFloat(e.target.value);
                                if (!isNaN(value)) {
                                  setItemQuantities(prev => ({
                                    ...prev,
                                    [item.itemId._id]: value
                                  }));
                                }
                              }}
                              className="w-24"
                              disabled={!selectedItems.has(item.itemId._id)}
                            />
                            <span className="text-sm text-muted-foreground">
                              {item.uomId.shortCode}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {selectedOrder ? 'No items in this order' : 'Select an order to view items'}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Handover Flow */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Handover Flow</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addStep}
                  disabled={!deliveryNote || deliveryNote.handoverFlow.length >= 6}
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Step
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Configure the handover flow steps for this delivery note. Steps marked as required
                  cannot be removed. The order of steps determines the sequence in which they must
                  be completed.
                </AlertDescription>
              </Alert>

              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="steps">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="space-y-2"
                    >
                      {deliveryNote?.handoverFlow.map((step, index) => (
                        <Draggable
                          key={index}
                          draggableId={`step-${index}`}
                          index={index}
                          isDragDisabled={step.required}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className="flex items-center space-x-4 p-4 bg-white border rounded-lg"
                            >
                              <div {...provided.dragHandleProps}>
                                <GripVertical className="h-5 w-5 text-gray-400" />
                              </div>
                              
                              <Select
                                value={step.stepType}
                                onValueChange={(value) => updateStep(index, { stepType: value })}
                                disabled={step.required}
                              >
                                <SelectTrigger className="w-[200px]">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {['DISPATCH', 'QUALITY_CHECK', 'DRIVER', 'SECURITY', 'SHOP', 'CUSTOMER'].map((type) => (
                                    <SelectItem
                                      key={type}
                                      value={type}
                                      disabled={deliveryNote.handoverFlow.some(
                                        (s, i) => i !== index && s.stepType === type
                                      )}
                                    >
                                      {type.charAt(0) + type.slice(1).toLowerCase()}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>

                              {!step.required && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeStep(index)}
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </Button>
                              )}

                              {step.required && (
                                <span className="text-sm text-gray-500">
                                  Required Step
                                </span>
                              )}
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>

              {(!deliveryNote?.handoverFlow || deliveryNote.handoverFlow.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  No handover steps configured. Click &quot;Add Step&quot; to get started.
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={() => router.back()}>Cancel</Button>
          <Button onClick={handleSave} disabled={saveMutation.isLoading}>
            {saveMutation.isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}
