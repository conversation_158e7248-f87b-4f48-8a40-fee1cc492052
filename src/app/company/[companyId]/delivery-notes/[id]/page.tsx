'use client';

import { use<PERSON>ara<PERSON> } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { 
  Loader2, 
  TruckIcon, 
  PenIcon, 
  XIcon,
  PrinterIcon,
  FileDownIcon,
  MoreVerticalIcon,
  CheckIcon,
  ClipboardSignatureIcon,
  AlertTriangle,
  RotateCcw
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import axios from 'axios';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Types
interface DeliveryItem {
  _id: string;
  itemId: {
    _id: string;
    name: string;
    description: string;
  };
  quantityPlanned: number;
  uomId: {
    _id: string;
    name: string;
    shortCode: string;
  };
}

interface HandoverStep {
  stepType: 'DISPATCH' | 'DRIVER' | 'SHOP';
  status: 'PENDING' | 'SIGNED' | 'SKIPPED';
  signedByUserId?: {
    _id: string;
    name: string;
  };
  signedAt?: string;
  signatureImageUrl?: string;
  confirmedItems: Array<{
    itemId: string;
    confirmedQty: number;
    reason?: string;
  }>;
  notes?: string;
}

interface DeliveryNote {
  _id: string;
  deliveryNoteNumber: string;
  status: 'DRAFT' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  orderId: {
    _id: string;
    orderNumber: string;
  };
  deliveryDate: string;
  items: DeliveryItem[];
  handoverFlow: HandoverStep[];
  createdAt: string;
  updatedAt: string;
}

// Status badge colors
const statusColors = {
  DRAFT: 'bg-gray-100 text-gray-800',
  IN_PROGRESS: 'bg-blue-100 text-blue-800',
  COMPLETED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800',
};

interface QuantityConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: DeliveryItem[];
  previousStepItems?: HandoverStep['confirmedItems'];
  onConfirm: (confirmedItems: HandoverStep['confirmedItems']) => void;
  stepType: HandoverStep['stepType'];
}

function QuantityConfirmationDialog({
  open,
  onOpenChange,
  items,
  previousStepItems,
  onConfirm,
  stepType
}: QuantityConfirmationDialogProps) {
  const [confirmedQuantities, setConfirmedQuantities] = useState<Record<string, number>>({});
  const [reasons, setReasons] = useState<Record<string, string>>({});
  const [notes, setNotes] = useState('');

  // Initialize quantities from planned quantities
  useEffect(() => {
    const initialQuantities: Record<string, number> = {};
    items.forEach((item, index) => {
      if (!item.itemId) return; // Skip items with null itemId
      
      // Use a safe key for the item
      const itemKey = item.itemId?._id || `item-${index}`;
      
      // If there's a previous step, use its quantities as default
      const previousQty = previousStepItems?.find(i => i.itemId === item.itemId?._id)?.confirmedQty;
      initialQuantities[itemKey] = previousQty ?? item.quantityPlanned;
    });
    setConfirmedQuantities(initialQuantities);
  }, [items, previousStepItems]);

  const handleConfirm = () => {
    const confirmedItems = items
      .filter(item => item.itemId) // Filter out items with null itemId
      .map((item, index) => {
        const itemKey = item.itemId?._id || `item-${index}`;
        return {
          itemId: item.itemId?._id || '',
          confirmedQty: confirmedQuantities[itemKey] || 0,
          reason: reasons[itemKey] || ''
        };
      });
    onConfirm(confirmedItems);
    onOpenChange(false);
  };

  const hasQuantityDiscrepancy = (itemId?: string) => {
    if (!itemId) return false;
    
    const plannedQty = items.find(item => item.itemId?._id === itemId)?.quantityPlanned;
    const previousQty = previousStepItems?.find(item => item.itemId === itemId)?.confirmedQty;
    const currentQty = confirmedQuantities[itemId];
    
    // If we don't have valid quantities to compare, don't show discrepancy
    if (plannedQty === undefined && previousQty === undefined) return false;
    
    return currentQty !== (previousQty ?? plannedQty);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Confirm Quantities - {stepType}</DialogTitle>
          <DialogDescription>
            Please verify and adjust quantities if needed. Provide a reason for any changes.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Previous Qty</TableHead>
                <TableHead>Current Qty</TableHead>
                <TableHead>Reason for Change</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item, index) => {
                const previousQty = previousStepItems?.find(i => i.itemId === item.itemId?._id)?.confirmedQty ?? item.quantityPlanned;
                const showReason = hasQuantityDiscrepancy(item.itemId?._id);
                
                return (
                  <TableRow key={item.itemId?._id || index}>
                    <TableCell>{item.itemId?.name || 'Unknown Item'}</TableCell>
                    <TableCell>{previousQty} {item.uomId?.shortCode || ''}</TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.itemId?._id ? confirmedQuantities[item.itemId._id] || 0 : 0}
                        onChange={(e) => {
                          const newQty = parseFloat(e.target.value);
                          setConfirmedQuantities(prev => ({
                            ...prev,
                            [item.itemId?._id || `item-${index}`]: newQty
                          }));
                        }}
                        className="w-24"
                      />
                      <span className="ml-2">{item.uomId?.shortCode || ''}</span>
                    </TableCell>
                    <TableCell>
                      {showReason && (
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                          <Input
                            placeholder="Reason for change"
                            value={item.itemId?._id ? reasons[item.itemId._id] || '' : ''}
                            onChange={(e) => {
                              setReasons(prev => ({
                                ...prev,
                                [item.itemId?._id || `item-${index}`]: e.target.value
                              }));
                            }}
                            className="flex-1"
                          />
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any additional notes about this handover step..."
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm}>
            Confirm and Sign
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function DeliveryNoteDetailPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params?.companyId as string;
  const deliveryNoteId = params?.id as string;
  const queryClient = useQueryClient();
  const [quantityDialogOpen, setQuantityDialogOpen] = useState(false);
  const [selectedStepIndex, setSelectedStepIndex] = useState<number | null>(null);

  // Fetch delivery note data
  const { data: deliveryNote, isLoading, error: queryError } = useQuery<DeliveryNote>({
    queryKey: ['deliveryNote', deliveryNoteId],
    queryFn: async () => {
      try {
        const response = await axios.get(
          `/api/company/${companyId}/delivery-notes/${deliveryNoteId}`,
          {
            headers: { 'company-id': companyId },
            withCredentials: true
          }
        );
        return response.data;
      } catch (error: any) {
        console.error('Error fetching delivery note:', error.response || error);
        throw new Error(error.response?.data?.error || 'Failed to fetch delivery note');
      }
    },
    enabled: !!userData && !!companyId && !!deliveryNoteId,
    staleTime: 30000, // 30 seconds
  });

  // Cancel delivery note mutation
  const cancelMutation = useMutation({
    mutationFn: async () => {
      await axios.patch(
        `/api/company/${companyId}/delivery-notes/${deliveryNoteId}`,
        { status: 'CANCELLED' },
        { headers: { 'company-id': companyId } }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deliveryNote', deliveryNoteId] });
      toast.success('Delivery note cancelled successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to cancel delivery note');
    },
  });

  // Sign step mutation
  const signStepMutation = useMutation({
    mutationFn: async ({ 
      stepIndex, 
      confirmedItems,
      notes 
    }: { 
      stepIndex: number; 
      confirmedItems: HandoverStep['confirmedItems'];
      notes?: string;
    }) => {
      const updatedHandoverFlow = [...(deliveryNote?.handoverFlow || [])];
      updatedHandoverFlow[stepIndex] = {
        ...updatedHandoverFlow[stepIndex],
        status: 'SIGNED',
        signedByUserId: userData?.uid ? { _id: userData.uid, name: userData.email?.split('@')[0] || 'User' } : undefined,
        signedAt: new Date().toISOString(),
        confirmedItems,
        notes
      };

      await axios.patch(
        `/api/company/${companyId}/delivery-notes/${deliveryNoteId}`,
        { 
          handoverFlow: updatedHandoverFlow,
          status: stepIndex === updatedHandoverFlow.length - 1 ? 'COMPLETED' : 'IN_PROGRESS'
        },
        { headers: { 'company-id': companyId } }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deliveryNote', deliveryNoteId] });
      toast.success('Step signed successfully');
      setSelectedStepIndex(null);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to sign step');
    },
  });

  // Undo step mutation
  const undoStepMutation = useMutation({
    mutationFn: async ({ stepIndex }: { stepIndex: number }) => {
      const updatedHandoverFlow = [...(deliveryNote?.handoverFlow || [])];

      // Reset the target step and all subsequent steps
      for (let i = stepIndex; i < updatedHandoverFlow.length; i++) {
        updatedHandoverFlow[i] = {
          ...updatedHandoverFlow[i],
          status: 'PENDING',
          signedByUserId: undefined,
          signedAt: undefined,
          confirmedItems: [],
          notes: undefined
        };
      }

      // Calculate new delivery note status
      let newStatus: DeliveryNote['status'];
      if (stepIndex === 0) {
        newStatus = 'DRAFT';
      } else {
        const lastSignedStep = updatedHandoverFlow[stepIndex - 1];
        newStatus = lastSignedStep.status === 'SIGNED' ? 'IN_PROGRESS' : 'DRAFT';
      }

      await axios.patch(
        `/api/company/${companyId}/delivery-notes/${deliveryNoteId}`,
        { 
          handoverFlow: updatedHandoverFlow,
          status: newStatus
        },
        { headers: { 'company-id': companyId } }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deliveryNote', deliveryNoteId] });
      toast.success('Step undone successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to undo step');
    },
  });

  const handleCancelClick = () => {
    if (window.confirm('Are you sure you want to cancel this delivery note?')) {
      cancelMutation.mutate();
    }
  };

  const handleSignStep = (stepIndex: number) => {
    setSelectedStepIndex(stepIndex);
    setQuantityDialogOpen(true);
  };

  const handleQuantityConfirm = (confirmedItems: HandoverStep['confirmedItems']) => {
    if (selectedStepIndex === null) return;
    setQuantityDialogOpen(false);
    signStepMutation.mutate({ 
      stepIndex: selectedStepIndex, 
      confirmedItems 
    });
  };

  const handleUndoStep = (stepIndex: number) => {
    const stepType = deliveryNote?.handoverFlow[stepIndex].stepType;
    const subsequentSteps = deliveryNote?.handoverFlow
      .slice(stepIndex + 1)
      .filter(step => step.status === 'SIGNED')
      .map(step => step.stepType)
      .join(', ');
    
    const message = subsequentSteps 
      ? `Are you sure you want to undo the ${stepType} step?\n\nThis will also reset the following steps that come after it: ${subsequentSteps}\n\nSteps before ${stepType} will remain unchanged.`
      : `Are you sure you want to undo the ${stepType} step?\n\nAll previous steps will remain unchanged.`;

    if (window.confirm(message)) {
      undoStepMutation.mutate({ stepIndex });
    }
  };

  const currentStep = deliveryNote?.handoverFlow.find(step => step.status === 'PENDING');
  const currentStepIndex = deliveryNote?.handoverFlow.findIndex(step => step.status === 'PENDING');
  const previousStep = currentStepIndex !== undefined && currentStepIndex > 0 
    ? deliveryNote?.handoverFlow[currentStepIndex - 1] 
    : undefined;

  // Check if user has management permissions
  const userRole = (userData as any)?.role || '';
  const isManager = ['MANAGER', 'ADMIN', 'OWNER'].includes(userRole.toUpperCase());

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (queryError) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col items-center justify-center bg-red-50 p-6 border border-red-200 rounded-lg">
          <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
          <h2 className="text-xl font-semibold text-red-700">Error Loading Delivery Note</h2>
          <p className="text-gray-600 mt-2">{(queryError as Error)?.message || 'Delivery note not found or access denied'}</p>
          <Link href={`/company/${companyId}/delivery-notes`} className="mt-4">
            <Button variant="outline">Back to Delivery Notes</Button>
          </Link>
        </div>
      </div>
    );
  }
  
  if (!deliveryNote) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col items-center justify-center bg-yellow-50 p-6 border border-yellow-200 rounded-lg">
          <AlertTriangle className="h-8 w-8 text-yellow-500 mb-2" />
          <h2 className="text-xl font-semibold">Delivery Note Not Found</h2>
          <p className="text-gray-600 mt-2">The requested delivery note could not be found.</p>
          <Link href={`/company/${companyId}/delivery-notes`} className="mt-4">
            <Button variant="outline">Back to Delivery Notes</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Delivery Note Details</h1>
          <p className="text-gray-500">
            {deliveryNote.deliveryNoteNumber} • Created{' '}
            {format(new Date(deliveryNote.createdAt), 'MMM d, yyyy')}
          </p>
        </div>
        <div className="flex space-x-2">
          {deliveryNote.status === 'DRAFT' && (
            <>
              <Link href={`/company/${companyId}/delivery-notes/${deliveryNoteId}/edit?orderId=${deliveryNote.orderId._id}`}>
                <Button variant="outline">
                  <PenIcon className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
              <Button variant="outline" onClick={handleCancelClick}>
                <XIcon className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </>
          )}
          <Button variant="outline">
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline">
            <FileDownIcon className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Status and Order Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Delivery Information</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="text-gray-500">Status</dt>
                <dd>
                  <Badge className={statusColors[deliveryNote.status]}>
                    {deliveryNote.status}
                  </Badge>
                </dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Order Number</dt>
                <dd>
                  <Link 
                    href={`/company/${companyId}/orders/${deliveryNote.orderId._id}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {deliveryNote.orderId.orderNumber}
                  </Link>
                </dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Delivery Date</dt>
                <dd>{format(new Date(deliveryNote.deliveryDate), 'MMM d, yyyy')}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Current Step</dt>
                <dd>{currentStep ? currentStep.stepType : 'COMPLETED'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        {/* Current Step Card */}
        {currentStep && deliveryNote.status !== 'CANCELLED' && (
          <Card>
            <CardHeader>
              <CardTitle>Current Step: {currentStep.stepType}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 mb-4">
                This step requires confirmation and signature from the {currentStep.stepType.toLowerCase()}.
              </p>
              <Button 
                className="w-full"
                onClick={() => handleSignStep(
                  deliveryNote.handoverFlow.findIndex(step => step.stepType === currentStep.stepType)
                )}
              >
                <ClipboardSignatureIcon className="h-4 w-4 mr-2" />
                Sign and Confirm
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Items Table with History */}
      <Card>
        <CardHeader>
          <CardTitle>Items and Quantity History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Item</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right w-[100px]">Order Qty</TableHead>
                <TableHead className="text-right w-[100px]">Planned Qty</TableHead>
                {deliveryNote.handoverFlow.map((step, index) => (
                  <TableHead key={step.stepType} className="text-right w-[120px]">
                    <div className="flex flex-col items-end gap-1">
                      <div className="font-medium">
                        {step.stepType} Qty
                        {step.status === 'SIGNED' && (
                          <span className="ml-2 text-xs text-muted-foreground block">
                            {format(new Date(step.signedAt!), 'MMM d, HH:mm')}
                          </span>
                        )}
                      </div>
                      {isManager && step.status === 'SIGNED' && (
                        <Button 
                          variant="outline"
                          size="sm"
                          onClick={() => handleUndoStep(index)}
                          className="flex items-center gap-1 text-xs border-red-200 hover:border-red-300 hover:bg-red-50"
                        >
                          <RotateCcw className="h-3 w-3" />
                          Undo
                        </Button>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {deliveryNote.items.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.itemId?.name || 'Unknown Item'}</TableCell>
                  <TableCell>{item.itemId?.description || '-'}</TableCell>
                  <TableCell className="text-right">{item.quantityPlanned} {item.uomId?.shortCode || ''}</TableCell>
                  <TableCell className="text-right">{item.quantityPlanned} {item.uomId?.shortCode || ''}</TableCell>
                  {deliveryNote.handoverFlow.map((step, stepIndex) => {
                    const confirmedItem = step.confirmedItems?.find(
                      (ci) => ci.itemId === item.itemId?._id
                    );
                    return (
                      <TableCell key={stepIndex} className="text-right">
                        {step.status === 'SIGNED' && confirmedItem
                          ? `${confirmedItem.confirmedQty} ${item.uomId?.shortCode || ''}`
                          : '-'}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Quantity Confirmation Dialog */}
      <QuantityConfirmationDialog
        open={quantityDialogOpen}
        onOpenChange={setQuantityDialogOpen}
        items={deliveryNote.items}
        previousStepItems={previousStep?.confirmedItems}
        onConfirm={handleQuantityConfirm}
        stepType={currentStep?.stepType || 'DISPATCH'}
      />
    </div>
  );
}
