'use client';

import { useEffect, useState } from "react";
import { useRequireCompanyUser } from "@/lib/auth";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, Plus, Trash2, ArrowUpDown, ArrowDown, ArrowUp } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";

interface DeliveryNote {
  _id: string;
  deliveryNoteNumber: string;
  status: 'DRAFT' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  orderId: {
    _id: string;
    orderNumber: string;
  };
  deliveryDate: string;
  items: Array<{
    itemId: {
      _id: string;
      name: string;
      description: string;
    };
    quantityPlanned: number;
    uomId: {
      _id: string;
      name: string;
      shortCode: string;
    };
  }>;
  handoverFlow: Array<{
    stepType: string;
    status: 'PENDING' | 'SIGNED' | 'SKIPPED';
  }>;
  createdAt: string;
  updatedAt: string;
}

const statusColors = {
  DRAFT: 'bg-gray-100 text-gray-800',
  IN_PROGRESS: 'bg-blue-100 text-blue-800',
  COMPLETED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800'
};

export default function DeliveryNotesPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const companyId = params?.companyId as string;

  const [deliveryNotes, setDeliveryNotes] = useState<DeliveryNote[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  
  // Selection state
  const [selectedNotes, setSelectedNotes] = useState<Record<string, boolean>>({});
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Pagination state
  const [page, setPage] = useState(() => {
    const pageParam = searchParams?.get("page");
    return pageParam ? parseInt(pageParam) : 1;
  });
  const [limit, setLimit] = useState(() => {
    const limitParam = searchParams?.get("limit");
    return limitParam ? parseInt(limitParam) : 10;
  });
  const [total, setTotal] = useState(0);
  const [statusCounts, setStatusCounts] = useState({
    draft: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0
  });
  
  // Sorting state
  type SortField = 'deliveryNoteNumber' | 'orderNumber' | 'deliveryDate' | 'status' | 'createdAt';
  type SortDirection = 'asc' | 'desc';
  const [sortField, setSortField] = useState<SortField>('deliveryNoteNumber');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  useEffect(() => {
    const fetchDeliveryNotes = async () => {
      try {
        console.log('Fetching delivery notes with params:', {
          page,
          limit,
          searchTerm,
          selectedStatus,
          companyId,
          sortField,
          sortDirection
        });

        let url = `/api/company/${companyId}/delivery-notes?page=${page}&limit=${limit}`;
        if (searchTerm) {
          url += `&search=${encodeURIComponent(searchTerm)}`;
        }
        if (selectedStatus) {
          url += `&status=${selectedStatus}`;
        }
        url += `&sortField=${sortField}&sortDirection=${sortDirection}`;

        const response = await fetch(url, {
          headers: {
            "company-id": companyId,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch delivery notes");
        }

        const data = await response.json();
        setDeliveryNotes(data.deliveryNotes);
        setTotal(data.pagination.total);
        setStatusCounts(data.counts);
        setError(null);
      } catch (error) {
        console.error("Error fetching delivery notes:", error);
        setError(
          error instanceof Error ? error.message : "Failed to fetch delivery notes"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeliveryNotes();
  }, [companyId, page, limit, searchTerm, selectedStatus, sortField, sortDirection]);

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    setPage(1); // Reset to first page when filter changes
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1); // Reset to first page when search changes
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    router.push(
      `/company/${companyId}/delivery-notes?page=${newPage}&limit=${limit}`
    );
  };
  
  const handleLimitChange = (value: string) => {
    const newLimit = parseInt(value);
    setLimit(newLimit);
    setPage(1); // Reset to first page when limit changes
    router.push(
      `/company/${companyId}/delivery-notes?page=1&limit=${newLimit}`
    );
  };
  
  // Selection handling functions
  const toggleSelectAll = () => {
    if (getSelectedCount() === deliveryNotes.length) {
      // If all are selected, deselect all
      setSelectedNotes({});
    } else {
      // Otherwise, select all
      const newSelectedNotes: Record<string, boolean> = {};
      deliveryNotes.forEach(note => {
        newSelectedNotes[note._id] = true;
      });
      setSelectedNotes(newSelectedNotes);
    }
  };
  
  const toggleSelectNote = (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedNotes(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  
  const getSelectedCount = () => {
    return Object.values(selectedNotes).filter(Boolean).length;
  };
  
  const handleSort = (field: SortField) => {
    // If clicking the same field, toggle direction
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking a new field, set it as the sort field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleDeleteSelected = async () => {
    try {
      setIsDeleting(true);
      const selectedIds = Object.entries(selectedNotes)
        .filter(([_, isSelected]) => isSelected)
        .map(([id]) => id);
      
      const response = await fetch(`/api/company/${companyId}/delivery-notes/bulk-delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        body: JSON.stringify({ ids: selectedIds })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete selected delivery notes');
      }
      
      // Clear selections and refresh data
      setSelectedNotes({});
      setIsDeleteDialogOpen(false);
      
      // Refresh the list (re-trigger the useEffect)
      const currentPage = selectedIds.length === deliveryNotes.length ? Math.max(1, page - 1) : page;
      setPage(currentPage);
      
      toast({
        title: 'Success',
        description: `${selectedIds.length} delivery note(s) deleted successfully.`,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error deleting delivery notes:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete delivery notes',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold">Delivery Notes</h1>
          <p className="text-muted-foreground">
            Manage your delivery notes and track their status
          </p>
        </div>
        <Button onClick={() => router.push(`/company/${companyId}/delivery-notes/new`)}>
          <Plus className="mr-2 h-4 w-4" />
          New Delivery Note
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <div className="relative max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search delivery notes..."
              value={searchTerm}
              onChange={handleSearch}
              className="pl-8"
            />
          </div>
        </div>
        <Select value={selectedStatus} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All</SelectItem>
            <SelectItem value="DRAFT">Draft</SelectItem>
            <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
            <SelectItem value="COMPLETED">Completed</SelectItem>
            <SelectItem value="CANCELLED">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        {getSelectedCount() > 0 && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setIsDeleteDialogOpen(true)}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Delete Selected ({getSelectedCount()})
          </Button>
        )}
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.draft}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.inProgress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.completed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts.cancelled}</div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Notes Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox 
                    checked={getSelectedCount() === deliveryNotes.length && deliveryNotes.length > 0}
                    onCheckedChange={toggleSelectAll}
                    aria-label="Select all"
                  />
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('deliveryNoteNumber')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Number</span>
                    {sortField === 'deliveryNoteNumber' ? (
                      sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                    ) : (
                      <ArrowUpDown className="h-4 w-4 opacity-50" />
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('orderNumber')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Order</span>
                    {sortField === 'orderNumber' ? (
                      sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                    ) : (
                      <ArrowUpDown className="h-4 w-4 opacity-50" />
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('deliveryDate')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Delivery Date</span>
                    {sortField === 'deliveryDate' ? (
                      sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                    ) : (
                      <ArrowUpDown className="h-4 w-4 opacity-50" />
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    {sortField === 'status' ? (
                      sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                    ) : (
                      <ArrowUpDown className="h-4 w-4 opacity-50" />
                    )}
                  </div>
                </TableHead>
                <TableHead>
                  Current Step
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Created At</span>
                    {sortField === 'createdAt' ? (
                      sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
                    ) : (
                      <ArrowUpDown className="h-4 w-4 opacity-50" />
                    )}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {deliveryNotes.map((note) => {
                const currentStep = note.handoverFlow.find(step => step.status === 'PENDING');
                
                return (
                  <TableRow 
                    key={note._id}
                    className="hover:bg-muted/50"
                  >
                    <TableCell className="p-0 pl-4">
                      <Checkbox 
                        checked={!!selectedNotes[note._id]}
                        onCheckedChange={(checked) => {
                          setSelectedNotes(prev => ({
                            ...prev,
                            [note._id]: !!checked,
                          }));
                        }}
                        onClick={(e) => e.stopPropagation()}
                        aria-label={`Select ${note.deliveryNoteNumber}`}
                      />
                    </TableCell>
                    <TableCell 
                      className="cursor-pointer"
                      onClick={() => router.push(`/company/${companyId}/delivery-notes/${note._id}`)}
                    >
                      {note.deliveryNoteNumber}
                    </TableCell>
                    <TableCell 
                      className="cursor-pointer"
                      onClick={() => router.push(`/company/${companyId}/delivery-notes/${note._id}`)}
                    >
                      {note.orderId.orderNumber}
                    </TableCell>
                    <TableCell 
                      className="cursor-pointer"
                      onClick={() => router.push(`/company/${companyId}/delivery-notes/${note._id}`)}
                    >
                      {format(new Date(note.deliveryDate), 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell 
                      className="cursor-pointer"
                      onClick={() => router.push(`/company/${companyId}/delivery-notes/${note._id}`)}
                    >
                      <Badge className={statusColors[note.status]}>
                        {note.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell 
                      className="cursor-pointer"
                      onClick={() => router.push(`/company/${companyId}/delivery-notes/${note._id}`)}
                    >
                      {currentStep?.stepType || '-'}
                    </TableCell>
                    <TableCell 
                      className="cursor-pointer"
                      onClick={() => router.push(`/company/${companyId}/delivery-notes/${note._id}`)}
                    >
                      {format(new Date(note.createdAt), 'MMM d, yyyy')}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-4">
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            Showing {Math.min((page - 1) * limit + 1, total || 0)} to{" "}
            {Math.min(page * limit, total || 0)} of {total || 0} entries
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Show</span>
            <Select value={limit.toString()} onValueChange={handleLimitChange}>
              <SelectTrigger className="w-[80px] h-8">
                <SelectValue>{limit}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">per page</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(page - 1)}
            disabled={page === 1}
            size="sm"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            onClick={() => handlePageChange(page + 1)}
            disabled={page * limit >= total}
            size="sm"
          >
            Next
          </Button>
        </div>
      </div>
      
      {/* Delete confirmation dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Selected Delivery Notes</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {getSelectedCount()} selected delivery notes? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)} 
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteSelected}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
