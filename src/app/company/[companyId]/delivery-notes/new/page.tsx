'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

export default function NewDeliveryNotePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { userData, loading: isAuthLoading } = useRequireCompanyUser();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('Auth state:', { userData, isAuthLoading });

    // Wait for auth to load
    if (isAuthLoading || !userData?.companyId) {
      return;
    }

    const orderId = searchParams.get('orderId');
    console.log('Order ID:', orderId);

    if (!orderId) {
      setError('Order ID is required');
      setIsLoading(false);
      return;
    }

    // Create new delivery note
    const createDeliveryNote = async () => {
      try {
        console.log('Creating delivery note for order:', orderId);
        const response = await fetch(`/api/company/${userData.companyId}/delivery-notes/new?orderId=${orderId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'company-id': userData.companyId,
          },
        });

        if (!response.ok) {
          const data = await response.json();
          console.error('Failed to create delivery note:', data);
          throw new Error(data.error || 'Failed to create delivery note');
        }

        const deliveryNote = await response.json();
        console.log('Created delivery note:', deliveryNote);
        
        // Navigate to the edit page
        router.push(`/company/${userData.companyId}/delivery-notes/${deliveryNote._id}/edit`);
      } catch (error: unknown) {
        console.error('Error creating delivery note:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to create delivery note';
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    createDeliveryNote();
  }, [userData?.companyId, searchParams, router, isAuthLoading, setError, setIsLoading]);

  if (isAuthLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Creating Delivery Note</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading && (
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
