'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Table, TableHeader, TableHead, TableBody, TableRow, TableCell } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Key } from 'lucide-react';

interface Role {
  _id: string;
  name: string;
  description: string;
  permissions: string[];
}

export default function RolesPage() {
  const { companyId } = useParams<{ companyId: string }>();
  const router = useRouter();
  const { isLoading: authLoading } = useRequireCompanyUser();
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    if (!authLoading && companyId) {
      fetchRoles();
    }
  }, [authLoading, companyId]);

  const fetchRoles = async () => {
    try {
      const res = await fetch(`/api/company/${companyId}/roles`, {
        headers: { 'company-id': companyId },
      });
      if (!res.ok) throw new Error('Failed to fetch roles');
      const data = await res.json();
      setRoles(data.roles);
    } catch (error: any) {
      toast.error(error.message || 'Error fetching roles');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Delete this role?')) return;
    try {
      const res = await fetch(`/api/company/${companyId}/roles/${id}`, {
        method: 'DELETE',
        headers: { 'company-id': companyId }
      });
      if (!res.ok) throw new Error('Failed to delete role');
      setRoles(prev => prev.filter(r => r._id !== id));
      toast.success('Role deleted');
    } catch (error: any) {
      toast.error(error.message || 'Error deleting role');
    }
  };

  if (authLoading || loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold flex items-center">
          <Key className="h-6 w-6 mr-2" /> Roles
        </h1>
        <Button onClick={() => router.push(`/company/${companyId}/roles/create`)}>Create Role</Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Permissions</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {roles.map((role) => (
            <TableRow key={role._id}>
              <TableCell>{role.name}</TableCell>
              <TableCell>{role.description}</TableCell>
              <TableCell>{role.permissions.join(', ')}</TableCell>
              <TableCell>
                <Button variant="outline" size="sm" className="mr-2" onClick={() => router.push(`/company/${companyId}/roles/${role._id}/edit`)}>Edit</Button>
                <Button variant="destructive" size="sm" onClick={() => handleDelete(role._id)}>Delete</Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
