// src/app/company/[companyId]/products/selling-options/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LocationMultiSelect } from '@/components/LocationMultiSelect';
import { DataTable } from '@/components/ui/data-table';

interface SellingOption {
  id: string;
  name: string;
  sourceType: 'recipe' | 'ingredient';
  sourceId: string;
  unitOfSellingShortCode: string;
  priceWithoutTax: number;
  priceWithTax: number;
  visibility: {
    type: 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';
    locations?: string[];
    externalAccess?: boolean;
  };
}

export default function SellingOptionsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;
  
  const [options, setOptions] = useState<SellingOption[]>([]);
  const [selected, setSelected] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBulkEditOpen, setIsBulkEditOpen] = useState(false);
  const [bulkEditType, setBulkEditType] = useState<string | null>(null);
  const [bulkEditLocations, setBulkEditLocations] = useState<string[]>([]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [sourceTypeFilter, setSourceTypeFilter] = useState<'all' | 'recipe' | 'ingredient'>('all');

  useEffect(() => {
    const fetchSellingOptions = async () => {
      try {
        console.log('Starting to fetch selling options:', {
          companyId,
          selectedLocations,
          sourceTypeFilter
        });

        const queryParams = new URLSearchParams();
        if (selectedLocations.length > 0) {
          queryParams.set('locations', selectedLocations.join(','));
        }
        if (sourceTypeFilter !== 'all') {
          queryParams.set('sourceType', sourceTypeFilter);
        }

        const url = `/api/company/${companyId}/selling-options?${queryParams.toString()}`;
        console.log('Fetching from URL:', url);
        
        const response = await fetch(url, {
          headers: {
            'company-id': companyId,
          },
        });

        console.log('Response status:', response.status);
        
        const data = await response.json();
        
        if (!response.ok) {
          console.error('API error response:', data);
          throw new Error(data.error ? `${data.error}: ${data.details}` : 'Failed to fetch selling options');
        }

        if (!Array.isArray(data)) {
          console.error('Invalid response format:', data);
          throw new Error('Invalid response format from server');
        }

        console.log('API Response:', {
          totalOptions: data.length,
          firstOption: data[0],
        });

        setOptions(data);
        setError(null);
      } catch (error) {
        console.error('Error in fetchSellingOptions:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch selling options');
      } finally {
        setIsLoading(false);
      }
    };

    if (companyId && !loading) {
      fetchSellingOptions();
    }
  }, [companyId, loading, selectedLocations, sourceTypeFilter]);

  const handleBulkVisibilityChange = (value: string) => {
    if (value === 'SPECIFIC_LOCATIONS') {
      setBulkEditType(value);
      setIsBulkEditOpen(true);
    } else {
      handleBulkUpdate(value);
    }
  };

  const handleBulkUpdate = async (visibilityType?: string) => {
    try {
      const visibility = {
        type: visibilityType || bulkEditType,
        locations: visibilityType === 'SPECIFIC_LOCATIONS' ? bulkEditLocations : undefined,
        externalAccess: visibilityType === 'EXTERNAL_ONLY' ? true : undefined,
      };

      const response = await fetch(`/api/company/${companyId}/selling-options`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify({
          optionIds: selected,
          visibility,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update selling options');
      }

      // Refresh the data
      const updatedOptions = options.map(option => {
        if (selected.includes(option.id)) {
          return {
            ...option,
            visibility,
          };
        }
        return option;
      });

      setOptions(updatedOptions);
      setSelected([]);
      setIsBulkEditOpen(false);
      setBulkEditType(null);
      setBulkEditLocations([]);
    } catch (error) {
      console.error('Error updating selling options:', error);
      setError(error instanceof Error ? error.message : 'Failed to update selling options');
    }
  };

  const handleClearSelection = () => {
    setSelected([]);
  };

  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null;
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Selling Options</h1>
          <div className="flex items-center space-x-4">
            {selected.length > 0 && (
              <>
                <Select value={bulkEditType} onValueChange={handleBulkVisibilityChange}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Set visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL_LOCATIONS">All Locations</SelectItem>
                    <SelectItem value="SPECIFIC_LOCATIONS">Specific Locations</SelectItem>
                    <SelectItem value="EXTERNAL_ONLY">External Only</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" onClick={handleClearSelection}>
                  Clear Selection ({selected.length})
                </Button>
              </>
            )}
          </div>
        </div>

        <div className="flex space-x-4 items-center">
          <div className="w-1/3">
            <LocationMultiSelect
              companyId={companyId}
              value={selectedLocations}
              onChange={setSelectedLocations}
              placeholder="Filter by locations..."
            />
          </div>
          <div>
            <Select value={sourceTypeFilter} onValueChange={(value: 'all' | 'recipe' | 'ingredient') => setSourceTypeFilter(value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="recipe">Recipes</SelectItem>
                <SelectItem value="ingredient">Ingredients</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DataTable
          columns={[
            {
              id: 'select',
              header: ({ table }) => (
                <Checkbox
                  checked={table.getIsAllPageRowsSelected()}
                  onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                  aria-label="Select all"
                />
              ),
              cell: ({ row }) => (
                <Checkbox
                  checked={row.getIsSelected()}
                  onCheckedChange={(value) => row.toggleSelected(!!value)}
                  aria-label="Select row"
                />
              ),
            },
            {
              accessorKey: 'name',
              header: 'Name',
            },
            {
              accessorKey: 'sourceType',
              header: 'Type',
              cell: ({ row }) => (
                <span className="capitalize">
                  {row.original.sourceType}
                </span>
              ),
            },
            {
              accessorKey: 'unitOfSellingShortCode',
              header: 'Unit',
            },
            {
              accessorKey: 'priceWithoutTax',
              header: 'Price (excl. tax)',
              cell: ({ row }) => (
                <span>{row.original.priceWithoutTax.toFixed(2)}</span>
              ),
            },
            {
              accessorKey: 'priceWithTax',
              header: 'Price (incl. tax)',
              cell: ({ row }) => (
                <span>{row.original.priceWithTax.toFixed(2)}</span>
              ),
            },
            {
              accessorKey: 'visibility',
              header: 'Visibility',
              cell: ({ row }) => {
                const visibility = row.original.visibility;
                return (
                  <span>
                    {visibility.type === 'ALL_LOCATIONS'
                      ? 'All Locations'
                      : visibility.type === 'SPECIFIC_LOCATIONS'
                      ? `${visibility.locations?.length || 0} Location(s)`
                      : 'External Only'}
                  </span>
                );
              },
            },
          ]}
          data={options}
          onSelectionChange={setSelected}
        />
      </div>

      <Dialog open={isBulkEditOpen} onOpenChange={setIsBulkEditOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Locations</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Label>Locations</Label>
            <LocationMultiSelect
              companyId={companyId}
              value={bulkEditLocations}
              onChange={setBulkEditLocations}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBulkEditOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleBulkUpdate()}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
