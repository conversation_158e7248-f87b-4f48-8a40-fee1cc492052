'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Package2, Utensils, UtensilsCrossed } from 'lucide-react';

export default function ProductsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null; // or redirect to unauthorized
  }

  const productCards = [
    {
      title: 'Ingredients',
      description: 'Manage your ingredients inventory, suppliers, and stock levels',
      icon: <Package2 className="h-12 w-12 text-primary" />,
      path: `ingredients`
    },
    {
      title: 'Recipes',
      description: 'Create and manage your recipes with detailed instructions and costs',
      icon: <Utensils className="h-12 w-12 text-primary" />,
      path: `recipes`
    },
    {
      title: 'Sub-Recipes',
      description: 'Manage intermediate preparations and component recipes',
      icon: <UtensilsCrossed className="h-12 w-12 text-primary" />,
      path: `sub-recipes`
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Products Management</h1>
        <p className="text-muted-foreground">Manage your ingredients, recipes, and sub-recipes</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {productCards.map((card) => (
          <Card
            key={card.title}
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => router.push(`/company/${companyId}/products/${card.path}`)}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle>{card.title}</CardTitle>
                  <CardDescription>{card.description}</CardDescription>
                </div>
                {card.icon}
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    </div>
  );
}
