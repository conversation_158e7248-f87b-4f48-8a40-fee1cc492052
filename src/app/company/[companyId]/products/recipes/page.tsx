// src/app/company/[companyId]/products/recipes/page.tsx
"use client";

import React, { useEffect, useState } from "react";
import { useRequireCompanyUser } from "@/lib/auth";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { RecipeCard } from "@/components/RecipeCard";
import { IRecipe } from "@/models/Recipe";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

export default function RecipesPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const companyId = params.companyId as string;

  const [recipes, setRecipes] = useState<IRecipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRecipes, setSelectedRecipes] = useState<Set<string>>(new Set());
  const [categories, setCategories] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // Pagination state
  const [page, setPage] = useState(() => {
    const pageParam = searchParams.get("page");
    return pageParam ? parseInt(pageParam) : 1;
  });
  const [limit, setLimit] = useState(() => {
    const limitParam = searchParams.get("limit");
    return limitParam ? parseInt(limitParam) : 10;
  });
  const [total, setTotal] = useState(0);

  useEffect(() => {
    const fetchRecipes = async () => {
      try {
        console.log('Fetching recipes with params:', {
          page,
          limit,
          searchTerm,
          companyId
        });

        const response = await fetch(
          `/api/recipes?isSubRecipe=false&page=${page}&limit=${limit}${
            searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : ""
          }`,
          {
            headers: {
              "company-id": companyId,
            },
          }
        );

        console.log('Response status:', response.status);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error response:', errorData);
          throw new Error(errorData.error || "Failed to fetch recipes");
        }

        const data = await response.json();
        console.log('API Response:', {
          recipesCount: data.recipes?.length || 0,
          firstRecipe: data.recipes?.[0],
          total: data.total
        });

        if (!data.recipes) {
          console.error('No recipes array in response:', data);
          throw new Error('Invalid response format');
        }

        setRecipes(data.recipes);
        setTotal(data.total);

        // Extract unique categories
        const uniqueCategories = Array.from(
          new Set(data.recipes.map((r: IRecipe) => r.Category).filter(Boolean))
        );
        setCategories(uniqueCategories);

        setError(null);
      } catch (error) {
        console.error("Error fetching recipes:", error);
        setError(
          error instanceof Error ? error.message : "Failed to fetch recipes"
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (userData && userData.companyId === companyId) {
      console.log('User authenticated, fetching recipes');
      fetchRecipes();
    } else {
      console.log('Not fetching recipes:', { 
        hasUserData: !!userData, 
        userCompanyId: userData?.companyId,
        pageCompanyId: companyId 
      });
    }
  }, [userData, companyId, page, limit, searchTerm]);

  // Update URL when pagination changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    params.set("limit", limit.toString());
    if (searchTerm) params.set("search", searchTerm);
    else params.delete("search");

    router.push(`?${params.toString()}`);
  }, [page, limit, searchTerm, router, searchParams]);

  const handleSelect = (recipeId: string) => {
    setSelectedRecipes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(recipeId)) {
        newSet.delete(recipeId);
      } else {
        newSet.add(recipeId);
      }
      return newSet;
    });
  };

  const handleBulkUpdateCategory = async (newCategory: string) => {
    // Convert "all" to empty string if you want to treat it that way
    const finalCategory = newCategory === "all" ? "" : newCategory;

    try {
      const response = await fetch("/api/recipes/bulk", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "company-id": companyId,
        },
        body: JSON.stringify({
          ids: Array.from(selectedRecipes),
          update: { Category: finalCategory },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update recipes");
      }

      // Refresh the recipes list
      window.location.reload();
    } catch (error) {
      console.error("Error updating recipes:", error);
      setError(
        error instanceof Error ? error.message : "Failed to update recipes"
      );
    }
  };

  const filteredRecipes = recipes.filter((recipe) => {
    const matchesSearch =
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchTerm.toLowerCase());

    // If selectedCategory is empty, that’s basically "All categories"
    const matchesCategory =
      !selectedCategory || recipe.Category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // We handle "loading" or "no user" states
  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null;
  }

  // When user picks "all", we set selectedCategory to ""
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value === "all" ? "" : value);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Recipes</h1>
          <p className="text-muted-foreground">Manage your recipes</p>
        </div>
        <Button variant="default">Create Recipe</Button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-md">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search recipes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>

        {/* Categories Select */}
        <Select value={selectedCategory || "all"} onValueChange={handleCategoryChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            {/* Use "all" as the value for "All categories" */}
            <SelectItem value="all">All categories</SelectItem>
            {categories.map((category, idx) => (
              <SelectItem key={`${idx}-${category}`} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Bulk Update */}
      {selectedRecipes.size > 0 && (
        <div className="flex items-center space-x-4 p-4 bg-muted rounded-lg">
          <span>{selectedRecipes.size} recipes selected</span>
          <Select onValueChange={handleBulkUpdateCategory}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Update category" />
            </SelectTrigger>
            <SelectContent>
              {/* Also offer "all" here if you want to clear the category for the selected recipes */}
              <SelectItem value="all">Clear category</SelectItem>
              {categories.map((category, idx) => (
                <SelectItem key={`bulk-${idx}-${category}`} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Recipes List */}
      <div className="space-y-4">
        {filteredRecipes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No recipes found. Create your first recipe to get started.
          </div>
        ) : (
          filteredRecipes.map((recipe) => (
            <RecipeCard
              key={recipe._id}
              recipe={recipe}
              isSelected={selectedRecipes.has(recipe._id)}
              onSelect={() => handleSelect(recipe._id)}
            />
          ))
        )}
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between border-t pt-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Items per page:</span>
          <Select
            value={limit.toString()}
            onValueChange={(value) => {
              setLimit(parseInt(value));
              setPage(1); // Reset to first page when changing limit
            }}
          >
            <SelectTrigger className="w-[70px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {[10, 25, 50, 100].map((value) => (
                <SelectItem key={value} value={value.toString()}>
                  {value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">
            {total > 0
              ? `Showing ${(page - 1) * limit + 1}-${
                  Math.min(page * limit, total)
                } of ${total}`
              : "No items"}
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="text-sm">Page {page}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => p + 1)}
            disabled={page * limit >= total}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
