'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import { RecipeForm } from '@/components/RecipeForm';
import { useRequireCompanyUser } from '@/lib/auth';

export default function NewRecipePage() {
  const params = useParams();
  const router = useRouter();
  const companyId = params.companyId as string;
  const { userData } = useRequireCompanyUser(companyId);

  const handleSubmit = async (data: any) => {
    const response = await fetch('/api/recipes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'company-id': companyId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create recipe');
    }

    router.push(`/company/${companyId}/products/recipes`);
  };

  if (!userData) return null;

  return (
    <div className="container mx-auto py-8">
      <RecipeForm
        companyId={companyId}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
