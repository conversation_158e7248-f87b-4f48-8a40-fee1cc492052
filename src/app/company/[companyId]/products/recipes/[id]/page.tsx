//src/app/company/[companyId]/products/recipes/[id]/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';
import { IRecipe } from '@/models/Recipe';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getRecipe } from '@/app/actions';
import { Button } from '@/components/ui/button';
import { ArrowLeft, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Utensils, Calculator, ShoppingBag } from 'lucide-react';

interface CostBreakdown {
  ingredientId: string;
  name: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  isSubRecipe: boolean;
}

interface CostingData {
  totalCost: number;
  costPerUnit: number;
  costBreakdown: CostBreakdown[];
  yield: number;
  yieldUnit: string;
}

export default function RecipeDetailsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const companyId = params.companyId as string;
  const recipeId = params.id as string;
  const [recipe, setRecipe] = useState<IRecipe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('ingredients');
  const [costingData, setCostingData] = useState<CostingData | null>(null);
  const [costingLoading, setCostingLoading] = useState(false);
  const [costingError, setCostingError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecipe = async () => {
      try {
        const response = await fetch(`/api/recipes/${recipeId}`, {
          headers: {
            'company-id': companyId
          }
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch recipe');
        }
        
        const data = await response.json();
        console.log('Recipe data from API:', {
          yield: data.yield,
          baseYieldUomId: data.baseYieldUomId,
          yieldUomShortCode: data.yieldUomShortCode,
          firstIngredient: data.recipeIngredients[0]
        });
        setRecipe(data);
        setError(null);
      } catch (error) {
        console.error('Error fetching recipe:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch recipe');
      } finally {
        setIsLoading(false);
      }
    };

    if (userData && userData.companyId === companyId) {
      fetchRecipe();
    }
  }, [userData, companyId, recipeId]);

  const fetchCostingData = async () => {
    setCostingLoading(true);
    setCostingError(null);
    
    try {
      const response = await fetch(`/api/recipes/${recipeId}/costing`, {
        headers: {
          'company-id': companyId
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch costing data');
      }
      
      const data = await response.json();
      console.log('Costing data:', data);
      setCostingData(data);
    } catch (error) {
      console.error('Error fetching costing data:', error);
      setCostingError(error instanceof Error ? error.message : 'Failed to fetch costing data');
    } finally {
      setCostingLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleDelete = () => {
    // implement delete logic here
  };

  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null;
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Link
          href={`/company/${params.companyId}/products/recipes`}
          className="inline-flex items-center text-orange-600 hover:text-orange-800"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to Recipes
        </Link>
        <div className="bg-red-50 text-red-600 p-4 rounded-md">
          {error}
        </div>
      </div>
    );
  }

  if (!recipe) {
    return (
      <div className="space-y-4">
        <Link
          href={`/company/${params.companyId}/products/recipes`}
          className="inline-flex items-center text-orange-600 hover:text-orange-800"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to Recipes
        </Link>
        <div>Recipe not found</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{recipe.name}</h1>
        <div className="space-x-2">
          <Link
            href={`/company/${params.companyId}/products/recipes/${params.id}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Edit
          </Link>
          <button
            onClick={handleDelete}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
        </div>
      </div>

      <div className="mb-4">
        <p className="text-gray-600">{recipe.description}</p>
        <p className="mt-2">
          Yield: {recipe.yield} {recipe.yieldUomShortCode}
        </p>
      </div>

      <Tabs defaultValue="ingredients" className="w-full" onValueChange={(value) => {
        setActiveTab(value);
        if (value === 'costing' && !costingData && !costingLoading) {
          fetchCostingData();
        }
      }}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="ingredients" className="flex items-center gap-2">
            <Utensils className="h-4 w-4" />
            Ingredients
          </TabsTrigger>
          <TabsTrigger value="costing" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Costing
          </TabsTrigger>
          <TabsTrigger value="selling" className="flex items-center gap-2">
            <ShoppingBag className="h-4 w-4" />
            Selling Options
          </TabsTrigger>
        </TabsList>

        <TabsContent value="ingredients">
          <Card>
            <CardHeader>
              <CardTitle>Recipe Components</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recipe.recipeIngredients.map((ingredient, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium">{ingredient.ingredientName}</p>
                      <p className="text-sm text-gray-600">
                        {ingredient.isSubRecipe ? 'Sub-Recipe' : 'Ingredient'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p>{ingredient.smallQuantity?.toLocaleString(undefined, { minimumFractionDigits: 1, maximumFractionDigits: 1 })} {ingredient.smallUomShortCode}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="costing">
          <Card>
            <CardHeader>
              <CardTitle>Recipe Costing</CardTitle>
            </CardHeader>
            <CardContent>
              {costingLoading ? (
                <div className="flex items-center justify-center p-6">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
                  <span className="ml-3">Loading costing data...</span>
                </div>
              ) : costingError ? (
                <div className="bg-red-50 p-4 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-600 mr-3 mt-0.5" />
                  <p className="text-red-600">{costingError}</p>
                </div>
              ) : costingData ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-4 rounded-md">
                      <p className="text-sm text-gray-600">Total Recipe Cost</p>
                      <p className="text-2xl font-semibold">${costingData.totalCost.toFixed(2)}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-md">
                      <p className="text-sm text-gray-600">Cost Per {costingData.yieldUnit}</p>
                      <p className="text-2xl font-semibold">${costingData.costPerUnit.toFixed(2)}</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">Cost Breakdown</h3>
                    <div className="border rounded-md overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ingredient</th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Cost</th>
                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {costingData.costBreakdown.map((item, index) => (
                            <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                <span className={`px-2 py-1 text-xs rounded-full ${item.isSubRecipe ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                                  {item.isSubRecipe ? 'Sub-Recipe' : 'Ingredient'}
                                </span>
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-right">{item.quantity} {item.unit}</td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-right">${item.unitCost.toFixed(2)}</td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 text-right">${item.totalCost.toFixed(2)}</td>
                            </tr>
                          ))}
                        </tbody>
                        <tfoot className="bg-gray-50">
                          <tr>
                            <td colSpan={4} className="px-4 py-3 text-sm text-gray-900 font-medium text-right">Total Cost:</td>
                            <td className="px-4 py-3 text-sm text-gray-900 font-bold text-right">${costingData.totalCost.toFixed(2)}</td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-6 text-center">
                  <Calculator className="h-10 w-10 text-gray-400 mb-2" />
                  <p className="text-gray-600">Click the button below to calculate the recipe cost</p>
                  <Button 
                    className="mt-4" 
                    variant="outline" 
                    onClick={fetchCostingData}
                  >
                    Calculate Costs
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="selling">
          <Card>
            <CardHeader>
              <CardTitle>Selling Options</CardTitle>
            </CardHeader>
            <CardContent>
              {recipe.sellingDetails && recipe.sellingDetails.length > 0 ? (
                <div className="space-y-4">
                  {recipe.sellingDetails.map((option, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div>
                        <p className="font-medium">Selling Unit: {option.unitOfSellingShortCode}</p>
                        <p className="text-sm text-gray-600">
                          1 unit = {option.conversionFactor} {recipe.baseYieldUOM.name}
                        </p>
                        <p className="text-sm text-gray-600">
                          Price: {option.priceWithoutTax.toFixed(2)} ({option.taxCategory} Tax: {option.taxRate}%)
                        </p>
                        <p className="text-sm font-medium text-gray-800">
                          Price with Tax: {option.priceWithTax.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600">No selling options defined.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      <Link
        href={`/company/${params.companyId}/products/recipes`}
        className="inline-flex items-center text-orange-600 hover:text-orange-800 mt-6"
      >
        <ArrowLeft className="w-5 h-5 mr-2" />
        Back to Recipes
      </Link>
    </div>
  );
}
