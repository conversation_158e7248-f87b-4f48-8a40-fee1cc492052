// src/app/company/[companyId]/products/recipes/[id]/edit/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { IRecipe } from '@/models/Recipe';
import { RecipeForm } from '@/components/RecipeForm';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Utensils, ShoppingBag } from 'lucide-react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';

export default function EditRecipePage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const [recipe, setRecipe] = useState<IRecipe | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const companyId = params.companyId as string;
  const recipeId = params.id as string;

  useEffect(() => {
    const fetchRecipe = async () => {
      try {
        const response = await fetch(`/api/recipes/${recipeId}`, {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) {
          throw new Error('Failed to fetch recipe');
        }
        const data = await response.json();
        setRecipe(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      }
    };

    fetchRecipe();
  }, [recipeId, companyId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!userData) {
    return null;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-600 mb-4">{error}</div>
        <Link
          href={`/company/${companyId}/products/recipes`}
          className="text-orange-600 hover:text-orange-700 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Recipes
        </Link>
      </div>
    );
  }

  if (!recipe) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Link
        href={`/company/${companyId}/products/recipes`}
        className="text-orange-600 hover:text-orange-700 flex items-center mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Recipes
      </Link>
      
      <h1 className="text-3xl font-bold mb-8">Edit Recipe</h1>
      
      <div className="space-y-8">
        <RecipeForm
          initialData={recipe}
          companyId={companyId}
          onSubmit={async (data) => {
            try {
              const response = await fetch(`/api/recipes/${recipeId}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                  'company-id': companyId,
                },
                body: JSON.stringify({
                  ...data,
                  _id: recipeId,
                  companyId
                }),
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to update recipe');
              }

              router.push(`/company/${companyId}/products/recipes/${recipeId}`);
            } catch (err) {
              console.error('Error updating recipe:', err);
              setError(err instanceof Error ? err.message : 'Failed to update recipe');
            }
          }}
        >
          <Tabs>
            <TabsList>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="ingredients">Ingredients</TabsTrigger>
              <TabsTrigger value="instructions">Instructions</TabsTrigger>
            </TabsList>
            <TabsContent value="general">
              <div className="grid gap-4 py-4">
                <div className="flex flex-col gap-4">
                  <div className="flex items-center gap-4">
                    <Checkbox
                      id="isSubRecipe"
                      checked={recipe.isSubRecipe}
                      onCheckedChange={(checked) =>
                        setRecipe({ ...recipe, isSubRecipe: checked as boolean })
                      }
                    />
                    <label
                      htmlFor="isSubRecipe"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Sub Recipe
                    </label>

                    <Checkbox
                      id="stockable"
                      checked={recipe.stockable}
                      onCheckedChange={(checked) =>
                        setRecipe({ ...recipe, stockable: checked as boolean })
                      }
                    />
                    <label
                      htmlFor="stockable"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Stockable
                    </label>

                    <Checkbox
                      id="canBeSold"
                      checked={recipe.canBeSold}
                      onCheckedChange={(checked) =>
                        setRecipe({ ...recipe, canBeSold: checked as boolean })
                      }
                    />
                    <label
                      htmlFor="canBeSold"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Can be Sold
                    </label>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="ingredients">
              {/* Ingredients tab content */}
            </TabsContent>
            <TabsContent value="instructions">
              {/* Instructions tab content */}
            </TabsContent>
          </Tabs>
        </RecipeForm>
      </div>
    </div>
  );
}
