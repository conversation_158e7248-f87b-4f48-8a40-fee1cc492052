'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';

export default function SubRecipesPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null; // or redirect to unauthorized
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Sub-Recipes</h1>
        <p className="text-muted-foreground">Manage your intermediate preparations and component recipes</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <p>Sub-recipe management coming soon...</p>
      </div>
    </div>
  );
}
