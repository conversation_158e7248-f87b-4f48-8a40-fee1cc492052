'use client';

import { useParams } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import MenuMappingList from './components/MenuMappingList';

export default function MenuMappingPage() {
  const params = useParams();
  const companyId = params.companyId as string;
  useRequireCompanyUser(companyId);

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Menu Item Mapping</h1>
      </div>
      <MenuMappingList companyId={companyId} />
    </div>
  );
}
