'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import MenuMappingForm from './MenuMappingForm';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface MenuItem {
  _id: string;
  name: string;
  type: 'DIRECT' | 'RECIPE';
  inventoryStatus: 'pending' | 'linked';
}

interface MenuMappingListProps {
  companyId: string;
}

export default function MenuMappingList({ companyId }: MenuMappingListProps) {
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);

  const { data: menuItems, isLoading } = useQuery({
    queryKey: ['menuItems', companyId],
    queryFn: async () => {
      const response = await fetch(`/api/company/${companyId}/menu-items`);
      if (!response.ok) throw new Error('Failed to fetch menu items');
      return response.json();
    },
  });

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {menuItems?.map((item: MenuItem) => (
            <TableRow key={item._id}>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.type}</TableCell>
              <TableCell>{item.inventoryStatus}</TableCell>
              <TableCell>
                <Button
                  variant="outline"
                  onClick={() => setSelectedItem(item)}
                >
                  Edit Mapping
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {selectedItem && (
        <MenuMappingForm
          menuItem={selectedItem}
          companyId={companyId}
          onClose={() => setSelectedItem(null)}
        />
      )}
    </div>
  );
}
