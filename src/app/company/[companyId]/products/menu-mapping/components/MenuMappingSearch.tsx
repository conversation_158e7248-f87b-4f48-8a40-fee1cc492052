'use client';

import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { Input } from '@/components/ui/input';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface Component {
  itemId: string;
  name: string;
  baseUOM: string;
  costPerUnit: number;
}

interface MenuMappingSearchProps {
  companyId: string;
  onSelect: (component: Component) => void;
}

export default function MenuMappingSearch({ companyId, onSelect }: MenuMappingSearchProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearch(value);
    }, 300),
    []
  );

  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['inventorySearch', companyId, search],
    queryFn: async () => {
      if (!search) return [];
      const response = await fetch(
        `/api/company/${companyId}/inventory/search?q=${encodeURIComponent(search)}`
      );
      if (!response.ok) throw new Error('Search failed');
      return response.json();
    },
    enabled: !!search,
  });

  const handleSelect = (component: Component) => {
    onSelect(component);
    setOpen(false);
    setSearch('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Input
          placeholder="Search inventory items..."
          onChange={(e) => debouncedSearch(e.target.value)}
        />
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput placeholder="Search inventory items..." />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {searchResults?.map((item: Component) => (
                <CommandItem
                  key={item.itemId}
                  onSelect={() => handleSelect(item)}
                >
                  <div className="flex justify-between w-full">
                    <span>{item.name}</span>
                    <span className="text-sm text-muted-foreground">
                      {item.baseUOM}
                    </span>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
