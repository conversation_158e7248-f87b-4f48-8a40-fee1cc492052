'use client';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface Component {
  itemId: string;
  name: string;
  baseUOM: string;
  quantity: number;
  costPerUnit: number;
}

interface MenuMappingRowProps {
  component: Component;
  onRemove: () => void;
  onUpdateQuantity: (quantity: number) => void;
}

export default function MenuMappingRow({
  component,
  onRemove,
  onUpdateQuantity,
}: MenuMappingRowProps) {
  const totalCost = component.quantity * component.costPerUnit;

  return (
    <div className="flex items-center space-x-4 p-2 border rounded-md">
      <div className="flex-1">
        <span className="font-medium">{component.name}</span>
      </div>
      <div className="w-24">
        <span className="text-sm text-muted-foreground">{component.baseUOM}</span>
      </div>
      <div className="w-32">
        <Input
          type="number"
          value={component.quantity}
          onChange={(e) => onUpdateQuantity(parseFloat(e.target.value) || 0)}
          min="0"
          step="0.01"
        />
      </div>
      <div className="w-32 text-right">
        <span className="text-sm">
          {component.costPerUnit.toFixed(2)} / {component.baseUOM}
        </span>
      </div>
      <div className="w-32 text-right">
        <span className="font-medium">{totalCost.toFixed(2)}</span>
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={onRemove}
        className="text-destructive"
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
}
