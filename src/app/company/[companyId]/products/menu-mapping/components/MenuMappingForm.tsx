'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import MenuMappingRow from './MenuMappingRow';
import MenuMappingSearch from './MenuMappingSearch';
import { Button } from '@/components/ui/button';

interface MenuItem {
  _id: string;
  name: string;
  type: 'DIRECT' | 'RECIPE';
}

interface Component {
  itemId: string;
  name: string;
  baseUOM: string;
  quantity: number;
  costPerUnit: number;
}

interface MenuMappingFormProps {
  menuItem: MenuItem;
  companyId: string;
  onClose: () => void;
}

export default function MenuMappingForm({ menuItem, companyId, onClose }: MenuMappingFormProps) {
  const [components, setComponents] = useState<Component[]>([]);
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (data: { menuItemId: string; components: Component[] }) => {
      const response = await fetch(`/api/company/${companyId}/menu-mapping`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to save mapping');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menuItems', companyId] });
      onClose();
    },
  });

  const handleAddComponent = (component: Component) => {
    setComponents([...components, component]);
  };

  const handleRemoveComponent = (index: number) => {
    setComponents(components.filter((_, i) => i !== index));
  };

  const handleUpdateQuantity = (index: number, quantity: number) => {
    const newComponents = [...components];
    newComponents[index].quantity = quantity;
    setComponents(newComponents);
  };

  const handleSave = () => {
    mutation.mutate({
      menuItemId: menuItem._id,
      components,
    });
  };

  return (
    <Dialog open={true} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Map Components for {menuItem.name}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <MenuMappingSearch
            companyId={companyId}
            onSelect={handleAddComponent}
          />

          <div className="space-y-2">
            {components.map((component, index) => (
              <MenuMappingRow
                key={index}
                component={component}
                onRemove={() => handleRemoveComponent(index)}
                onUpdateQuantity={(qty) => handleUpdateQuantity(index, qty)}
              />
            ))}
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={mutation.isPending}>
              Save Mapping
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
