//src/app/company/[companyId]/products/ingredients/[id]/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { IIngredient, SupplierDetail, Unit } from '@/types/ingredient';
import { toast } from 'sonner';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

export default function IngredientDetailsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const companyId = params.companyId as string;
  const ingredientId = params.id as string;
  const [ingredient, setIngredient] = useState<IIngredient | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchIngredient = async () => {
      try {
        const response = await fetch(`/api/ingredients/${ingredientId}`, {
          headers: {
            'company-id': companyId
          }
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch ingredient');
        }
        
        const data = await response.json();
        console.log('Fetched ingredient data:', JSON.stringify(data, null, 2));
        setIngredient(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        toast.error('Error', {
          description: errorMessage
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (companyId && ingredientId) {
      fetchIngredient();
    }
  }, [companyId, ingredientId]);

  const handleBack = () => {
    router.push(`/company/${companyId}/products/ingredients`);
  };

  const handleEdit = () => {
    router.push(`/company/${companyId}/products/ingredients/${ingredientId}/edit`);
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this ingredient?')) {
      return;
    }

    try {
      const response = await fetch(`/api/ingredients/${ingredientId}`, {
        method: 'DELETE',
        headers: {
          'company-id': companyId
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete ingredient');
      }

      toast({
        title: "Success",
        description: "Ingredient deleted successfully",
      });
      router.push(`/company/${companyId}/products/ingredients`);
    } catch (err) {
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to delete ingredient',
      });
    }
  };

  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null;
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  if (!ingredient) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <div>Ingredient not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="ghost" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <div className="space-x-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" /> Delete
          </Button>
        </div>
      </div>

      {ingredient && (
        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Ingredient Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-gray-500">Name</h3>
                  <p>{ingredient.name}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">SKU</h3>
                  <p>{ingredient.SKU}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">Category</h3>
                  <p>{ingredient.category}</p>
                </div>
                {ingredient.baseUomId && (
                  <div>
                    <h3 className="font-medium text-gray-500">Base Unit</h3>
                    <p>{ingredient.baseUomId.name} ({ingredient.baseUomId.shortCode})</p>
                  </div>
                )}
                {ingredient.description && (
                  <div className="col-span-2">
                    <h3 className="font-medium text-gray-500">Description</h3>
                    <p>{ingredient.description}</p>
                  </div>
                )}
                {ingredient.reorderPoint && (
                  <div>
                    <h3 className="font-medium text-gray-500">Reorder Point</h3>
                    <p>{ingredient.reorderPoint}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="suppliers" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="suppliers">Supplier Details</TabsTrigger>
              <TabsTrigger value="selling">Selling Options</TabsTrigger>
            </TabsList>

            <TabsContent value="suppliers">
              <Card>
                <CardHeader>
                  {ingredient.defaultSupplierId && (
                    <CardDescription>
                      Default Supplier: {ingredient.defaultSupplierId.name}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  {ingredient.supplierDetails && ingredient.supplierDetails.length > 0 ? (
                    <Accordion type="single" collapsible className="w-full">
                      {ingredient.supplierDetails.map((supplierDetail, supplierIndex) => (
                        supplierDetail?.supplierId && (
                          <AccordionItem 
                            key={`supplier-${supplierDetail.supplierId._id}`} 
                            value={supplierDetail.supplierId._id}
                          >
                            <AccordionTrigger>
                              {supplierDetail.supplierId.name || `Supplier ${supplierIndex + 1}`}
                            </AccordionTrigger>
                            <AccordionContent>
                              {supplierDetail.unitsOfOrdering && supplierDetail.unitsOfOrdering.length > 0 ? (
                                <div className="space-y-4 pt-4">
                                  {supplierDetail.unitsOfOrdering.map((unit, unitIndex) => (
                                    unit?.unitOfMeasure && (
                                      <div 
                                        key={`${supplierDetail.supplierId._id}-unit-${unitIndex}`} 
                                        className="grid grid-cols-4 gap-4 bg-gray-50 p-3 rounded"
                                      >
                                        <div>
                                          <span className="text-sm text-gray-500">Unit</span>
                                          <p>{unit.unitOfMeasure.name} ({unit.unitOfMeasure.shortCode})</p>
                                        </div>
                                        <div>
                                          <span className="text-sm text-gray-500">Quantity in Base Unit</span>
                                          <p>{unit.quantityInBaseUom} {ingredient.baseUomId?.shortCode}</p>
                                        </div>
                                        <div>
                                          <span className="text-sm text-gray-500">Price</span>
                                          <p>${unit.price.toFixed(2)}</p>
                                        </div>
                                        <div>
                                          <span className="text-sm text-gray-500">Price per {ingredient.baseUomId?.shortCode}</span>
                                          <p>${(unit.pricePerBaseUom || 0).toFixed(2)}</p>
                                        </div>
                                      </div>
                                    )
                                  ))}
                                </div>
                              ) : (
                                <p className="text-gray-500">No units of ordering defined</p>
                              )}
                            </AccordionContent>
                          </AccordionItem>
                        )
                      ))}
                    </Accordion>
                  ) : (
                    <p className="text-gray-500">No supplier details available</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="selling">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    {ingredient.sellingDetails && ingredient.sellingDetails.length > 0 ? (
                      <Accordion type="single" collapsible className="w-full">
                        {ingredient.sellingDetails.map((sellingDetail, index) => (
                          sellingDetail?.unitOfSelling && (
                            <AccordionItem 
                              key={`selling-${index}`} 
                              value={`selling-${index}`}
                            >
                              <AccordionTrigger>
                                {sellingDetail.unitOfSelling.name} - ${(sellingDetail.priceWithoutTax || 0).toFixed(2)}
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="space-y-2 pt-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <span className="text-sm text-gray-500">Unit</span>
                                      <p>{sellingDetail.unitOfSelling.name} ({sellingDetail.unitOfSelling.shortCode})</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Conversion Factor</span>
                                      <p>{sellingDetail.conversionFactor} {ingredient.baseUomId?.shortCode}s per {sellingDetail.unitOfSelling.shortCode}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Price (without tax)</span>
                                      <p>${(sellingDetail.priceWithoutTax || 0).toFixed(2)}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Price (with tax)</span>
                                      <p>${(sellingDetail.priceWithTax || 0).toFixed(2)}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Tax Rate</span>
                                      <p>{sellingDetail.taxRate}%</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Tax Category</span>
                                      <p>{sellingDetail.taxCategory}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Source Type</span>
                                      <p>{sellingDetail.sourceType.replace(/_/g, ' ')}</p>
                                    </div>
                                    <div>
                                      <span className="text-sm text-gray-500">Markup Type</span>
                                      <p>{sellingDetail.markupType.replace(/_/g, ' ')}</p>
                                    </div>
                                    <div className="col-span-2">
                                      <span className="text-sm text-gray-500">Visibility</span>
                                      <p>{sellingDetail.visibility.type.replace(/_/g, ' ')}</p>
                                      {sellingDetail.visibility.locations?.length > 0 && (
                                        <p className="text-sm text-gray-500 mt-1">
                                          Specific locations: {sellingDetail.visibility.locations.join(', ')}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )
                        ))}
                      </Accordion>
                    ) : (
                      <p className="text-gray-500">No selling options available</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
