// src/app/company/[companyId]/products/ingredients/[id]/edit/page.tsx

'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import Link from 'next/link';
import { ArrowLeft, Loader2, Plus, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { IIngredient } from '@/types/ingredient';
import IngredientSellingOptions from '@/components/admin/IngredientSellingOptions';

interface Supplier {
  _id: string;
  name: string;
}

interface Unit {
  unitOfMeasure: {
    _id: string;
    name?: string;
    shortCode?: string;
  };
  quantityInBaseUom: number;
  price: number;
  pricePerBaseUom: number;
}

interface SupplierWithUnits {
  supplierId: {
    _id: string;
    name?: string;
  };
  unitsOfOrdering: Unit[];
}

interface UOM {
  _id: string;
  name: string;
  shortCode: string;
}

interface SellingOption {
  // Add properties for selling options
  sourceType: 'Central Kitchen only' | 'External only' | 'Both Central Kitchen and External';
  pricePerBaseUom?: number;
  markup?: number;
}

const ingredientSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  reorderPoint: z.number().min(0).optional().nullable(),
  baseUomId: z.string().min(1, 'Base UOM is required'),
  category: z.string().min(1, 'Category is required'),
  SKU: z.string().optional(),
  defaultSupplierId: z.string().optional(),
  canBeSold: z.boolean().default(false),
});

export default function EditIngredientPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const router = useRouter();
  const companyId = params.companyId as string;
  const ingredientId = params.id as string;

  const [ingredient, setIngredient] = useState<IIngredient | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uoms, setUoms] = useState<UOM[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [availableSuppliers, setAvailableSuppliers] = useState<Supplier[]>([]);
  const [selectedSuppliers, setSelectedSuppliers] = useState<SupplierWithUnits[]>([]);
  const [selectedSupplierId, setSelectedSupplierId] = useState<string>('');
  const [sellingDetails, setSellingDetails] = useState<any[]>([]);

  const form = useForm<z.infer<typeof ingredientSchema>>({
    resolver: zodResolver(ingredientSchema),
    defaultValues: {
      name: '',
      description: '',
      reorderPoint: null,
      baseUomId: '',
      category: '',
      SKU: '',
      defaultSupplierId: '',
      canBeSold: false,
    },
  });

  useEffect(() => {
    const fetchIngredient = async () => {
      try {
        const response = await fetch(`/api/ingredients/${ingredientId}`, {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) {
          throw new Error('Failed to fetch ingredient');
        }
        const data = await response.json();
        console.log('API Response Data:', JSON.stringify(data, null, 2));
        setIngredient(data);
        
        // Set supplier details with complete unit information
        const suppliersWithUnits = data.supplierDetails.map(supplier => {
          console.log('Processing supplier:', supplier);
          return {
            supplierId: {
              _id: supplier.supplierId._id,
              name: supplier.supplierId.name
            },
            unitsOfOrdering: supplier.unitsOfOrdering.map(unit => {
              console.log('Processing unit:', unit);
              return {
                unitOfMeasure: unit.unitOfMeasure,  // Keep the full unitOfMeasure object
                quantityInBaseUom: unit.quantityInBaseUom,
                price: unit.price,
                pricePerBaseUom: unit.pricePerBaseUom
              };
            })
          };
        });
        console.log('Processed suppliers:', JSON.stringify(suppliersWithUnits, null, 2));
        setSelectedSuppliers(suppliersWithUnits);

        // Set form values
        form.reset({
          name: data.name,
          description: data.description || '',
          reorderPoint: data.reorderPoint,
          baseUomId: data.baseUomId._id,
          category: data.category,
          SKU: data.SKU || '',
          defaultSupplierId: data.defaultSupplierId?._id || '',
          canBeSold: data.canBeSold,
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast.error(err instanceof Error ? err.message : 'Failed to fetch ingredient');
      }
    };

    const fetchUoms = async () => {
      try {
        const response = await fetch('/api/uoms', {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch UOMs');
        const data = await response.json();
        setUoms(data.uoms || []); // Access the uoms property from the response
      } catch (error) {
        toast.error('Failed to fetch UOMs');
      }
    };

    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/ingredients?page=1&limit=1', {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch categories');
        const data = await response.json();
        if (Array.isArray(data.categories)) {
          setCategories(data.categories);
        }
      } catch (error) {
        toast.error('Failed to fetch categories');
      }
    };

    const fetchSuppliers = async () => {
      try {
        const response = await fetch('/api/suppliers', {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch suppliers');
        const data = await response.json();
        setAvailableSuppliers(data || []);
      } catch (error) {
        toast.error('Failed to fetch suppliers');
      }
    };

    if (companyId && ingredientId) {
      fetchIngredient();
      fetchUoms();
      fetchCategories();
      fetchSuppliers();
    }
  }, [companyId, ingredientId, form]);

  useEffect(() => {
    if (ingredient?.sellingDetails) {
      setSellingDetails(ingredient.sellingDetails);
    }
  }, [ingredient]);

  const handleAddSellingOption = (updatedOptions: any[]) => {
    console.log('Updating selling options:', updatedOptions);
    setSellingDetails(updatedOptions);
  };

  const handleDeleteSellingOption = (index: number) => {
    console.log('Deleting selling option at index:', index);
    setSellingDetails(prev => {
      const updated = prev.filter((_, i) => i !== index);
      console.log('Updated selling details after deletion:', updated);
      return updated;
    });
  };

  const onSubmit = async (values: z.infer<typeof ingredientSchema>) => {
    console.log('Submitting with canBeSold:', values.canBeSold);
    console.log('Current sellingDetails state:', sellingDetails);
    try {
      // Format supplier details to match API expectations
      const formattedSupplierDetails = selectedSuppliers.map(supplier => ({
        supplierId: {
          _id: supplier.supplierId._id,
          name: supplier.supplierId.name
        },
        unitsOfOrdering: supplier.unitsOfOrdering.map(unit => ({
          unitOfMeasure: {
            _id: unit.unitOfMeasure._id
          },
          quantityInBaseUom: unit.quantityInBaseUom,
          price: unit.price,
          pricePerBaseUom: unit.price / unit.quantityInBaseUom
        }))
      }));

      // Prepare the request data
      const requestData = {
        ...values,
        supplierDetails: formattedSupplierDetails,
        // Only include sellingDetails if canBeSold is true and there are selling details
        sellingDetails: values.canBeSold ? sellingDetails : []
      };

      console.log('Sending request data:', JSON.stringify(requestData, null, 2));

      const response = await fetch(`/api/ingredients/${ingredientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update ingredient');
      }

      const updatedIngredient = await response.json();
      console.log('Updated ingredient:', JSON.stringify({
        id: updatedIngredient._id,
        canBeSold: updatedIngredient.canBeSold,
        sellingDetails: updatedIngredient.sellingDetails?.length || 0
      }, null, 2));

      toast.success('Ingredient updated successfully');
      router.push(`/company/${companyId}/products/ingredients/${ingredientId}`);
    } catch (err) {
      console.error('Error updating ingredient:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to update ingredient');
    }
  };

  const handleAddSupplier = () => {
    if (!selectedSupplierId) return;

    const supplier = availableSuppliers.find(s => s._id === selectedSupplierId);
    if (supplier && !selectedSuppliers.some(s => s.supplierId._id === supplier._id)) {
      setSelectedSuppliers([
        ...selectedSuppliers,
        {
          supplierId: { 
            _id: supplier._id,
            name: supplier.name 
          },
          unitsOfOrdering: [],
        },
      ]);
      setSelectedSupplierId('');
    }
  };

  const handleRemoveSupplier = (supplierId: string) => {
    setSelectedSuppliers(selectedSuppliers.filter(s => s.supplierId._id !== supplierId));
  };

  // Use this helper to update the price when sourceType changes
  const handleSourceTypeChange = (optionId: string, newSourceType: SellingOption['sourceType']) => {
    setSellingDetails((current) => current.map(opt => {
      if (opt.id !== optionId) return opt;
      const updated = { ...opt, sourceType: newSourceType };
      if (newSourceType === 'Central Kitchen only' || newSourceType === 'Both Central Kitchen and External') {
        const basePrice = updated.pricePerBaseUom || 0;
        const markupValue = updated.markup || 0;
        updated.priceWithoutTax = basePrice * (1 + markupValue / 100);
      }
      return updated;
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Link
          href={`/company/${companyId}/products/ingredients/${ingredientId}`}
          className="inline-flex items-center text-primary hover:text-primary/90"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to Ingredient
        </Link>
        <div className="bg-red-50 text-red-600 p-4 rounded-md">
          {error}
        </div>
      </div>
    );
  }

  if (!ingredient) {
    return (
      <div className="p-6 space-y-4">
        <Link
          href={`/company/${companyId}/products/ingredients/${ingredientId}`}
          className="inline-flex items-center text-primary hover:text-primary/90"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to Ingredient
        </Link>
        <div>Ingredient not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link
            href={`/company/${companyId}/products/ingredients/${ingredientId}`}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Ingredient
          </Link>
          <h1 className="text-2xl font-bold">Edit Ingredient</h1>
        </div>
      </div>

      {error ? (
        <div className="text-red-500">{error}</div>
      ) : loading || !ingredient ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      ) : (
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Name
                    </label>
                    <Input
                      id="name"
                      {...form.register('name')}
                      placeholder="Enter ingredient name"
                    />
                    {form.formState.errors.name && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.name.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="SKU" className="text-sm font-medium">
                      SKU
                    </label>
                    <Input
                      id="SKU"
                      {...form.register('SKU')}
                      placeholder="Enter SKU"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="category" className="text-sm font-medium">
                      Category
                    </label>
                    <Select
                      value={form.getValues('category')}
                      onValueChange={(value) => form.setValue('category', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.category && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.category.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="baseUomId" className="text-sm font-medium">
                      Base UOM
                    </label>
                    {ingredient ? (
                      <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                        {ingredient.baseUomId.name} ({ingredient.baseUomId.shortCode})
                      </div>
                    ) : (
                      <>
                        <Select
                          value={form.getValues('baseUomId')}
                          onValueChange={(value) => form.setValue('baseUomId', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a base UOM" />
                          </SelectTrigger>
                          <SelectContent>
                            {uoms.map((uom) => (
                              <SelectItem key={uom._id} value={uom._id}>
                                {uom.name} ({uom.shortCode})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <p className="text-sm text-muted-foreground mt-1">
                          Note: The Base UOM cannot be changed after creation. Choose carefully as it will be used for all conversions and calculations.
                        </p>
                      </>
                    )}
                    {form.formState.errors.baseUomId && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.baseUomId.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mt-6">
                  <div className="flex flex-row items-center space-x-2">
                    <Checkbox 
                      id="canBeSold"
                      checked={form.watch('canBeSold')}
                      onCheckedChange={(checked) => {
                        form.setValue('canBeSold', !!checked);
                      }}
                    />
                    <div className="space-y-1 leading-none">
                      <label 
                        htmlFor="canBeSold" 
                        className="block text-sm font-medium text-gray-700"
                      >
                        Can be sold
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Tabs 
              defaultValue="details" 
              className="w-full"
              value={form.watch('canBeSold') ? undefined : 'details'}
            >
              <TabsList className="grid w-full" style={{ 
                gridTemplateColumns: form.watch('canBeSold') ? '1fr 1fr' : '1fr' 
              }}>
                <TabsTrigger value="details">Details</TabsTrigger>
                {form.watch('canBeSold') && (
                  <TabsTrigger value="selling-options">
                    Selling Options
                  </TabsTrigger>
                )}
              </TabsList>

              <TabsContent value="details">
                <Card>
                  <CardContent className="pt-6 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <Input {...form.register('description')} />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Reorder Point</label>
                      <Input 
                        type="number" 
                        {...form.register('reorderPoint')} 
                        value={form.watch('reorderPoint') ?? ''}
                        onChange={(e) => form.setValue('reorderPoint', e.target.value ? Number(e.target.value) : null)}
                      />
                    </div>

                    {/* Supplier section */}
                    <div className="space-y-4">
                      <h2 className="text-lg font-semibold">Suppliers</h2>

                      <div className="flex gap-2">
                        <Select
                          value={selectedSupplierId}
                          onValueChange={setSelectedSupplierId}
                        >
                          <SelectTrigger className="w-[300px]">
                            <SelectValue placeholder="Select supplier to add" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableSuppliers
                              .filter(s => !selectedSuppliers.some(selected => selected.supplierId._id === s._id))
                              .map((supplier) => (
                                <SelectItem key={supplier._id} value={supplier._id}>
                                  {supplier.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleAddSupplier}
                          disabled={!selectedSupplierId}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add
                        </Button>
                      </div>

                      <div className="space-y-2">
                        {selectedSuppliers.map((supplier) => (
                          <Card key={supplier.supplierId._id}>
                            <CardContent className="p-4">
                              <div className="flex justify-between items-center mb-4">
                                <span className="font-medium">
                                  {supplier.supplierId.name || availableSuppliers.find(s => s._id === supplier.supplierId._id)?.name || 'Unknown Supplier'}
                                </span>
                                <div className="flex gap-2">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const newUnit = {
                                        unitOfMeasure: { _id: '' },
                                        quantityInBaseUom: 0,
                                        price: 0,
                                        pricePerBaseUom: 0,
                                      };
                                      setSelectedSuppliers(
                                        selectedSuppliers.map(s =>
                                          s.supplierId._id === supplier.supplierId._id
                                            ? { ...s, unitsOfOrdering: [...s.unitsOfOrdering, newUnit] }
                                            : s
                                        )
                                      );
                                    }}
                                  >
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Unit
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveSupplier(supplier.supplierId._id)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>

                              {supplier.unitsOfOrdering.map((unit, unitIndex) => (
                                <div key={unitIndex} className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg mb-4">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700">Unit Name</label>
                                    <Select
                                      value={unit.unitOfMeasure?._id || ''}
                                      onValueChange={(value) => {
                                        const selectedUom = uoms.find(u => u._id === value);
                                        setSelectedSuppliers(
                                          selectedSuppliers.map(s =>
                                            s.supplierId._id === supplier.supplierId._id
                                              ? {
                                                  ...s,
                                                  unitsOfOrdering: s.unitsOfOrdering.map((u, i) =>
                                                    i === unitIndex
                                                      ? { 
                                                          ...u, 
                                                          unitOfMeasure: {
                                                            _id: value,
                                                            name: selectedUom?.name,
                                                            shortCode: selectedUom?.shortCode
                                                          }
                                                        }
                                                      : u
                                                  )
                                                }
                                              : s
                                          )
                                        );
                                      }}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select unit">
                                          {unit.unitOfMeasure?.name && `${unit.unitOfMeasure.name} (${unit.unitOfMeasure.shortCode})`}
                                        </SelectValue>
                                      </SelectTrigger>
                                      <SelectContent>
                                        {uoms.map((uom) => (
                                          <SelectItem key={uom._id} value={uom._id}>
                                            {uom.name} ({uom.shortCode})
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700">Quantity</label>
                                    <Input
                                      type="number"
                                      value={unit.quantityInBaseUom}
                                      onChange={(e) => {
                                        const value = parseFloat(e.target.value);
                                        setSelectedSuppliers(
                                          selectedSuppliers.map(s =>
                                            s.supplierId._id === supplier.supplierId._id
                                              ? {
                                                  ...s,
                                                  unitsOfOrdering: s.unitsOfOrdering.map((u, i) =>
                                                    i === unitIndex
                                                      ? {
                                                          ...u,
                                                          quantityInBaseUom: value,
                                                          pricePerBaseUom: value > 0 ? u.price / value : 0,
                                                        }
                                                      : u
                                                  )
                                                }
                                              : s
                                          )
                                        );
                                      }}
                                      placeholder="10"
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700">Price</label>
                                    <Input
                                      type="number"
                                      value={unit.price}
                                      onChange={(e) => {
                                        const value = parseFloat(e.target.value);
                                        setSelectedSuppliers(
                                          selectedSuppliers.map(s =>
                                            s.supplierId._id === supplier.supplierId._id
                                              ? {
                                                  ...s,
                                                  unitsOfOrdering: s.unitsOfOrdering.map((u, i) =>
                                                    i === unitIndex
                                                      ? {
                                                          ...u,
                                                          price: value,
                                                          pricePerBaseUom: u.quantityInBaseUom > 0 ? value / u.quantityInBaseUom : 0,
                                                        }
                                                      : u
                                                  )
                                                }
                                              : s
                                          )
                                        );
                                      }}
                                      placeholder="50"
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700">Price per Unit</label>
                                    <Input
                                      type="number"
                                      value={unit.pricePerBaseUom}
                                      disabled
                                      className="bg-gray-100"
                                    />
                                  </div>
                                </div>
                              ))}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {form.watch('canBeSold') && (
                <TabsContent value="selling-options">
                  <Card>
                    <CardContent className="pt-6">
                      <IngredientSellingOptions
                        ingredientId={ingredientId}
                        companyId={companyId}
                        onAddSellingOption={handleAddSellingOption}
                        onDeleteSellingOption={handleDeleteSellingOption}
                        sellingDetails={sellingDetails}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>
              )}
            </Tabs>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/company/${companyId}/products/ingredients/${ingredientId}`)}
              >
                Cancel
              </Button>
              <Button type="submit">
                Save Changes
              </Button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
}
