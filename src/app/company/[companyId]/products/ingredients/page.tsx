// src/app/company/[companyId]/products/ingredients/page.tsx
'use client';

import React from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useParams } from 'next/navigation';
import IngredientManagement from '@/components/admin/IngredientManagement';

export default function IngredientsPage() {
  const { userData, loading } = useRequireCompanyUser();
  const params = useParams();
  const companyId = params.companyId as string;

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!userData || userData.companyId !== companyId) {
    return null; // or redirect to unauthorized
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Ingredients</h1>
        <p className="text-muted-foreground">Manage your ingredients inventory and details</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <IngredientManagement />
      </div>
    </div>
  );
}
