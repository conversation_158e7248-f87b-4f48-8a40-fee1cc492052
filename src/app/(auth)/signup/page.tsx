// src/app/(auth)/signup/page.tsx
'use client';

import { useState, useEffect } from 'react';
// import { useRouter } from 'next/navigation'; // Commented out: unused as signup flow is incomplete
import Link from 'next/link';
// import { useAuth } from '@/lib/auth'; // Commented out: unused as signup flow is incomplete
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
// import { createCompany } from '@/lib/services/companyService'; // Commented out: unused as signup flow is incomplete

export default function SignupPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [subdomain, setSubdomain] = useState('');
  const [error, setError] = useState('');
  // const { signOut } = useAuth(); // Commented out: unused as signup flow is incomplete
  // const router = useRouter(); // Commented out: unused as signup flow is incomplete

  useEffect(() => {
    // Remove the automatic redirect on user change
    // We'll handle redirect after company creation
  }, []);

  // NOTE: In this refactor, company creation requires an ownerId (user UID).
  // You must first register the user via a separate registration flow/API.
  // This page should collect user info and company info, then call registration and company creation sequentially.
  // const createCompanyForOwner = async (ownerId: string) => { // Function commented out as it's currently unused
  //   try {
  //     const companyData = {
  //       name: companyName,
  //       subdomain: subdomain.toLowerCase(),
  //       ownerId,
  //     };
  //     const company = await createCompany(companyData);
  //     router.replace(`/company/${company.id}/dashboard`);
  //   } catch (error: unknown) {
  //     console.error('Error creating company:', error);
  //     setError(error instanceof Error ? error.message : 'An unknown error occurred');
  //     await signOut?.();
  //   }
  // };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    if (!companyName || !subdomain || !email || !password) {
      setError('All fields are required');
      return;
    }
    // TODO: Implement user registration API call here to get ownerId (user UID)
    setError('User registration is not implemented. Please contact support.');
    // Example:
    // const userCredential = await registerUser(email, password);
    // if (!userCredential?.uid) throw new Error('Failed to create user');
    // await createCompanyForOwner(userCredential.uid);
  };



  return (
    <div className="flex min-h-screen items-center justify-center">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Sign Up</CardTitle>
          <CardDescription>Create your account and company</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Input
                type="text"
                placeholder="Company Name"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Input
                type="text"
                placeholder="Company Subdomain"
                value={subdomain}
                onChange={(e) => setSubdomain(e.target.value)}
                required
              />
              <p className="text-sm text-muted-foreground">
                Your company URL will be: {subdomain.toLowerCase()}.foodprepai.com
              </p>
            </div>
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            {error && <p className="text-sm text-red-500">{error}</p>}
            <Button type="submit" className="w-full">
              Sign Up
            </Button>
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">Or</span>
              </div>
            </div>

            <p className="text-sm text-center">
              Already have an account?{' '}
              <Link href="/login" className="text-primary hover:underline">
                Sign in
              </Link>
            </p>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}