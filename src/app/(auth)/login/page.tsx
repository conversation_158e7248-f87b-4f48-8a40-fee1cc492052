// src/app/(auth)/login/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';  // Fixed import path
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { signIn, loading, userData } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('Login page useEffect triggered with:', {
      loading,
      userDataExists: !!userData,
      userType: userData?.userType,
      companyId: userData && userData.userType === 'company_user' ? userData.companyId : undefined
    });

    if (!loading && userData) {
      console.log('Attempting redirect with userType:', userData.userType);
      if (userData.userType === 'company_user' && 'companyId' in userData && userData.companyId) {
        const redirectPath = `/company/${userData.companyId}/dashboard`;
        console.log('Redirecting to company dashboard:', redirectPath);
        router.replace(redirectPath);
      } else if (userData.userType === 'superuser') {
        console.log('Redirecting to superuser dashboard');
        router.replace('/superadmin/dashboard');
      }
    }
  }, [loading, userData, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await signIn(email, password);
      // Redirection will happen automatically based on user type
    } catch (error: unknown) {
      console.error('Login error:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  };



  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4 py-8">
          <div>Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md space-y-8 px-4 py-8">
        <div>
          <h2 className="text-center text-3xl font-bold tracking-tight">
            Sign in to your account
          </h2>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          <div className="space-y-4 rounded-md shadow-sm">
            <div>
              <Input
                type="email"
                required
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <Input
                type="password"
                required
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-4">
            <Button type="submit" className="w-full">
              Sign in
            </Button>


          </div>
        </form>
      </div>
    </div>
  );
}
