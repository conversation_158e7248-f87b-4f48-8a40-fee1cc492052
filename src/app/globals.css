@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    /* Font family variables */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
      'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'JetBrains Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, 
      Consolas, monospace;
    
    /* Font size variables */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    
    /* Line height variables */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Professional Orange Suite Base Colors */
    --orange-50: 30 100% 97%;    /* #FFF4ED */
    --orange-100: 30 100% 94%;   /* #FFE9DB */
    --orange-200: 28 100% 89%;   /* #FFD4B8 */
    --orange-300: 25 100% 82%;   /* #FFB894 */
    --orange-400: 22 100% 70%;   /* #FF9666 */
    --orange-500: 20 100% 49%;   /* #F85E00 - Primary */
    --orange-600: 18 100% 40%;   /* #CC4E00 */
    --orange-700: 16 90% 33%;    /* #A63F00 */
    --orange-800: 14 85% 26%;    /* #832F00 */
    --orange-900: 12 80% 20%;    /* #662400 */
    --orange-950: 10 85% 12%;    /* #401600 */

    /* Surface Colors */
    --surface-50: 60 9% 98%;     /* #FAFAF9 */
    --surface-100: 60 5% 96%;    /* #F5F5F4 */
    --surface-200: 20 6% 90%;    /* #E7E5E4 */
    --surface-300: 24 6% 83%;    /* #D6D3D1 */
    --surface-400: 24 5% 64%;    /* #A8A29E */
    --surface-500: 25 5% 45%;    /* #78716C */
    --surface-600: 24 5% 34%;    /* #57534E */
    --surface-700: 24 6% 25%;    /* #44403C */
    --surface-800: 23 6% 16%;    /* #292524 */
    --surface-900: 20 6% 10%;    /* #1C1917 */
    --surface-950: 20 9% 6%;     /* #0C0A09 */

    /* Accent Colors */
    --slate-600: 215 19% 35%;    /* #475569 */
    --sky-500: 199 89% 48%;      /* #0EA5E9 */

    /* Semantic Colors */
    --success: 142 72% 29%;      /* #16A34A */
    --warning: 45 93% 47%;       /* #EAB308 */
    --error: 0 84% 60%;          /* #EF4444 */
    --info: 214 90% 52%;         /* #3B82F6 */

    /* Shadcn System Colors */
    --background: var(--surface-50);
    --foreground: var(--surface-900);

    --card: var(--surface-50);
    --card-foreground: var(--surface-900);
 
    --popover: var(--surface-50);
    --popover-foreground: var(--surface-900);
 
    --primary: var(--orange-500);
    --primary-foreground: var(--surface-50);
 
    --secondary: var(--surface-100);
    --secondary-foreground: var(--surface-900);
 
    --muted: var(--surface-100);
    --muted-foreground: var(--surface-500);
 
    --accent: var(--surface-100);
    --accent-foreground: var(--surface-900);
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: var(--surface-50);

    --border: var(--surface-200);
    --input: var(--surface-200);
    --ring: var(--orange-500);
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: var(--surface-950);
    --foreground: var(--surface-50);
 
    --card: var(--surface-900);
    --card-foreground: var(--surface-50);
 
    --popover: var(--surface-900);
    --popover-foreground: var(--surface-50);
 
    --primary: var(--orange-500);
    --primary-foreground: var(--surface-50);
 
    --secondary: var(--surface-800);
    --secondary-foreground: var(--surface-50);
 
    --muted: var(--surface-800);
    --muted-foreground: var(--surface-400);
 
    --accent: var(--surface-800);
    --accent-foreground: var(--surface-50);
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: var(--surface-50);
 
    --border: var(--surface-800);
    --input: var(--surface-800);
    --ring: var(--orange-400);
  }
}
 
@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: var(--font-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "rlig" 1, "calt" 1;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  pre, code {
    font-family: var(--font-mono);
  }
}

@layer utilities {
  .animate-accordion-down {
    animation: accordion-down 0.2s ease-out;
  }

  .animate-accordion-up {
    animation: accordion-up 0.2s ease-out;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}