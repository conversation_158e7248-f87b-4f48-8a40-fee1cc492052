'use client';

import { useEffect, useState } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import IngredientSellingOptions from '@/components/admin/IngredientSellingOptions';

interface Supplier {
  _id: string;
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
}

interface Ingredient {
  _id: string;
  name: string;
  description: string;
  suppliers: Supplier[];
  sellingDetails: any[];
}

export default function IngredientPage({ params }: { params: { id: string } }) {
  const { userData, loading } = useRequireCompanyUser();
  const [ingredient, setIngredient] = useState<Ingredient | null>(null);

  useEffect(() => {
    const fetchIngredient = async () => {
      try {
        if (!userData || userData.userType !== 'company_user') return;
        const response = await fetch(`/api/ingredients/${params.id}`, {
          headers: {
            'company-id': userData.companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch ingredient');
        const data = await response.json();
        setIngredient(data);
      } catch (error) {
        console.error('Error fetching ingredient:', error);
      }
    };

    if (userData && userData.userType === 'company_user' && userData.companyId) {
      fetchIngredient();
    }
  }, [params.id, userData]);

  const handleAddSellingOption = async (option: any) => {
    if (!ingredient) return;
    const updatedDetails = [...ingredient.sellingDetails, option];
    setIngredient({ ...ingredient, sellingDetails: updatedDetails });
  };

  const handleDeleteSellingOption = async (index: number) => {
    if (!ingredient) return;
    const updatedDetails = ingredient.sellingDetails.filter((_, i) => i !== index);
    setIngredient({ ...ingredient, sellingDetails: updatedDetails });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!ingredient) {
    return <div>Ingredient not found</div>;
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">{ingredient.name}</h1>
      
      <Tabs defaultValue="suppliers" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="suppliers">Supplier Details</TabsTrigger>
          <TabsTrigger value="selling">Selling Options</TabsTrigger>
        </TabsList>

        <TabsContent value="suppliers">
          <Card>
            <CardContent className="pt-6">
              <Accordion type="single" collapsible className="w-full">
                {ingredient.suppliers.map((supplier) => (
                  <AccordionItem key={supplier._id} value={supplier._id}>
                    <AccordionTrigger>{supplier.name}</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 p-4">
                        {supplier.contactPerson && (
                          <p><span className="font-semibold">Contact Person:</span> {supplier.contactPerson}</p>
                        )}
                        {supplier.email && (
                          <p><span className="font-semibold">Email:</span> {supplier.email}</p>
                        )}
                        {supplier.phone && (
                          <p><span className="font-semibold">Phone:</span> {supplier.phone}</p>
                        )}
                        {supplier.address && (
                          <p><span className="font-semibold">Address:</span> {supplier.address}</p>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="selling">
          <Card>
            <CardContent className="pt-6">
              <IngredientSellingOptions
                ingredientId={ingredient._id}
                companyId={userData && userData.userType === 'company_user' ? userData.companyId : ''}
                onAddSellingOption={handleAddSellingOption}
                onDeleteSellingOption={handleDeleteSellingOption}
                sellingDetails={ingredient.sellingDetails}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
