import mongoose, { Schema, Document } from 'mongoose';

export interface ISupplier extends Document {
  companyId: mongoose.Types.ObjectId;
  name: string;
  taxNumber?: string;
  contactInfo?: {
    address?: string;
    phone?: string;
    email?: string;
  };
  paymentTerms?: string;
  locationId?: mongoose.Types.ObjectId | null;
  status: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
}

const SupplierSchema = new Schema<ISupplier>({
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true },
  name: { type: String, required: true },
  taxNumber: { type: String, required: false },
  contactInfo: {
    address: { type: String, required: false },
    phone: { type: String, required: false },
    email: { type: String, required: false },
  },
  paymentTerms: { type: String, required: false },
  locationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Location', default: null },
  status: { type: String, default: 'active', enum: ['active', 'inactive'] },
  type: { type: String, required: true, enum: ['external', 'internal'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt timestamp before saving
SupplierSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Supplier = mongoose.models.Supplier || mongoose.model<ISupplier>('Supplier', SupplierSchema);

export default Supplier;
