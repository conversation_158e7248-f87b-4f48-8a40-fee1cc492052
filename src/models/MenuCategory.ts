import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IMenuCategory extends Document {
  _id: Types.ObjectId;
  companyId: Types.ObjectId;
  name: string;
  description: string;
  order: number;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

const MenuCategorySchema = new Schema<IMenuCategory>({
  companyId: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  order: { type: Number, default: 0 },
  status: { 
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Create compound index for company and name uniqueness
MenuCategorySchema.index({ companyId: 1, name: 1 }, { unique: true });

export const MenuCategory = mongoose.models.MenuCategory || mongoose.model<IMenuCategory>('MenuCategory', MenuCategorySchema);
