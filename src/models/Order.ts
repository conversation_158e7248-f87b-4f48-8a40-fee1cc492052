import mongoose, { Document, Schema, Types } from 'mongoose';

/**
 * Represents an Order document in the database.
 */
export interface IOrder extends Document {
  /** The unique identifier for the order. */
  _id: Types.ObjectId;
  /** The company associated with this order. */
  companyId: Types.ObjectId;
  /** The unique order number assigned to this order. */
  orderNumber: string;
  /** The original order number, if imported from an external system like IonicPOS. */
  originalOrderNumber?: string;
  /** The current status of the order. */
  status: 'DRAFT' | 'INCOMING' | 'CONFIRMED' | 'NOT_DELIVERED' | 'PARTIALLY_DELIVERED' | 'DELIVERED' | 'CANCELLED' | 'APPROVED' | 'DELETED';
  /** Information about the buyer. */
  buyer: {
    /** The type of the buyer entity (e.g., Customer, Supplier). */
    buyerType: 'CUSTOMER' | 'SUPPLIER' | 'BRANCH' | 'CENTRAL_KITCHEN';
    /** The identifier of the buyer (can be ObjectId or other string ID). */
    buyerId: string;
    /** The name of the buyer (populated). */
    name?: string; // Populated name
    /** An alternative field for buyer name. */
    buyerName?: string;
  };
  /** Information about the seller (optional). */
  seller?: {
    /** The type of the seller entity. */
    sellerType: 'CUSTOMER' | 'SUPPLIER' | 'BRANCH' | 'CENTRAL_KITCHEN';
    /** The identifier of the seller. */
    sellerId: string;
    /** The name of the seller. */
    sellerName?: string;
  };
  /**
   * The location ID of the seller. Can be a direct ID or a populated object.
   * @ref Location
   */
  sellerLocationId: string | { _id: Types.ObjectId; name: string };
  /**
   * The location ID of the buyer (optional). Can be a direct ID or a populated object.
   * @ref Location
   */
  buyerLocationId?: string | { _id: Types.ObjectId; name: string };
  /** An array of items included in the order. */
  items: Array<{
    /** The type of the item (e.g., Ingredient, Recipe). */
    itemType: 'INGREDIENT' | 'RECIPE';
    /**
     * The identifier of the item. Can be a direct ID or a populated object.
     * @ref Ingredient | Recipe
     */
    itemId: string | { _id: Types.ObjectId; name: string; description: string };
    /** A description of the item. */
    description: string;
    /** The quantity of the item ordered. Must be greater than 0. */
    quantity: number;
    /** The quantity of the item that has been delivered. */
    deliveredQuantity: number;
    /**
     * The unit of measure ID for the item. Can be a direct ID or a populated object.
     * @ref UnitOfMeasure
     */
    uomId: string | { _id: Types.ObjectId; name: string; shortCode: string };
    /** The price per unit of the item. Must be greater than or equal to 0. */
    unitPrice: number;
    /** The total cost for this line item (quantity * unitPrice). Must be greater than or equal to 0. */
    lineTotal: number;
  }>;
  /** An array of associated Delivery Note IDs. */
  deliveryNoteIds: Types.ObjectId[];
  // Sync-related fields for Ionic app integration
  /** The source system where the order originated. */
  orderSource?: 'WEB' | 'IONIC' | 'POS';
  /** The synchronization status with external systems. */
  syncStatus?: 'NOT_SYNCED' | 'PENDING_SYNC' | 'SYNCED' | 'SYNC_FAILED';
  /** The ID of the last synchronization record. */
  lastSyncId?: string;
  /** The timestamp of the last synchronization. */
  lastSyncTimestamp?: Date;
  /** The original branch ID from the Ionic app, if applicable. */
  branchId?: string;
  /** The local identifier used in the Ionic app, if applicable. */
  localId?: number;
  /** Version number for tracking changes during synchronization. */
  version?: number;
  /** The identifier of the user who created the order. */
  createdBy?: string; // Should ideally be Types.ObjectId referencing User
  /** The identifier of the user who last modified the order. */
  modifiedBy?: string; // Should ideally be Types.ObjectId referencing User
  /** The timestamp when the order was created. */
  createdAt: Date;
  /** The timestamp when the order was last updated. */
  updatedAt: Date;
}

/**
 * Helper function to validate if an array is not empty.
 * Used in schema validation.
 * @param arr The array to check.
 * @returns True if the array has one or more elements, false otherwise.
 */
const arrayIsNotEmpty = (arr: any[]) => {
  return arr && arr.length > 0;
};

/**
 * Mongoose schema definition for the Order model.
 */
const OrderSchema = new Schema<IOrder>({
  /** The company associated with this order. References the 'Company' model. */
  companyId: {
    type: Schema.Types.ObjectId,
    required: [true, 'Company ID is required.'], // Added error message
    ref: 'Company',
    index: true // Added index for performance
  },
  /** The unique order number assigned to this order. */
  orderNumber: {
    type: String,
    required: [true, 'Order number is required.'], // Added error message
    unique: true, // Ensure order numbers are unique within a context (consider indexing with companyId if needed globally)
    index: true
  },
  /** The original order number from an external source (e.g., POS). */
  originalOrderNumber: {
    type: String,
    required: false
  },
  /** The current status of the order. */
  status: {
    type: String,
    enum: {
      values: ['DRAFT', 'INCOMING', 'CONFIRMED', 'NOT_DELIVERED', 'PARTIALLY_DELIVERED', 'DELIVERED', 'CANCELLED', 'APPROVED', 'DELETED'],
      message: '{VALUE} is not a supported order status.' // Custom error message
    },
    required: [true, 'Order status is required.'],
    default: 'DRAFT',
    index: true // Added index for performance
  },
  /** Details of the buyer. */
  buyer: {
    /** The type of the buyer entity. */
    buyerType: {
      type: String,
      enum: ['CUSTOMER', 'SUPPLIER', 'BRANCH', 'CENTRAL_KITCHEN'],
      required: [true, 'Buyer type is required.']
    },
    /**
     * The ID of the buyer. This could be an ObjectId referencing Customer, Supplier, etc.,
     * or potentially another identifier depending on the buyerType.
     * Indexing this depends on query patterns.
     */
    buyerId: {
      type: String, // Using String to accommodate different ID types based on buyerType
      required: [true, 'Buyer ID is required.']
    },
    /** Populated field for the buyer's name. Not stored directly in this schema. */
    name: {
      type: String
    },
    /** Alternative field for the buyer's name. */
    buyerName: {
      type: String
    }
  },
  /** Details of the seller (optional). */
  seller: {
    /** The type of the seller entity. */
    sellerType: {
      type: String,
      enum: ['CUSTOMER', 'SUPPLIER', 'BRANCH', 'CENTRAL_KITCHEN']
      // Not required as seller is optional
    },
    /** The ID of the seller. */
    sellerId: {
      type: String // Using String for flexibility
    },
    /** The name of the seller. */
    sellerName: {
      type: String
    }
  },
  /**
   * The location ID of the seller. References the 'Location' model.
   * Indexed for performance in lookups.
   */
  sellerLocationId: {
    type: Schema.Types.ObjectId,
    ref: 'Location',
    required: [true, 'Seller location ID is required.'],
    index: true
  },
  /** The location ID of the buyer (optional). References the 'Location' model. */
  buyerLocationId: {
    type: Schema.Types.ObjectId,
    ref: 'Location',
    required: false // Optional field
  },
  /**
   * Array of items included in the order.
   * Must contain at least one item.
   */
  items: {
    type: [{
      /** The type of the item (Ingredient or Recipe). */
      itemType: {
        type: String,
        enum: ['INGREDIENT', 'RECIPE'],
        required: [true, 'Item type is required.']
      },
      /**
       * The ID of the item. References either 'Ingredient' or 'Recipe' model
       * based on itemType. Validation might require custom logic if strict typing is needed.
       */
      itemId: {
        type: Schema.Types.ObjectId, // Assuming ObjectId, adjust if string IDs are used
        required: [true, 'Item ID is required.'],
        refPath: 'items.itemType' // Dynamic referencing based on itemType
      },
      /** Description of the item. */
      description: {
        type: String,
        required: [true, 'Item description is required.']
      },
      /** Quantity ordered. Must be greater than 0. */
      quantity: {
        type: Number,
        required: [true, 'Item quantity is required.'],
        min: [0.000001, 'Quantity must be greater than 0.'] // Using a small epsilon greater than 0
      },
      /** Quantity delivered. Defaults to 0. */
      deliveredQuantity: {
        type: Number,
        required: true,
        default: 0,
        min: [0, 'Delivered quantity cannot be negative.']
      },
      /**
       * The Unit of Measure ID. References the 'UnitOfMeasure' model.
       */
      uomId: {
        type: Schema.Types.ObjectId,
        required: [true, 'Unit of Measure ID is required.'],
        ref: 'UnitOfMeasure'
      },
      /** Price per unit. Must be non-negative. */
      unitPrice: {
        type: Number,
        required: [true, 'Unit price is required.'],
        min: [0, 'Unit price cannot be negative.']
      },
      /** Total cost for the line item. Must be non-negative. */
      lineTotal: {
        type: Number,
        required: [true, 'Line total is required.'],
        min: [0, 'Line total cannot be negative.']
      }
    }],
    validate: [arrayIsNotEmpty, 'Order must contain at least one item.'] // Validation rule
  },
  /** Array of associated Delivery Note IDs. References the 'DeliveryNote' model. */
  deliveryNoteIds: [{
    type: Schema.Types.ObjectId,
    ref: 'DeliveryNote'
  }],

  // --- Sync Fields ---
  /** Source of the order (WEB, IONIC, POS). */
  orderSource: { type: String, enum: ['WEB', 'IONIC', 'POS'], required: false },
  /** Sync status with external systems. */
  syncStatus: { type: String, enum: ['NOT_SYNCED', 'PENDING_SYNC', 'SYNCED', 'SYNC_FAILED'], default: 'NOT_SYNCED', index: true },
  /** ID of the last sync operation. */
  lastSyncId: { type: String, required: false },
  /** Timestamp of the last sync operation. */
  lastSyncTimestamp: { type: Date, required: false },
  /** Original branch ID from Ionic app. */
  branchId: { type: String, required: false },
  /** Local ID used in the Ionic app. */
  localId: { type: Number, required: false }, // Consider if indexing is needed
  /** Version number for sync conflict resolution. */
  version: { type: Number, default: 1 },

  // --- Audit Fields ---
  /** User who created the order. References 'User' model (adjust if different). */
  createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: false }, // Changed type to ObjectId
  /** User who last modified the order. References 'User' model. */
  modifiedBy: { type: Schema.Types.ObjectId, ref: 'User', required: false } // Changed type to ObjectId
}, {
  timestamps: true, // Automatically add createdAt and updatedAt fields
  toJSON: { virtuals: true }, // Ensure virtuals are included when converting to JSON
  toObject: { virtuals: true } // Ensure virtuals are included when converting to Object
});

// --- Indexes ---
// Compound index for common queries (e.g., finding orders for a company and status)
OrderSchema.index({ companyId: 1, status: 1 });
// Index for sorting by creation date
OrderSchema.index({ createdAt: -1 });
// Index for order number uniqueness within a company (assuming orderNumber should be unique per company)
OrderSchema.index({ companyId: 1, orderNumber: 1 }, { unique: true });

// Consider adding indexes based on frequent query patterns, e.g., buyerId, sellerId

// --- Virtuals ---
// Example: Calculate total order value (can be complex with different currencies/taxes)
// OrderSchema.virtual('totalValue').get(function() {
//   return this.items.reduce((total, item) => total + item.lineTotal, 0);
// });

// --- Hooks ---
// Example: Pre-save hook to calculate line totals automatically
OrderSchema.pre('save', function(next) {
  this.items.forEach(item => {
    // Ensure quantity and unitPrice are numbers before multiplying
    const quantity = typeof item.quantity === 'number' ? item.quantity : 0;
    const unitPrice = typeof item.unitPrice === 'number' ? item.unitPrice : 0;
    // Add rounding logic if necessary, e.g., to 2 decimal places
    item.lineTotal = Math.round((quantity * unitPrice) * 100) / 100;
  });
  // Update version number on modification, excluding initial creation
  if (!this.isNew) {
      this.version = (this.version || 1) + 1;
  }
  next();
});

// Add more specific validation logic if needed, e.g., ensuring buyerId/sellerId exist

const Order = mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema);

export default Order;
