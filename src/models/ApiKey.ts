import mongoose, { Document, Schema, Model, Types } from 'mongoose';

// Interface representing the document structure in MongoDB
export interface IApiKey extends Document {
  key: string;
  userId: Types.ObjectId; // Reference to User model
  companyId: Types.ObjectId; // Reference to Company/Tenant
  role: string; // Role associated with the key
  description?: string; // Optional description
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Mongoose Schema definition
const ApiKeySchema: Schema<IApiKey> = new Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User', // Reference to the User model
    required: true,
    index: true,
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company', // Assuming a Company model might exist or is planned
    required: true,
    index: true,
  },
  role: {
    type: String,
    required: true,
    // Consider adding an enum if roles are predefined
    // enum: ['admin', 'pos_device', 'integration_partner'],
  },
  description: {
    type: String,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
}, {
  timestamps: true, // Automatically manage createdAt and updatedAt fields
  collection: 'apikeys', // Explicit collection name
});

// Prevent model overwrite during hot-reloading in development
let model: Model<IApiKey>;
try {
  model = mongoose.model<IApiKey>('ApiKey');
} catch (error) {
  model = mongoose.model<IApiKey>('ApiKey', ApiKeySchema);
}

export default model;
