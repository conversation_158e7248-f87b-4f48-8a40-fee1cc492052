import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IDeliveryNote extends Document {
  _id: Types.ObjectId;
  companyId: Types.ObjectId;
  deliveryNoteNumber: string;
  status: 'PENDING_REVIEW' | 'DRAFT' | 'FINALIZED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  source: 'WEB' | 'IONIC';
  reviewed: boolean;
  orderId: Types.ObjectId;
  deliveryDate: Date;
  items: Array<{
    itemId: Types.ObjectId;
    quantityPlanned: number;
    uomId: Types.ObjectId;
  }>;
  handoverFlow: Array<{
    stepType: 'DISPATCH' | 'DRIVER' | 'SHOP';
    status: 'PENDING' | 'SIGNED' | 'SKIPPED';
    signedByUserId?: Types.ObjectId;
    signedAt?: Date;
    signatureImageUrl?: string;
    confirmedItems: Array<{
      itemId: Types.ObjectId;
      confirmedQty: number;
    }>;
    notes?: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

const DeliveryNoteSchema = new Schema<IDeliveryNote>(
  {
    companyId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Company',
    },
    deliveryNoteNumber: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ['PENDING_REVIEW', 'DRAFT', 'FINALIZED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
      default: 'DRAFT',
    },
    source: {
      type: String,
      enum: ['WEB', 'IONIC'],
      default: 'WEB',
    },
    reviewed: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Order',
    },
    deliveryDate: {
      type: Date,
      required: true,
    },
    items: [{
      itemId: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'Item',
      },
      quantityPlanned: {
        type: Number,
        required: true,
      },
      uomId: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'UOM',
      },
    }],
    handoverFlow: [{
      stepType: {
        type: String,
        enum: ['DISPATCH', 'DRIVER', 'SHOP'],
        required: true,
      },
      status: {
        type: String,
        enum: ['PENDING', 'SIGNED', 'SKIPPED'],
        default: 'PENDING',
      },
      signedByUserId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
      },
      signedAt: Date,
      signatureImageUrl: String,
      confirmedItems: [{
        itemId: {
          type: Schema.Types.ObjectId,
          required: true,
          ref: 'Item',
        },
        confirmedQty: {
          type: Number,
          required: true,
        },
      }],
      notes: String,
    }],
  },
  {
    timestamps: true,
    collection: 'deliveryNotes', 
  }
);

// Create indexes
DeliveryNoteSchema.index({ companyId: 1, orderId: 1 });
DeliveryNoteSchema.index({ companyId: 1, deliveryNoteNumber: 1 }, { unique: true });

// Check if the model exists before creating it
const DeliveryNote = (mongoose.models?.DeliveryNote as mongoose.Model<IDeliveryNote>) || mongoose.model<IDeliveryNote>('DeliveryNote', DeliveryNoteSchema);

export default DeliveryNote;
