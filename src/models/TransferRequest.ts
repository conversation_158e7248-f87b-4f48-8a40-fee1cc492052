// src/models/TransferRequest.ts
import mongoose, { Schema, Document, Types } from 'mongoose';

export interface TransferItem {
  itemId: Types.ObjectId;
  itemType: 'INGREDIENT' | 'RECIPE';
  quantity: number;
  unitId: Types.ObjectId;
  cost: number;
  requestedQuantity?: number;
  approvedQuantity?: number;
}

export interface PriceMarkup {
  type: 'PERCENTAGE' | 'FIXED';
  value: number;
  applyTax: boolean;
}

export interface ITransferRequest extends Document {
  companyId: Types.ObjectId;
  sourceLocationId: Types.ObjectId;
  destinationLocationId: Types.ObjectId;
  requestedBy: Types.ObjectId;
  items: TransferItem[];
  notes?: string;
  status: 'PENDING' | 'SOURCE_APPROVED' | 'DESTINATION_APPROVED' | 'APPROVED' | 'REJECTED' | 'COMPLETED' | 'CANCELLED';
  sourceApproved: boolean;
  sourceApprovedBy?: Types.ObjectId;
  sourceApprovedAt?: Date;
  destinationApproved: boolean;
  destinationApprovedBy?: Types.ObjectId;
  destinationApprovedAt?: Date;
  transferOutTransactionId?: Types.ObjectId;
  transferInTransactionId?: Types.ObjectId;
  priceMarkup?: PriceMarkup;
  createdAt: Date;
  updatedAt: Date;
}

const TransferRequestSchema = new Schema<ITransferRequest>({
  companyId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true
  },
  sourceLocationId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true
  },
  destinationLocationId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true
  },
  requestedBy: { 
    type: Schema.Types.ObjectId, 
    required: true 
  },
  items: [{
    itemId: { 
      type: Schema.Types.ObjectId, 
      required: true 
    },
    itemType: { 
      type: String, 
      enum: ['INGREDIENT', 'RECIPE'], 
      required: true 
    },
    quantity: { 
      type: Number, 
      required: true 
    },
    unitId: { 
      type: Schema.Types.ObjectId, 
      required: true 
    },
    cost: { 
      type: Number, 
      required: true 
    },
    requestedQuantity: { 
      type: Number 
    },
    approvedQuantity: { 
      type: Number 
    }
  }],
  notes: { 
    type: String 
  },
  status: { 
    type: String, 
    enum: ['PENDING', 'SOURCE_APPROVED', 'DESTINATION_APPROVED', 'APPROVED', 'REJECTED', 'COMPLETED', 'CANCELLED'], 
    default: 'PENDING',
    index: true
  },
  sourceApproved: { 
    type: Boolean, 
    default: false 
  },
  sourceApprovedBy: { 
    type: Schema.Types.ObjectId
  },
  sourceApprovedAt: { 
    type: Date 
  },
  destinationApproved: { 
    type: Boolean, 
    default: false 
  },
  destinationApprovedBy: { 
    type: Schema.Types.ObjectId 
  },
  destinationApprovedAt: { 
    type: Date 
  },
  transferOutTransactionId: { 
    type: Schema.Types.ObjectId 
  },
  transferInTransactionId: { 
    type: Schema.Types.ObjectId 
  },
  priceMarkup: {
    type: {
      type: String,
      enum: ['PERCENTAGE', 'FIXED']
    },
    value: Number,
    applyTax: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Compound indexes for efficient querying
TransferRequestSchema.index({ companyId: 1, status: 1 });
TransferRequestSchema.index({ sourceLocationId: 1, status: 1 });
TransferRequestSchema.index({ destinationLocationId: 1, status: 1 });

// Create or get the model
const TransferRequest = mongoose.models.TransferRequest as mongoose.Model<ITransferRequest> || 
  mongoose.model<ITransferRequest>('TransferRequest', TransferRequestSchema);

export default TransferRequest;
