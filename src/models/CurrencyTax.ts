// src/models/CurrencyTax.ts
import mongoose, { Schema, Document } from 'mongoose';

export interface ICurrencyTax extends Document {
  companyId: mongoose.Types.ObjectId;
  baseCurrency: string;
  vatOnSales: boolean;
  vatOnPurchases: boolean;
  vatRateOnSales: number;
  vatRateOnPurchases: number;
  defaultVatCategory: 'STANDARD' | 'REDUCED' | 'ZERO' | 'EXEMPT';
  createdAt: Date;
  updatedAt: Date;
}

const CurrencyTaxSchema = new Schema<ICurrencyTax>({
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Tenant', required: true },
  baseCurrency: { type: String, required: true },
  vatOnSales: { type: Boolean, default: false },
  vatOnPurchases: { type: Boolean, default: false },
  vatRateOnSales: { type: Number, min: 0, max: 100, default: 20 },
  vatRateOnPurchases: { type: Number, min: 0, max: 100, default: 20 },
  defaultVatCategory: { 
    type: String, 
    enum: ['STANDARD', 'REDUCED', 'ZERO', 'EXEMPT'],
    default: 'STANDARD'
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const CurrencyTax = mongoose.models.CurrencyTax || mongoose.model<ICurrencyTax>('CurrencyTax', CurrencyTaxSchema);

export default CurrencyTax;
