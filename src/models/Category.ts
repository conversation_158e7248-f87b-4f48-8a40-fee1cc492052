import mongoose from 'mongoose'

const categorySchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true 
  },
  groupId: { 
    type: String, 
    required: true,
    index: true
  },
  companyId: { 
    type: String, 
    required: true,
    index: true
  },
  isGlobal: { 
    type: Boolean, 
    default: false,
    index: true
  },
  isCustomizable: { 
    type: Boolean, 
    default: true
  }
}, {
  timestamps: true,
  indexes: [
    { companyId: 1 },
    { groupId: 1 },
    { companyId: 1, groupId: 1 }
  ]
})

export const Category = mongoose.models.Category || mongoose.model('Category', categorySchema)
