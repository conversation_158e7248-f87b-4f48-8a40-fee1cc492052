import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IUOM extends Document {
  companyId?: Types.ObjectId;
  name: string;
  shortCode: string;
  system: 'metric' | 'imperial';
  baseType: 'mass' | 'volume' | 'count';
  factorToCanonical: number;
  synonyms: string[];
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

const UOMSchema = new Schema<IUOM>(
  {
    companyId: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: false,
      index: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    shortCode: {
      type: String,
      required: true,
      trim: true,
    },
    system: {
      type: String,
      required: true,
      enum: ['metric', 'imperial'],
    },
    baseType: {
      type: String,
      required: true,
      enum: ['mass', 'volume', 'count'],
      trim: true,
    },
    factorToCanonical: {
      type: Number,
      required: true,
      validate: {
        validator: function(value: number) {
          return value > 0;
        },
        message: 'Factor must be a positive number'
      }
    },
    synonyms: [{
      type: String,
      trim: true,
    }],
    description: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

// Drop existing indexes to avoid conflicts
if (mongoose.models.UOM) {
  delete mongoose.models.UOM;
}

// Create compound index for company-specific uniqueness on shortCode
UOMSchema.index(
  { companyId: 1, shortCode: 1 }, 
  { 
    unique: true, 
    sparse: true,
    partialFilterExpression: { companyId: { $exists: true } }
  }
);

// Create compound index for company-specific uniqueness on name
UOMSchema.index(
  { companyId: 1, name: 1 }, 
  { 
    unique: true, 
    sparse: true,
    partialFilterExpression: { companyId: { $exists: true } }
  }
);

// Create index for global UOMs
UOMSchema.index(
  { shortCode: 1 }, 
  { 
    unique: true, 
    sparse: true,
    partialFilterExpression: { companyId: { $exists: false } }
  }
);

// Create index for global UOMs names
UOMSchema.index(
  { name: 1 }, 
  { 
    unique: true, 
    sparse: true,
    partialFilterExpression: { companyId: { $exists: false } }
  }
);

export const UOM = mongoose.model<IUOM>('UOM', UOMSchema);

export default UOM;
