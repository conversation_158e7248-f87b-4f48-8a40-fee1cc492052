import mongoose, { Schema, Document } from 'mongoose';

export interface ISupplierSettings extends Document {
  companyId: mongoose.Types.ObjectId;
  requiredFields: {
    taxNumber: boolean;
    address: boolean;
    phone: boolean;
    email: boolean;
    paymentTerms: boolean;
  };
  updatedAt: Date;
}

const SupplierSettingsSchema = new Schema<ISupplierSettings>({
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true, unique: true },
  requiredFields: {
    taxNumber: { type: Boolean, default: false },
    address: { type: Boolean, default: false },
    phone: { type: Boolean, default: false },
    email: { type: Boolean, default: false },
    paymentTerms: { type: Boolean, default: false },
  },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt timestamp before saving
SupplierSettingsSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const SupplierSettings = mongoose.models.SupplierSettings || mongoose.model<ISupplierSettings>('SupplierSettings', SupplierSettingsSchema);

export default SupplierSettings;
