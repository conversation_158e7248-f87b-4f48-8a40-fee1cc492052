import mongoose, { Schema, Types, Document, Model } from 'mongoose';
import Recipe from './Recipe';
import { Ingredient } from './Ingredient';

export interface IBranchInventory extends Document {
  companyId: Types.ObjectId;
  locationId: Types.ObjectId;
  itemId: Types.ObjectId;
  itemType: 'RECIPE' | 'INGREDIENT';
  category: string;
  sellingOptionId: Types.ObjectId;
  currentStock: number;
  pendingStock: number;
  parLevel: number;
  reorderPoint: number;
  baseUomId: Types.ObjectId;
  lastUpdated: Date;
  lastCountDate?: Date;
  isActive: boolean;
  isLocked: boolean;
  lockedBy?: Types.ObjectId;
  lockedAt?: Date;
  stockCountId?: Types.ObjectId;
  // Cost tracking fields for branch transfers with markup
  costBasis?: number;
  originalCost?: number;
  markup?: {
    percentage?: number;
    fixedAmount?: number;
    appliedDate?: Date;
    transferId?: Types.ObjectId;
  };
  // Sync-related fields for Ionic integration
  lastSyncId?: string;
  lastSyncTimestamp?: Date;
  syncSource?: 'WEB' | 'IONIC';
  lastSnapshot?: {
    stock: number;
    snapshotDate: Date;
    reason: string;
    stockCountId?: Types.ObjectId;
  };
  createSnapshot: (reason: string, stockCountId?: Types.ObjectId) => void;
  restoreFromSnapshot: () => Promise<void>;
  // Optional flag to bypass selling option validation for specific operations
  skipSellingOptionValidation?: boolean;
  // Budget tracking fields
  orderBudgetQuantity?: number;
  orderBudgetPeriod?: 'daily' | 'weekly';
  orderBudgetLastReset?: Date;
  orderBudgetUsed?: number;
  // Central kitchen integration fields
  centralKitchenStock?: number;
  centralKitchenParLevel?: number;
  branchParLevel?: number;
  orderingUOM?: string;
  orderingConversionFactor?: number;
  minOrderQuantity?: number;
  maxOrderQuantity?: number;
  leadTimeDays?: number;
  isOrderable?: boolean;
  orderingNotes?: string;
}

// Export interface to make it available for other files
export interface IBranchInventoryModel extends Model<IBranchInventory> {
  validateBulk: (entries: IBranchInventory[]) => Promise<string[]>;
  // Add properties that should be available on the model instance
  currentStock: number;
  pendingStock: number;
  parLevel: number;
  reorderPoint: number;
  lastUpdated: Date;
  costBasis?: number;
  originalCost?: number;
  markup?: {
    percentage?: number;
    fixedAmount?: number;
    appliedDate?: Date;
    transferId?: Types.ObjectId;
  };
  lastSyncId?: string;
  lastSyncTimestamp?: Date;
}

const branchInventorySchema = new Schema<IBranchInventory>({
  companyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Company',
    index: true
  },
  locationId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Location',
    index: true
  },
  itemId: {
    type: Schema.Types.ObjectId,
    required: true,
    refPath: 'itemType',
    index: true
  },
  itemType: {
    type: String,
    required: true,
    enum: ['RECIPE', 'INGREDIENT']
  },
  category: {
    type: String,
    required: false
  },
  sellingOptionId: {
    type: Schema.Types.ObjectId,
    required: false,
    index: true
  },
  currentStock: {
    type: Number,
    required: true,
    default: 0
  },
  pendingStock: {
    type: Number,
    required: true,
    default: 0
  },
  parLevel: {
    type: Number,
    required: true,
    default: 0
  },
  reorderPoint: {
    type: Number,
    required: true,
    default: 0
  },
  baseUomId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'UOM',
    validate: [{
      validator: async function(this: IBranchInventory, value: Types.ObjectId): Promise<boolean> {
        if (this.itemType !== 'INGREDIENT') return true;
        const ingredient = await Ingredient.findById(this.itemId);
        return ingredient ? ingredient.baseUomId.toString() === value.toString() : false;
      },
      message: 'BaseUomId must match the ingredient\'s canonical base UOM'
    }]
  },
  costBasis: {
    type: Number,
    default: 0
  },
  originalCost: {
    type: Number,
    default: 0
  },
  markup: {
    percentage: Number,
    fixedAmount: Number,
    appliedDate: Date,
    transferId: Schema.Types.ObjectId
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  lastSyncId: {
    type: String,
    required: false
  },
  lastSyncTimestamp: {
    type: Date,
    required: false
  },
  syncSource: {
    type: String,
    enum: ['WEB', 'IONIC'],
    required: false
  },
  lastCountDate: {
    type: Date
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  isLocked: {
    type: Boolean,
    default: false
  },
  lockedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  lockedAt: {
    type: Date
  },
  stockCountId: {
    type: Schema.Types.ObjectId,
    ref: 'StockCount'
  },
  lastSnapshot: {
    type: {
      stock: Number,
      snapshotDate: Date,
      reason: String,
      stockCountId: {
        type: Schema.Types.ObjectId,
        ref: 'StockCount'
      }
    },
    required: false
  },
  orderBudgetQuantity: {
    type: Number,
    required: false
  },
  orderBudgetPeriod: {
    type: String,
    enum: ['daily', 'weekly'],
    required: false
  },
  orderBudgetLastReset: {
    type: Date,
    required: false
  },
  orderBudgetUsed: {
    type: Number,
    default: 0,
    required: false
  },
  // Central kitchen integration fields
  centralKitchenStock: {
    type: Number,
    required: false
  },
  centralKitchenParLevel: {
    type: Number,
    required: false
  },
  branchParLevel: {
    type: Number,
    required: false
  },
  orderingUOM: {
    type: String,
    required: false
  },
  orderingConversionFactor: {
    type: Number,
    required: false,
    default: 1
  },
  minOrderQuantity: {
    type: Number,
    required: false,
    default: 0
  },
  maxOrderQuantity: {
    type: Number,
    required: false
  },
  leadTimeDays: {
    type: Number,
    required: false,
    default: 0
  },
  isOrderable: {
    type: Boolean,
    default: true,
    required: false
  },
  orderingNotes: {
    type: String,
    required: false
  }
});

// Compound indexes
branchInventorySchema.index({ companyId: 1, locationId: 1, itemId: 1 }, { unique: true });
branchInventorySchema.index({ companyId: 1, locationId: 1, isActive: 1 });
branchInventorySchema.index({ companyId: 1, locationId: 1, isLocked: 1 });

// Method to create a snapshot before stock count
branchInventorySchema.methods.createSnapshot = function(reason: string, stockCountId?: Types.ObjectId) {
  this.lastSnapshot = {
    stock: this.currentStock,
    snapshotDate: new Date(),
    reason,
    stockCountId
  };
  this.lastUpdated = new Date();
};

// Method to restore from snapshot
branchInventorySchema.methods.restoreFromSnapshot = async function() {
  if (!this.lastSnapshot) {
    throw new Error('No snapshot available to restore from');
  }
  this.currentStock = this.lastSnapshot.stock;
  this.lastUpdated = new Date();
  await this.save();
};

// Pre-save middleware to ensure baseUomId matches ingredient
branchInventorySchema.pre('save', async function(this: IBranchInventory, next) {
  if (this.itemType === 'INGREDIENT' && (this.isNew || this.isModified('baseUomId'))) {
    const ingredient = await Ingredient.findById(this.itemId);
    if (!ingredient) {
      return next(new Error('Referenced ingredient not found'));
    }
    this.baseUomId = ingredient.baseUomId;
  }
  next();
});

// Pre-save hook for validating selling options using the unique selling detail _id
branchInventorySchema.pre('save', async function(this: IBranchInventory, next) {
  try {
    // Skip validation if the item is inactive or no sellingOptionId is provided
    if (!this.isActive || !this.sellingOptionId) {
      return next();
    }

    console.log(`Validating BranchInventory with sellingOptionId: ${this.sellingOptionId}`);

    const sourceItem = await (this.itemType === 'INGREDIENT'
      ? Ingredient.findOne({ _id: this.itemId, companyId: this.companyId })
      : Recipe.findOne({ _id: this.itemId, companyId: this.companyId }));

    if (!sourceItem) {
      throw new Error(`${this.itemType.toLowerCase()} not found with ID ${this.itemId}`);
    }

    const sellingDetails = sourceItem.sellingDetails || [];
    console.log(`Found ${sellingDetails.length} selling options for ${this.itemType} ${this.itemId}`);
    console.log(`Full selling details: ${JSON.stringify(sellingDetails)}`);

    const sellingOptionIdStr = this.sellingOptionId.toString();

    if (sellingDetails.length === 0) {
      console.log(`No selling options available, allowing creation with sellingOptionId: ${sellingOptionIdStr}`);
      return next();
    }

    // Compare the stored sellingOptionId with each selling detail's unique _id
    let matchingOption;
    for (const option of sellingDetails) {
      const optionId = option._id ? option._id.toString() : '';
      console.log(`Comparing selling option _id ${optionId} with ${sellingOptionIdStr}`);
      if (optionId === sellingOptionIdStr) {
        matchingOption = option;
        console.log('Match found!');
        break;
      }
    }

    if (!matchingOption) {
      console.log(`No matching selling option found for ${sellingOptionIdStr}`);
      throw new Error('Selling option not found');
    }

    // Visibility checks
    if (matchingOption.visibility.type === 'ALL_LOCATIONS') {
      return next();
    }

    if (matchingOption.visibility) {
      const visibility = matchingOption.visibility;
      const locationType = visibility.type;

      if (locationType === 'EXTERNAL_ONLY') {
        throw new Error('Cannot create inventory for external-only selling option');
      }

      if (locationType === 'SPECIFIC_LOCATIONS' && Array.isArray(visibility.locations) && visibility.locations.length > 0) {
        const locationIdStr = this.locationId.toString();
        let isAllowed = false;

        for (const loc of visibility.locations) {
          const locId = typeof loc === 'object'
            ? (loc._id || loc.$oid || '').toString()
            : loc.toString();
          if (locId === locationIdStr) {
            isAllowed = true;
            break;
          }
        }

        if (!isAllowed) {
          throw new Error(`This ${this.itemType.toLowerCase()} is not available at this location`);
        }
      }
    }

    return next();
  } catch (error) {
    return next(error instanceof Error ? error : new Error(String(error)));
  }
});

// Enhanced validateBulk method remains unchanged
branchInventorySchema.statics.validateBulk = async function(entries: IBranchInventory[]) {
  const errors: string[] = [];

  for (const entry of entries) {
    try {
      if (entry.itemType === 'INGREDIENT') {
        const ingredient = await Ingredient.findById(entry.itemId);
        if (!ingredient) {
          errors.push(`Ingredient not found for entry ${entry._id}`);
          continue;
        }
        if (entry.baseUomId.toString() !== ingredient.baseUomId.toString()) {
          errors.push(`Invalid baseUomId for entry ${entry._id}. Must match ingredient's baseUomId`);
          continue;
        }
      }

      const Model: any = entry.itemType === 'RECIPE' ? Recipe : Ingredient;
      const sourceItem = await Model.findOne({ _id: entry.itemId });
      if (!sourceItem) {
        errors.push(`${entry.itemType.toLowerCase()} not found for entry ${entry._id}`);
        continue;
      }

      const sellingOption = sourceItem.sellingOptions?.find(
        (opt: any) => opt._id.toString() === entry.sellingOptionId.toString()
      );

      if (!sellingOption) {
        errors.push(`Selling option not found for entry ${entry._id}`);
        continue;
      }

      if (sellingOption.visibility.type === 'EXTERNAL_ONLY') {
        errors.push(`Cannot create inventory for external-only selling option (entry ${entry._id})`);
      }
    } catch (error) {
      errors.push(`Validation error for entry ${entry._id}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  return errors;
};

// Initialize or reuse the BranchInventory model with correct typings
const BranchInventory: IBranchInventoryModel = (mongoose.models.BranchInventory as IBranchInventoryModel) ||
  mongoose.model<IBranchInventory, IBranchInventoryModel>('BranchInventory', branchInventorySchema);

export default BranchInventory;
