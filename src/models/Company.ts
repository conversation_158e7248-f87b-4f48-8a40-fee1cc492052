// src/models/Company.ts
import mongoose, { Schema, Document } from 'mongoose';

export interface CompanyDocument extends Document {
  name: string;
  subdomain: string;
  ownerId: mongoose.Types.ObjectId;
  companyCode?: string;
  posPassword?: string;
  weekStartDay?: number;
  createdAt: Date;
  updatedAt: Date;
}

const CompanySchema = new Schema<CompanyDocument>({
  name: { type: String, required: true },
  subdomain: { type: String, required: true, unique: true },
  ownerId: { type: mongoose.Schema.Types.ObjectId, required: true, ref: 'User' },
  companyCode: { type: String, sparse: true, index: true },
  posPassword: { type: String },
  weekStartDay: {
    type: Number,
    min: 0,
    max: 6,
    default: 1 // Default to Monday
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
}, {
  timestamps: true,
});

// Only initialize the model on the server side
const Company = mongoose.models.Company || mongoose.model<CompanyDocument>('Company', CompanySchema);

export { CompanySchema };
export default Company;
