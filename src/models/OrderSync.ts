// src/models/OrderSync.ts
import mongoose, { Schema, Document, Types } from 'mongoose';

export interface ISyncError {
  orderId: string;
  error: string;
}

export interface IOrderSync extends Document {
  companyId: Types.ObjectId;
  locationId: Types.ObjectId;
  syncId: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'PARTIAL';
  startTime: Date;
  endTime?: Date;
  ordersReceived: number;
  ordersProcessed: number;
  syncErrors?: ISyncError[];
  createdAt: Date;
  updatedAt: Date;
}

const OrderSyncSchema = new Schema<IOrderSync>(
  {
    companyId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Company',
      index: true
    },
    locationId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Location',
      index: true
    },
    syncId: {
      type: String,
      required: true,
      unique: true
    },
    status: {
      type: String,
      required: true,
      enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PARTIAL'],
      default: 'PENDING'
    },
    startTime: {
      type: Date,
      required: true,
      default: Date.now
    },
    endTime: {
      type: Date,
      required: false
    },
    ordersReceived: {
      type: Number,
      required: true,
      default: 0
    },
    ordersProcessed: {
      type: Number,
      required: true,
      default: 0
    },
    syncErrors: [{
      orderId: String,
      error: String
    }]
  },
  {
    timestamps: true
  }
);

// Create indexes for performance
OrderSyncSchema.index({ companyId: 1, locationId: 1, startTime: -1 });
// Avoid creating duplicate indexes - the syncId field already has an index from unique: true
// OrderSyncSchema.index({ syncId: 1 });
OrderSyncSchema.index({ status: 1 });

const OrderSync = mongoose.models.OrderSync || mongoose.model<IOrderSync>('OrderSync', OrderSyncSchema);

export default OrderSync;
