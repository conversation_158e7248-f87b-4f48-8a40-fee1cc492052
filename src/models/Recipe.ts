// src/models/Recipe.ts
import mongoose, { Schema, Document, Types } from 'mongoose';
import { UOM as UnitOfMeasure } from '../utils/uomConversion';

interface UOMDoc {
  _id: string;
  name: string;
  shortCode: string;
  system: string;
  baseType: string;
  factorToCanonical: number;
  synonyms: string[];
  description: string;
}

export interface RecipeIngredient {
  ingredientName: string;
  quantity: number;
  unitOfMeasure: Types.ObjectId | UOMDoc;
  ingredientId: Types.ObjectId | null;
  recipesId: Types.ObjectId | null;
  isSubRecipe: boolean;
}

export interface IRecipe extends Document {
  _id: Types.ObjectId;
  name: string;
  description: string;
  isSubRecipe: boolean;
  yield: number;
  baseYieldUOM: Types.ObjectId | UOMDoc;
  Category: string;
  category: string;
  stockable: boolean;
  canBeSold: boolean;
  currentStock: number;
  pendingStock: number;
  recipeIngredients: RecipeIngredient[];
  companyId: Types.ObjectId;
  sellingDetails: any[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const RecipeSchema = new Schema<IRecipe>({
  companyId: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  isSubRecipe: { type: Boolean, default: false },
  yield: { type: Number, required: true },
  baseYieldUOM: { 
    type: Schema.Types.ObjectId,
    ref: 'UOM',
    required: true,
    validate: {
      validator: function(v: any) {
        return Types.ObjectId.isValid(v);
      },
      message: 'baseYieldUOM must be a valid ObjectId'
    }
  },
  Category: { type: String },
  category: { type: String },
  stockable: { type: Boolean, default: false },
  canBeSold: { type: Boolean, default: false },
  currentStock: { type: Number, default: 0 },
  pendingStock: { type: Number, default: 0 },
  recipeIngredients: [{
    ingredientName: { type: String, required: true },
    quantity: { type: Number, required: true },
    unitOfMeasure: { 
      type: Schema.Types.ObjectId,
      ref: 'UOM',
      required: true,
      validate: {
        validator: function(v: any) {
          return Types.ObjectId.isValid(v);
        },
        message: 'unitOfMeasure must be a valid ObjectId'
      }
    },
    ingredientId: { 
      type: Schema.Types.ObjectId,
      ref: 'Ingredient',
      validate: {
        validator: function(v: any) {
          return v === null || Types.ObjectId.isValid(v);
        },
        message: 'ingredientId must be null or a valid ObjectId'
      }
    },
    recipesId: { 
      type: Schema.Types.ObjectId,
      ref: 'Recipe',
      validate: {
        validator: function(v: any) {
          return v === null || Types.ObjectId.isValid(v);
        },
        message: 'recipesId must be null or a valid ObjectId'
      }
    },
    isSubRecipe: { type: Boolean, default: false }
  }],
  sellingDetails: [{
    unitOfSelling: { type: String, required: true },
    priceWithoutTax: { type: Number, required: true },
    priceWithTax: { type: Number, required: true },
    taxRate: { type: Number, required: true },
    taxCategory: { 
      type: String, 
      enum: ['STANDARD', 'REDUCED', 'ZERO', 'EXEMPT'],
      default: 'STANDARD'
    },
    conversionFactor: { type: Number, required: true },
    visibility: {
      type: {
        type: String,
        enum: ['ALL_LOCATIONS', 'SPECIFIC_LOCATIONS', 'EXTERNAL_ONLY'],
        default: 'ALL_LOCATIONS'
      },
      locations: [{ 
        type: Schema.Types.ObjectId, 
        ref: 'Location',
        validate: {
          validator: function(v: any) {
            return Types.ObjectId.isValid(v);
          },
          message: 'Location ID must be a valid ObjectId'
        }
      }],
      externalAccess: { type: Boolean, default: false }
    }
  }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  toJSON: {
    transform(doc, ret) {
      ret.id = ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Drop existing model to avoid conflicts
if (mongoose.models.Recipe) {
  delete mongoose.models.Recipe;
}

export default mongoose.models.Recipe || mongoose.model<IRecipe>('Recipe', RecipeSchema);