import mongoose, { Document, Schema, Types } from 'mongoose';

// Interface defining the structure for combo requirements
interface IComboRequirement {
  requiredItems: Array<{
    itemId: Types.ObjectId; // Reference to BranchInventory or Ingredient/Recipe?
    quantity: number;
  }>;
  discountedItems: Array<{
    itemId: Types.ObjectId; // Reference to BranchInventory or Ingredient/Recipe?
    discountValue: number;
    discountType: 'percentage' | 'fixed';
  }>;
}

// Interface defining the Discount document structure
export interface IDiscount extends Document {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  type: 'percentage' | 'fixed' | 'combo';
  value: number; // Percentage value or fixed amount
  startDate?: Date;
  endDate?: Date;
  applicableItems?: Types.ObjectId[]; // References to MenuItems? BranchInventory?
  applicableCategories?: Types.ObjectId[]; // References to Categories
  minimumOrderValue?: number;
  maximumDiscountAmount?: number;
  isActive: boolean;
  companyId: Types.ObjectId; // Reference to Company
  locationId?: Types.ObjectId; // Optional: Reference to Location (for location-specific discounts)
  comboRequirements?: IComboRequirement;
  createdAt: Date;
  updatedAt: Date;
}

const ComboRequirementSchema = new Schema({
  requiredItems: [
    {
      itemId: { type: Schema.Types.ObjectId, required: true }, // Needs ref confirmation
      quantity: { type: Number, required: true, min: 1 },
      _id: false,
    },
  ],
  discountedItems: [
    {
      itemId: { type: Schema.Types.ObjectId, required: true }, // Needs ref confirmation
      discountValue: { type: Number, required: true, min: 0 },
      discountType: {
        type: String,
        enum: ['percentage', 'fixed'],
        required: true,
      },
      _id: false,
    },
  ],
});

const DiscountSchema = new Schema<IDiscount>(
  {
    name: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    type: {
      type: String,
      enum: ['percentage', 'fixed', 'combo'],
      required: true,
    },
    value: { type: Number, required: true, min: 0 }, // Consider validation based on type
    startDate: { type: Date },
    endDate: { type: Date },
    applicableItems: [{ type: Schema.Types.ObjectId }], // Needs ref confirmation
    applicableCategories: [{ type: Schema.Types.ObjectId, ref: 'Category' }], // Assuming Category model exists
    minimumOrderValue: { type: Number, min: 0 },
    maximumDiscountAmount: { type: Number, min: 0 },
    isActive: { type: Boolean, default: true, required: true },
    companyId: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true,
      index: true,
    },
    locationId: {
      type: Schema.Types.ObjectId,
      ref: 'Location',
      required: false,
      index: true,
    },
    comboRequirements: { type: ComboRequirementSchema, required: false },
  },
  { timestamps: true }
);

// Indexes
DiscountSchema.index({ companyId: 1, locationId: 1, isActive: 1 });
DiscountSchema.index({ isActive: 1, startDate: 1, endDate: 1 });

// Add validation for endDate >= startDate if both exist
DiscountSchema.path('endDate').validate(function (value) {
  if (this.startDate && value) {
    return value >= this.startDate;
  }
  return true;
}, 'End date must be greater than or equal to start date.');

// Add validation for combo requirements if type is 'combo'
DiscountSchema.path('comboRequirements').validate(function (value) {
  if (this.type === 'combo') {
    return value && value.requiredItems?.length > 0 && value.discountedItems?.length > 0;
  }
  return true;
}, 'Combo requirements are mandatory for combo type discounts.');

// Add validation for value based on type (e.g., percentage <= 100)
DiscountSchema.path('value').validate(function (value) {
  if (this.type === 'percentage') {
    return value >= 0 && value <= 100;
  }
  return value >= 0;
}, 'Invalid discount value for the selected type.');

const Discount = mongoose.models.Discount || mongoose.model<IDiscount>('Discount', DiscountSchema);

export default Discount;
