import mongoose, { Schema, Document } from 'mongoose';

export interface LocationDoc extends Document {
  companyId: mongoose.Types.ObjectId;
  name: string;
  locationType: 'CENTRAL_KITCHEN' | 'RETAIL_SHOP' | 'SINGLE_LOCATION';
  canSellToExternal: boolean;
  canDoTransfers: boolean;
  canBuyfromExternalSuppliers: boolean;
  address?: string;
  contactInfo?: {
    phone?: string;
    email?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const LocationSchema = new Schema<LocationDoc>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true, ref: 'Tenant' },
  name: { type: String, required: true },
  locationType: { type: String, required: true, enum: ['CENTRAL_KITCHEN', 'RETAIL_SHOP', 'SINGLE_LOCATION'] },
  canSellToExternal: { type: Boolean, required: true },
  canDoTransfers: { type: Boolean, required: true },
  canBuyfromExternalSuppliers: { type: Boolean, required: true, default: false },
  address: { type: String },
  contactInfo: {
    phone: { type: String },
    email: { type: String },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Location = mongoose.models.Location || mongoose.model<LocationDoc>('Location', LocationSchema);
export default Location;
