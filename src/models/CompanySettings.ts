import mongoose, { Schema, Document, Types } from 'mongoose';

export interface HandoverStep {
  stepType: 'DISPATCH' | 'QUALITY_CHECK' | 'DRIVER' | 'SECURITY' | 'SHOP' | 'CUSTOMER';
  required: boolean;
}

export interface CompanySettings extends Document {
  companyId: Types.ObjectId;
  handoverSteps: HandoverStep[];
  createdAt: Date;
  updatedAt: Date;
}

const companySettingsSchema = new Schema<CompanySettings>(
  {
    companyId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Company',
    },
    handoverSteps: [{
      stepType: {
        type: String,
        enum: ['DISPATCH', 'QUALITY_CHECK', 'DRIVER', 'SECURITY', 'SHOP', 'CUSTOMER'],
        required: true,
      },
      required: {
        type: Boolean,
        default: false,
      },
    }],
  },
  {
    timestamps: true,
  }
);

// Ensure one settings document per company
companySettingsSchema.index({ companyId: 1 }, { unique: true });

export default mongoose.models.CompanySettings || 
  mongoose.model<CompanySettings>('CompanySettings', companySettingsSchema);
