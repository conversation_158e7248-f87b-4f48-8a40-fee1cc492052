import mongoose from 'mongoose';

const TaxSettingsSchema = new mongoose.Schema({
  companyId: {
    type: String,
    required: true,
    index: true
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  vatOnSales: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 20
  },
  vatOnPurchases: {
    type: Number,
    required: true,
    min: 0,
    max: 100,
    default: 20
  },
  enableVatOnSales: {
    type: Boolean,
    required: true,
    default: true
  },
  enableVatOnPurchases: {
    type: Boolean,
    required: true,
    default: true
  },
  defaultVatCategory: {
    type: String,
    required: true,
    enum: ['STANDARD', 'REDUCED', 'ZERO', 'EXEMPT'],
    default: 'STANDARD'
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Ensure one settings document per company
TaxSettingsSchema.index({ companyId: 1 }, { unique: true });

export default mongoose.models.TaxSettings || mongoose.model('TaxSettings', TaxSettingsSchema);
