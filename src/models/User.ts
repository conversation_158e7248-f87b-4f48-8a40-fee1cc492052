import mongoose, { Schema, Document } from 'mongoose';

export interface IUser extends Document {
  email: string;
  passwordHash: string;
  name?: string; // Added name field
  displayName?: string;
  userType: 'superuser' | 'company_user';
  companyId?: mongoose.Types.ObjectId;
  // System roles: 'owner', 'admin', 'manager', 'user', 'storekeeper'
  // Or can be a custom role ID reference
  role?: string;
  // Can directly reference specific permissions for fine-grained control
  permissions?: string[];
  // For custom roles: stores the role document ID
  customRoleId?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  lastModified?: Date;
  syncStatus?: 'pending' | 'synced';
  modifiedFields?: string[];
  pin?: string;
  isDeleted?: boolean;
  // UPDATED: Change to support multiple locations
  locationIds: mongoose.Types.ObjectId[]; // Array of location IDs
  locationId?: mongoose.Types.ObjectId; // Single location ID for primary assignment
  canUseIonicApp?: boolean; // Flag to indicate if user can use the IonicPOS app
  posAccess?: boolean; // Whether user can access POS system
  posSettings?: {
    defaultRegister?: string;
    canOpenDrawer?: boolean;
    canGiveDiscount?: boolean;
    canVoidSales?: boolean;
    [key: string]: any;
  }; // POS-specific settings
  locationsAccess?: mongoose.Types.ObjectId[]; // List of locations user can access
}

const UserSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: true },
  name: { type: String },
  displayName: { type: String },
  userType: { type: String, required: true, enum: ['superuser', 'company_user'] },
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company' },
  // Role can now be either a system role string or a custom role ID
  role: { 
    type: String, 
    required: function(this: IUser) { 
      return this.userType === 'company_user'; 
    },
    // Custom validator to validate system roles but allow ObjectIds for custom roles
    validate: {
      validator: function(v: string) {
        // Allow system roles
        const systemRoles = ['owner', 'admin', 'manager', 'user', 'storekeeper'];
        if (systemRoles.includes(v)) return true;
        
        // Allow MongoDB ObjectId strings (for custom roles)
        try {
          return mongoose.Types.ObjectId.isValid(v);
        } catch (e) {
          return false;
        }
      },
      message: props => `${props.value} is not a valid role`
    }
  },
  // Store the reference to custom role document
  customRoleId: { type: mongoose.Schema.Types.ObjectId, ref: 'Role' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastModified: { type: Date, default: Date.now },
  syncStatus: { type: String, enum: ['pending', 'synced'], default: 'pending' },
  modifiedFields: { type: [String] },
  permissions: { type: [String] },
  pin: { type: String },
  isDeleted: { type: Boolean, default: false },
  // UPDATED: Change locationId to locationIds
  locationIds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Location' }],
  // Single primary location for POS
  locationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Location' },
  // Access to multiple locations
  locationsAccess: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Location' }],
  canUseIonicApp: { type: Boolean, default: false },
  posAccess: { type: Boolean, default: false },
  posSettings: {
    defaultRegister: { type: String },
    canOpenDrawer: { type: Boolean, default: false },
    canGiveDiscount: { type: Boolean, default: false },
    canVoidSales: { type: Boolean, default: false },
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: {}
  }
});

// Add indexes for efficient sync queries
UserSchema.index({ lastModified: 1 });
UserSchema.index({ companyId: 1, lastModified: 1 });
// UPDATED: Index for multiple locations
UserSchema.index({ companyId: 1, locationIds: 1 });
UserSchema.index({ customRoleId: 1 });

// Pre-save middleware to handle role and customRoleId sync
UserSchema.pre('save', async function(next) {
  // If role is an ObjectId string, make sure it's set as customRoleId too
  if (this.role && mongoose.Types.ObjectId.isValid(this.role)) {
    this.customRoleId = new mongoose.Types.ObjectId(this.role);
  }
  this.updatedAt = new Date();
  this.lastModified = new Date();
  next();
});

// Only initialize the model on the server side
const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
