// src/models/Role.ts
import mongoose, { Schema, Document } from 'mongoose';

export interface IRole extends Document {
  companyId: mongoose.Types.ObjectId;
  name: string;
  description: string;
  type: 'hq' | 'branch';
  isSystemRole: boolean;
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;
}

const RoleSchema = new Schema<IRole>({
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true },
  name: { type: String, required: true },
  description: { type: String, default: '' },
  type: { type: String, enum: ['hq', 'branch'], required: true, default: 'hq' },
  isSystemRole: { type: Boolean, default: false },
  permissions: { type: [String], default: [] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const Role = mongoose.models.Role || mongoose.model<IRole>('Role', RoleSchema);

export default Role;
