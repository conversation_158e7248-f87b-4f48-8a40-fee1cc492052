// src/models/Ingredient.ts
import mongoose, { Document, Schema, Types } from 'mongoose';
import './Supplier'; // Import Supplier model to ensure it's registered
import './UOM'; // Import UOM model to ensure it's registered
import './Location'; // Import Location model to ensure it's registered

const UnitSchema = new Schema({
  unitOfMeasure: {
    type: Schema.Types.ObjectId,
    ref: 'UOM',
    required: true,
    validate: {
      validator: (v: any) => Types.ObjectId.isValid(v),
      message: 'Unit of measure must be a valid ObjectId'
    }
  },
  quantityInBaseUom: { 
    type: Number, 
    required: true,
    min: 0
  },
  price: { 
    type: Number, 
    required: true,
    min: 0
  },
  pricePerBaseUom: { 
    type: Number, 
    required: true,
    min: 0
  }
});

const SupplierDetailSchema = new Schema({
  supplierId: {
    type: Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true,
    validate: {
      validator: (v: any) => Types.ObjectId.isValid(v),
      message: 'Supplier ID must be a valid ObjectId'
    }
  },
  unitsOfOrdering: [UnitSchema]
});

const SellingOptionSchema = new Schema({
  unitOfSelling: { 
    type: Schema.Types.ObjectId,
    ref: 'UOM',
    required: true,
    validate: {
      validator: (v: any) => Types.ObjectId.isValid(v),
      message: 'Unit of selling must be a valid ObjectId'
    }
  },
  priceWithoutTax: { 
    type: Number, 
    required: true, 
    min: 0 
  },
  priceWithTax: { 
    type: Number, 
    required: true, 
    min: 0 
  },
  taxRate: { 
    type: Number, 
    required: true, 
    min: 0 
  },
  taxCategory: {
    type: String,
    enum: ['STANDARD', 'REDUCED', 'ZERO', 'EXEMPT'],
    default: 'STANDARD'
  },
  conversionFactor: { 
    type: Number, 
    required: true, 
    min: 0.01 
  },
  visibility: {
    type: {
      type: String,
      enum: ['ALL_LOCATIONS', 'SPECIFIC_LOCATIONS', 'EXTERNAL_ONLY'],
      default: 'ALL_LOCATIONS'
    },
    locations: [{
      type: Schema.Types.ObjectId,
      ref: 'Location',
      validate: {
        validator: (v: any) => Types.ObjectId.isValid(v),
        message: 'Location ID must be a valid ObjectId'
      }
    }],
    externalAccess: { 
      type: Boolean, 
      default: false 
    }
  },
  sourceType: {
    type: String,
    enum: ['CENTRAL_KITCHEN', 'EXTERNAL_SUPPLIER', 'BOTH'],
    required: true
  },
  markupType: {
    type: String,
    enum: ['AT_COST', 'MARKUP'],
    required: function(this: any) {
      return this.sourceType === 'CENTRAL_KITCHEN' || this.sourceType === 'BOTH';
    }
  },
  markupPercentage: {
    type: Number,
    min: 0,
    required: function(this: any) {
      return this.markupType === 'MARKUP';
    }
  }
});

export interface IIngredient extends Document {
  name: string;
  description: string;
  reorderPoint: number | null;
  baseUomId: Types.ObjectId;
  category: string;
  SKU: string;
  companyId: Types.ObjectId;
  defaultSupplierId: Types.ObjectId | null;
  currentStock: number;
  pendingStock: number;
  supplierDetails: Array<{
    supplierId: Types.ObjectId;
    unitsOfOrdering: Array<{
      unitOfMeasure: Types.ObjectId;
      quantityInBaseUom: number;
      price: number;
      pricePerBaseUom: number;
    }>;
  }>;
  canBeSold: boolean;
  sellingDetails: Array<{
    unitOfSelling: Types.ObjectId;
    priceWithoutTax: number;
    priceWithTax: number;
    taxRate: number;
    taxCategory: 'STANDARD' | 'REDUCED' | 'ZERO' | 'EXEMPT';
    conversionFactor: number;
    visibility: {
      type: 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';
      locations: Types.ObjectId[];
      externalAccess: boolean;
    };
    sourceType: 'CENTRAL_KITCHEN' | 'EXTERNAL_SUPPLIER' | 'BOTH';
    markupType?: 'AT_COST' | 'MARKUP';
    markupPercentage?: number;
  }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const IngredientSchema = new Schema<IIngredient>(
  {
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      default: ''
    },
    reorderPoint: {
      type: Number,
      default: null
    },
    baseUomId: {
      type: Schema.Types.ObjectId,
      ref: 'UOM',
      required: true,
      validate: {
        validator: (v: any) => Types.ObjectId.isValid(v),
        message: 'Base UOM ID must be a valid ObjectId'
      }
    },
    category: {
      type: String,
      required: true
    },
    SKU: {
      type: String,
      required: true
    },
    companyId: {
      type: Schema.Types.ObjectId,
      required: true,
      validate: {
        validator: (v: any) => Types.ObjectId.isValid(v),
        message: 'Company ID must be a valid ObjectId'
      }
    },
    defaultSupplierId: {
      type: Schema.Types.ObjectId,
      ref: 'Supplier',
      default: null,
      validate: {
        validator: (v: any) => v === null || Types.ObjectId.isValid(v),
        message: 'Default supplier ID must be null or a valid ObjectId'
      }
    },
    supplierDetails: [SupplierDetailSchema],
    currentStock: {
    type: Number,
    default: 0
  },
  pendingStock: {
    type: Number,
    default: 0
  },
  canBeSold: {
      type: Boolean,
      default: false
    },
    sellingDetails: [SellingOptionSchema],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

// Drop existing model to avoid conflicts
if (mongoose.models.Ingredient) {
  delete mongoose.models.Ingredient;
}

export const Ingredient = mongoose.model<IIngredient>('Ingredient', IngredientSchema);
