// src/models/MarkupRule.ts
import mongoose, { Schema, Document, Types } from 'mongoose';

interface MarkupCondition {
  type: 'CATEGORY' | 'LOCATION' | 'ITEM_TYPE' | 'SPECIFIC_ITEM';
  value: string;
}

export interface IMarkupRule extends Document {
  companyId: Types.ObjectId;
  name: string;
  description?: string;
  priority: number;
  conditions: MarkupCondition[];
  markupType: 'PERCENTAGE' | 'FIXED';
  markupValue: number;
  applyTax: boolean;
  isActive: boolean;
  createdBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const MarkupRuleSchema = new Schema<IMarkupRule>({
  companyId: {
    type: Schema.Types.ObjectId,
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  priority: {
    type: Number,
    default: 10,
    index: true
  },
  conditions: [{
    type: {
      type: String,
      enum: ['CATEGORY', 'LOCATION', 'ITEM_TYPE', 'SPECIFIC_ITEM'],
      required: true
    },
    value: {
      type: String,
      required: true
    }
  }],
  markupType: {
    type: String,
    enum: ['PERCENTAGE', 'FIXED'],
    required: true
  },
  markupValue: {
    type: Number,
    required: true
  },
  applyTax: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    required: true
  }
}, {
  timestamps: true
});

// Create indexes for efficient querying
MarkupRuleSchema.index({ companyId: 1, isActive: 1 });
MarkupRuleSchema.index({ companyId: 1, isActive: 1, priority: 1 });

const MarkupRule = mongoose.models.MarkupRule as mongoose.Model<IMarkupRule> || 
  mongoose.model<IMarkupRule>('MarkupRule', MarkupRuleSchema);

export default MarkupRule;
