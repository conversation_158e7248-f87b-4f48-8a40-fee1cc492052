import mongoose, { Schema, Types, Document } from 'mongoose';

export interface IStockCount extends Document {
  _id: Types.ObjectId;
  companyId: Types.ObjectId;
  locationId: Types.ObjectId;
  status: 'IN_PROGRESS' | 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
  countDate: Date;
  startedBy: Types.ObjectId;
  approvedBy?: Types.ObjectId;
  approvedAt?: Date;
  notes?: string;
  items: Array<{
    itemId: Types.ObjectId;
    itemType: 'RECIPE' | 'INGREDIENT';
    sellingOptionId: string;
    systemStock: number;
    countedStock: number;
    difference: number;
    baseUomId: Types.ObjectId;
    notes?: string;
  }>;
  isLocked: boolean; // Indicates if inventory updates are locked during count
  lockedAt?: Date;
  unlockedAt?: Date;
}

const stockCountSchema = new Schema<IStockCount>({
  companyId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Company',
    index: true
  },
  locationId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Location',
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['IN_PROGRESS', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'CANCELLED'],
    default: 'IN_PROGRESS'
  },
  countDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  startedBy: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'User'
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: {
    type: Date
  },
  notes: {
    type: String
  },
  items: [{
    itemId: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: 'items.itemType'
    },
    itemType: {
      type: String,
      required: true,
      enum: ['RECIPE', 'INGREDIENT']
    },
    sellingOptionId: {
      type: String,
      required: true
    },
    systemStock: {
      type: Number,
      required: true
    },
    countedStock: {
      type: Number,
      required: true
    },
    difference: {
      type: Number,
      required: true
    },
    baseUomId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'UOM'
    },
    notes: String
  }],
  isLocked: {
    type: Boolean,
    default: true
  },
  lockedAt: {
    type: Date
  },
  unlockedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Compound indexes
stockCountSchema.index({ companyId: 1, locationId: 1, countDate: -1 });
stockCountSchema.index({ companyId: 1, locationId: 1, status: 1 });

const StockCount = mongoose.models.StockCount || 
  mongoose.model<IStockCount>('StockCount', stockCountSchema);

export default StockCount;
