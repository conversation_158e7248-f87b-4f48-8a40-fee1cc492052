// src/models/InventorySync.ts
import mongoose, { Schema, Types, Document } from 'mongoose';

export interface IInventorySync extends Document {
  companyId: Types.ObjectId;
  locationId: Types.ObjectId;
  syncId: string;  // Unique ID from Ionic app
  syncTimestamp: Date;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'PARTIAL';
  items: Array<{
    itemId: Types.ObjectId;
    itemType: 'RECIPE' | 'INGREDIENT';
    previousStock: number;
    newStock: number;
    transactions: Array<{
      type: 'SALE' | 'WASTAGE' | 'ADJUSTMENT';
      quantity: number;
      menuItemId?: string;
      timestamp: Date;
      reason?: string;
    }>;
    syncStatus: 'SUCCESS' | 'FAILED';
    errorMessage?: string;
  }>;
  completedAt?: Date;
  errorDetails?: string;
}

const inventorySyncSchema = new Schema<IInventorySync>({
  // Schema definition
});

const InventorySync = mongoose.models.InventorySync || 
  mongoose.model<IInventorySync>('InventorySync', inventorySyncSchema);

export default InventorySync;