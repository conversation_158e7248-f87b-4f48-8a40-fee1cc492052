import mongoose from 'mongoose'

const groupSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  companyId: { 
    type: String, 
    required: true,
    index: true
  },
  description: { 
    type: String,
    trim: true 
  },
  isGlobal: { 
    type: Boolean, 
    default: false,
    index: true
  }
}, {
  timestamps: true,
  indexes: [
    { companyId: 1 },
    { companyId: 1, isGlobal: 1 }
  ]
})

export const Group = mongoose.models.Group || mongoose.model('Group', groupSchema)
