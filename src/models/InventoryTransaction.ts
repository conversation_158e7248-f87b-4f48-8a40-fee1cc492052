import mongoose, { Schema, Types, Document } from 'mongoose';

export interface IInventoryTransaction extends Document {
  timestamp: Date;
  metadata: {
    companyId: Types.ObjectId;
    locationId: Types.ObjectId;
    itemId: Types.ObjectId;
    itemType: 'RECIPE' | 'INGREDIENT';
    transactionType: 'PURCHASE' | 'WASTAGE' | 'ADJUSTMENT' | 'PRODUCTION_INPUT' | 
                     'PRODUCTION_OUTPUT' | 'TRANSFER_OUT' | 'TRANSFER_IN' | 'COUNT' |
                     'SALE' | 'RECEIVED' | 'DISPATCHED' | 'THEFT';
    referenceId?: Types.ObjectId;
    referenceType?: 'STOCK_COUNT' | 'PURCHASE_ORDER' | 'PRODUCTION' | 'TRANSFER' | 
                    'ADJUSTMENT' | 'ORDER' | 'DELIVERY_NOTE' | 'WASTAGE_REPORT';
    userId: Types.ObjectId;
  };
  // Common transaction fields
  previousStock: number;
  newStock: number;
  difference: number;
  previousPendingStock?: number;
  newPendingStock?: number;
  pendingDifference?: number;
  notes?: string;
  unitId: Types.ObjectId;
  unitName?: string;
  
  // Purchase specific fields
  cost?: number;
  lotNumber?: string;
  expiryDate?: Date;
  supplierName?: string;
  invoiceNumber?: string;
  purchaseOrderRef?: string;
  
  // Wastage specific fields
  reason?: string;
  disposalMethod?: string;
  
  // Production specific fields
  recipeId?: Types.ObjectId;       // Reference to recipe being produced/consumed
  expectedQuantity?: number;       // Expected quantity based on recipe
  variancePercentage?: number;     // Variance between expected and actual
  
  // Transfer specific fields
  sourceLocationId?: Types.ObjectId;
  destinationLocationId?: Types.ObjectId;
  transferReference?: string;
  transferMarkup?: {
    originalCost: number;
    markedUpCost: number;
    markupType: 'PERCENTAGE' | 'FIXED';
    markupValue: number;
    taxIncluded?: boolean;
  };
  
  // Count specific fields
  countBatch?: string;
  countStatus?: 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED' | 'REVERTED';
  systemStockBeforeCount?: number;  // Theoretical stock before count
  approvedBy?: Types.ObjectId;
  approvedAt?: Date;
  revertedBy?: Types.ObjectId;
  revertedAt?: Date;
  revertReason?: string;
  
  // Items array (for COUNT transactions)
  items?: any[];
}

const inventoryTransactionSchema = new Schema<IInventoryTransaction>(
  {
    timestamp: { type: Date, required: true },
    metadata: {
      companyId: { type: Schema.Types.ObjectId, required: true },
      locationId: { type: Schema.Types.ObjectId, required: true },
      itemId: { type: Schema.Types.ObjectId, required: true },
      itemType: {
        type: String,
        required: true,
        enum: ['RECIPE', 'INGREDIENT']
      },
      transactionType: {
        type: String,
        required: true,
        enum: ['PURCHASE', 'WASTAGE', 'ADJUSTMENT', 'PRODUCTION_INPUT', 'PRODUCTION_OUTPUT', 
               'TRANSFER_OUT', 'TRANSFER_IN', 'COUNT', 'SALE', 'RECEIVED', 'DISPATCHED', 'THEFT']
      },
      referenceId: { type: Schema.Types.ObjectId },
      referenceType: {
        type: String,
        enum: ['STOCK_COUNT', 'PURCHASE_ORDER', 'PRODUCTION', 'TRANSFER', 'ADJUSTMENT', 
               'ORDER', 'DELIVERY_NOTE', 'WASTAGE_REPORT']
      },
      userId: { type: Schema.Types.ObjectId, required: true }
    },
    // Common transaction fields
    previousStock: { type: Number, required: true },
    newStock: { type: Number, required: true },
    difference: { type: Number, required: true },
    notes: { type: String },
    unitId: { type: Schema.Types.ObjectId, required: true },
    unitName: { type: String },
    
    // Purchase specific fields
    cost: { type: Number },
    lotNumber: { type: String },
    expiryDate: { type: Date },
    supplierName: { type: String },
    invoiceNumber: { type: String },
    purchaseOrderRef: { type: String },
    
    // Wastage specific fields
    reason: { type: String },
    disposalMethod: { type: String },
    
    // Production specific fields
    recipeId: { type: Schema.Types.ObjectId },
    expectedQuantity: { type: Number },
    variancePercentage: { type: Number },
    
    // Transfer specific fields
    sourceLocationId: { type: Schema.Types.ObjectId },
    destinationLocationId: { type: Schema.Types.ObjectId },
    transferReference: { type: String },
    transferMarkup: {
      originalCost: { type: Number },
      markedUpCost: { type: Number },
      markupType: { type: String, enum: ['PERCENTAGE', 'FIXED'] },
      markupValue: { type: Number },
      taxIncluded: { type: Boolean }
    },
    
    // Count specific fields
    countBatch: { type: String },
    countStatus: {
      type: String,
      enum: ['DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED', 'REVERTED']
    },
    systemStockBeforeCount: { type: Number },
    approvedBy: { type: Schema.Types.ObjectId },
    approvedAt: { type: Date },
    revertedBy: { type: Schema.Types.ObjectId },
    revertedAt: { type: Date },
    revertReason: { type: String },
    items: { type: Array }
  },
  {
    // Disable versioning as this is a timeseries collection
    versionKey: false,
    // Enable _id as we need it for document creation
    _id: true,
    // Add timestamps for better tracking
    timestamps: true
  }
);

// Only initialize the model on the server side
const InventoryTransaction = (mongoose.models.InventoryTransaction ||
  mongoose.model<IInventoryTransaction>('InventoryTransaction', inventoryTransactionSchema, 'inventoryTransactions')) as mongoose.Model<IInventoryTransaction>;

export default InventoryTransaction;
