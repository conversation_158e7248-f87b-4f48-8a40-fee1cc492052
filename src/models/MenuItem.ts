import mongoose, { Schema, Types, Document } from 'mongoose';

interface Price {
  basePrice: number;
  vatRate: number;
  vatAmount: number;
  finalPrice: number;
}

interface InventoryItem {
  itemId: Types.ObjectId;
  unit: string;  // Required for inventory tracking
}

interface RecipeComponent {
  itemId: Types.ObjectId;
  quantity: number;
  unit: string;  // Required for inventory tracking
}

export interface IMenuItem extends Document {
  _id: Types.ObjectId;
  companyId: Types.ObjectId;
  name: string;
  description?: string;
  categoryId: Types.ObjectId;
  type: 'single' | 'recipe';  // Aligned with Ionic app
  prices: Price;
  status: 'active' | 'inactive';
  inventoryItem?: InventoryItem;
  recipeComponents?: RecipeComponent[];
  inventoryStatus: 'pending' | 'linked';
  createdAt: Date;
  updatedAt: Date;
}

const MenuItemSchema = new Schema<IMenuItem>({
  companyId: { 
    type: Schema.Types.ObjectId, 
    required: true,
    index: true 
  },
  name: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String 
  },
  categoryId: { 
    type: Schema.Types.ObjectId, 
    ref: 'MenuCategory',
    required: true 
  },
  type: { 
    type: String, 
    enum: ['single', 'recipe'],  // Aligned with Ionic app
    required: true 
  },
  prices: {
    basePrice: { type: Number, required: true },
    vatRate: { type: Number, required: true },
    vatAmount: { type: Number, required: true },
    finalPrice: { type: Number, required: true }
  },
  status: { 
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  inventoryItem: {
    itemId: { 
      type: Schema.Types.ObjectId,
      ref: 'BranchInventory'
    },
    unit: { type: String, required: true }  // Required for inventory tracking
  },
  recipeComponents: [{
    itemId: { 
      type: Schema.Types.ObjectId,
      ref: 'BranchInventory'
    },
    quantity: { type: Number, required: true },
    unit: { type: String, required: true }  // Required for inventory tracking
  }],
  inventoryStatus: {
    type: String,
    enum: ['pending', 'linked'],
    default: 'pending'
  }
}, {
  timestamps: true,
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Update inventoryStatus when inventory links change
MenuItemSchema.pre('save', function(next) {
  if (this.type === 'single') {
    this.inventoryStatus = this.inventoryItem?.itemId ? 'linked' : 'pending';
    // Clear recipe components for single items
    this.recipeComponents = undefined;
  } else if (this.type === 'recipe') {
    this.inventoryStatus = (this.recipeComponents && this.recipeComponents.length > 0) ? 'linked' : 'pending';
    // Clear inventory item for recipe items
    this.inventoryItem = undefined;
  }
  next();
});

// Compound index for company and name uniqueness
MenuItemSchema.index({ companyId: 1, name: 1 }, { unique: true });

export const MenuItem = mongoose.models.MenuItem || mongoose.model<IMenuItem>('MenuItem', MenuItemSchema);
