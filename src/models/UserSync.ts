// src/models/UserSync.ts
import mongoose, { Schema, Document } from 'mongoose';

export interface IUserSync extends Document {
  companyId: mongoose.Types.ObjectId;
  locationId?: mongoose.Types.ObjectId;
  syncId: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'PARTIAL' | 'ERROR';
  startTime: Date;
  endTime?: Date;
  usersProcessed: number;
  usersReceived: number;
  syncErrors: Array<{
    userId: string;
    error: string;
  }>;
}

const UserSyncSchema = new Schema<IUserSync>({
  companyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Company', required: true },
  locationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Location' },
  syncId: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'PARTIAL', 'ERROR'],
    default: 'PENDING'
  },
  startTime: { type: Date, default: Date.now },
  endTime: { type: Date },
  usersProcessed: { type: Number, default: 0 },
  usersReceived: { type: Number, default: 0 },
  syncErrors: [{
    userId: { type: String },
    error: { type: String }
  }]
}, {
  timestamps: true
});

// Add indexes for efficient queries
UserSyncSchema.index({ companyId: 1, syncId: 1 });
UserSyncSchema.index({ companyId: 1, startTime: -1 });
UserSyncSchema.index({ status: 1 });

const UserSync = mongoose.models.UserSync || mongoose.model<IUserSync>('UserSync', UserSyncSchema);

export default UserSync;