import mongoose, { Schema, Document, Types } from 'mongoose';
import crypto from 'crypto';

export interface ITestApiKey extends Document {
  key: string;
  userId: Types.ObjectId;
  companyId: Types.ObjectId;
  role: string;
  description: string;
  createdAt: Date;
  expiresAt: Date;
}

const TestApiKeySchema = new Schema<ITestApiKey>({
  key: { type: String, required: true, unique: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  role: { type: String, required: true },
  description: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, required: true }
});

// Generate a secure random key
TestApiKeySchema.statics.generateKey = function() {
  return crypto.randomBytes(32).toString('hex');
};

const TestApiKey = mongoose.models.TestApiKey || mongoose.model<ITestApiKey>('TestApiKey', TestApiKeySchema);
export default TestApiKey;
