import { useState, useEffect } from 'react';
import { UOM } from '@/utils/uomConversion';

export const useUOMs = (companyId: string) => {
  const [uoms, setUOMs] = useState<UOM[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUOMs = async () => {
      try {
        const response = await fetch('/api/uoms', {
          headers: {
            'company-id': companyId
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch UOMs');
        }

        const data = await response.json();
        setUOMs(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch UOMs');
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchUOMs();
    }
  }, [companyId]);

  const getBaseUOM = (baseType: string, system: string): UOM | undefined => {
    return uoms.find(uom => 
      uom.baseType === baseType && 
      uom.system === system && 
      uom.factorToCanonical === 1
    );
  };

  const getDisplayUOM = (baseType: string, system: string): UOM | undefined => {
    return uoms.find(uom => 
      uom.baseType === baseType && 
      uom.system === system && 
      uom.factorToCanonical < 1
    );
  };

  const getCompatibleUOMs = (baseType: string, system: string): UOM[] => {
    return uoms.filter(uom => 
      uom.baseType === baseType && 
      uom.system === system
    );
  };

  return {
    uoms,
    loading,
    error,
    getBaseUOM,
    getDisplayUOM,
    getCompatibleUOMs
  };
};
