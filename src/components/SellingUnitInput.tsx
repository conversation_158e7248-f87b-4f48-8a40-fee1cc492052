'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UOM } from '@/utils/uomConversion';

interface SellingUnitInputProps {
  id: string;
  value: string;
  onChange: (value: string) => void;
  uoms: UOM[];
  showLabel?: boolean;
}

export function SellingUnitInput({
  id,
  value,
  onChange,
  uoms,
  showLabel = false,
}: SellingUnitInputProps) {
  return (
    <div className="space-y-2">
      {showLabel && <Label>Unit of Selling</Label>}
      <Select
        value={value}
        onValueChange={onChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select unit" />
        </SelectTrigger>
        <SelectContent>
          {uoms.map((uom) => (
            <SelectItem key={uom._id.toString()} value={uom._id.toString()}>
              {uom.name} ({uom.shortCode})
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
