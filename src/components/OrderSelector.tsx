'use client';

import React, { useState, useMemo } from 'react';
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

interface OrderSelectorProps {
  companyId: string;
  value: string;
  onSelect: (orderId: string) => void;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: string;
  buyer?: {
    name: string;
  };
}

interface OrdersResponse {
  orders: Order[];
}

export function OrderSelector({ companyId, value, onSelect }: OrderSelectorProps) {
  console.log('OrderSelector render:', { value, companyId });

  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");

  // Fetch orders
  const { data: ordersData, isLoading: isLoadingOrders } = useQuery<OrdersResponse>({
    queryKey: ['orders', companyId],
    queryFn: async () => {
      const response = await axios.get('/api/orders', {
        headers: { 'company-id': companyId }
      });
      console.log('Orders fetched:', response.data);
      return response.data;
    }
  });

  // Find selected order
  const selectedOrder = useMemo(() => {
    if (!ordersData?.orders) return null;
    console.log('Finding selected order for value:', value);
    return ordersData.orders.find((order: Order) => order._id === value);
  }, [ordersData, value]);

  console.log('Selected order:', selectedOrder);

  // Filter orders based on search
  const filteredOrders = useMemo(() => {
    if (!ordersData?.orders) return [];
    return ordersData.orders.filter((order: Order) => 
      order.orderNumber.toLowerCase().includes(search.toLowerCase()) ||
      order.buyer?.name?.toLowerCase().includes(search.toLowerCase())
    );
  }, [ordersData, search]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedOrder ? (
            <span>{selectedOrder.orderNumber}</span>
          ) : (
            <span className="text-muted-foreground">Select an order...</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput 
            placeholder="Search orders..." 
            value={search}
            onValueChange={setSearch}
          />
          <CommandEmpty>No orders found.</CommandEmpty>
          <CommandGroup>
            {isLoadingOrders ? (
              <CommandItem disabled>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </CommandItem>
            ) : (
              filteredOrders.map((order: Order) => (
                <CommandItem
                  key={order._id}
                  onSelect={() => {
                    console.log('Order selected:', order);
                    onSelect(order._id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === order._id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex flex-col">
                    <span>{order.orderNumber}</span>
                    <span className="text-sm text-muted-foreground">
                      {order.buyer?.name || 'No buyer'}
                    </span>
                  </div>
                </CommandItem>
              ))
            )}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
