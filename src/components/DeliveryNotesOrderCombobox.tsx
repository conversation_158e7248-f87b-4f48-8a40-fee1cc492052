'use client';

import React from 'react';
import { Check, ChevronsUpDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useRequireCompanyUser } from '@/lib/auth';

interface OrderOption {
  id: string;
  orderNumber: string;
  buyerName: string;
}

interface DeliveryNotesOrderComboboxProps {
  value?: string;
  placeholder?: string;
  onChange: (orderId: string) => void;
  companyId: string;
}

export function DeliveryNotesOrderCombobox({
  value,
  placeholder = 'Select order...',
  onChange,
  companyId,
}: DeliveryNotesOrderComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [options, setOptions] = React.useState<OrderOption[]>([]);
  const [loading, setLoading] = React.useState(false);
  const { userData } = useRequireCompanyUser();

  // Fetch initial order if value is provided
  React.useEffect(() => {
    const fetchInitialOrder = async () => {
      if (!value || options.some(opt => opt.id === value)) return;

      setLoading(true);
      try {
        const response = await fetch(`/api/orders/${value}`, {
          headers: { 'company-id': companyId }
        });
        const order = await response.json();
        if (order) {
          setOptions(prev => {
            // Add the order to options if it's not already there
            if (!prev.some(opt => opt.id === order._id)) {
              return [...prev, {
                id: order._id,
                orderNumber: order.orderNumber,
                buyerName: order.buyer?.name || 'Unknown Buyer'
              }];
            }
            return prev;
          });
        }
      } catch (error) {
        console.error('Error fetching initial order:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialOrder();
  }, [value, companyId, options]);

  // Handle search
  React.useEffect(() => {
    const searchOrders = async () => {
      if (!userData?.companyId) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(
          `/api/orders${searchQuery ? `?${new URLSearchParams({
            search: searchQuery
          })}` : ''}`,
          {
            headers: {
              'company-id': companyId
            }
          }
        );
        const data = await response.json();
        console.log('Orders search results:', data);
        
        if (data.orders) {
          const orderOptions = data.orders.map((order: any) => ({
            id: order._id,
            orderNumber: order.orderNumber,
            buyerName: order.buyer?.name || 'Unknown Buyer'
          }));
          setOptions(orderOptions);
        }
      } catch (error) {
        console.error('Error searching orders:', error);
      } finally {
        setLoading(false);
      }
    };

    // Debounce search for typed queries
    const timer = setTimeout(() => {
      if (searchQuery) {
        searchOrders();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, userData?.companyId, companyId]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {value ? options.find((option) => option.id === value)?.orderNumber || 'Loading...' : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <div className="flex items-center border-b px-3">
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          <Input
            placeholder="Search orders..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              if (!open) setOpen(true);
            }}
            className="h-9 border-0 focus-visible:ring-0"
          />
        </div>
        <ScrollArea className="max-h-[300px] overflow-auto">
          {loading ? (
            <div className="p-4 text-sm text-muted-foreground text-center">
              Loading...
            </div>
          ) : options.length === 0 ? (
            <div className="p-4 text-sm text-muted-foreground text-center">
              {searchQuery ? "No orders found." : "Type to search orders"}
            </div>
          ) : (
            <div className="p-1">
              {options.map((option) => (
                <div
                  key={option.id}
                  onClick={() => {
                    onChange(option.id);
                    setOpen(false);
                    setSearchQuery('');
                  }}
                  className={cn(
                    "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                    value === option.id && "bg-accent text-accent-foreground"
                  )}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex flex-col">
                    <span>{option.orderNumber}</span>
                    <span className="text-xs text-muted-foreground">
                      {option.buyerName}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
