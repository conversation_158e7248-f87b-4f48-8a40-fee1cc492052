import React from 'react';
import { Check, ChevronsUpDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useRequireCompanyUser } from '@/lib/auth';
import { toast } from 'sonner';

interface OrderItemOption {
  id: string;
  name: string;
  type: 'ingredient' | 'recipe';
  isSubRecipe?: boolean;
  baseUomId?: string;
  baseUomName?: string;
  baseUomShortCode?: string;
}

interface OrderItemComboboxProps {
  value?: string;
  placeholder?: string;
  onChange: (value: { 
    ingredientName: string; 
    baseUomId: string; 
    baseUomShortCode: string;
    isSubRecipe: boolean;
    itemId: string;
  }) => void;
  companyId: string;
}

export function OrderItemCombobox({
  value,
  placeholder = 'Select item...',
  onChange,
  companyId,
}: OrderItemComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [options, setOptions] = React.useState<OrderItemOption[]>([]);
  const [loading, setLoading] = React.useState(false);
  const { userData } = useRequireCompanyUser();

  React.useEffect(() => {
    const searchItems = async () => {
      if (!searchQuery || !userData?.companyId) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(
          `/api/company/${companyId}/search?${new URLSearchParams({
            query: searchQuery
          })}`,
          {
            headers: {
              'company-id': companyId
            }
          }
        );

        if (!response.ok) {
          console.error('Failed to search:', await response.text());
          setOptions([]);
          return;
        }

        const data = await response.json();
        console.log('Raw search results:', data);
        
        // Transform flat array into OrderItemOption[]
        const combinedResults = data.map((item: any) => ({
          id: item.id,
          name: item.name,
          type: item.type as 'ingredient' | 'recipe',
          isSubRecipe: item.type === 'recipe',
          baseUomId: item.baseUomId,
          baseUomName: item.baseUomName,
          baseUomShortCode: item.baseUomShortCode
        })).sort((a: any, b: any) => a.name.localeCompare(b.name));

        console.log('Transformed results:', combinedResults);
        setOptions(combinedResults);
      } catch (error) {
        console.error('Error searching:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimeout = setTimeout(searchItems, 300);
    return () => clearTimeout(debounceTimeout);
  }, [searchQuery, companyId, userData?.companyId]);

  const handleSelect = async (option: OrderItemOption) => {
    try {
      onChange({
        ingredientName: option.name,
        baseUomId: option.baseUomId || '',
        baseUomShortCode: option.baseUomShortCode || '',
        isSubRecipe: option.type === 'recipe',
        itemId: option.id
      });
    } catch (error) {
      console.error('Error fetching item details:', error);
      toast.error('Failed to get item details');
    }
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {value || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="start">
        <div className="flex items-center border-b px-3">
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          <Input
            placeholder="Search items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="border-0 focus:ring-0"
          />
        </div>
        <ScrollArea className="h-80">
          {loading ? (
            <div className="flex items-center justify-center py-6">
              Loading...
            </div>
          ) : options.length === 0 ? (
            <div className="flex items-center justify-center py-6">
              No results found
            </div>
          ) : (
            options.map((option) => (
              <Button
                key={option.id}
                onClick={() => handleSelect(option)}
                variant="ghost"
                className={cn(
                  'w-full justify-start gap-2',
                  value === option.name && 'bg-accent'
                )}
              >
                <Check
                  className={cn(
                    'h-4 w-4',
                    value === option.name ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <div className="flex-1 text-left">
                  <div>{option.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {option.type} {option.isSubRecipe ? '(sub-recipe)' : ''}
                  </div>
                </div>
              </Button>
            ))
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
