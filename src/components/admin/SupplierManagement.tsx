// src/components/admin/SupplierManagement.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Plus, Pencil, Trash } from "lucide-react";
import { toast } from "sonner";

interface Supplier {
  _id: string;
  name: string;
  taxNumber?: string;
  contactInfo?: {
    address?: string;
    phone?: string;
    email?: string;
  };
  paymentTerms?: string;
  status: string;
  type: string;
  locationId?: string | null;
}

interface Location {
  _id: string;
  name: string;
  locationType: 'CENTRAL_KITCHEN' | 'RETAIL_SHOP' | 'SINGLE_LOCATION';
}

interface SupplierFormData {
  name: string;
  taxNumber?: string;
  contactInfo?: {
    address?: string;
    phone?: string;
    email?: string;
  };
  paymentTerms?: string;
  status: string;
  type: string;
  locationId?: string | null;
}

interface SupplierSettings {
  requiredFields: {
    taxNumber: boolean;
    address: boolean;
    phone: boolean;
    email: boolean;
    paymentTerms: boolean;
  };
}

const initialFormData: SupplierFormData = {
  name: '',
  taxNumber: '',
  contactInfo: {
    address: '',
    phone: '',
    email: '',
  },
  paymentTerms: '',
  status: 'active',
  type: 'external',
  locationId: null
};

const SupplierManagement = () => {
  const { userData } = useRequireCompanyUser();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<SupplierFormData>(initialFormData);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedSuppliers, setSelectedSuppliers] = useState<string[]>([]);
  const [settings, setSettings] = useState<SupplierSettings>({
    requiredFields: {
      taxNumber: false,
      address: false,
      phone: false,
      email: false,
      paymentTerms: false,
    }
  });

  useEffect(() => {
    if (userData?.companyId) {
      fetchSuppliers();
      fetchSettings();
      fetchLocations();
    }
  }, [userData?.companyId, fetchSuppliers, fetchSettings, fetchLocations]);

  const fetchSuppliers = useCallback(async () => {
    try {
      const response = await fetch('/api/suppliers', {
        headers: {
          'company-id': userData?.companyId || '',
        },
      });
      if (!response.ok) throw new Error('Failed to fetch suppliers');
      const data = await response.json();
      setSuppliers(data);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      toast.error('Failed to load suppliers');
    } finally {
      setLoading(false);
    }
  }, [userData?.companyId, setSuppliers, setLoading]);

  const fetchSettings = useCallback(async () => {
    try {
      const response = await fetch('/api/suppliers/settings', {
        headers: {
          'company-id': userData?.companyId || '',
        }
      });
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error fetching supplier settings:', error);
      toast.error('Failed to load supplier settings');
    }
  }, [userData?.companyId, setSettings]);

  const fetchLocations = useCallback(async () => {
    try {
      const response = await fetch('/api/locations', {
        headers: {
          'company-id': userData?.companyId || '',
        },
      });
      if (!response.ok) throw new Error('Failed to fetch locations');
      const data = await response.json();
      setLocations(data);
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast.error('Failed to load locations');
    }
  }, [userData?.companyId, setLocations]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch(`/api/suppliers${editingId ? `/${editingId}` : ''}`, {
        method: editingId ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData?.companyId || '',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to save supplier');

      toast.success(`Supplier ${editingId ? 'updated' : 'created'} successfully`);
      setIsDialogOpen(false);
      setFormData(initialFormData);
      setEditingId(null);
      fetchSuppliers();
    } catch (error) {
      console.error('Error saving supplier:', error);
      toast.error('Failed to save supplier');
    }
  };

  const handleEdit = (supplier: Supplier) => {
    setFormData({
      name: supplier.name,
      taxNumber: supplier.taxNumber,
      contactInfo: supplier.contactInfo,
      paymentTerms: supplier.paymentTerms,
      status: supplier.status,
      type: supplier.type,
      locationId: supplier.locationId,
    });
    setEditingId(supplier._id);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this supplier?')) return;

    try {
      const response = await fetch(`/api/suppliers/${id}`, {
        method: 'DELETE',
        headers: {
          'company-id': userData?.companyId || '',
        },
      });

      if (!response.ok) throw new Error('Failed to delete supplier');

      toast.success('Supplier deleted successfully');
      fetchSuppliers();
    } catch (error) {
      console.error('Error deleting supplier:', error);
      toast.error('Failed to delete supplier');
    }
  };

  const handleBulkDelete = async () => {
    if (!selectedSuppliers.length) return;
    if (!confirm(`Are you sure you want to delete ${selectedSuppliers.length} suppliers?`)) return;

    try {
      const response = await fetch('/api/suppliers', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData?.companyId || '',
        },
        body: JSON.stringify({ ids: selectedSuppliers }),
      });

      if (!response.ok) throw new Error('Failed to delete suppliers');

      toast.success('Suppliers deleted successfully');
      setSelectedSuppliers([]);
      fetchSuppliers();
    } catch (error) {
      console.error('Error deleting suppliers:', error);
      toast.error('Failed to delete suppliers');
    }
  };

  const toggleSelectSupplier = (id: string) => {
    setSelectedSuppliers(prev => 
      prev.includes(id) 
        ? prev.filter(supplierId => supplierId !== id)
        : [...prev, id]
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Suppliers</h2>
        <div className="space-x-2">
          {selectedSuppliers.length > 0 && (
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              className="gap-2"
            >
              <Trash className="h-4 w-4" />
              Delete Selected ({selectedSuppliers.length})
            </Button>
          )}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => {
                  setFormData(initialFormData);
                  setEditingId(null);
                }}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Supplier
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingId ? 'Edit Supplier' : 'Add New Supplier'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxNumber">
                    Tax Number {settings.requiredFields.taxNumber && '*'}
                  </Label>
                  <Input
                    id="taxNumber"
                    value={formData.taxNumber}
                    onChange={(e) =>
                      setFormData({ ...formData, taxNumber: e.target.value })
                    }
                    required={settings.requiredFields.taxNumber}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">
                    Address {settings.requiredFields.address && '*'}
                  </Label>
                  <Input
                    id="address"
                    value={formData.contactInfo?.address}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        contactInfo: {
                          ...formData.contactInfo,
                          address: e.target.value,
                        },
                      })
                    }
                    required={settings.requiredFields.address}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">
                    Phone {settings.requiredFields.phone && '*'}
                  </Label>
                  <Input
                    id="phone"
                    value={formData.contactInfo?.phone}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        contactInfo: {
                          ...formData.contactInfo,
                          phone: e.target.value,
                        },
                      })
                    }
                    required={settings.requiredFields.phone}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">
                    Email {settings.requiredFields.email && '*'}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.contactInfo?.email}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        contactInfo: {
                          ...formData.contactInfo,
                          email: e.target.value,
                        },
                      })
                    }
                    required={settings.requiredFields.email}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="paymentTerms">
                    Payment Terms {settings.requiredFields.paymentTerms && '*'}
                  </Label>
                  <Input
                    id="paymentTerms"
                    value={formData.paymentTerms}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        paymentTerms: e.target.value,
                      })
                    }
                    required={settings.requiredFields.paymentTerms}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      className="w-full border rounded-md p-2"
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <select
                      id="type"
                      className="w-full border rounded-md p-2"
                      value={formData.type}
                      onChange={(e) => {
                        const newType = e.target.value;
                        setFormData({
                          ...formData,
                          type: newType,
                          locationId: newType === 'external' ? null : formData.locationId
                        });
                      }}
                    >
                      <option value="external">External</option>
                      <option value="internal">Internal</option>
                    </select>
                  </div>
                </div>

                {formData.type === 'internal' && (
                  <div className="space-y-2">
                    <Label htmlFor="locationId">Location</Label>
                    <select
                      id="locationId"
                      className="w-full border rounded-md p-2"
                      value={formData.locationId || ''}
                      onChange={(e) => setFormData({ ...formData, locationId: e.target.value })}
                      required={formData.type === 'internal'}
                    >
                      <option value="">Select a location</option>
                      {locations.map((location) => (
                        <option key={location._id} value={location._id}>
                          {location.name} ({location.locationType})
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingId ? 'Update' : 'Create'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={selectedSuppliers.length === suppliers.length}
                  onChange={(e) =>
                    setSelectedSuppliers(
                      e.target.checked ? suppliers.map((s) => s._id) : []
                    )
                  }
                  className="h-4 w-4"
                />
              </TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Location</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {suppliers.map((supplier) => (
              <TableRow key={supplier._id}>
                <TableCell>
                  <input
                    type="checkbox"
                    checked={selectedSuppliers.includes(supplier._id)}
                    onChange={() => toggleSelectSupplier(supplier._id)}
                    className="h-4 w-4"
                  />
                </TableCell>
                <TableCell>{supplier.name}</TableCell>
                <TableCell>{supplier.type}</TableCell>
                <TableCell>{supplier.status}</TableCell>
                <TableCell>
                  {supplier.type === 'internal' && 
                    locations.find(l => l._id === supplier.locationId)?.name
                  }
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEdit(supplier)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDelete(supplier._id)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {suppliers.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No suppliers found. Add your first supplier to get started.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export { SupplierManagement };
