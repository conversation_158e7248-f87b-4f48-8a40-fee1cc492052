// src/components/admin/PermissionManagement.tsx
import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { 
  Card, 
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription 
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Plus } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface Permission {
  _id: string;
  name: string;
  description: string;
  category: string;
  isSystemLevel: boolean;
}

const PERMISSION_CATEGORIES = [
  { value: 'hq:general', label: 'HQ General', description: 'General administrative permissions for the main app' },
  { value: 'hq:inventory', label: 'HQ Inventory', description: 'Central inventory management in the main app' },
  { value: 'hq:finance', label: 'HQ Finance', description: 'Financial operations in the main app' },
  { value: 'hq:reports', label: 'HQ Reports', description: 'Reporting features in the main app' },
  { value: 'branch:general', label: 'Branch General', description: 'General branch operations in the Ionic POS app' },
  { value: 'branch:sales', label: 'Branch Sales', description: 'Sales operations in the Ionic POS app' },
  { value: 'branch:inventory', label: 'Branch Inventory', description: 'Branch inventory management in the Ionic POS app' },
  { value: 'storekeeper', label: 'Storekeeper', description: 'For central kitchen/HQ inventory operations in the Ionic app' },
];

export function PermissionManagement({ companyId }: { companyId: string }) {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newPermission, setNewPermission] = useState({
    name: '',
    description: '',
    category: '',
    isSystemLevel: false
  });

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/permissions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch permissions');
        return;
      }

      const data = await response.json();
      setPermissions(data);
    } catch (err) {
      console.error('Error fetching permissions:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePermission = async () => {
    try {
      if (!newPermission.name || !newPermission.description || !newPermission.category) {
        setError('All fields are required');
        return;
      }

      const response = await fetch('/api/permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify(newPermission),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create permission');
        return;
      }

      // Reset form and close dialog
      setNewPermission({
        name: '',
        description: '',
        category: '',
        isSystemLevel: false
      });
      setShowCreateDialog(false);
      
      // Refresh permissions list
      fetchPermissions();
    } catch (err) {
      console.error('Error creating permission:', err);
      setError('An unexpected error occurred');
    }
  };

  const handleDeletePermission = async (permissionId: string) => {
    try {
      const response = await fetch('/api/permissions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify({ permissionId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete permission');
        return;
      }

      // Refresh permissions list
      fetchPermissions();
    } catch (err) {
      console.error('Error deleting permission:', err);
      setError('An unexpected error occurred');
    }
  };

  // Group permissions by category
  const groupedPermissions = permissions.reduce((groups, permission) => {
    const category = permission.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);

  if (loading) {
    return (
      <div className="w-full flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Permission Management</h2>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button variant="default">
              <Plus className="h-4 w-4 mr-2" /> Add Permission
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Permission</DialogTitle>
              <DialogDescription>
                Add a new permission for your company. System-level permissions can only be created by administrators.
              </DialogDescription>
            </DialogHeader>
            
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Permission Name</Label>
                <Input
                  id="name"
                  placeholder="e.g., view:inventory"
                  value={newPermission.name}
                  onChange={(e) => setNewPermission({ ...newPermission, name: e.target.value })}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  placeholder="Describe what this permission allows"
                  value={newPermission.description}
                  onChange={(e) => setNewPermission({ ...newPermission, description: e.target.value })}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={newPermission.category}
                  onValueChange={(value) => setNewPermission({ ...newPermission, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {PERMISSION_CATEGORIES.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="isSystemLevel" 
                  checked={newPermission.isSystemLevel}
                  onCheckedChange={(checked) => 
                    setNewPermission({ ...newPermission, isSystemLevel: checked as boolean })
                  }
                />
                <Label htmlFor="isSystemLevel">System-level permission</Label>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>Cancel</Button>
              <Button onClick={handleCreatePermission}>Create</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <Alert variant="destructive" className="my-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* HQ Permissions Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">HQ / Main Application Permissions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(groupedPermissions)
            .filter(([category]) => category.startsWith('hq:'))
            .map(([category, perms]) => {
              const categoryInfo = PERMISSION_CATEGORIES.find(c => c.value === category);
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle>{categoryInfo?.label || category}</CardTitle>
                    <CardDescription>{categoryInfo?.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {perms.map(permission => (
                        <div key={permission._id} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                          <div>
                            <p className="text-sm font-medium">{permission.name}</p>
                            <p className="text-xs text-gray-500">{permission.description}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {permission.isSystemLevel && (
                              <Badge variant="secondary">System</Badge>
                            )}
                            {!permission.isSystemLevel && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDeletePermission(permission._id)}
                              >
                                Delete
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                      {perms.length === 0 && (
                        <p className="text-sm text-gray-500">No permissions in this category</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
        </div>
      </div>

      {/* Branch Permissions Section */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">Branch / Ionic POS App Permissions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(groupedPermissions)
            .filter(([category]) => category.startsWith('branch:') || category === 'storekeeper')
            .map(([category, perms]) => {
              const categoryInfo = PERMISSION_CATEGORIES.find(c => c.value === category);
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle>{categoryInfo?.label || category}</CardTitle>
                    <CardDescription>{categoryInfo?.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {perms.map(permission => (
                        <div key={permission._id} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                          <div>
                            <p className="text-sm font-medium">{permission.name}</p>
                            <p className="text-xs text-gray-500">{permission.description}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {permission.isSystemLevel && (
                              <Badge variant="secondary">System</Badge>
                            )}
                            {!permission.isSystemLevel && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDeletePermission(permission._id)}
                              >
                                Delete
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                      {perms.length === 0 && (
                        <p className="text-sm text-gray-500">No permissions in this category</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
        </div>
      </div>

      {/* Other/Uncategorized Permissions */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">Other Permissions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(groupedPermissions)
            .filter(([category]) => !category.startsWith('hq:') && !category.startsWith('branch:') && category !== 'storekeeper')
            .map(([category, perms]) => {
              const categoryInfo = PERMISSION_CATEGORIES.find(c => c.value === category);
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle>{categoryInfo?.label || category}</CardTitle>
                    <CardDescription>{categoryInfo?.description || 'Miscellaneous permissions'}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {perms.map(permission => (
                        <div key={permission._id} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                          <div>
                            <p className="text-sm font-medium">{permission.name}</p>
                            <p className="text-xs text-gray-500">{permission.description}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {permission.isSystemLevel && (
                              <Badge variant="secondary">System</Badge>
                            )}
                            {!permission.isSystemLevel && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDeletePermission(permission._id)}
                              >
                                Delete
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                      {perms.length === 0 && (
                        <p className="text-sm text-gray-500">No permissions in this category</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
        </div>
      </div>
    </div>
  );
}
