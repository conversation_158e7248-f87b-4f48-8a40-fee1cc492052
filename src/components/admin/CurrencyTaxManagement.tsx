// src/components/admin/CurrencyTaxManagement.tsx
import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useRouter } from 'next/navigation';

interface CurrencyTaxSettings {
  _id?: string;
  baseCurrency: string;
  vatOnSales: boolean;
  vatOnPurchases: boolean;
  vatRateOnSales: number;
  vatRateOnPurchases: number;
  defaultVatCategory: 'STANDARD' | 'REDUCED' | 'ZERO' | 'EXEMPT';
}

interface CurrencyTaxManagementProps {
  companyId: string;
}

export function CurrencyTaxManagement({ companyId }: CurrencyTaxManagementProps) {
  const { userData, loading } = useRequireCompanyUser('admin');
  const router = useRouter();

  const [settings, setSettings] = useState<CurrencyTaxSettings>({
    baseCurrency: '',
    vatOnSales: false,
    vatOnPurchases: false,
    vatRateOnSales: 20,
    vatRateOnPurchases: 20,
    defaultVatCategory: 'STANDARD'
  });
  const [loadingSettings, setLoadingSettings] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (!loading && !userData) {
      router.replace('/login');
    }
  }, [userData, loading, router]);

  useEffect(() => {
    if (!loading && userData) {
      fetchSettings();
    }
  }, [userData, loading, fetchSettings]);

  const fetchSettings = async () => {
    try {
      setLoadingSettings(true);
      const response = await fetch('/api/currency-tax', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch settings');
        return;
      }

      const data = await response.json();
      setSettings(data || {
        baseCurrency: '',
        vatOnSales: false,
        vatOnPurchases: false,
        vatRateOnSales: 20,
        vatRateOnPurchases: 20,
        defaultVatCategory: 'STANDARD'
      });
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoadingSettings(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!settings.baseCurrency) {
      setError('Base currency is required');
      return;
    }

    try {
      console.log('Submitting settings:', settings); // Debug log
      const response = await fetch('/api/currency-tax', {
        method: settings._id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData); // Debug log
        setError(errorData.error || 'Failed to save settings');
        return;
      }

      const data = await response.json();
      console.log('Saved settings:', data); // Debug log
      setSettings(data);
      setSuccess('Settings saved successfully');
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('An unexpected error occurred');
    }
  };

  const handleVATRateChange = (type: 'sales' | 'purchases', value: number) => {
    console.log(`Updating ${type} VAT rate to:`, value); // Debug log
    setSettings(prev => ({
      ...prev,
      [type === 'sales' ? 'vatRateOnSales' : 'vatRateOnPurchases']: value
    }));
  };

  if (loading || loadingSettings) {
    return (
      <div className="w-full flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Currency & Tax Settings</h2>
          <p className="mt-1 text-sm text-gray-500">
            Configure your company&apos;s currency and tax settings
          </p>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <form onSubmit={handleSubmit} className="divide-y divide-gray-200">
          <div className="p-6 space-y-6">
            {error && (
              <div className="p-4 text-red-500 bg-red-50 rounded-md">
                Error: {error}
              </div>
            )}
            {success && (
              <div className="p-4 text-green-500 bg-green-50 rounded-md">
                {success}
              </div>
            )}

            <div className="space-y-6">
              {/* Base Currency */}
              <div>
                <label htmlFor="baseCurrency" className="block text-sm font-medium text-gray-700">
                  Base Currency
                </label>
                <div className="mt-1">
                  <select
                    id="baseCurrency"
                    value={settings.baseCurrency}
                    onChange={(e) => setSettings({ ...settings, baseCurrency: e.target.value })}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">Select a currency</option>
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="RWF">RWF - Rwandan Franc</option>
                  </select>
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Select your company&apos;s base currency
                </p>
              </div>

              {/* VAT Settings */}
              <div className="space-y-4">
                {/* VAT on Sales */}
                <div>
                  <div className="flex items-center justify-between">
                    <div className="relative flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="vatOnSales"
                          type="checkbox"
                          checked={settings.vatOnSales}
                          onChange={(e) => setSettings({ ...settings, vatOnSales: e.target.checked })}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="ml-3">
                        <label htmlFor="vatOnSales" className="font-medium text-gray-700">
                          VAT on Sales
                        </label>
                        <p className="text-sm text-gray-500">Apply VAT to sales transactions</p>
                      </div>
                    </div>
                    {settings.vatOnSales && (
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          id="vatRateOnSales"
                          value={settings.vatRateOnSales}
                          onChange={(e) => handleVATRateChange('sales', Number(e.target.value))}
                          min="0"
                          max="100"
                          step="0.1"
                          className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <span className="text-gray-600">%</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* VAT on Purchases */}
                <div>
                  <div className="flex items-center justify-between">
                    <div className="relative flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="vatOnPurchases"
                          type="checkbox"
                          checked={settings.vatOnPurchases}
                          onChange={(e) => setSettings({ ...settings, vatOnPurchases: e.target.checked })}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </div>
                      <div className="ml-3">
                        <label htmlFor="vatOnPurchases" className="font-medium text-gray-700">
                          VAT on Purchases
                        </label>
                        <p className="text-sm text-gray-500">Apply VAT to purchase transactions</p>
                      </div>
                    </div>
                    {settings.vatOnPurchases && (
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          id="vatRateOnPurchases"
                          value={settings.vatRateOnPurchases}
                          onChange={(e) => handleVATRateChange('purchases', Number(e.target.value))}
                          min="0"
                          max="100"
                          step="0.1"
                          className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <span className="text-gray-600">%</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Default VAT Category */}
              <div>
                <label htmlFor="defaultVatCategory" className="block text-sm font-medium text-gray-700">
                  Default VAT Category
                </label>
                <select
                  id="defaultVatCategory"
                  value={settings.defaultVatCategory}
                  onChange={(e) => setSettings({ ...settings, defaultVatCategory: e.target.value as CurrencyTaxSettings['defaultVatCategory'] })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="STANDARD">Standard Rate</option>
                  <option value="REDUCED">Reduced Rate</option>
                  <option value="ZERO">Zero Rate</option>
                  <option value="EXEMPT">VAT Exempt</option>
                </select>
                <p className="mt-2 text-sm text-gray-500">
                  This will be the default VAT category for new items. You can override this for specific categories or items.
                </p>
              </div>
            </div>
          </div>

          <div className="px-6 py-4 bg-gray-50">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={fetchSettings}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Reset
              </button>
              <button
                type="submit"
                className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Save Settings
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
