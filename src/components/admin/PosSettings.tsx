// src/components/admin/PosSettings.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, CheckCircle2, Smartphone } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

interface PosSettingsProps {
  companyId: string;
}

export function PosSettings({ companyId }: PosSettingsProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [companyCode, setCompanyCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [currentCompanyCode, setCurrentCompanyCode] = useState<string | null>(null);
  const [hasExistingPassword, setHasExistingPassword] = useState(false);

  // Fetch current POS settings
  useEffect(() => {
    const fetchPosSettings = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/admin/company/pos-settings?companyId=${companyId}`, {
          headers: {
            'Content-Type': 'application/json',
            'company-id': companyId
          }
          // No need for credentials:'include' as it's handled by Next.js
        });

        if (!response.ok) {
          if (response.status === 404) {
            // No settings yet, that's okay
            setCurrentCompanyCode(null);
            setHasExistingPassword(false);
          } else {
            throw new Error('Failed to fetch POS settings');
          }
          return;
        }

        const data = await response.json();
        setCurrentCompanyCode(data.companyCode || null);
        setHasExistingPassword(!!data.hasPassword);
        setCompanyCode(data.companyCode || '');
      } catch (err) {
        console.error('Error fetching POS settings:', err);
        setError('Failed to load current POS settings');
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchPosSettings();
    }
  }, [companyId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset states
    setError(null);
    setSuccess(null);
    
    // Form validation
    if (!companyCode.trim()) {
      setError('Company code is required');
      return;
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(companyCode)) {
      setError('Company code must contain only letters, numbers, underscores, and hyphens');
      return;
    }

    // If we're setting or changing the password
    if (!hasExistingPassword || password) {
      if (password.length < 6) {
        setError('Password must be at least 6 characters long');
        return;
      }

      if (password !== confirmPassword) {
        setError('Passwords do not match');
        return;
      }
    }

    try {
      setLoading(true);
      
      const payload: Record<string, string> = {
        companyId,
        companyCode,
      };
      
      // Only include password if it's provided (for updates)
      if (password) {
        payload.posPassword = password;
      }

      const response = await fetch('/api/admin/company/pos-setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId
        },
        // No need for credentials:'include' as it's handled by Next.js
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to update POS settings');
      }

      const data = await response.json();
      setSuccess('POS settings updated successfully');
      setCurrentCompanyCode(data.data.companyCode);
      setHasExistingPassword(true);
      setPassword('');
      setConfirmPassword('');
    } catch (err: any) {
      console.error('Error updating POS settings:', err);
      setError(err.message || 'An error occurred while updating POS settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            <span>POS App Settings</span>
          </CardTitle>
          <CardDescription>
            Configure access credentials for your company&apos;s POS mobile app.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success && (
            <Alert className="mb-4 bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Success</AlertTitle>
              <AlertDescription className="text-green-700">{success}</AlertDescription>
            </Alert>
          )}

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 mb-2">Current Settings</h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm mb-1">
                <span className="font-semibold">Company Code:</span>{' '}
                {currentCompanyCode ? (
                  <span className="text-green-600">{currentCompanyCode}</span>
                ) : (
                  <span className="text-gray-500 italic">Not set</span>
                )}
              </p>
              <p className="text-sm">
                <span className="font-semibold">Password:</span>{' '}
                {hasExistingPassword ? (
                  <span className="text-green-600">••••••••</span>
                ) : (
                  <span className="text-gray-500 italic">Not set</span>
                )}
              </p>
            </div>
          </div>

          <Separator className="my-6" />

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="companyCode" className="text-sm font-medium">
                  Company Code
                </Label>
                <p className="text-xs text-gray-500 mb-1">
                  This is the code your staff will use to identify your company in the POS app.
                </p>
                <Input
                  id="companyCode"
                  value={companyCode}
                  onChange={(e) => setCompanyCode(e.target.value)}
                  placeholder="E.g., acme-foods"
                  className="mt-1"
                  disabled={loading}
                />
              </div>

              <div>
                <Label htmlFor="posPassword" className="text-sm font-medium">
                  Password {hasExistingPassword && <span className="font-normal text-gray-500">(leave blank to keep current password)</span>}
                </Label>
                <p className="text-xs text-gray-500 mb-1">
                  This password will be used along with the company code to access the POS app.
                </p>
                <Input
                  id="posPassword"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={hasExistingPassword ? "••••••••" : "Enter password"}
                  className="mt-1"
                  disabled={loading}
                />
              </div>

              <div>
                <Label htmlFor="confirmPassword" className="text-sm font-medium">
                  Confirm Password
                </Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm password"
                  className="mt-1"
                  disabled={loading || !password}
                />
              </div>
            </div>

            <div className="mt-6">
              <Button 
                type="submit" 
                className="w-full md:w-auto" 
                disabled={loading}
              >
                {loading ? 'Saving...' : hasExistingPassword ? 'Update Settings' : 'Save Settings'}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="bg-gray-50 border-t px-6 py-4">
          <div className="text-xs text-gray-500">
            <p className="mb-1">
              <span className="font-semibold">Note:</span> These credentials will be used by all your staff when accessing the POS mobile app.
            </p>
            <p>
              For security reasons, we recommend using a strong password and changing it periodically.
            </p>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
