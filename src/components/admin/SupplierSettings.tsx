'use client';

import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

interface SupplierSettingsData {
  requiredFields: {
    taxNumber: boolean;
    address: boolean;
    phone: boolean;
    email: boolean;
    paymentTerms: boolean;
  };
}

type RequiredField = keyof SupplierSettingsData['requiredFields'];

const FIELD_LABELS: Record<RequiredField, string> = {
  taxNumber: 'Tax Number',
  address: 'Address',
  phone: 'Phone Number',
  email: 'Email',
  paymentTerms: 'Payment Terms'
};

const FIELD_DESCRIPTIONS: Record<RequiredField, string> = {
  taxNumber: 'Require suppliers to provide their tax identification number',
  address: 'Require suppliers to provide their physical address',
  phone: 'Require suppliers to provide their contact phone number',
  email: 'Require suppliers to provide their email address',
  paymentTerms: 'Require suppliers to specify their payment terms'
};

export function SupplierSettings() {
  const { userData } = useRequireCompanyUser();
  const [settings, setSettings] = useState<SupplierSettingsData>({
    requiredFields: {
      taxNumber: false,
      address: false,
      phone: false,
      email: false,
      paymentTerms: false,
    }
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setError(null);
      const response = await fetch('/api/suppliers/settings', {
        headers: {
          'company-id': userData?.companyId || '',
        }
      });
      if (!response.ok) {
        throw new Error('Failed to load settings');
      }
      const data = await response.json();
      setSettings(data);
    } catch (error) {
      console.error('Error fetching supplier settings:', error);
      setError('Failed to load supplier settings');
      toast.error('Failed to load supplier settings');
    } finally {
      setInitialLoading(false);
    }
  };

  const handleToggle = async (field: RequiredField) => {
    try {
      setLoading(true);
      setError(null);
      const newSettings = {
        ...settings,
        requiredFields: {
          ...settings.requiredFields,
          [field]: !settings.requiredFields[field],
        },
      };

      const response = await fetch('/api/suppliers/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData?.companyId || '',
        },
        body: JSON.stringify(newSettings),
      });

      if (!response.ok) {
        throw new Error('Failed to update settings');
      }

      setSettings(newSettings);
      toast.success('Settings updated successfully');
    } catch (error) {
      console.error('Error updating supplier settings:', error);
      toast.error('Failed to update settings');
      setError('Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (error && !settings) {
    return (
      <Card>
        <CardContent className="py-6">
          <div className="text-center text-destructive">
            <p>{error}</p>
            <Button 
              variant="outline" 
              onClick={fetchSettings} 
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Supplier Form Settings</CardTitle>
        <CardDescription>Configure which fields are required when creating or editing suppliers.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {Object.entries(FIELD_LABELS).map(([field, label]) => (
          <div key={field} className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor={field}>{label}</Label>
              <div className="text-sm text-muted-foreground">
                {FIELD_DESCRIPTIONS[field as RequiredField]}
              </div>
            </div>
            <Switch
              id={field}
              checked={settings.requiredFields[field as RequiredField]}
              onCheckedChange={() => handleToggle(field as RequiredField)}
              disabled={loading}
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
