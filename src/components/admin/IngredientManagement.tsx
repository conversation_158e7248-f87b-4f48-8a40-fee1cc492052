import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRequireCompanyUser } from '@/lib/auth';
import { Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Check, ChevronsUpDown, Eye } from 'lucide-react';
import { ingredientSchema } from '@/schemas/ingredient';
import { ScrollArea } from '../ui/scroll-area';
import { useRouter } from 'next/navigation';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import IngredientSellingOptions from './IngredientSellingOptions';
import { IIngredient, Supplier, UOM } from '@/types/ingredient';
import { SellingOption } from '@/types/selling';
import { Types } from 'mongoose'; // Import Types

// Define the Zod schema type for clarity
type IngredientFormValues = z.infer<typeof ingredientSchema>;

// Placeholder type - needs refinement based on exact API expectations for create/update
// Assumes API expects string IDs for relations and handles population/merging.
// SupplierDetail on backend requires unitsOfOrdering - how is this handled on create/update?
type ApiIngredientPayload = Omit<IngredientFormValues, 'supplierDetails'> & {
  _id?: string; // Optional for update payloads
  supplierDetails: { supplierId: string; unitsOfOrdering?: any[] }[];
  companyId: string;
};

function prepareIngredientForApi(
  formValues: IngredientFormValues,
  companyId: string
): Omit<ApiIngredientPayload, '_id' | 'companyId'> & { companyId: Types.ObjectId } { // Match API expected types
  
  // Map the 'supplierDetails' array from the form 
  // to the 'supplierDetails' structure expected by API
  const supplierDetails = (formValues.supplierDetails || []).map((detail) => ({
    supplierId: detail.supplierId, // Already validated as string by Zod
    unitsOfOrdering: (detail.unitsOfOrdering || []).map(unit => ({
      unitOfMeasure: unit.unitOfMeasure, // Already validated as string by Zod
      quantityInBaseUom: unit.quantityInBaseUom,
      price: unit.price,
      pricePerBaseUom: unit.pricePerBaseUom
      // Ensure all fields expected by the backend UnitOfOrderingSchema are present
    })),
  }));

  // Ensure baseUomId is a valid ObjectId string for the API
  const baseUomId = Types.ObjectId.isValid(formValues.baseUomId) ? formValues.baseUomId : undefined;
  if (!baseUomId) {
    // This should ideally be caught by Zod validation, but double-check
    console.error("Invalid baseUomId in prepareIngredientForApi", formValues.baseUomId);
    // Decide how to handle - throw error? return specific structure?
    // For now, let's omit it, though the API might require it
  }

  return {
    name: formValues.name,
    description: formValues.description || null, // Ensure null if empty
    reorderPoint: formValues.reorderPoint || null,
    baseUomId: baseUomId!, // Send string ID, assert non-null after check
    category: formValues.category,
    defaultSupplierId: formValues.defaultSupplierId,
    supplierDetails: supplierDetails, // Use the mapped structure
    SKU: formValues.SKU,
    canBeSold: Boolean(formValues.canBeSold),
    // Pass sellingDetails through - form state should match SellingOption[]
    sellingDetails: formValues.sellingDetails || [], 
    companyId: new Types.ObjectId(companyId), // API likely expects ObjectId
  };
}

// Helper to normalize potentially populated IIngredient -> flat form values
function normalizeIngredientForForm(
  ingredient: IIngredient | null
): IngredientFormValues {
  if (!ingredient) {
    // Return default values if no ingredient
    return {
      name: '',
      description: '',
      reorderPoint: null,
      baseUomId: '',
      category: '',
      supplierDetails: [], 
      defaultSupplierId: null,
      SKU: '',
      canBeSold: false,
      sellingDetails: [],
    };
  }

  // Handle baseUomId potentially being an object or string
  const baseUomId = typeof ingredient.baseUomId === 'object' && ingredient.baseUomId !== null
    ? ingredient.baseUomId._id.toString()
    : String(ingredient.baseUomId || ''); // Handle null/undefined/string case

  // Extract string IDs for suppliers and their unitsOfOrdering
  const supplierDetails = (ingredient.supplierDetails || []).map(detail => ({
    supplierId: typeof detail.supplierId === 'object' && detail.supplierId?._id
      ? detail.supplierId._id.toString()
      : String(detail.supplierId || ''),
    unitsOfOrdering: (detail.unitsOfOrdering || []).map(unit => ({
      unitOfMeasure: typeof unit.unitOfMeasure === 'object' && unit.unitOfMeasure?._id
        ? unit.unitOfMeasure._id.toString()
        : String(unit.unitOfMeasure || ''),
      quantityInBaseUom: unit.quantityInBaseUom ?? 0,
      price: unit.price ?? 0,
      pricePerBaseUom: unit.pricePerBaseUom ?? 0
    }))
  })).filter((detail: any) => detail.supplierId && Types.ObjectId.isValid(detail.supplierId)); // Filter out invalid/empty supplier IDs

  // Extract string ID for defaultSupplierId
  const defaultSupplierId = ingredient.defaultSupplierId
    ? (typeof ingredient.defaultSupplierId === 'object' && ingredient.defaultSupplierId._id
        ? ingredient.defaultSupplierId._id.toString()
        : String(ingredient.defaultSupplierId))
    : null;

  // Normalize sellingDetails to match SellingOption interface (flat structure)
  const sellingDetails = (ingredient.sellingDetails || []).map((option: any) => ({
      // Ensure ID is a string, generate if missing (though backend should provide)
      // If form needs to manage this, add fields and update logic.
      id: typeof option.id === 'string' ? option.id : (option._id?.toString() || crypto.randomUUID()), 
      // Ensure unitOfSelling is an object { _id: string }
      unitOfSelling: typeof option.unitOfSelling === 'object' && option.unitOfSelling?._id 
        ? { _id: option.unitOfSelling._id.toString() } // Keep as object
        : (typeof option.unitOfSelling === 'string' && Types.ObjectId.isValid(option.unitOfSelling) ? { _id: option.unitOfSelling } : { _id: '' }), // Handle string ID case
      priceWithoutTax: option.priceWithoutTax ?? 0,
      priceWithTax: option.priceWithTax ?? 0,
      taxRate: option.taxRate ?? 0,
      taxCategory: option.taxCategory ?? 'STANDARD',
      conversionFactor: option.conversionFactor ?? 1,
      visibility: {
        type: option.visibility?.type || 'ALL_LOCATIONS',
        // Ensure location IDs are strings
        locations: (option.visibility?.locations || []).map((loc: any) => 
          typeof loc === 'object' && loc?._id ? loc._id.toString() : String(loc || '')
        ).filter((id: string) => id && Types.ObjectId.isValid(id)), // Add type annotation & Ensure valid string IDs
        externalAccess: !!option.visibility?.externalAccess,
      },
      sourceType: option.sourceType || 'MANUAL', // Add default sourceType
  }));


  return {
    name: ingredient.name,
    description: ingredient.description ?? '',
    reorderPoint: ingredient.reorderPoint ?? null,
    baseUomId: baseUomId,
    category: ingredient.category,
    supplierDetails: supplierDetails, // Use the processed supplierDetails
    defaultSupplierId: defaultSupplierId, // Add normalized default supplier ID
    SKU: ingredient.SKU,
    canBeSold: ingredient.canBeSold,
    sellingDetails: sellingDetails, // Use normalized selling details
  };
}

export default function IngredientManagement() {
  const { userData } = useRequireCompanyUser();
  const companyId = userData?.companyId;
  const router = useRouter();

  const [ingredients, setIngredients] = useState<IIngredient[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [uoms, setUoms] = useState<UOM[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [selectedIngredient, setSelectedIngredient] = useState<IIngredient | null>(null);
  const [sellingDetails, setSellingDetails] = useState<SellingOption[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUoms, setIsLoadingUoms] = useState(false);
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const form = useForm<IngredientFormValues>({
    resolver: zodResolver(ingredientSchema),
    defaultValues: {
      name: '',
      description: '',
      reorderPoint: null,
      baseUomId: '',
      category: '',
      supplierDetails: [], // Array of supplier IDs (string)
      defaultSupplierId: null,
      SKU: '',
      canBeSold: false, // Explicitly set default to false
      sellingDetails: [], // SellingOption[]
    },
  });

  // Add useEffect to update form when selectedIngredient changes
  useEffect(() => {
    // Use the helper function to get normalized values
    const normalizedValues = normalizeIngredientForForm(selectedIngredient);
    form.reset(normalizedValues);
    // Update local sellingDetails state if needed (e.g., for IngredientSellingOptions)
    // setSellingDetails(normalizedValues.sellingDetails);
  }, [selectedIngredient, form.reset]);

  const fetchIngredients = async () => {
    try {
      setIsLoading(true);
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: '50',
      });

      if (searchTerm) searchParams.append('search', searchTerm);
      if (selectedCategory && selectedCategory !== 'all') {
        searchParams.append('category', selectedCategory);
      }

      const response = await fetch(`/api/ingredients?${searchParams}`, {
        headers: {
          'company-id': companyId as string,
        },
      });

      if (!response.ok) throw new Error('Failed to fetch ingredients');

      const data = await response.json();
      setIngredients(data.ingredients);
      setCategories(data.categories);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      toast.error('Failed to fetch ingredients');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUoms = async () => {
    try {
      setIsLoadingUoms(true);
      const response = await fetch('/api/uoms', {
        headers: {
          'company-id': companyId as string,
        },
      });
      if (!response.ok) throw new Error('Failed to fetch UOMs');
      const data = await response.json();
      setUoms(data); 
    } catch (error) {
      toast.error('Failed to fetch UOMs');
      setUoms([]);
    } finally {
      setIsLoadingUoms(false);
    }
  };

  const fetchSuppliers = async () => {
    try {
      setIsLoadingSuppliers(true);
      const response = await fetch('/api/suppliers', {
        headers: {
          'company-id': companyId as string
        },
      });
      if (!response.ok) throw new Error('Failed to fetch suppliers');
      const data = await response.json();
      setSuppliers(data);
    } catch (error) {
      toast.error('Failed to fetch suppliers');
      setSuppliers([]);
    } finally {
      setIsLoadingSuppliers(false);
    }
  };

  useEffect(() => {
    if (companyId) {
      fetchIngredients();
      fetchUoms();
      fetchSuppliers();
    }
  }, [companyId, page, searchTerm, selectedCategory]);

  const onSubmit = async (values: IngredientFormValues) => {
    try {
      const endpoint = selectedIngredient 
        ? `/api/ingredients/${selectedIngredient._id}`
        : '/api/ingredients';
      
      const method = selectedIngredient ? 'PUT' : 'POST';
      
      // Prepare the data using the helper function
      const dataToSend = prepareIngredientForApi(values, companyId as string);

      console.log('Submitting ingredient data:', JSON.stringify({
        id: selectedIngredient?._id,
        canBeSold: dataToSend.canBeSold,
        sellingDetails: dataToSend.sellingDetails?.length || 0
      }, null, 2));
      
      const response = await fetch(endpoint, {
        method,
        headers: { 
          'Content-Type': 'application/json',
          'company-id': companyId as string
        },
        body: JSON.stringify(dataToSend),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save ingredient');
      }

      const updatedIngredient = await response.json();
      console.log('Server response:', JSON.stringify({
        id: updatedIngredient._id,
        canBeSold: updatedIngredient.canBeSold,
        sellingDetails: updatedIngredient.sellingDetails?.length || 0
      }, null, 2));

      toast.success(`Ingredient ${selectedIngredient ? 'updated' : 'created'} successfully`);
      setIsDialogOpen(false);
      await fetchIngredients(); // Refresh the ingredients list
      setSelectedIngredient(null); // Clear selected ingredient
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleEdit = async (ingredient: IIngredient) => {
    try {
      // Ensure UOMs and suppliers are loaded before opening dialog
      if (!uoms?.length) {
        await fetchUoms();
      }
      if (!suppliers?.length) {
        await fetchSuppliers();
      }
      
      // Set the selected ingredient - form will be updated by useEffect
      setSelectedIngredient(ingredient);
      
      console.log('Opening edit dialog for ingredient:', JSON.stringify({
        id: ingredient._id,
        canBeSold: ingredient.canBeSold,
        sellingDetails: ingredient.sellingDetails?.length || 0
      }, null, 2));
      
      // Open dialog
      setIsDialogOpen(true);
    } catch (error) {
      toast.error('Failed to load form data');
    }
  };

  const handleDelete = async (ingredient: Ingredient) => {
    if (!confirm('Are you sure you want to delete this ingredient?')) return;

    try {
      const response = await fetch(`/api/ingredients/${ingredient._id}`, {
        method: 'DELETE',
        headers: {
          'company-id': companyId as string
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete ingredient');
      }

      toast.success('Ingredient deleted successfully');
      fetchIngredients();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleAddNew = () => {
    setSelectedIngredient(null);
    form.reset();
    setIsDialogOpen(true);
  };

  const handleAddSellingOption = async (option: any) => {
    console.log('=== START: handleAddSellingOption ===');
    console.log('Received selling option:', JSON.stringify(option, null, 2));
    console.log('Current selectedIngredient:', JSON.stringify({
      id: selectedIngredient?._id,
      name: selectedIngredient?.name,
      sellingDetails: selectedIngredient?.sellingDetails || []
    }, null, 2));
    
    if (!selectedIngredient) {
      console.error('No ingredient selected');
      return;
    }

    try {
      // Create a deep copy of the selected ingredient to avoid mutation
      const currentIngredient = JSON.parse(JSON.stringify(selectedIngredient));
      console.log('Current ingredient (deep copy):', JSON.stringify({
        id: currentIngredient._id,
        sellingDetails: currentIngredient.sellingDetails || []
      }, null, 2));
      
      // Initialize sellingDetails if it doesn't exist
      if (!currentIngredient.sellingDetails) {
        console.log('Initializing empty sellingDetails array');
        currentIngredient.sellingDetails = [];
      }

      const updatedSellingDetails = [...currentIngredient.sellingDetails, option];
      console.log('Updated selling details:', JSON.stringify(updatedSellingDetails, null, 2));
      
      const updateData = {
        ...currentIngredient,
        sellingDetails: updatedSellingDetails
      };
      console.log('Sending PUT request with data:', JSON.stringify(updateData, null, 2));

      const response = await fetch(`/api/ingredients/${selectedIngredient._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId as string
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update ingredient');
      }

      const responseData = await response.json();
      console.log('PUT response:', JSON.stringify(responseData, null, 2));

      // Update local state with the response data
      setSelectedIngredient(responseData);
      console.log('Updated selectedIngredient state');
      
      // Refresh the ingredients list
      await fetchIngredients();
      console.log('Refreshed ingredients list');

      toast.success('Selling option added successfully');
      console.log('=== END: handleAddSellingOption ===');
    } catch (error: any) {
      console.error('Error in handleAddSellingOption:', error);
      toast.error(error.message || 'Failed to add selling option');
    }
  };

  const handleDeleteSellingOption = async (index: number) => {
    if (!selectedIngredient) return;

    try {
      const updatedSellingDetails = selectedIngredient.sellingDetails.filter((_, i) => i !== index);
      
      const response = await fetch(`/api/ingredients/${selectedIngredient._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId as string
        },
        body: JSON.stringify({
          ...selectedIngredient,
          sellingDetails: updatedSellingDetails
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update ingredient');
      }

      // Update local state
      setSelectedIngredient({
        ...selectedIngredient,
        sellingDetails: updatedSellingDetails
      });
      
      // Refresh the ingredients list
      fetchIngredients();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  return (
    <div className="space-y-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search ingredients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Select
            value={selectedCategory}
            onValueChange={setSelectedCategory}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button onClick={handleAddNew}>Add Ingredient</Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Base UOM</TableHead>
              <TableHead>Suppliers</TableHead>
              <TableHead>Reorder Point</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : ingredients.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center">
                  No ingredients found.
                </TableCell>
              </TableRow>
            ) : (
              ingredients.map((ingredient) => (
                <TableRow key={ingredient._id}>
                  <TableCell>{ingredient.name}</TableCell>
                  <TableCell>{ingredient.SKU}</TableCell>
                  <TableCell>{ingredient.category}</TableCell>
                  <TableCell>{ingredient.baseUomId?.shortCode || '-'}</TableCell>
                  <TableCell>
                    {ingredient.suppliers?.map(s => s.name).join(', ') || '-'}
                  </TableCell>
                  <TableCell>{ingredient.reorderPoint || '-'}</TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/company/${companyId}/products/ingredients/${ingredient._id}`)}
                      className="text-primary hover:text-primary/90"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(ingredient)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="py-2">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedIngredient ? 'Edit Ingredient' : 'Add New Ingredient'}
            </DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="selling" disabled={!selectedIngredient}>
                Selling Options
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="SKU"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SKU</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Input {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="baseUomId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Base UOM</FormLabel>
                        <FormControl>
                          {selectedIngredient ? (
                            <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                              {selectedIngredient.baseUomId.name} ({selectedIngredient.baseUomId.shortCode})
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                disabled={isLoadingUoms}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a UOM" />
                                </SelectTrigger>
                                <SelectContent>
                                  {isLoadingUoms ? (
                                    <SelectItem value="loading" disabled>
                                      Loading UOMs...
                                    </SelectItem>
                                  ) : uoms.length === 0 ? (
                                    <SelectItem value="no-uoms" disabled>
                                      No UOMs available
                                    </SelectItem>
                                  ) : (
                                    uoms.map((uom) => (
                                      <SelectItem key={uom._id} value={uom._id}>
                                        {uom.name} ({uom.shortCode})
                                      </SelectItem>
                                    ))
                                  )}
                                </SelectContent>
                              </Select>
                              <p className="text-sm text-orange-500 mt-1">
                                Note: The Base UOM cannot be changed after creation. Choose carefully as it will be used for all conversions and calculations.
                              </p>
                            </div>
                          )}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="supplierDetails"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier Details</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            {field.value?.map((supplierDetail, index) => (
                              <div key={supplierDetail.supplierId} className="flex flex-col space-y-2">
                                <Select
                                  value={supplierDetail.supplierId}
                                  onValueChange={(value) => {
                                    const newSupplierDetails = [...field.value];
                                    newSupplierDetails[index].supplierId = value;
                                    field.onChange(newSupplierDetails);
                                  }}
                                  disabled={isLoadingSuppliers}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a supplier" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {isLoadingSuppliers ? (
                                      <SelectItem value="loading" disabled>
                                        Loading suppliers...
                                      </SelectItem>
                                    ) : suppliers.length === 0 ? (
                                      <SelectItem value="no-suppliers" disabled>
                                        No suppliers available
                                      </SelectItem>
                                    ) : (
                                      suppliers.map((supplier) => (
                                        <SelectItem key={supplier._id} value={supplier._id}>
                                          {supplier.name}
                                        </SelectItem>
                                      ))
                                    )}
                                  </SelectContent>
                                </Select>
                                <div className="flex flex-col space-y-2">
                                  <FormLabel>Units of Ordering</FormLabel>
                                  {supplierDetail.unitsOfOrdering?.map((unitOfOrdering, unitIndex) => (
                                    <div key={unitIndex} className="flex flex-col space-y-2">
                                      <Select
                                        value={unitOfOrdering.unitOfMeasure}
                                        onValueChange={(value) => {
                                          const newSupplierDetails = [...field.value];
                                          newSupplierDetails[index].unitsOfOrdering[unitIndex].unitOfMeasure = value;
                                          field.onChange(newSupplierDetails);
                                        }}
                                        disabled={isLoadingUoms}
                                      >
                                        <SelectTrigger>
                                          <SelectValue placeholder="Select a unit of measure" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {isLoadingUoms ? (
                                            <SelectItem value="loading" disabled>
                                              Loading UOMs...
                                            </SelectItem>
                                          ) : uoms.length === 0 ? (
                                            <SelectItem value="no-uoms" disabled>
                                              No UOMs available
                                            </SelectItem>
                                          ) : (
                                            uoms.map((uom) => (
                                              <SelectItem key={uom._id} value={uom._id}>
                                                {uom.name} ({uom.shortCode})
                                              </SelectItem>
                                            ))
                                          )}
                                        </SelectContent>
                                      </Select>
                                      <Input
                                        type="number"
                                        value={unitOfOrdering.quantityInBaseUom}
                                        onChange={(e) => {
                                          const newSupplierDetails = [...field.value];
                                          newSupplierDetails[index].unitsOfOrdering[unitIndex].quantityInBaseUom = Number(e.target.value);
                                          field.onChange(newSupplierDetails);
                                        }}
                                        placeholder="Quantity in base UOM"
                                      />
                                      <Input
                                        type="number"
                                        value={unitOfOrdering.price}
                                        onChange={(e) => {
                                          const newSupplierDetails = [...field.value];
                                          newSupplierDetails[index].unitsOfOrdering[unitIndex].price = Number(e.target.value);
                                          field.onChange(newSupplierDetails);
                                        }}
                                        placeholder="Price"
                                      />
                                      <Input
                                        type="number"
                                        value={unitOfOrdering.pricePerBaseUom}
                                        onChange={(e) => {
                                          const newSupplierDetails = [...field.value];
                                          newSupplierDetails[index].unitsOfOrdering[unitIndex].pricePerBaseUom = Number(e.target.value);
                                          field.onChange(newSupplierDetails);
                                        }}
                                        placeholder="Price per base UOM"
                                      />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="reorderPoint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reorder Point</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                            value={field.value ?? ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="canBeSold"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                        <FormControl>
                          <input
                            type="checkbox"
                            checked={field.value}
                            onChange={field.onChange}
                            className="h-4 w-4 rounded border-gray-300"
                          />
                        </FormControl>
                        <FormLabel className="font-normal">Can Be Sold</FormLabel>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">
                      {selectedIngredient ? 'Update' : 'Create'}
                    </Button>
                  </div>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="selling">
              {selectedIngredient && (
                <IngredientSellingOptions
                  ingredientId={selectedIngredient._id}
                  companyId={companyId}
                  sellingDetails={selectedIngredient.sellingDetails || []}
                  onAddSellingOption={handleAddSellingOption}
                  onDeleteSellingOption={handleDeleteSellingOption}
                />
              )}
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Define Zod schema for Units of Ordering
const unitOfOrderingSchema = z.object({
  unitOfMeasure: z.string().refine(Types.ObjectId.isValid, {
    message: "Invalid Unit of Measure ID",
  }),
  quantityInBaseUom: z.number().positive("Quantity must be positive"),
  price: z.number().nonnegative("Price cannot be negative"),
  pricePerBaseUom: z.number().nonnegative("Price per base UOM cannot be negative") // Consider making this calculated or optional
});

// Define Zod schema for Supplier Detail including Units of Ordering
const supplierDetailSchema = z.object({
  supplierId: z.string().refine(Types.ObjectId.isValid, {
    message: "Invalid Supplier ID",
  }),
  unitsOfOrdering: z.array(unitOfOrderingSchema).min(1, "At least one unit of ordering is required per supplier")
});

// Define Zod schema for the form
const ingredientSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional().nullable(),
  reorderPoint: z.number().nullable(),
  baseUomId: z.string().refine(Types.ObjectId.isValid, {
    message: "Invalid Base UOM ID",
  }),
  category: z.string().min(1, { message: 'Category is required' }),
  // Replace suppliers with supplierDetails
  supplierDetails: z.array(supplierDetailSchema).optional(), // Make optional for now, can add min(1) later if needed
  defaultSupplierId: z.string().nullable().refine(val => val === null || Types.ObjectId.isValid(val), {
    message: "Invalid Default Supplier ID",
  }),
  SKU: z.string().min(1, { message: 'SKU is required' }),
  canBeSold: z.boolean().default(false),
  sellingDetails: z.array(z.any()).optional(), // Keep sellingDetails flexible for now
});
