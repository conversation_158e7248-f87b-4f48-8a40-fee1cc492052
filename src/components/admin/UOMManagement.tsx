import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { UOM } from '@/types/uom';
import { Pencil, Trash2 } from 'lucide-react';

const uomSchema = z.object({
  name: z.string().min(1, "Name is required"),
  shortCode: z.string().min(1, "Short code is required"),
  system: z.enum(["metric", "imperial"]),
  baseType: z.enum(["mass", "volume", "count"]),
  factorToCanonical: z.number().min(0.000001, "Factor must be greater than 0"),
  synonyms: z.array(z.string()).optional(),
  description: z.string().optional(),
});

export default function UOMManagement() {
  const { companyId } = useParams();
  const [uoms, setUoms] = useState<UOM[]>([]);
  const [canonicalUoms, setCanonicalUoms] = useState<UOM[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUom, setSelectedUom] = useState<UOM | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm({
    resolver: zodResolver(uomSchema),
    defaultValues: {
      name: "",
      shortCode: "",
      system: "metric" as const,
      baseType: "",
      factorToCanonical: 1,
      synonyms: [],
      description: "",
    },
  });

  const selectedSystem = form.watch("system");
  const selectedBaseType = form.watch("baseType");

  const fetchUoms = async () => {
    try {
      const response = await fetch('/api/uoms', {
        headers: {
          'company-id': companyId as string
        }
      });
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch UOMs');
      }
      
      setUoms(data);
      // Filter out canonical UOMs (global UOMs with no companyId)
      const canonical = data.filter((uom: UOM) => !uom.companyId);
      setCanonicalUoms(canonical);
    } catch (error) {
      console.error('Error fetching UOMs:', error);
      toast.error('Failed to load UOMs');
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof uomSchema>) => {
    try {
      const endpoint = selectedUom 
        ? `/api/uoms/${selectedUom._id}`
        : '/api/uoms';
      
      const method = selectedUom ? 'PUT' : 'POST';
      
      const response = await fetch(endpoint, {
        method,
        headers: { 
          'Content-Type': 'application/json',
          'company-id': companyId as string
        },
        body: JSON.stringify({
          ...values,
          synonyms: values.synonyms || [],
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        const action = selectedUom ? 'update' : 'create';
        const errorMessage = data.error || `Failed to ${action} UOM`;
        
        toast.error(errorMessage, {
          description: errorMessage.includes('already exists') 
            ? "Please choose a different name or short code."
            : undefined,
          duration: 5000,
        });
        return;
      }

      toast.success(`UOM ${selectedUom ? 'updated' : 'created'} successfully`);
      setIsDialogOpen(false);
      fetchUoms();
      form.reset();
      setSelectedUom(null);
    } catch (error: any) {
      console.error('Error:', error);
      toast.error("Failed to save UOM", {
        description: "An unexpected error occurred. Please try again.",
        duration: 5000,
      });
    }
  };

  const handleDelete = async (uom: UOM) => {
    if (!confirm('Are you sure you want to delete this UOM?')) return;

    try {
      const response = await fetch(`/api/uoms/${uom._id}`, {
        method: 'DELETE',
        headers: {
          'company-id': companyId as string
        }
      });

      const data = await response.json();
      
      if (!response.ok) {
        toast.error("Failed to delete UOM", {
          description: data.error || "An error occurred while deleting the UOM",
          duration: 5000,
        });
        return;
      }

      toast.success('UOM deleted successfully');
      fetchUoms();
    } catch (error: any) {
      console.error('Error:', error);
      toast.error("Failed to delete UOM", {
        description: "An unexpected error occurred. Please try again.",
        duration: 5000,
      });
    }
  };

  const handleEdit = (uom: UOM) => {
    setSelectedUom(uom);
    form.reset({
      name: uom.name,
      shortCode: uom.shortCode,
      system: uom.system,
      baseType: uom.baseType,
      factorToCanonical: uom.factorToCanonical,
      synonyms: uom.synonyms,
      description: uom.description,
    });
    setIsDialogOpen(true);
  };

  const handleAddNew = () => {
    setSelectedUom(null);
    form.reset();
    setIsDialogOpen(true);
  };

  useEffect(() => {
    fetchUoms();
  }, [companyId]);

  const getBaseUnitLabel = (type: string) => {
    switch (type) {
      case 'mass':
        return 'Kilogram (kg)';
      case 'volume':
        return 'Liter (L)';
      case 'count':
        return 'Unit';
      default:
        return '';
    }
  };

  const getConversionLabel = (type: string) => {
    switch (type) {
      case 'mass':
        return 'kilograms';
      case 'volume':
        return 'liters';
      case 'count':
        return 'units';
      default:
        return '';
    }
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Units of Measure</h3>
        <Button onClick={handleAddNew}>Add New UOM</Button>
      </div>

      {uoms.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No UOMs found. You can add custom UOMs for your company.
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Short Code</TableHead>
              <TableHead>System</TableHead>
              <TableHead>Base Type</TableHead>
              <TableHead>Factor</TableHead>
              <TableHead>Global/Company</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {uoms.map((uom) => (
              <TableRow key={uom._id.toString()}>
                <TableCell>{uom.name}</TableCell>
                <TableCell>{uom.shortCode}</TableCell>
                <TableCell>{uom.system}</TableCell>
                <TableCell>{uom.baseType}</TableCell>
                <TableCell>{uom.factorToCanonical}</TableCell>
                <TableCell>{uom.companyId ? 'Company' : 'Global'}</TableCell>
                <TableCell className="text-right">
                  {uom.companyId && (
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(uom)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(uom)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedUom ? 'Edit UOM' : 'Add New UOM'}
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., Box of 1000, Half Gallon" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="shortCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Short Code</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., BOX1000, HALFGAL" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="system"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>System</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a system" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="metric">Metric System</SelectItem>
                        <SelectItem value="imperial">Imperial System</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="baseType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Base Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a base type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="mass">Mass (base: Kilogram)</SelectItem>
                        <SelectItem value="volume">Volume (base: Liter)</SelectItem>
                        <SelectItem value="count">Count (base: Unit)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="factorToCanonical"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversion Factor</FormLabel>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-muted-foreground">1 {form.getValues('name') || '[UOM]'} =</p>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="any"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <p className="text-sm text-muted-foreground">{getConversionLabel(form.getValues('baseType'))}</p>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {selectedUom ? 'Update' : 'Create'}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
