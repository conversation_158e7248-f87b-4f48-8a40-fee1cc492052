// src/components/admin/LocationManagement.tsx
import React, { useState, useEffect } from 'react';
import { useRequireCompanyUser } from '@/lib/auth';
import { useRouter } from 'next/navigation';
import { Location } from '@/lib/types/location';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface LocationFormData {
  name: string;
  locationType: 'CENTRAL_KITCHEN' | 'RETAIL_SHOP' | 'SINGLE_LOCATION';
  canSellToExternal: boolean;
  canDoTransfers: boolean;
  canBuyfromExternalSuppliers: boolean;
  address: string;
  contactInfo: {
    phone: string;
    email: string;
  };
}

export function LocationManagement() {
  const { userData, loading } = useRequireCompanyUser('admin');
  const router = useRouter();

  const [locations, setLocations] = useState<Location[]>([]);
  const [loadingLocations, setLoadingLocations] = useState(true);
  const [error, setError] = useState('');

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [formData, setFormData] = useState<LocationFormData>({
    name: '',
    locationType: 'SINGLE_LOCATION',
    canSellToExternal: false,
    canDoTransfers: false,
    canBuyfromExternalSuppliers: false,
    address: '',
    contactInfo: {
      phone: '',
      email: ''
    }
  });

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deletingLocation, setDeletingLocation] = useState<Location | null>(null);

  useEffect(() => {
    if (!loading && !userData) {
      router.replace('/login');
    }
  }, [userData, loading, router]);

  useEffect(() => {
    if (!loading && userData) {
      fetchLocations();
    }
  }, [userData, loading]);

  const fetchLocations = async () => {
    try {
      setLoadingLocations(true);
      const response = await fetch('/api/locations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData?.companyId || '',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch locations');
        return;
      }

      const data = await response.json();
      setLocations(data);
    } catch (err) {
      console.error('Error fetching locations:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoadingLocations(false);
    }
  };

  const handleAddLocation = async () => {
    try {
      // Validate required fields
      if (!userData?.companyId) {
        setError('Company ID is missing. Please try logging in again.');
        return;
      }

      if (!formData.name.trim()) {
        setError('Location name is required');
        return;
      }

      if (!formData.locationType) {
        setError('Location type is required');
        return;
      }

      console.log('Sending location data:', {
        ...formData,
        companyId: userData.companyId,
        canBuyfromExternalSuppliers: formData.canBuyfromExternalSuppliers
      });

      const response = await fetch('/api/locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData?.companyId || '',
        },
        body: JSON.stringify({
          name: formData.name,
          locationType: formData.locationType,
          canSellToExternal: formData.canSellToExternal,
          canDoTransfers: formData.canDoTransfers,
          canBuyfromExternalSuppliers: formData.canBuyfromExternalSuppliers,
          address: formData.address,
          contactInfo: formData.contactInfo,
          companyId: userData.companyId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to create location:', errorData);
        setError(errorData.error || 'Failed to create location');
        return;
      }

      const createdLocation = await response.json();
      console.log('Created location:', createdLocation);

      // Clear any existing error
      setError('');
      await fetchLocations();
      setIsAddModalOpen(false);
      setFormData({
        name: '',
        locationType: 'SINGLE_LOCATION',
        canSellToExternal: false,
        canDoTransfers: false,
        canBuyfromExternalSuppliers: false,
        address: '',
        contactInfo: {
          phone: '',
          email: ''
        }
      });
    } catch (err) {
      console.error('Error creating location:', err);
      setError('An unexpected error occurred while creating the location');
    }
  };

  const handleEdit = (location: Location) => {
    setEditingLocation(location);
    setFormData({
      name: location.name,
      locationType: location.locationType,
      canSellToExternal: location.canSellToExternal,
      canDoTransfers: location.canDoTransfers,
      canBuyfromExternalSuppliers: location.canBuyfromExternalSuppliers ?? false,
      address: location.address || '',
      contactInfo: {
        phone: location.contactInfo?.phone || '',
        email: location.contactInfo?.email || ''
      }
    });
    setIsEditModalOpen(true);
  };

  const handleEditSubmit = async () => {
    try {
      if (!userData?.companyId || !editingLocation?._id) {
        setError('Missing required data for edit');
        return;
      }

      console.log('Sending edit data:', {
        ...formData,
        canBuyfromExternalSuppliers: formData.canBuyfromExternalSuppliers
      });

      const response = await fetch(`/api/locations/${editingLocation._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData.companyId,
        },
        body: JSON.stringify({
          name: formData.name,
          locationType: formData.locationType,
          canSellToExternal: formData.canSellToExternal,
          canDoTransfers: formData.canDoTransfers,
          canBuyfromExternalSuppliers: formData.canBuyfromExternalSuppliers,
          address: formData.address,
          contactInfo: formData.contactInfo
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to update location:', errorData);
        setError(errorData.error || 'Failed to update location');
        return;
      }

      const updatedLocation = await response.json();
      console.log('Updated location:', updatedLocation);

      setLocations(locations.map(loc => 
        loc._id === editingLocation._id ? updatedLocation : loc
      ));
      
      setIsEditModalOpen(false);
      setEditingLocation(null);
      setFormData({
        name: '',
        locationType: 'SINGLE_LOCATION',
        canSellToExternal: false,
        canDoTransfers: false,
        canBuyfromExternalSuppliers: false,
        address: '',
        contactInfo: {
          phone: '',
          email: ''
        }
      });
    } catch (error) {
      console.error('Error updating location:', error);
      setError('An unexpected error occurred while updating the location');
    }
  };

  const handleDeleteClick = (location: Location) => {
    setDeletingLocation(location);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!userData?.companyId || !deletingLocation?._id) {
        setError('Missing required data for deletion');
        return;
      }

      const response = await fetch(`/api/locations/${deletingLocation._id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData.companyId,
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete location');
        return;
      }

      setLocations((prev) => prev.filter((location) => location._id !== deletingLocation._id));
      setIsDeleteModalOpen(false);
      setDeletingLocation(null);
    } catch (err) {
      console.error('Error deleting location:', err);
      setError('An unexpected error occurred while deleting the location');
    }
  };

  // This is a helper function that was not used directly, but it's used to delete locations
  // We've decided to preserve it but mark it clearly as internal, in case it's needed in the future
  const _handleDeleteById = async (id: string) => {
    try {
      const response = await fetch(`/api/locations/delete/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'company-id': userData?.companyId || '',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete location');
        return;
      }

      setLocations((prev) => prev.filter((location) => location._id !== id));
    } catch (err) {
      console.error('Error deleting location:', err);
      setError('An unexpected error occurred');
    }
  };

  if (loading || loadingLocations) {
    return (
      <div className="w-full flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full p-4 text-red-500 bg-red-50 rounded-md">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Location Management</h2>
        <button 
          className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => setIsAddModalOpen(true)}
        >
          Add Location
        </button>
      </div>

      {locations.length === 0 ? (
        <div className="w-full text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-600">No locations found.</p>
          <p className="text-sm text-gray-500 mt-1">Add a location to get started.</p>
        </div>
      ) : (
        <div className="w-full overflow-x-auto rounded-lg border border-gray-200">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Can Sell
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Can Transfer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Can Buy External
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {locations.map((location) => (
                <tr key={location._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {location.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {location.locationType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      location.canSellToExternal ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {location.canSellToExternal ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      location.canDoTransfers ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {location.canDoTransfers ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      location.canBuyfromExternalSuppliers ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {location.canBuyfromExternalSuppliers ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                    <button 
                      onClick={() => handleEdit(location)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Edit
                    </button>
                    <button 
                      onClick={() => handleDeleteClick(location)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <Transition appear show={isAddModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={() => setIsAddModalOpen(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 mb-4"
                  >
                    Add New Location
                  </Dialog.Title>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Location Type <span className="text-red-500">*</span>
                      </label>
                      <select
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={formData.locationType}
                        onChange={(e) => setFormData({ ...formData, locationType: e.target.value as LocationFormData['locationType'] })}
                      >
                        <option value="">Select a location type</option>
                        <option value="SINGLE_LOCATION">Single Location</option>
                        <option value="CENTRAL_KITCHEN">Central Kitchen</option>
                        <option value="RETAIL_SHOP">Retail Shop</option>
                      </select>
                    </div>
                    <div className="flex gap-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          checked={formData.canSellToExternal}
                          onChange={(e) => setFormData({ ...formData, canSellToExternal: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-600">Can Sell to External</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          checked={formData.canDoTransfers}
                          onChange={(e) => setFormData({ ...formData, canDoTransfers: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-600">Can Do Transfers</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          checked={formData.canBuyfromExternalSuppliers}
                          onChange={(e) => setFormData({ ...formData, canBuyfromExternalSuppliers: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-600">Can Buy from External Suppliers</span>
                      </label>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Address</label>
                      <textarea
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        rows={3}
                        value={formData.address}
                        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Phone</label>
                      <input
                        type="tel"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={formData.contactInfo.phone}
                        onChange={(e) => setFormData({
                          ...formData,
                          contactInfo: { ...formData.contactInfo, phone: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <input
                        type="email"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={formData.contactInfo.email}
                        onChange={(e) => setFormData({
                          ...formData,
                          contactInfo: { ...formData.contactInfo, email: e.target.value }
                        })}
                      />
                    </div>
                  </div>

                  {error && (
                    <div className="mt-2 text-sm text-red-600">
                      {error}
                    </div>
                  )}

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      onClick={() => setIsAddModalOpen(false)}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      onClick={handleAddLocation}
                    >
                      Add Location
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Edit Modal */}
      <Transition appear show={isEditModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={() => setIsEditModalOpen(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 mb-4"
                  >
                    Edit Location
                  </Dialog.Title>
                  <div className="space-y-4">
                    {/* Reuse the same form fields as Add Modal */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Location Type <span className="text-red-500">*</span>
                      </label>
                      <select
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={formData.locationType}
                        onChange={(e) => setFormData({ ...formData, locationType: e.target.value as LocationFormData['locationType'] })}
                      >
                        <option value="SINGLE_LOCATION">Single Location</option>
                        <option value="CENTRAL_KITCHEN">Central Kitchen</option>
                        <option value="RETAIL_SHOP">Retail Shop</option>
                      </select>
                    </div>
                    <div className="flex gap-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          checked={formData.canSellToExternal}
                          onChange={(e) => setFormData({ ...formData, canSellToExternal: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-600">Can Sell to External</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          checked={formData.canDoTransfers}
                          onChange={(e) => setFormData({ ...formData, canDoTransfers: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-600">Can Do Transfers</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          checked={formData.canBuyfromExternalSuppliers}
                          onChange={(e) => setFormData({ ...formData, canBuyfromExternalSuppliers: e.target.checked })}
                        />
                        <span className="ml-2 text-sm text-gray-600">Can Buy from External Suppliers</span>
                      </label>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Address</label>
                      <textarea
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        rows={3}
                        value={formData.address}
                        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Contact Phone</label>
                        <input
                          type="tel"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          value={formData.contactInfo.phone}
                          onChange={(e) => setFormData({
                            ...formData,
                            contactInfo: { ...formData.contactInfo, phone: e.target.value }
                          })}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Contact Email</label>
                        <input
                          type="email"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          value={formData.contactInfo.email}
                          onChange={(e) => setFormData({
                            ...formData,
                            contactInfo: { ...formData.contactInfo, email: e.target.value }
                          })}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-gray-500"
                      onClick={() => setIsEditModalOpen(false)}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500"
                      onClick={handleEditSubmit}
                    >
                      Save Changes
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Delete Confirmation Modal */}
      <Transition appear show={isDeleteModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={() => setIsDeleteModalOpen(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900"
                  >
                    Delete Location
                  </Dialog.Title>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Are you sure you want to delete {deletingLocation?.name}? This action cannot be undone.
                    </p>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-gray-500"
                      onClick={() => setIsDeleteModalOpen(false)}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-red-500"
                      onClick={handleDeleteConfirm}
                    >
                      Delete
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
}
