// src/components/admin/EnhancedRoleManagement.tsx
import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>rigger, 
  <PERSON>bsContent 
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertCircle, 
  Building as BuildingIcon, 
  ShieldCheck, 
  Store, 
  Plus, 
  Smartphone, 
  Trash2, 
  Edit, 
  RefreshCw 
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface Permission {
  _id: string;
  name: string;
  description: string;
  category: string;
  isSystemLevel: boolean;
}

interface Role {
  _id: string;
  name: string;
  description: string;
  type: 'hq' | 'branch';
  isSystemRole: boolean;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

// Role types for the selection dropdown
const ROLE_TYPES = [
  { 
    value: 'hq', 
    label: 'HQ Role', 
    description: 'For headquarters staff using the main web application',
    icon: <BuildingIcon className="h-4 w-4" />
  },
  { 
    value: 'branch', 
    label: 'Branch Role', 
    description: 'For branch staff using the Ionic POS application',
    icon: <Store className="h-4 w-4" /> 
  }
];

export function EnhancedRoleManagement({ companyId }: { companyId: string }) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddRoleDialog, setShowAddRoleDialog] = useState(false);
  const [selectedRoleId, setSelectedRoleId] = useState<string | null>(null);
  const [newRoleData, setNewRoleData] = useState({
    name: '',
    description: '',
    type: 'hq' as 'hq' | 'branch',
    permissions: [] as string[]
  });

  // Fetch both roles and permissions on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        await Promise.all([
          fetchRoles(),
          fetchPermissions()
        ]);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/roles', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch roles');
        return;
      }

      const data = await response.json();
      setRoles(data);
    } catch (err) {
      console.error('Error fetching roles:', err);
      throw err;
    }
  };

  const fetchPermissions = async () => {
    try {
      console.log('EnhancedRoleManagement: Fetching permissions for company:', companyId);
      const response = await fetch(`/api/company/${companyId}/permissions`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
      });

      console.log('Permissions response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        setError(`Failed to fetch permissions: ${response.status}`);
        return;
      }

      const data = await response.json();
      console.log('Permissions data received:', data);
      
      if (data.permissions && Array.isArray(data.permissions)) {
        console.log('Setting permissions, count:', data.permissions.length);
        setPermissions(data.permissions);
      } else {
        console.error('Invalid permissions data structure');
        setError('Invalid permissions data received from server');
      }
    } catch (err) {
      console.error('Error fetching permissions:', err);
      setError('An error occurred while fetching permissions');
      throw err;
    }
  };

  const handleCreateRole = async () => {
    try {
      if (!newRoleData.name) {
        setError('Role name is required');
        return;
      }

      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify(newRoleData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create role');
        return;
      }

      // Reset form and close dialog
      setNewRoleData({
        name: '',
        description: '',
        type: 'hq',
        permissions: []
      });
      setShowAddRoleDialog(false);
      
      // Refresh roles list
      await fetchRoles();
    } catch (err) {
      console.error('Error creating role:', err);
      setError('An unexpected error occurred');
    }
  };

  const handleUpdateRole = async (roleId: string) => {
    try {
      const roleToUpdate = roles.find(role => role._id === roleId);
      if (!roleToUpdate) {
        setError('Role not found');
        return;
      }

      // Code for updating a role would go here
      // This is a placeholder for now

      // Refresh roles list after update
      await fetchRoles();
    } catch (err) {
      console.error('Error updating role:', err);
      setError('An unexpected error occurred');
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    try {
      const response = await fetch('/api/roles', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'company-id': companyId,
        },
        body: JSON.stringify({ roleId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete role');
        return;
      }

      // Refresh roles list
      await fetchRoles();
    } catch (err) {
      console.error('Error deleting role:', err);
      setError('An unexpected error occurred');
    }
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setNewRoleData({
        ...newRoleData,
        permissions: [...newRoleData.permissions, permissionId]
      });
    } else {
      setNewRoleData({
        ...newRoleData,
        permissions: newRoleData.permissions.filter(id => id !== permissionId)
      });
    }
  };

  // Filter permissions based on type (hq, branch, or storekeeper)
  const getFilteredPermissions = (roleType: 'hq' | 'branch', permissions: Permission[]) => {
    if (roleType === 'hq') {
      return permissions.filter(p => p.category.startsWith('hq:'));
    } else if (roleType === 'branch') {
      return permissions.filter(p => p.category.startsWith('branch:') || p.category === 'storekeeper');
    }
    return [];
  };

  // Group permissions by category for the UI
  const getGroupedPermissions = (permissions: Permission[]) => {
    return permissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  };

  // Filter roles based on type (hq or branch)
  const hqRoles = roles.filter(role => role.type === 'hq');
  const branchRoles = roles.filter(role => role.type === 'branch');

  if (loading) {
    return (
      <div className="w-full flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Role Management</h2>
        <Dialog open={showAddRoleDialog} onOpenChange={setShowAddRoleDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" /> Add Role
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <DialogTitle>Create New Role</DialogTitle>
              <DialogDescription>
                Add a new role for your company. Define whether it&apos;s for HQ or branch operations.
              </DialogDescription>
            </DialogHeader>
            
            <div className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 py-4">
                <div className="space-y-4 md:col-span-1">
                  <div className="space-y-2">
                    <Label htmlFor="name">Role Name</Label>
                    <Input
                      id="name"
                      placeholder="e.g., Branch Manager"
                      value={newRoleData.name}
                      onChange={(e) => setNewRoleData({ ...newRoleData, name: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      placeholder="Describe this role's responsibilities"
                      value={newRoleData.description}
                      onChange={(e) => setNewRoleData({ ...newRoleData, description: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="type">Role Type</Label>
                    <Select 
                      value={newRoleData.type}
                      onValueChange={(value: 'hq' | 'branch') => setNewRoleData({ ...newRoleData, type: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select role type" />
                      </SelectTrigger>
                      <SelectContent>
                        {ROLE_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center">
                              {type.icon}
                              <span className="ml-2">{type.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      {ROLE_TYPES.find(t => t.value === newRoleData.type)?.description}
                    </p>
                  </div>
                </div>
                
                <div className="md:col-span-2 border rounded-md p-4">
                  <Label className="block mb-2">Permissions</Label>
                  {permissions.length === 0 ? (
                    <div className="h-[400px] flex items-center justify-center">
                      <p className="text-sm text-gray-500">Loading permissions...</p>
                    </div>
                  ) : (
                    <ScrollArea className="h-[400px] pr-4">
                      <div className="space-y-6">
                        {Object.entries(getGroupedPermissions(getFilteredPermissions(newRoleData.type, permissions))).length > 0 ? (
                          Object.entries(getGroupedPermissions(getFilteredPermissions(newRoleData.type, permissions))).map(([category, perms]) => (
                            <div key={category} className="space-y-2">
                              <h4 className="text-sm font-medium text-gray-900 bg-gray-100 px-2 py-1 rounded">{category}</h4>
                              <div className="grid grid-cols-1 gap-2">
                                {Array.isArray(perms) && perms.map((permission, idx) => (
                                  <div 
                                    key={permission._id} 
                                    className={`flex items-start space-x-2 p-2 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 rounded-md`}
                                  >
                                    <Checkbox 
                                      id={permission._id}
                                      checked={newRoleData.permissions.includes(permission._id)}
                                      onCheckedChange={(checked) => 
                                        handlePermissionChange(permission._id, checked as boolean)
                                      }
                                    />
                                    <div>
                                      <Label 
                                        htmlFor={permission._id}
                                        className="text-sm font-medium cursor-pointer"
                                      >
                                        {permission.name}
                                      </Label>
                                      <p className="text-xs text-gray-500">{permission.description}</p>
                                    </div>
                                  </div>
                                ))}
                                {(!Array.isArray(perms) || perms.length === 0) && (
                                  <p className="text-xs text-gray-500 italic px-2">No permissions in this category</p>
                                )}
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8">
                            <p className="text-sm text-gray-500">No permissions available for this role type</p>
                          </div>
                        )}
                        
                        <div className="text-xs text-gray-500 pt-2 border-t mt-4">
                          <p>Selected: {newRoleData.permissions.length} permission(s)</p>
                          <p>Total available: {permissions.length} permission(s)</p>
                        </div>
                      </div>
                    </ScrollArea>
                  )}
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddRoleDialog(false)}>Cancel</Button>
              <Button onClick={handleCreateRole}>Create Role</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="hq" className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="hq" className="flex items-center">
            <BuildingIcon className="h-4 w-4 mr-2" />
            HQ Roles
          </TabsTrigger>
          <TabsTrigger value="branch" className="flex items-center">
            <Smartphone className="h-4 w-4 mr-2" />
            Branch Roles (Ionic App)
          </TabsTrigger>
        </TabsList>

        <TabsContent value="hq" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BuildingIcon className="h-5 w-5 mr-2" /> 
                Headquarters Roles
              </CardTitle>
              <CardDescription>
                These roles are for staff using the main web application at headquarters level.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {hqRoles.length === 0 ? (
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">No HQ roles found.</p>
                  <p className="text-sm text-gray-500 mt-1">Add an HQ role to get started.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Permissions
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {hqRoles.map((role) => (
                        <tr key={role._id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {role.name}
                            {role.isSystemRole && (
                              <Badge variant="secondary" className="ml-2">System</Badge>
                            )}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {role.description || 'No description'}
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex flex-wrap gap-1">
                              {role.permissions.slice(0, 3).map((perm, idx) => (
                                <Badge variant="outline" key={idx} className="text-xs">
                                  {perm}
                                </Badge>
                              ))}
                              {role.permissions.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{role.permissions.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {!role.isSystemRole ? (
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline" className="h-8 px-2">
                                  <Edit className="h-3.5 w-3.5 mr-1" />
                                  Edit
                                </Button>
                                <Button size="sm" variant="destructive" className="h-8 px-2"
                                  onClick={() => handleDeleteRole(role._id)}
                                >
                                  <Trash2 className="h-3.5 w-3.5 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            ) : (
                              <Badge variant="outline">System Role</Badge>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branch" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Smartphone className="h-5 w-5 mr-2" /> 
                Branch Roles (Ionic App)
              </CardTitle>
              <CardDescription>
                These roles are for staff using the Ionic POS app at branch locations and storekeepers at HQ.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {branchRoles.length === 0 ? (
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">No branch roles found.</p>
                  <p className="text-sm text-gray-500 mt-1">Add a branch role to get started.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Permissions
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {branchRoles.map((role) => (
                        <tr key={role._id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {role.name}
                            {role.isSystemRole && (
                              <Badge variant="secondary" className="ml-2">System</Badge>
                            )}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {role.description || 'No description'}
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex flex-wrap gap-1">
                              {role.permissions.slice(0, 3).map((perm, idx) => (
                                <Badge variant="outline" key={idx} className="text-xs">
                                  {perm}
                                </Badge>
                              ))}
                              {role.permissions.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{role.permissions.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {!role.isSystemRole ? (
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline" className="h-8 px-2">
                                  <Edit className="h-3.5 w-3.5 mr-1" />
                                  Edit
                                </Button>
                                <Button size="sm" variant="destructive" className="h-8 px-2"
                                  onClick={() => handleDeleteRole(role._id)}
                                >
                                  <Trash2 className="h-3.5 w-3.5 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            ) : (
                              <Badge variant="outline">System Role</Badge>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RefreshCw className="h-5 w-5 mr-2" /> 
            Role Synchronization Status
          </CardTitle>
          <CardDescription>
            Track the status of role and permission synchronization with the Ionic application.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-md">
            <div>
              <p className="font-medium">Last Successful Sync</p>
              <p className="text-sm text-gray-500">March 17, 2025 at 11:00 AM</p>
            </div>
            <Button variant="outline" className="flex items-center">
              <RefreshCw className="h-4 w-4 mr-2" /> 
              Force Sync Now
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
