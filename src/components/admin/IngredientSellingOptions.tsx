'use client';

import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Plus, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { sellingOptionSchema } from '@/schemas/ingredient';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { LocationMultiSelect } from '@/components/LocationMultiSelect';

interface Props {
  ingredientId: string;
  companyId: string;
  onAddSellingOption: (option: any) => void;
  onDeleteSellingOption: (index: number) => void;
  sellingDetails: any[];
}

export default function IngredientSellingOptions({ 
  ingredientId, 
  companyId, 
  onAddSellingOption,
  onDeleteSellingOption,
  sellingDetails 
}: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const [uoms, setUoms] = useState<any[]>([]);
  const [ingredient, setIngredient] = useState<any>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const router = useRouter();
  const form = useForm<z.infer<typeof sellingOptionSchema>>({
    resolver: zodResolver(sellingOptionSchema),
    defaultValues: {
      unitOfSelling: {
        _id: '',
        name: '',
        shortCode: ''
      },
      priceWithoutTax: 0,
      priceWithTax: 0,
      taxRate: 0,
      taxCategory: 'STANDARD',
      conversionFactor: 1,
      visibility: {
        type: 'ALL_LOCATIONS',
        locations: [],
        externalAccess: false
      },
      sourceType: 'EXTERNAL_SUPPLIER',
      markupType: 'AT_COST',
      markupPercentage: 0
    }
  });

  useEffect(() => {
    console.log('IngredientSellingOptions mounted with props:', {
      ingredientId,
      companyId,
      sellingDetails: sellingDetails?.length || 0
    });
  }, [ingredientId, companyId, sellingDetails]);

  useEffect(() => {
    const subscription = form.watch((value) => {
      console.log('Form values changed:', JSON.stringify(value, null, 2));
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Get base price from supplier details
  const getBasePrice = (ingredient: any) => {
    if (!ingredient?.supplierDetails?.length) return 0;

    // If there's a default supplier, use its first unit's pricePerBaseUom
    if (ingredient.defaultSupplierId) {
      const defaultSupplier = ingredient.supplierDetails.find(
        (s: any) => s.supplierId._id === ingredient.defaultSupplierId._id
      );
      if (defaultSupplier?.unitsOfOrdering?.length) {
        return defaultSupplier.unitsOfOrdering[0].pricePerBaseUom;
      }
    }

    // Otherwise use the first supplier's first unit's pricePerBaseUom
    const firstSupplier = ingredient.supplierDetails[0];
    if (firstSupplier?.unitsOfOrdering?.length) {
      return firstSupplier.unitsOfOrdering[0].pricePerBaseUom;
    }

    return 0;
  };

  // Fetch UOMs
  useEffect(() => {
    const fetchUOMs = async () => {
      try {
        const response = await fetch('/api/uoms', {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch UOMs');
        const data = await response.json();
        setUoms(data);
      } catch (error) {
        console.error('Error fetching UOMs:', error);
        toast({
          title: 'Error',
          description: 'Failed to load units of measure',
          variant: 'destructive',
        });
      }
    };

    fetchUOMs();
  }, [companyId]);

  const getValues = useCallback(form.getValues, [form]);
  const setValue = useCallback(form.setValue, [form]);

  // Fetch ingredient data
  useEffect(() => {
    const fetchIngredient = async () => {
      try {
        const response = await fetch(`/api/ingredients/${ingredientId}`, {
          headers: {
            'company-id': companyId,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch ingredient');
        const data = await response.json();
        setIngredient(data);
        
        // Set initial price based on conversion factor
        const basePrice = getBasePrice(data);
        const currentConversionFactor = getValues('conversionFactor');
        if (basePrice && currentConversionFactor) {
          const adjustedPrice = basePrice * currentConversionFactor;
          setValue('priceWithoutTax', adjustedPrice);
        }
      } catch (error) {
        console.error('Error fetching ingredient:', error);
        toast({
          title: 'Error',
          description: 'Failed to load ingredient details',
          variant: 'destructive',
        });
      }
    };

    fetchIngredient();
  }, [ingredientId, companyId, getValues, setValue]);

  const startEditing = (index: number) => {
    const option = sellingDetails[index];
    setEditingIndex(index);

    // Find the UOM by ID or by name/shortCode as fallback
    let selectedUom = uoms.find(u => u._id === option.unitOfSelling?._id);
    if (!selectedUom && option.unitOfSelling?.name) {
      selectedUom = uoms.find(u => 
        u.name === option.unitOfSelling.name || 
        u.shortCode === option.unitOfSelling.shortCode
      );
    }

    // Reset form with the option's values
    form.reset({
      unitOfSelling: {
        _id: selectedUom?._id || '',
        name: selectedUom?.name || option.unitOfSelling?.name || '',
        shortCode: selectedUom?.shortCode || option.unitOfSelling?.shortCode || ''
      },
      priceWithoutTax: typeof option.priceWithoutTax === 'number' ? option.priceWithoutTax : 0,
      priceWithTax: typeof option.priceWithTax === 'number' ? option.priceWithTax : 0,
      taxRate: typeof option.taxRate === 'number' ? option.taxRate : 0,
      taxCategory: option.taxCategory || 'STANDARD',
      conversionFactor: typeof option.conversionFactor === 'number' ? option.conversionFactor : 1,
      visibility: {
        type: option.visibility?.type || 'ALL_LOCATIONS',
        locations: Array.isArray(option.visibility?.locations) ? option.visibility.locations : [],
        externalAccess: Boolean(option.visibility?.externalAccess)
      },
      sourceType: option.sourceType || 'EXTERNAL_SUPPLIER',
      markupType: option.markupType || 'AT_COST',
      markupPercentage: typeof option.markupPercentage === 'number' ? option.markupPercentage : 0
    });

    // Ensure form state is updated
    form.setValue('visibility.type', option.visibility?.type || 'ALL_LOCATIONS', { shouldValidate: true });
    if (option.visibility?.type === 'SPECIFIC_LOCATIONS') {
      form.setValue('visibility.locations', option.visibility.locations || [], { shouldValidate: true });
    }
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    form.reset({
      unitOfSelling: {
        _id: '',
        name: '',
        shortCode: ''
      },
      priceWithoutTax: 0,
      priceWithTax: 0,
      taxRate: 0,
      taxCategory: 'STANDARD',
      conversionFactor: 1,
      visibility: {
        type: 'ALL_LOCATIONS',
        locations: [],
        externalAccess: false
      },
      sourceType: 'EXTERNAL_SUPPLIER',
      markupType: 'AT_COST',
      markupPercentage: 0
    });
  };

  const onSubmit = async (data: z.infer<typeof sellingOptionSchema>) => {
    console.log('Form submitted with values:', JSON.stringify(data, null, 2));
    setIsLoading(true);
    try {
      // Calculate price with tax if not set
      if (!data.priceWithTax && data.priceWithoutTax && data.taxRate) {
        data.priceWithTax = data.priceWithoutTax * (1 + data.taxRate / 100);
      }

      // Ensure the unit of selling has all required fields
      const selectedUom = uoms.find(u => u._id === data.unitOfSelling._id);
      console.log('Selected UOM:', JSON.stringify(selectedUom, null, 2));
      
      if (!selectedUom) {
        throw new Error('Selected UOM not found');
      }

      data.unitOfSelling = {
        _id: selectedUom._id,
        name: selectedUom.name,
        shortCode: selectedUom.shortCode
      };

      // Handle conditional required fields based on sourceType
      if (data.sourceType === 'CENTRAL_KITCHEN' || data.sourceType === 'BOTH') {
        data.markupType = data.markupType || 'AT_COST';
        if (data.markupType === 'MARKUP') {
          data.markupPercentage = data.markupPercentage || 0;
        }
      }

      // Ensure all required fields are present
      const completeData = {
        ...data,
        visibility: {
          type: data.visibility?.type || 'ALL_LOCATIONS',
          locations: data.visibility?.locations || [],
          externalAccess: Boolean(data.visibility?.externalAccess)
        },
        taxCategory: data.taxCategory || 'STANDARD',
        conversionFactor: Number(data.conversionFactor) || 1,
        priceWithoutTax: Number(data.priceWithoutTax) || 0,
        priceWithTax: Number(data.priceWithTax) || 0,
        taxRate: Number(data.taxRate) || 0
      };

      let updatedSellingDetails;
      if (editingIndex !== null) {
        // Update existing option
        updatedSellingDetails = [...sellingDetails];
        updatedSellingDetails[editingIndex] = completeData;
      } else {
        // Add new option
        updatedSellingDetails = [...sellingDetails, completeData];
      }

      // Call the parent's onAddSellingOption with the complete array
      await onAddSellingOption(updatedSellingDetails);
      
      // Reset form and editing state
      setEditingIndex(null);
      form.reset({
        unitOfSelling: {
          _id: '',
          name: '',
          shortCode: ''
        },
        priceWithoutTax: 0,
        priceWithTax: 0,
        taxRate: 0,
        taxCategory: 'STANDARD',
        conversionFactor: 1,
        visibility: {
          type: 'ALL_LOCATIONS',
          locations: [],
          externalAccess: false
        },
        sourceType: 'EXTERNAL_SUPPLIER',
        markupType: 'AT_COST',
        markupPercentage: 0
      });

      toast({
        title: 'Success',
        description: editingIndex !== null ? 'Selling option updated successfully' : 'Selling option added successfully',
      });
    } catch (error) {
      console.error('Error in onSubmit:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save selling option',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onDelete = async (index: number) => {
    setIsLoading(true);
    try {
      onDeleteSellingOption(index);
      toast({
        title: 'Success',
        description: 'Selling option deleted successfully',
      });
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete selling option',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Watch source type to conditionally show markup fields
  const sourceType = form.watch('sourceType');
  const markupType = form.watch('markupType');
  const conversionFactor = form.watch('conversionFactor');

  // Watch for changes in conversion factor and update price
  useEffect(() => {
    if (ingredient) {
      const basePrice = getBasePrice(ingredient);
      if (basePrice && conversionFactor) {
        const adjustedPrice = basePrice * conversionFactor;
        setValue('priceWithoutTax', adjustedPrice);
      }
    }
  }, [conversionFactor, ingredient, setValue]);

  // Add this to watch the visibility type
  const visibilityType = form.watch('visibility.type');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add Selling Option</CardTitle>
          <CardDescription>
            Configure how this ingredient can be sold to branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="sourceType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Source Type</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        console.log('Source type changed to:', value);
                        field.onChange(value);
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select source type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="CENTRAL_KITCHEN">Central Kitchen</SelectItem>
                        <SelectItem value="EXTERNAL_SUPPLIER">External Supplier</SelectItem>
                        <SelectItem value="BOTH">Both</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {(sourceType === 'CENTRAL_KITCHEN' || sourceType === 'BOTH') && (
                <FormField
                  control={form.control}
                  name="markupType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Markup Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select markup type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="AT_COST">At Cost</SelectItem>
                          <SelectItem value="MARKUP">With Markup</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {markupType === 'MARKUP' && (
                <FormField
                  control={form.control}
                  name="markupPercentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Markup Percentage</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="unitOfSelling._id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit of Selling</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        console.log('Unit of selling changed to:', value);
                        const selectedUom = uoms.find(u => u._id === value);
                        console.log('Selected UOM:', selectedUom);
                        if (selectedUom) {
                          form.setValue('unitOfSelling', {
                            _id: selectedUom._id,
                            name: selectedUom.name,
                            shortCode: selectedUom.shortCode
                          });
                        }
                      }}
                      value={field.value || ''}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {uoms.map((uom) => (
                          <SelectItem key={uom._id} value={uom._id}>
                            {uom.name} ({uom.shortCode})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="conversionFactor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversion Factor</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.0001"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                          field.onChange(value);
                          if (ingredient) {
                            const basePrice = getBasePrice(ingredient);
                            if (basePrice) {
                              const adjustedPrice = basePrice * value;
                              setValue('priceWithoutTax', adjustedPrice || 0);
                            }
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="priceWithoutTax"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price (excl. tax)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...field}
                          disabled={true}
                          className="bg-gray-50"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="taxRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tax Rate (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="visibility.type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Visibility</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || 'ALL_LOCATIONS'}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select visibility" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ALL_LOCATIONS">All Locations</SelectItem>
                        <SelectItem value="SPECIFIC_LOCATIONS">Specific Locations</SelectItem>
                        <SelectItem value="EXTERNAL_ONLY">External Only</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {visibilityType === 'SPECIFIC_LOCATIONS' && (
                <FormField
                  control={form.control}
                  name="visibility.locations"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Select Locations</FormLabel>
                      <FormControl>
                        <LocationMultiSelect
                          companyId={companyId}
                          selectedLocations={field.value || []}
                          onChange={(locations) => {
                            field.onChange(locations);
                            form.setValue('visibility.locations', locations, {
                              shouldValidate: true,
                            });
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <Button 
                onClick={form.handleSubmit(onSubmit)}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {editingIndex !== null ? 'Update Selling Option' : 'Add Selling Option'}
              </Button>
              {editingIndex !== null && (
                <Button 
                  onClick={cancelEditing}
                  variant="outline"
                  className="w-full mt-2"
                >
                  Cancel Editing
                </Button>
              )}
            </div>
          </Form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current Selling Options</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px]">
            <div className="space-y-4">
              {sellingDetails?.map((option: any, index: number) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="font-medium">
                          {option.sourceType === 'CENTRAL_KITCHEN' ? 'Central Kitchen' :
                           option.sourceType === 'EXTERNAL_SUPPLIER' ? 'External Supplier' : 'Both'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Unit: {option.unitOfSelling?.name || 'Unknown'} |
                          Price: ${typeof option.priceWithoutTax === 'number' ? option.priceWithoutTax.toFixed(2) : '0.00'} |
                          Tax: {typeof option.taxRate === 'number' ? option.taxRate : 0}%
                        </div>
                        {option.markupType && (
                          <div className="text-sm text-muted-foreground">
                            Markup: {option.markupType === 'AT_COST' ? 'At Cost' : 
                              `${typeof option.markupPercentage === 'number' ? option.markupPercentage : 0}%`}
                          </div>
                        )}
                        <div className="text-sm text-muted-foreground">
                          Visibility: {(option.visibility?.type || 'ALL_LOCATIONS').replace(/_/g, ' ')}
                          {option.visibility?.type === 'SPECIFIC_LOCATIONS' && option.visibility?.locations?.length > 0 && (
                            <span> ({option.visibility.locations.length} locations)</span>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => startEditing(index)}
                          disabled={isLoading || editingIndex !== null}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDelete(index)}
                          disabled={isLoading || editingIndex !== null}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
