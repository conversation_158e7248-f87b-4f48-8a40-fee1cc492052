// src/components/company/CompanyNav.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import {
  Package,
  Users,
  Key,
  Warehouse,
  ShoppingCart,
  CheckSquare,
  Utensils,
  Tags,
  LayoutDashboard,
  Truck,
  BoxesIcon,
  ClipboardList,
  Factory,
  Settings,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';

interface NavItemBase {
  icon: React.ReactNode;
  roles: Array<'admin' | 'owner' | 'manager' | 'storekeeper'>;
}

interface NavItem extends NavItemBase {
  path: string;
  label: string;
  items?: never;
  name?: never;
}

interface NavItemWithChildren extends NavItemBase {
  name: string;
  path: string;
  items: Array<{
    name: string;
    path: string;
    icon: React.ReactNode;
  }>;
  label?: never;
}

// Type guard to differentiate between item types
function isNavItemWithChildren(item: NavItem | NavItemWithChildren): item is NavItemWithChildren {
  return (item as NavItemWithChildren).items !== undefined;
}

export function CompanyNav() {
  const { userData } = useRequireCompanyUser();
  const pathname = usePathname() || '';

  if (!userData || userData.userType !== 'company_user') return null;

  // Define navigation items - FIXED TO HAVE MENU MANAGEMENT AND RECIPE MANAGEMENT AS SEPARATE TOP-LEVEL ITEMS
  const navItems: (NavItem | NavItemWithChildren)[] = [
    { path: 'dashboard', label: 'Dashboard', icon: <LayoutDashboard className="h-5 w-5" />, roles: ['admin', 'owner', 'manager', 'storekeeper'] },
    { path: 'locations', label: 'Locations', icon: <Warehouse className="h-5 w-5" />, roles: ['admin', 'owner', 'manager'] },
    {
      name: 'Products',
      icon: <Package className="h-5 w-5" />,
      path: 'products',
      items: [
        { name: 'Products List', path: 'products', icon: <Package className="h-5 w-5" /> },
        { name: 'Recipes', path: 'products/recipes', icon: <Utensils className="h-5 w-5" /> },
        { name: 'Selling Options', path: 'products/selling-options', icon: <Tags className="h-5 w-5" /> },
      ],
      roles: ['admin', 'owner', 'manager']
    },
    { path: 'suppliers', label: 'Suppliers', icon: <Truck className="h-5 w-5" />, roles: ['admin', 'owner', 'manager'] },
    { path: 'inventory', label: 'Inventory', icon: <BoxesIcon className="h-5 w-5" />, roles: ['admin', 'owner', 'manager', 'storekeeper'] },
    { path: 'orders', label: 'Orders', icon: <ClipboardList className="h-5 w-5" />, roles: ['admin', 'owner', 'manager', 'storekeeper'] },
    { path: 'production', label: 'Production Planning', icon: <Factory className="h-5 w-5" />, roles: ['admin', 'owner', 'manager'] },
    { path: 'users', label: 'Users', icon: <Users className="h-5 w-5" />, roles: ['admin', 'owner'] },
    { path: 'roles', label: 'Roles', icon: <Key className="h-5 w-5" />, roles: ['admin', 'owner'] },
    { path: 'settings/menus', label: 'Menu Management', icon: <Utensils className="h-5 w-5" />, roles: ['admin', 'owner', 'manager'] },
    { path: 'settings/menu-recipes', label: 'Recipe Management', icon: <Utensils className="h-5 w-5" />, roles: ['admin', 'owner', 'manager'] },
    { path: 'settings/general', label: 'Settings', icon: <Settings className="h-5 w-5" />, roles: ['admin', 'owner'] },
    // Additional storekeeper features
    { path: 'inventory-count', label: 'Inventory Count', icon: <Warehouse className="h-5 w-5" />, roles: ['admin', 'owner', 'manager', 'storekeeper'] },
    { path: 'goods-reception', label: 'Goods Reception', icon: <ShoppingCart className="h-5 w-5" />, roles: ['admin', 'owner', 'manager', 'storekeeper'] },
    { path: 'orders-status', label: 'Orders Status', icon: <CheckSquare className="h-5 w-5" />, roles: ['admin', 'owner', 'manager', 'storekeeper'] }
  ];

  // Authorization check: limit items to those that the user can access
  const userRole = userData.role;
  const filteredNavItems = navItems.filter(item => item.roles.includes(userRole));

  // Dynamic base path injected via pathname and [companyId]
  const companyIdMatch = pathname.match(/\/company\/([^/]+)/);
  const companyId = companyIdMatch ? companyIdMatch[1] : '';
  const basePath = companyId ? `/company/${companyId}` : '';

  if (!basePath) return null; // Don't render until we have a company context

  return (
    <div className="relative h-full min-h-screen border-r">
      <nav className="space-y-6 pb-24 p-4">
        <div className="space-y-1">
          {filteredNavItems.map(item => {
            if (isNavItemWithChildren(item)) {
              // This is a parent item with children (Products)
              const parentFullPath = `${basePath}/${item.path}`;
              const isActive = pathname.startsWith(parentFullPath);

              return (
                <div key={item.name || item.path} className="space-y-1">
                  {/* Parent Item Row */}
                  <div className="flex items-center justify-between rounded-lg hover:bg-primary/5 group">
                    <Link
                      href={parentFullPath}
                      className={`flex flex-grow items-center px-3 py-2 rounded-lg transition-colors font-medium ${
                        pathname === parentFullPath || (isActive && pathname !== parentFullPath)
                          ? 'bg-primary/10 text-primary'
                          : 'group-hover:bg-primary/5'
                      }`}
                    >
                      {item.icon}
                      <span className="ml-3">{item.name}</span>
                    </Link>
                    <div className="pr-3 flex items-center h-full">
                      {isActive ? (
                        <ChevronUp className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      )}
                    </div>
                  </div>

                  {/* Render child items for parent with children */}
                  {isActive && (
                    <div className="ml-4 pl-3 border-l border-muted-foreground/20 space-y-1">
                      {item.items.map((childItem) => {
                        const childFullPath = `${basePath}/${childItem.path}`;
                        const isChildActive = pathname === childFullPath || pathname.startsWith(`${childFullPath}/`);

                        return (
                          <Link
                            key={childItem.path}
                            href={childFullPath}
                            className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-sm ${
                              isChildActive
                                ? 'bg-primary/10 text-primary font-medium'
                                : 'hover:bg-primary/5 text-muted-foreground hover:text-foreground'
                            }`}
                          >
                            {childItem.icon}
                            <span>{childItem.name}</span>
                          </Link>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            } else {
              // This is a regular menu item (including Menu Management and Recipe Management)
              const fullPath = `${basePath}/${item.path}`;
              const isActive = pathname === fullPath || pathname.startsWith(`${fullPath}/`);

              return (
                <Link
                  key={item.path}
                  href={fullPath}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors font-medium ${
                    isActive
                      ? 'bg-primary/10 text-primary'
                      : 'hover:bg-primary/5'
                  }`}
                >
                  {item.icon}
                  <span className="ml-3">{item.label}</span>
                </Link>
              );
            }
          })}
        </div>
      </nav>
    </div>
  );
}
