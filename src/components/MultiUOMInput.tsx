'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface EnhancedInventoryItem {
  _id: string;
  itemId: {
    _id: string;
    name: string;
  };
  currentStock: number;
  baseUomId: {
    _id: string;
    name: string;
    shortCode: string;
  };
  itemType: string;
  countedQuantity?: number;
  itemDetails: {
    baseYieldUOM?: {
      _id: string;
      name: string;
      shortCode: string;
    };
    sellingDetails: Array<{
      _id: string;
      unitOfSelling: {
        _id: string;
        name: string;
        shortCode: string;
      };
      conversionFactor: number;
    }>;
  };
  totalCountedQuantity?: number;
}

interface MultiUOMInputProps {
  item: EnhancedInventoryItem;
  onChange: (totalQuantity: number) => void;
}

export function MultiUOMInput({ item, onChange }: MultiUOMInputProps) {
  const [quantities, setQuantities] = useState<Record<string, number>>({});

  // Get the base UOM based on item type
  const baseUOM = item.itemType === 'RECIPE' 
    ? item.itemDetails.baseYieldUOM 
    : item.baseUomId;

  // Group selling options by UOM
  const groupedOptions = (item.itemDetails?.sellingDetails || []).reduce((acc, option) => {
    const uomId = option.unitOfSelling._id;
    if (!acc[uomId]) {
      acc[uomId] = {
        ...option.unitOfSelling,
        count: 1,
        conversionFactor: option.conversionFactor
      };
    } else {
      acc[uomId].count++;
      // Use the highest conversion factor if there are multiple options with the same UOM
      acc[uomId].conversionFactor = Math.max(
        acc[uomId].conversionFactor,
        option.conversionFactor
      );
    }
    return acc;
  }, {} as Record<string, {
    _id: string;
    name: string;
    shortCode: string;
    count: number;
    conversionFactor: number;
  }>);

  // Sort by conversion factor (highest first) and take top 3
  const topUOMs = Object.values(groupedOptions)
    .sort((a, b) => b.conversionFactor - a.conversionFactor)
    .slice(0, 3);

  const handleUOMChange = (uomId: string, quantity: number) => {
    const newQuantities = {
      ...quantities,
      [uomId]: quantity || 0 // Convert empty string or undefined to 0
    };
    setQuantities(newQuantities);

    // Calculate total in base UOM
    const total = Object.entries(newQuantities).reduce((sum, [uomId, qty]) => {
      const uom = groupedOptions[uomId];
      return sum + (qty * (uom?.conversionFactor || 1));
    }, 0);

    onChange(total);
  };

  return (
    <div className="flex gap-4 items-center">
      {topUOMs.map(uom => (
        <div key={uom._id} className="flex flex-col">
          <Label>{uom.shortCode}</Label>
          <Input
            type="number"
            min="0"
            value={quantities[uom._id] || ''}
            onChange={e => handleUOMChange(uom._id, Number(e.target.value))}
            className="w-24"
          />
        </div>
      ))}
      <div className="text-sm text-gray-500">
        Total: {item.totalCountedQuantity?.toFixed(2)} {baseUOM?.shortCode || ''}
      </div>
    </div>
  );
}
