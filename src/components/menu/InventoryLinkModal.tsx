import { useState, useEffect } from 'react';
import { MenuItem, MenuCategory, InventoryItem } from '@/types';

interface Props {
  item: MenuItem & { category: MenuCategory };
  onClose: () => void;
  onSave: () => void;
}

export function InventoryLinkModal({ item, onClose, onSave }: Props) {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [selectedInventoryId, setSelectedInventoryId] = useState<string>(item.inventoryItemId?.toString() || '');
  const [recipeComponents, setRecipeComponents] = useState<{
    inventoryItemId: string;
    quantity: number;
    unit: string;
  }[]>(item.recipeComponents || []);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchInventoryItems = async () => {
      try {
        const response = await fetch(`/api/company/${item.companyId}/inventory/items`);
        if (!response.ok) throw new Error('Failed to fetch inventory items');
        const data = await response.json();
        setInventoryItems(data.items);
      } catch (error) {
        console.error('Error fetching inventory items:', error);
        setError('Failed to load inventory items');
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryItems();
  }, [item.companyId]);

  const handleSave = async () => {
    try {
      setSaving(true);
      const payload = {
        type: item.type,
        ...(item.type === 'single' 
          ? { inventoryItemId: selectedInventoryId }
          : { recipeComponents }
        )
      };

      const response = await fetch(`/api/company/${item.companyId}/menu/items/${item._id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) throw new Error('Failed to update menu item');
      
      onSave();
    } catch (error) {
      console.error('Error saving inventory link:', error);
      setError('Failed to save changes');
    } finally {
      setSaving(false);
    }
  };

  const addRecipeComponent = () => {
    setRecipeComponents([
      ...recipeComponents,
      { inventoryItemId: '', quantity: 1, unit: 'unit' }
    ]);
  };

  const removeRecipeComponent = (index: number) => {
    setRecipeComponents(recipeComponents.filter((_, i) => i !== index));
  };

  const updateRecipeComponent = (index: number, field: string, value: string | number) => {
    setRecipeComponents(recipeComponents.map((comp, i) => 
      i === index ? { ...comp, [field]: value } : comp
    ));
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
        <div className="bg-white p-6 rounded-lg">
          Loading...
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white p-6 rounded-lg w-full max-w-2xl">
        <h2 className="text-xl font-bold mb-4">
          Link {item.name} to Inventory
        </h2>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {item.type === 'single' ? (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Inventory Item
            </label>
            <select
              className="form-select w-full"
              value={selectedInventoryId}
              onChange={e => setSelectedInventoryId(e.target.value)}
            >
              <option value="">Select an item...</option>
              {inventoryItems.map(invItem => (
                <option key={invItem._id} value={invItem._id}>
                  {invItem.name} ({invItem.unit})
                </option>
              ))}
            </select>
          </div>
        ) : (
          <div>
            <div className="flex justify-between items-center mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Recipe Components
              </label>
              <button
                type="button"
                onClick={addRecipeComponent}
                className="px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700"
              >
                Add Component
              </button>
            </div>

            {recipeComponents.map((component, index) => (
              <div key={index} className="grid grid-cols-12 gap-4 mb-4">
                <div className="col-span-5">
                  <select
                    className="form-select w-full"
                    value={component.inventoryItemId}
                    onChange={e => updateRecipeComponent(index, 'inventoryItemId', e.target.value)}
                  >
                    <option value="">Select an item...</option>
                    {inventoryItems.map(invItem => (
                      <option key={invItem._id} value={invItem._id}>
                        {invItem.name} ({invItem.unit})
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-span-3">
                  <input
                    type="number"
                    className="form-input w-full"
                    value={component.quantity}
                    onChange={e => updateRecipeComponent(index, 'quantity', parseFloat(e.target.value))}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div className="col-span-3">
                  <input
                    type="text"
                    className="form-input w-full"
                    value={component.unit}
                    onChange={e => updateRecipeComponent(index, 'unit', e.target.value)}
                  />
                </div>
                <div className="col-span-1">
                  <button
                    type="button"
                    onClick={() => removeRecipeComponent(index)}
                    className="text-red-600 hover:text-red-900"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSave}
            disabled={saving || (item.type === 'single' ? !selectedInventoryId : recipeComponents.length === 0)}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400"
          >
            {saving ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
}
