'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Card, 
  CardContent,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
// Select components currently unused but kept for future use
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
import { X, AlertCircle, Search } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';

// Types
export interface SellableItem {
  id: string;
  name: string;
  description?: string;
  type: 'ingredient' | 'recipe';
  unit: string;
  unitName: string;
  category?: string;
}

export interface RecipeComponent {
  itemId: string;
  name: string;
  quantity: number;
  unit: string;
  type: 'ingredient' | 'recipe';
}

interface RecipeComponentSelectorProps {
  companyId: string;
  value: RecipeComponent[];
  onChange: (components: RecipeComponent[]) => void;
  maxComponents?: number;
}

export default function RecipeComponentSelector({
  companyId,
  value,
  onChange,
  maxComponents = 50
}: RecipeComponentSelectorProps) {
  const [components, setComponents] = useState<RecipeComponent[]>(value || []);
  const [search, setSearch] = useState('');
  const [open, setOpen] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Fetch sellable inventory items
  const { data, isLoading } = useQuery({
    queryKey: ['sellable-items', companyId, search],
    queryFn: async () => {
      const response = await fetch(
        `/api/company/${companyId}/inventory/sellable-items?search=${encodeURIComponent(search)}`,
        { 
          headers: { 'company-id': companyId }
        }
      );
      if (!response.ok) throw new Error('Failed to fetch sellable items');
      const data = await response.json();
      return data.items as SellableItem[];
    },
    enabled: !!companyId
  });

  // Update parent component when components change
  useEffect(() => {
    onChange(components);
  }, [components, onChange]);

  // Handle adding a component
  const handleAddComponent = (item: SellableItem) => {
    // Check if the item is already added
    if (components.some(c => c.itemId === item.id)) {
      setErrors([...errors, `${item.name} is already added to the recipe`]);
      return;
    }

    // Check if we've reached the maximum allowed components
    if (components.length >= maxComponents) {
      setErrors([...errors, `Maximum of ${maxComponents} components allowed`]);
      return;
    }

    const newComponent: RecipeComponent = {
      itemId: item.id,
      name: item.name,
      quantity: 1,
      unit: item.unit,
      type: item.type
    };

    setComponents([...components, newComponent]);
    setOpen(false);
  };

  // Handle removing a component
  const handleRemoveComponent = (index: number) => {
    setComponents(components.filter((_, i) => i !== index));
  };

  // Handle updating a component's quantity
  const handleUpdateQuantity = (index: number, quantity: number) => {
    if (quantity <= 0) {
      setErrors([...errors, 'Quantity must be greater than zero']);
      return;
    }

    const newComponents = [...components];
    newComponents[index].quantity = quantity;
    setComponents(newComponents);
  };

  // Clear error after 5 seconds
  useEffect(() => {
    if (errors.length > 0) {
      const timer = setTimeout(() => {
        setErrors(errors.slice(1));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errors]);

  return (
    <div className="space-y-4">
      {/* Error messages */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errors[0]}</AlertDescription>
        </Alert>
      )}

      {/* Component search */}
      <div className="flex space-x-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button 
              variant="outline" 
              className="w-full justify-between"
              disabled={components.length >= maxComponents}
            >
              <span>Add Ingredient or Recipe</span>
              <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="p-0 w-[400px]" side="bottom" align="start">
            <Command>
              <CommandInput 
                placeholder="Search ingredients and recipes..."
                value={search}
                onValueChange={setSearch}
              />
              <CommandList>
                <CommandEmpty>No items found.</CommandEmpty>
                <CommandGroup>
                  {!isLoading && data?.map((item) => (
                    <CommandItem
                      key={item.id}
                      value={item.id}
                      onSelect={() => handleAddComponent(item)}
                    >
                      <div className="flex flex-col">
                        <div className="font-medium">{item.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {item.type === 'ingredient' ? 'Ingredient' : 'Recipe'} • {item.unitName}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {/* List of components */}
      <div className="space-y-2">
        {components.length === 0 && (
          <div className="flex items-center justify-center h-20 border border-dashed rounded-lg">
            <p className="text-sm text-muted-foreground">No components added yet</p>
          </div>
        )}

        {components.map((component, index) => (
          <Card key={index} className="relative">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium">{component.name}</h4>
                    <Badge variant={component.type === 'ingredient' ? 'default' : 'secondary'}>
                      {component.type === 'ingredient' ? 'Ingredient' : 'Recipe'}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor={`quantity-${index}`}>Quantity:</Label>
                      <Input
                        id={`quantity-${index}`}
                        type="number"
                        value={component.quantity}
                        onChange={(e) => handleUpdateQuantity(index, parseFloat(e.target.value))}
                        className="w-24"
                        min="0.01"
                        step="0.01"
                      />
                      <span className="text-sm text-muted-foreground">{component.unit}</span>
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveComponent(index)}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
