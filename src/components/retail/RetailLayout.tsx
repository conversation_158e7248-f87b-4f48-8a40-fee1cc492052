'use client';

import React from 'react';
import { RetailSidebar } from './RetailSidebar';
import { useLocation } from '@/contexts/LocationContext';
import { useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';

export function RetailLayout({ children }: { children: React.ReactNode }) {
  const { userData } = useRequireCompanyUser();
  const { selectedLocation } = useLocation();
  const router = useRouter();

  // If no location is selected or it's not a retail shop, redirect to dashboard
  React.useEffect(() => {
    if (!selectedLocation || selectedLocation.locationType !== 'RETAIL_SHOP') {
      router.replace('/dashboard');
    }
  }, [selectedLocation, router]);

  if (!selectedLocation || selectedLocation.locationType !== 'RETAIL_SHOP') {
    return null;
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <div className="w-64 flex-shrink-0">
        <RetailSidebar />
      </div>
      <div className="flex-1 overflow-auto">
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
