'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { UOMInput } from './UOMInput';
import { IRecipe, RecipeIngredient } from '@/models/Recipe';
import { Types } from 'mongoose';
import { useUOMs } from '@/hooks/useUOMs';
import { UOM, convertUOMValue } from '@/utils/uomConversion';
import { getDisplayUnits } from '@/utils/uomHelpers';
import { Plus, Trash2 } from 'lucide-react';
import { RecipeIngredientInput } from './RecipeIngredientInput';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ICurrencyTax } from '@/models/CurrencyTax';
import { SellingUnitInput } from './SellingUnitInput';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Utensils, ShoppingBag } from 'lucide-react';
import { LocationMultiSelect } from './LocationMultiSelect';
import { SellingOptionVisibility, VisibilityType } from '@/types/selling';

// Local interface to avoid name collision with imported types
interface RecipeSellingOption {
  id: string;
  unitOfSelling: string;
  priceWithoutTax: number;
  priceWithTax: number;
  taxRate: number;
  taxCategory: 'STANDARD' | 'REDUCED' | 'ZERO' | 'EXEMPT';
  conversionFactor: number;
  visibility: SellingOptionVisibility;
}

interface RecipeFormProps {
  initialData?: IRecipe;
  onSubmit: (data: any) => void;
  companyId: string;
  children?: React.ReactNode;
}

// Extended RecipeIngredient with additional properties used in the form
interface ExtendedRecipeIngredient extends RecipeIngredient {
  baseUomId?: string;
  baseQuantity?: number;
  smallQuantity?: number;
  largeQuantity?: number;
  smallUomShortCode?: string;
  largeUomShortCode?: string;
  uomShortCode?: string;
  preferredUomId?: string;
}

interface RecipeFormState {
  name: string;
  description: string;
  Category: string;
  yield: number;
  baseYieldUOMId: string | Types.ObjectId; // Can be either string ID or ObjectId
  stockable: boolean;
  isSubRecipe: boolean;
  canBeSold: boolean;
  recipeIngredients: ExtendedRecipeIngredient[];
  sellingOptions: RecipeSellingOption[];
}

export function RecipeForm({ initialData, onSubmit, companyId, children }: RecipeFormProps) {
  const [formData, setFormData] = useState<RecipeFormState>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    Category: initialData?.Category || '',
    yield: initialData?.yield || 0,
    baseYieldUOMId: initialData?.baseYieldUOM?._id?.toString() || (initialData?.baseYieldUOM as unknown as string) || '',
    stockable: initialData?.stockable || false,
    isSubRecipe: initialData?.isSubRecipe || false,
    canBeSold: initialData?.canBeSold || false,
    recipeIngredients: initialData?.recipeIngredients || [],
    sellingOptions: initialData?.sellingDetails || []
  });

  const [yieldBaseUom, setYieldBaseUom] = useState<UOM | null>(null);
  const [yieldDisplayUom, setYieldDisplayUom] = useState<UOM | null>(null);
  const [currencyTax, setCurrencyTax] = useState<ICurrencyTax | null>(null);
  const [loadingTax, setLoadingTax] = useState(false);
  const [taxError, setTaxError] = useState<string | null>(null);

  const { uoms, loading, error } = useUOMs(companyId);

  useEffect(() => {
    if (!loading && uoms.length > 0) {
      // Find the selected base yield UOM or default to mass unit
      const selectedBaseUom = uoms.find(u => u._id.toString() === formData.baseYieldUOMId);
      const defaultBaseUom = uoms.find(u => u.baseType === 'mass' && u.system === 'metric' && u.factorToCanonical === 1);
      const baseUom = selectedBaseUom || defaultBaseUom;
      
      if (baseUom) {
        const { small: displayUom, large: canonicalUom } = getDisplayUnits(uoms, baseUom);
        setYieldBaseUom(baseUom); // Use the actual selected UOM
        setYieldDisplayUom(displayUom);
        
        // If no initial data, set default baseYieldUomId
        if (!initialData?.baseYieldUOM && !formData.baseYieldUOMId) {
          setFormData(prev => ({
            ...prev,
            baseYieldUOMId: canonicalUom._id.toString()
          }));
        }
      }
    }
  }, [loading, uoms, initialData, formData.baseYieldUOMId]); // Added formData.baseYieldUOMId as dependency

  useEffect(() => {
    if (!loading && uoms.length > 0 && yieldBaseUom && yieldDisplayUom) {
      setFormData(prev => ({
        ...prev,
        recipeIngredients: prev.recipeIngredients.map(ing => {
          // Get the UOMs for this ingredient
          const ingredientBaseUom = uoms.find(u => u._id.toString() === ing.baseUomId) || yieldBaseUom;
          const { small: ingredientDisplayUom, large: ingredientCanonicalUom } = getDisplayUnits(uoms, ingredientBaseUom);

          // Use quantity as our canonical value (in base units)
          const canonicalQuantity = ing.quantity || ing.baseQuantity || 0;

          // Calculate small and large quantities
          const smallQuantity = ing.smallQuantity ?? convertUOMValue(canonicalQuantity, ingredientCanonicalUom, ingredientDisplayUom);
          const largeQuantity = ing.largeQuantity ?? canonicalQuantity;

          return {
            ...ing,
            quantity: canonicalQuantity,
            baseQuantity: canonicalQuantity,
            smallQuantity,
            largeQuantity,
            smallUomShortCode: ingredientDisplayUom.shortCode,
            largeUomShortCode: ingredientCanonicalUom.shortCode,
            uomShortCode: ingredientCanonicalUom.shortCode,
            baseUomId: ingredientCanonicalUom._id.toString(),
            preferredUomId: ingredientDisplayUom._id.toString()
          };
        })
      }));
    }
  }, [loading, uoms, yieldBaseUom, yieldDisplayUom]);

  useEffect(() => {
    if (initialData?.recipeIngredients && !loading && uoms.length > 0) {
      const updatedIngredients = initializeIngredientQuantities([...initialData.recipeIngredients]);
      setFormData(prev => ({
        ...prev,
        recipeIngredients: updatedIngredients,
      }));
    }
  }, [initialData, loading, uoms]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value,
    }));
  };

  const handleCheckboxChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: !prev[name as keyof typeof prev],
    }));
  };

  const handleIngredientUpdate = (index: number, updates: Partial<ExtendedRecipeIngredient>) => {
    setFormData(prev => {
      const ingredient = prev.recipeIngredients[index];
      const ingredientBaseUom = uoms.find(u => u._id.toString() === ingredient.baseUomId) || yieldBaseUom;
      const { small: ingredientDisplayUom, large: ingredientCanonicalUom } = getDisplayUnits(uoms, ingredientBaseUom);

      // If large quantity changed, recalculate small quantity
      if (updates.largeQuantity !== undefined) {
        const smallQuantity = convertUOMValue(updates.largeQuantity, ingredientCanonicalUom, ingredientDisplayUom);
        updates.smallQuantity = smallQuantity;
      }
      // If small quantity changed, recalculate large quantity
      else if (updates.smallQuantity !== undefined) {
        const largeQuantity = convertUOMValue(updates.smallQuantity, ingredientDisplayUom, ingredientCanonicalUom);
        updates.largeQuantity = largeQuantity;
        updates.quantity = largeQuantity; // Update canonical quantity
      }

      return {
        ...prev,
        recipeIngredients: prev.recipeIngredients.map((ing, i) => 
          i === index ? { ...ing, ...updates } : ing
        ),
      };
    });
  };

  const handleAddIngredient = () => {
    if (!uoms.length) return;
    
    const baseUom = uoms.find(u => u.baseType === 'mass' && u.system === 'metric' && u.factorToCanonical === 1);
    if (!baseUom) return;
    
    const { small: displayUom, large: canonicalUom } = getDisplayUnits(uoms, baseUom);
    if (!displayUom || !canonicalUom) return;

    const newIngredient: RecipeIngredient = {
      ingredientName: '',
      quantity: 0,
      largeQuantity: 0,
      smallQuantity: 0,
      baseUomId: canonicalUom._id.toString(),
      preferredUomId: displayUom._id.toString(),
      baseUomShortCode: canonicalUom.shortCode,
      smallUomShortCode: displayUom.shortCode,
      largeUomShortCode: canonicalUom.shortCode,
      ingredientId: '',
      recipesId: null,
      isSubRecipe: false,
    };

    setFormData(prev => ({
      ...prev,
      recipeIngredients: [...(prev.recipeIngredients || []), newIngredient],
    }));
  };

  const handleRemoveIngredient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      recipeIngredients: prev.recipeIngredients.filter((_, i) => i !== index),
    }));
  };

  const initializeIngredientQuantities = (ingredients: RecipeIngredient[]): ExtendedRecipeIngredient[] => {
    return ingredients.map(ingredient => {
      const ingredientBaseUom = uoms.find(u => u._id.toString() === ingredient.baseUomId) || yieldBaseUom;
      const { small: ingredientDisplayUom, large: ingredientCanonicalUom } = getDisplayUnits(uoms, ingredientBaseUom);
      
      if (!ingredientBaseUom || !ingredientDisplayUom || !ingredientCanonicalUom) return ingredient;

      // If we have a large quantity but no small quantity
      if (ingredient.largeQuantity !== undefined && ingredient.smallQuantity === undefined) {
        ingredient.smallQuantity = convertUOMValue(ingredient.largeQuantity, ingredientCanonicalUom, ingredientDisplayUom);
      }
      // If we have a small quantity but no large quantity
      else if (ingredient.smallQuantity !== undefined && ingredient.largeQuantity === undefined) {
        ingredient.largeQuantity = convertUOMValue(ingredient.smallQuantity, ingredientDisplayUom, ingredientCanonicalUom);
      }
      // If we have neither, initialize both to 0
      else if (ingredient.largeQuantity === undefined && ingredient.smallQuantity === undefined) {
        ingredient.largeQuantity = 0;
        ingredient.smallQuantity = 0;
      }

      // Ensure UOM short codes are set
      ingredient.baseUomShortCode = ingredientCanonicalUom.shortCode;
      ingredient.smallUomShortCode = ingredientDisplayUom.shortCode;
      ingredient.largeUomShortCode = ingredientCanonicalUom.shortCode;

      return ingredient;
    });
  };

  const fetchCurrencyTax = async () => {
    if (!companyId) return;
    
    setLoadingTax(true);
    try {
      const response = await fetch(`/api/currency-taxes?companyId=${companyId}`, {
        headers: {
          'company-id': companyId,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch currency tax settings');
      }
      const data = await response.json();
      setCurrencyTax(data);
      
      // Update form data with default tax settings
      if (data && formData.sellingOptions) {
        setFormData(prev => ({
          ...prev,
          sellingOptions: prev.sellingOptions.map(option => ({
            ...option,
            taxRate: data.vatRateOnSales,
            taxCategory: data.defaultVatCategory,
          }))
        }));
      }
    } catch (err) {
      setTaxError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoadingTax(false);
    }
  };

  useEffect(() => {
    if (formData.canBeSold) {
      fetchCurrencyTax();
    }
  }, [companyId, formData.canBeSold]);

  const handleSellingDetailsChange = (optionId: string, field: keyof RecipeSellingOption, value: any) => {
    setFormData(prev => {
      const updatedOptions = prev.sellingOptions.map(option => {
        if (option.id !== optionId) return option;

        const updatedOption = {
          ...option,
          [field]: value
        };

        // Recalculate prices based on tax rate
        if (field === 'priceWithoutTax' || field === 'taxRate') {
          const priceWithoutTax = field === 'priceWithoutTax' ? value : updatedOption.priceWithoutTax;
          const taxRate = field === 'taxRate' ? value : updatedOption.taxRate;
          updatedOption.priceWithTax = Number((priceWithoutTax * (1 + taxRate / 100)).toFixed(2));
        } else if (field === 'priceWithTax') {
          const taxRate = updatedOption.taxRate;
          updatedOption.priceWithoutTax = Number((value / (1 + taxRate / 100)).toFixed(2));
        }

        return updatedOption;
      });

      return {
        ...prev,
        sellingOptions: updatedOptions
      };
    });
  };

  const handleAddSellingOption = () => {
    const newOption: RecipeSellingOption = {
      id: crypto.randomUUID(),
      unitOfSelling: '',
      priceWithoutTax: 0,
      priceWithTax: 0,
      taxRate: currencyTax?.vatRateOnSales || 0,
      taxCategory: currencyTax?.defaultVatCategory || 'STANDARD',
      conversionFactor: 1,
      visibility: {
        type: 'ALL_LOCATIONS',
        externalAccess: false
      }
    };

    setFormData(prev => ({
      ...prev,
      sellingOptions: [...prev.sellingOptions, newOption]
    }));
  };

  const handleRemoveSellingOption = (optionId: string) => {
    setFormData(prev => ({
      ...prev,
      sellingOptions: prev.sellingOptions.filter(option => option.id !== optionId)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Transform data for API
    const transformedData = {
      ...formData,
      baseYieldUOM: formData.baseYieldUOMId, // Map to the correct field name
      baseYieldUOMId: formData.baseYieldUOMId, // Keep for frontend compatibility
      recipeIngredients: formData.recipeIngredients.map(ingredient => {
        // Handle ingredientId based on whether it's a sub-recipe or not
        let ingredientId = null;
        if (!ingredient.isSubRecipe && typeof ingredient.ingredientId === 'string' && ingredient.ingredientId) {
          ingredientId = ingredient.ingredientId;
        }

        // Handle recipesId for sub-recipes
        let recipesId = null;
        if (ingredient.isSubRecipe && typeof ingredient.recipesId === 'string' && ingredient.recipesId) {
          recipesId = ingredient.recipesId;
        }

        return {
          ingredientName: ingredient.ingredientName,
          quantity: Number(ingredient.quantity || 0),
          unitOfMeasure: ingredient.unitOfMeasure,
          ingredientId,
          recipesId,
          isSubRecipe: Boolean(ingredient.isSubRecipe),
          smallQuantity: ingredient.smallQuantity ? Number(ingredient.smallQuantity) : undefined,
          largeQuantity: ingredient.largeQuantity ? Number(ingredient.largeQuantity) : undefined
        };
      }),
      sellingDetails: formData.sellingOptions,
      companyId
    };

    onSubmit(transformedData);
  };

  if (loading) return <div className="p-4">Loading...</div>;
  if (error) return <div className="p-4 text-red-600">Error loading UOMs: {error}</div>;
  if (!yieldBaseUom || !yieldDisplayUom) return <div className="p-4">Loading UOMs...</div>;

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information - Always visible */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Basic Information</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Recipe Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter recipe name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter recipe description"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="Category">Category</Label>
            <Input
              id="Category"
              name="Category"
              value={formData.Category}
              onChange={handleChange}
              placeholder="Enter category"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="yield">Yield</Label>
              <Input
                id="yield"
                name="yield"
                type="number"
                value={formData.yield}
                onChange={handleChange}
                placeholder="Enter yield amount"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="baseYieldUOMId">Yield Unit</Label>
              <Select
                value={formData.baseYieldUOMId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, baseYieldUOMId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select unit" />
                </SelectTrigger>
                <SelectContent>
                  {uoms.map((uom) => (
                    <SelectItem key={uom._id.toString()} value={uom._id.toString()}>
                      {uom.name} ({uom.shortCode})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="stockable"
                checked={formData.stockable}
                onCheckedChange={() => handleCheckboxChange('stockable')}
              />
              <Label htmlFor="stockable">Stockable</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isSubRecipe"
                checked={formData.isSubRecipe}
                onCheckedChange={() => handleCheckboxChange('isSubRecipe')}
              />
              <Label htmlFor="isSubRecipe">Sub Recipe</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="canBeSold"
                checked={formData.canBeSold}
                onCheckedChange={() => handleCheckboxChange('canBeSold')}
              />
              <Label htmlFor="canBeSold">Can Be Sold</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Section */}
      <div className="mt-8">
        <Tabs defaultValue="ingredients">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="ingredients" className="flex items-center gap-2">
              <Utensils className="h-4 w-4" />
              Ingredients & Costing
            </TabsTrigger>
            {formData.canBeSold && (
              <TabsTrigger value="selling" className="flex items-center gap-2">
                <ShoppingBag className="h-4 w-4" />
                Selling Options
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="ingredients">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <h3 className="text-lg font-semibold">Recipe Ingredients</h3>
                <Button
                  type="button"
                  onClick={handleAddIngredient}
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Ingredient
                </Button>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Small Quantity</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Small Unit</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Large Quantity</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Large Unit</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {formData.recipeIngredients.map((ingredient, index) => (
                        <RecipeIngredientInput
                          key={index}
                          index={index}
                          ingredient={ingredient}
                          onUpdate={handleIngredientUpdate}
                          onRemove={() => handleRemoveIngredient(index)}
                          companyId={companyId}
                          baseUom={{ shortCode: ingredient.unitOfMeasure?.shortCode || 'g' }}
                          displayUom={{ shortCode: ingredient.unitOfMeasure?.shortCode || 'g' }}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {formData.canBeSold && (
            <TabsContent value="selling">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-2xl font-semibold">Selling Options</h3>
                  <Button 
                    onClick={handleAddSellingOption}
                    className="bg-orange-600 hover:bg-orange-700 text-white rounded-full px-6"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Selling Option
                  </Button>
                </div>

                {formData.sellingOptions.map((option, index) => (
                  <div key={option.id} className="bg-white rounded-lg border p-6 space-y-6">
                    <div className="flex justify-between items-center">
                      <h4 className="text-xl font-medium">Selling Option {index + 1}</h4>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveSellingOption(option.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-5 w-5" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-8">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Unit of Selling</Label>
                        <SellingUnitInput
                          id={`selling-unit-${index}`}
                          value={option.unitOfSelling}
                          onChange={(value) => handleSellingDetailsChange(option.id, 'unitOfSelling', value)}
                          uoms={uoms}
                          showLabel={false}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Conversion Factor</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={option.conversionFactor}
                          onChange={(e) => handleSellingDetailsChange(option.id, 'conversionFactor', parseFloat(e.target.value))}
                        />
                        {option.unitOfSelling && (
                          <p className="text-sm text-gray-500 mt-1">
                            1 {uoms.find(u => u._id.toString() === option.unitOfSelling)?.shortCode || ''} = {option.conversionFactor} {uoms.find(u => u._id.toString() === formData.baseYieldUOMId)?.shortCode || ''}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-8">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Price (excl. Tax)</Label>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            value={option.priceWithoutTax}
                            onChange={(e) => handleSellingDetailsChange(option.id, 'priceWithoutTax', parseFloat(e.target.value))}
                            className="w-full"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Tax Rate (%)</Label>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.1"
                            value={option.taxRate}
                            onChange={(e) => handleSellingDetailsChange(option.id, 'taxRate', parseFloat(e.target.value))}
                            className="w-full"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Price (incl. Tax)</Label>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            value={option.priceWithTax}
                            onChange={(e) => handleSellingDetailsChange(option.id, 'priceWithTax', parseFloat(e.target.value))}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4 mt-4">
                      <h5 className="text-sm font-medium mb-4">Visibility & Access</h5>
                      
                      <div className="space-y-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-[200px]">
                            <Select
                              value={option.visibility.type}
                              onValueChange={(value: VisibilityType) => 
                                handleSellingDetailsChange(option.id, 'visibility', {
                                  ...option.visibility,
                                  type: value,
                                  locations: value === 'SPECIFIC_LOCATIONS' ? [] : undefined,
                                  externalAccess: value === 'EXTERNAL_ONLY' ? true : option.visibility.externalAccess
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="ALL_LOCATIONS">All Locations</SelectItem>
                                <SelectItem value="SPECIFIC_LOCATIONS">Specific Locations</SelectItem>
                                <SelectItem value="EXTERNAL_ONLY">External Only</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`external-access-${option.id}`}
                              checked={option.visibility.externalAccess}
                              onCheckedChange={(checked) => 
                                handleSellingDetailsChange(option.id, 'visibility', {
                                  ...option.visibility,
                                  externalAccess: checked as boolean
                                })
                              }
                              disabled={option.visibility.type === 'EXTERNAL_ONLY'}
                            />
                            <Label 
                              htmlFor={`external-access-${option.id}`}
                              className={option.visibility.type === 'EXTERNAL_ONLY' ? "text-gray-400" : ""}
                            >
                              Available for External Customers
                            </Label>
                          </div>
                        </div>

                        {option.visibility.type === 'SPECIFIC_LOCATIONS' && (
                          <div className="pl-4 border-l-2 border-gray-200">
                            <Label className="text-sm mb-2">Select Locations</Label>
                            <LocationMultiSelect
                              companyId={companyId}
                              selectedLocations={option.visibility.locations || []}
                              onChange={(locations) => 
                                handleSellingDetailsChange(option.id, 'visibility', {
                                  ...option.visibility,
                                  locations
                                })
                              }
                              requireSelection={true}
                            />
                            {(!option.visibility.locations || option.visibility.locations.length === 0) && (
                              <p className="text-sm text-amber-600 mt-2">
                                Please select at least one location
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {formData.sellingOptions.length === 0 && (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <p className="text-gray-500">No selling options added yet.</p>
                    <p className="text-gray-500">Click &quot;Add Selling Option&quot; to get started.</p>
                  </div>
                )}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>

      <div className="flex justify-end space-x-4">
        <Button type="submit" className="bg-orange-600 hover:bg-orange-700 text-white">
          Save Recipe
        </Button>
      </div>
    </form>
  );
}
