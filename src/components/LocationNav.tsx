'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import {
  LayoutDashboard,
  ShoppingCart,
  ClipboardList,
  Package,
  History,
  Building2,
  Boxes,
  ClipboardCheck,
} from 'lucide-react';

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  isExternal?: boolean;
}

export function LocationNav() {
  const { userData } = useRequireCompanyUser();
  const pathname = usePathname();
  const router = useRouter();
  const { selectedLocation, setSelectedLocation } = useLocation();

  // Extract companyId from the pathname
  const pathParts = pathname.split('/');
  const companyIdIndex = pathParts.indexOf('company') + 1;
  const companyId = pathParts[companyIdIndex];

  // Use the actual locationId from context
  const locationId = selectedLocation?._id;

  const handleReturnToHQ = () => {
    // Clear location from context
    setSelectedLocation(null);
    
    // Clear all location-related data from localStorage
    const allKeys = Object.keys(localStorage);
    allKeys.forEach(key => {
      if (key.startsWith('location_') || key === 'lastSelectedLocation') {
        localStorage.removeItem(key);
      }
    });
    
    console.log('Debug - Cleared location data, returning to HQ');
    router.push(`/company/${companyId}/dashboard`);
  };

  const navItems: NavItem[] = [
    {
      path: `/company/${companyId}/dashboard`,
      label: 'Return to HQ',
      icon: <Building2 className="h-5 w-5" />,
      isExternal: true,
    },
    {
      path: 'dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      path: 'inventory-items',
      label: 'Inventory Items',
      icon: <Boxes className="h-5 w-5" />,
    },
    {
      path: 'stock-counts',
      label: 'Stock Counts',
      icon: <ClipboardCheck className="h-5 w-5" />,
    },
    {
      path: 'items-to-order',
      label: 'Items to Order',
      icon: <Package className="h-5 w-5" />,
    },
    {
      path: 'test',
      label: 'Test',
      icon: <ClipboardList className="h-5 w-5" />,
    },
    {
      path: 'orders',
      label: 'Orders',
      icon: <ClipboardList className="h-5 w-5" />,
    },
    {
      path: 'place-order',
      label: 'Place Order',
      icon: <ShoppingCart className="h-5 w-5" />,
    },
    {
      path: 'order-history',
      label: 'Order History',
      icon: <History className="h-5 w-5" />,
    },
  ];

  if (!userData || !locationId) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      <nav
        className={`
          relative
          px-4
          pb-4
          flex flex-col
          h-full
          bg-background
          border-r
        `}
      >
        <div className="space-y-4 py-4">
          {navItems.map((item) => {
            const isActive = item.isExternal
              ? false
              : pathname.includes(`/company/${companyId}/location/${locationId}/${item.path}`);

            return (
              <div
                key={item.path}
                onClick={item.label === 'Return to HQ' ? handleReturnToHQ : undefined}
              >
                {item.label === 'Return to HQ' ? (
                  <div
                    className={`
                      flex
                      items-center
                      gap-x-2
                      text-sm
                      font-[500]
                      px-3
                      py-2
                      rounded-lg
                      hover:bg-accent
                      hover:text-accent-foreground
                      cursor-pointer
                      transition-all
                      duration-200
                    `}
                  >
                    {item.icon}
                    {item.label}
                  </div>
                ) : (
                  <Link
                    href={`/company/${companyId}/location/${locationId}/${item.path}`}
                    className={`
                      flex
                      items-center
                      gap-x-2
                      text-sm
                      font-[500]
                      px-3
                      py-2
                      rounded-lg
                      transition-all
                      duration-200
                      ${
                        isActive
                          ? 'text-primary bg-primary/10'
                          : 'hover:bg-accent hover:text-accent-foreground'
                      }
                    `}
                  >
                    {item.icon}
                    {item.label}
                  </Link>
                )}
              </div>
            );
          })}
        </div>
      </nav>
    </div>
  );
}
