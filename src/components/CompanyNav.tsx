// src/components/CompanyNav.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import {
  Package,
  Users,
  Warehouse,
  ShoppingCart,
  CheckSquare,
  Utensils,
  Tags,
  LayoutDashboard,
  Truck,
  BoxesIcon,
  ClipboardList,
  Factory,
  Settings,
  Store
} from 'lucide-react';

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  roles: Array<'admin' | 'owner' | 'manager' | 'storekeeper'>;
}

interface NavItemWithChildren {
  name: string;
  icon: React.ReactNode;
  items: Array<{
    name: string;
    path: string;
    icon: React.ReactNode;
  }>;
  roles: Array<'admin' | 'owner' | 'manager' | 'storekeeper'>;
}

export function CompanyNav() {
  const { userData } = useRequireCompanyUser();
  const pathname = usePathname();

  const navItems: (NavItem | NavItemWithChildren)[] = [
    {
      path: 'dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    },
    {
      path: 'locations',
      label: 'Locations',
      icon: <Warehouse className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager']
    },
    {
      name: 'Products',
      icon: <Package className="h-5 w-5" />,
      items: [
        {
          name: 'Products',
          path: 'products',
          icon: <Package className="h-5 w-5" />,
        },
        {
          name: 'Recipes',
          path: 'products/recipes',
          icon: <Utensils className="h-5 w-5" />,
        },
        {
          name: 'Selling Options',
          path: 'products/selling-options',
          icon: <Tags className="h-5 w-5" />,
        },
      ],
      roles: ['admin', 'owner', 'manager']
    },
    {
      path: 'suppliers',
      label: 'Suppliers',
      icon: <Truck className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager']
    },
    {
      path: 'inventory',
      label: 'Inventory',
      icon: <BoxesIcon className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    },
    {
      path: 'orders',
      label: 'Orders',
      icon: <ClipboardList className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    },
    {
      path: 'shop-portal',
      label: 'Shop Portal',
      icon: <Store className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    },
    {
      path: 'production',
      label: 'Production Planning',
      icon: <Factory className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager']
    },
    {
      path: 'users',
      label: 'Users',
      icon: <Users className="h-5 w-5" />,
      roles: ['admin', 'owner']
    },
    {
      path: 'settings/general',
      label: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      roles: ['admin', 'owner']
    },
    // Additional storekeeper features
    {
      path: 'inventory-count',
      label: 'Inventory Count',
      icon: <Warehouse className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    },
    {
      path: 'goods-reception',
      label: 'Goods Reception',
      icon: <ShoppingCart className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    },
    {
      path: 'orders-status',
      label: 'Orders Status',
      icon: <CheckSquare className="h-5 w-5" />,
      roles: ['admin', 'owner', 'manager', 'storekeeper']
    }
  ];

  if (!userData) {
    return null;
  } 

  const filteredNavItems = userData?.role ? navItems.filter(item =>
    (item as NavItem).roles?.includes(userData.role as 'admin' | 'owner' | 'manager' | 'storekeeper')
  ) : [];

  if (!userData?.role) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      <nav
        className={`
          relative
          px-4
          pb-4
          flex flex-col
          h-full
          bg-background
          border-r
        `}
      >
        <div className="space-y-4">
          {filteredNavItems.map((item) => {
            if ((item as NavItemWithChildren).items) {
              const navItem = item as NavItemWithChildren;
              return (
                <div key={navItem.name} className="space-y-2">
                  <div className="flex items-center px-3 py-1 text-sm font-medium text-muted-foreground">
                    {navItem.icon}
                    <span className="ml-3">{navItem.name}</span>
                  </div>
                  {navItem.items.map((childItem) => (
                    <Link
                      key={childItem.path}
                      href={`/company/${userData.companyId}/${childItem.path}`}
                      className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                        pathname.includes(childItem.path)
                          ? 'bg-primary/10 text-primary'
                          : 'hover:bg-primary/5'
                      }`}
                    >
                      {childItem.icon}
                      <span>{childItem.name}</span>
                    </Link>
                  ))}
                </div>
              );
            } else {
              const navItem = item as NavItem;
              const isActive = pathname.includes(navItem.path);
              return (
                <Link
                  key={navItem.path}
                  href={`/company/${userData.companyId}/${navItem.path}`}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary'
                      : 'hover:bg-primary/5'
                  }`}
                >
                  {navItem.icon}
                  <span>{navItem.label}</span>
                </Link>
              );
            }
          })}
        </div>
      </nav>
    </div>
  );
}
