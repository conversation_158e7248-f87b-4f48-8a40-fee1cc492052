import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface Location {
  _id: string;
  name: string;
}

interface LocationMultiSelectProps {
  companyId: string;
  value?: string[];
  selectedLocations?: string[];
  onChange: (locations: string[]) => void;
  requireSelection?: boolean;
  placeholder?: string;
}

export function LocationMultiSelect({
  companyId,
  value,
  selectedLocations,
  onChange,
  requireSelection = false,
  placeholder = "Select locations..."
}: LocationMultiSelectProps) {
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Use either value or selectedLocations prop
  const selectedLocationIds = value || selectedLocations || [];

  useEffect(() => {
    const fetchLocations = async () => {
      try {
        setError(null);
        const response = await fetch(`/api/locations`, {
          headers: {
            'company-id': companyId,
            'Content-Type': 'application/json',
          }
        });
        
        if (!response.ok) {
          const clonedResponse = response.clone();
          const errorData = await clonedResponse.json().catch(() => ({ 
            message: `HTTP error! status: ${response.status}`
          }));
          throw new Error(errorData.message || 'Failed to fetch locations');
        }
        
        const data = await response.json();
        setLocations(data);
      } catch (error) {
        console.error('Error fetching locations:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch locations');
      } finally {
        setLoading(false);
      }
    };

    fetchLocations();
  }, [companyId]);

  const filteredLocations = locations.filter(location =>
    location.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLocationToggle = (locationId: string) => {
    const isSelected = selectedLocationIds.includes(locationId);
    let newSelected: string[];

    if (isSelected) {
      // If this is the last selected location and selection is required, don't remove it
      if (requireSelection && selectedLocationIds.length === 1) {
        return;
      }
      newSelected = selectedLocationIds.filter(id => id !== locationId);
    } else {
      newSelected = [...selectedLocationIds, locationId];
    }

    onChange(newSelected);
  };

  if (loading) {
    return <div className="text-sm text-muted-foreground">Loading locations...</div>;
  }

  if (error) {
    return <div className="text-sm text-red-500">{error}</div>;
  }

  return (
    <Card className="p-4">
      <div className="relative mb-4">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-8"
        />
      </div>

      <ScrollArea className="h-[200px] pr-4">
        <div className="space-y-2">
          {filteredLocations.map((location) => (
            <div
              key={location._id}
              className="flex items-center space-x-2"
            >
              <Checkbox
                id={location._id}
                checked={selectedLocationIds.includes(location._id)}
                onCheckedChange={() => handleLocationToggle(location._id)}
              />
              <label
                htmlFor={location._id}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                {location.name}
              </label>
            </div>
          ))}
        </div>
      </ScrollArea>

      <div className="text-sm text-muted-foreground mt-2">
        {selectedLocationIds.length} location{selectedLocationIds.length === 1 ? '' : 's'} selected
        {requireSelection && selectedLocationIds.length === 0 && (
          <span className="text-red-500 ml-1">(at least one required)</span>
        )}
      </div>
    </Card>
  );
}
