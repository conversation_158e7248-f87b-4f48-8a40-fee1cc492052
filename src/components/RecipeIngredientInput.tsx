'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { IngredientCombobox } from './IngredientCombobox';
import { UOM } from '@/utils/uomConversion';

interface RecipeIngredientInputProps {
  index: number;
  ingredient: {
    ingredientName: string;
    smallQuantity?: number;
    smallUomShortCode?: string;
    largeQuantity?: number;
    largeUomShortCode?: string;
    isSubRecipe?: boolean;
    baseUomId?: string;
  };
  baseUom: { shortCode: string };
  displayUom: { shortCode: string };
  onUpdate: (index: number, update: Partial<any>) => void;
  onRemove: (index: number) => void;
  companyId: string;
}

export function RecipeIngredientInput({
  index,
  ingredient,
  baseUom,
  displayUom,
  onUpdate,
  onRemove,
  companyId,
}: RecipeIngredientInputProps) {
  const handleSmallQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove any non-numeric characters except decimal points and commas
    const cleanValue = e.target.value.replace(/[^\d.,]/g, '');
    // Convert to number (removing commas)
    const smallQuantity = parseFloat(cleanValue.replace(/,/g, '')) || 0;
    onUpdate(index, { 
      smallQuantity,
      smallUomShortCode: displayUom.shortCode,
    });
  };

  const handleLargeQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove any non-numeric characters except decimal points and commas
    const cleanValue = e.target.value.replace(/[^\d.,]/g, '');
    // Convert to number (removing commas)
    const largeQuantity = parseFloat(cleanValue.replace(/,/g, '')) || 0;
    onUpdate(index, { 
      quantity: largeQuantity, // Update the canonical quantity
      largeQuantity,
      largeUomShortCode: baseUom.shortCode,
    });
  };

  const formatSmallQuantity = (value: number | undefined) => {
    if (typeof value !== 'number') return '';
    return value.toLocaleString(undefined, { 
      minimumFractionDigits: 1, 
      maximumFractionDigits: 1,
      useGrouping: true
    });
  };

  const formatLargeQuantity = (value: number | undefined) => {
    if (typeof value !== 'number') return '';
    return value.toLocaleString(undefined, { 
      minimumFractionDigits: 3, 
      maximumFractionDigits: 3,
      useGrouping: true
    });
  };

  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap">
        <IngredientCombobox
          value={ingredient.ingredientName}
          onChange={(value) => onUpdate(index, value)}
          companyId={companyId}
          placeholder="Enter ingredient name"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <Input
          type="text"
          inputMode="decimal"
          value={formatSmallQuantity(ingredient.smallQuantity)}
          onChange={handleSmallQuantityChange}
          placeholder="0.0"
          className="w-32"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {ingredient.smallUomShortCode || displayUom.shortCode}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <Input
          type="text"
          inputMode="decimal"
          value={formatLargeQuantity(ingredient.largeQuantity)}
          onChange={handleLargeQuantityChange}
          placeholder="0.000"
          className="w-32"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {ingredient.largeUomShortCode || baseUom.shortCode}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {ingredient.isSubRecipe ? 'Sub Recipe' : 'Ingredient'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <Button
          variant="ghost"
          size="icon"
          className="text-red-600 hover:text-red-800 hover:bg-red-100"
          onClick={() => onRemove(index)}
        >
          <Trash2 className="h-5 w-5" />
        </Button>
      </td>
    </tr>
  );
}
