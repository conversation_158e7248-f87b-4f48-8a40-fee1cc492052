'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { UOM } from '@/utils/uomConversion';
import { Label } from '@/components/ui/label';

interface UOMInputProps {
  baseValue: number;
  baseUom: UOM;
  displayUom: UOM;
  onChange: (values: { baseValue: number }) => void;
  showBothValues?: boolean;
}

export function UOMInput({
  baseValue = 0,
  baseUom,
  displayUom,
  onChange,
  showBothValues = false,
}: UOMInputProps) {
  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value) || 0;
    onChange({ baseValue: newValue });
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <Input
          type="number"
          value={baseValue || ''}
          onChange={handleValueChange}
          className="flex-1"
          min={0}
          step="0.01"
        />
        <div className="text-sm text-gray-600 min-w-[80px]">
          {displayUom?.shortCode || baseUom?.shortCode || ''}
        </div>
      </div>
      {showBothValues && typeof baseValue === 'number' && baseUom && (
        <p className="text-sm text-gray-500 mt-1">
          Base value: {baseValue.toFixed(2)} {baseUom.shortCode}
        </p>
      )}
    </div>
  );
}
