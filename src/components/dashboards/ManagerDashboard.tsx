import React from 'react';
import { useCompanyContext } from '../../lib/contexts/CompanyContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Loader2 } from 'lucide-react';

export function ManagerDashboard() {
  const { companyData, locationId, loading } = useCompanyContext();

  if (loading || !locationId) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div
      data-testid={window.matchMedia('(max-width: 768px)').matches ? 'dashboard-mobile' : 'dashboard-desktop'}
      className="space-y-6"
    >
      <h1 className="text-3xl font-bold">Location Dashboard</h1>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Location Inventory */}
        <Card>
          <CardHeader>
            <CardTitle>Location Inventory</CardTitle>
            <CardDescription>Current stock levels</CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: Implement location inventory metrics */}
            <p>Loading inventory data...</p>
          </CardContent>
        </Card>

        {/* Local Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Local Orders</CardTitle>
            <CardDescription>Orders for this location</CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: Implement local order metrics */}
            <p>Loading order data...</p>
          </CardContent>
        </Card>

        {/* Production Schedule */}
        <Card>
          <CardHeader>
            <CardTitle>Production Schedule</CardTitle>
            <CardDescription>Today&apos;s production plan</CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: Implement production schedule */}
            <p>Loading schedule data...</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
