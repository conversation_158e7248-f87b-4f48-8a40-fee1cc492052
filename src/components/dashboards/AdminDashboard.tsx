//src/components/dashboards/AdminDashboard.tsx
import React, { useState } from 'react';
import LocationManagement from '@/components/admin/LocationManagement';
import UserManagement from '@/components/admin/UserManagement';
import RoleManagement from '@/components/admin/RoleManagement';

const tabs = [
  { label: 'Locations', component: <LocationManagement /> },
  { label: 'Users', component: <UserManagement /> },
  { label: 'Roles', component: <RoleManagement /> },
];

export function AdminDashboard() {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
      <div className="flex space-x-4 mb-6 border-b pb-2">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`px-4 py-2 ${activeTab === index ? 'border-b-2 border-primary text-primary' : ''}`}
            onClick={() => setActiveTab(index)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div>{tabs[activeTab].component}</div>
    </div>
  );
}
