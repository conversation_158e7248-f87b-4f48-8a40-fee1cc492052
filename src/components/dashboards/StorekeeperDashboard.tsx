import React from 'react';
import { useCompanyContext } from '../../lib/contexts/CompanyContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Loader2 } from 'lucide-react';

export function StorekeeperDashboard() {
  const { companyData, locationId, loading } = useCompanyContext();

  if (loading || !locationId) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Storekeeper Dashboard</h1>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Pending Receipts */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Receipts</CardTitle>
            <CardDescription>Awaiting goods reception</CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: Implement pending receipts list */}
            <p>Loading receipts data...</p>
          </CardContent>
        </Card>

        {/* Today's Inventory Counts */}
        <Card>
          <CardHeader>
            <CardTitle>Today&apos;s Counts</CardTitle>
            <CardDescription>Scheduled inventory counts</CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: Implement inventory count schedule */}
            <p>Loading count data...</p>
          </CardContent>
        </Card>

        {/* Outstanding Tasks */}
        <Card>
          <CardHeader>
            <CardTitle>Outstanding Tasks</CardTitle>
            <CardDescription>Tasks requiring attention</CardDescription>
          </CardHeader>
          <CardContent>
            {/* TODO: Implement task list */}
            <p>Loading task data...</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
