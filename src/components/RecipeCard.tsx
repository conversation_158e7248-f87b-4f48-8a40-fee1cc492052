import React from 'react';
import { Card } from '@/components/ui/card';
import { IRecipe } from '@/models/Recipe';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useUOMs } from '@/hooks/useUOMs';

interface RecipeCardProps {
  recipe: IRecipe;
  isSelected: boolean;
  onSelect: (recipeId: string) => void;
}

export function RecipeCard({ recipe, isSelected, onSelect }: RecipeCardProps) {
  const router = useRouter();
  const params = useParams();
  const companyId = params.companyId as string;
  const { uoms } = useUOMs(companyId);

  const baseYieldUom = recipe.baseYieldUOM && typeof recipe.baseYieldUOM === 'object' 
    ? recipe.baseYieldUOM 
    : uoms.find(u => u._id.toString() === recipe.baseYieldUOM);

  // Handle yield display
  const displayYield = baseYieldUom 
    ? `${recipe.yield} ${baseYieldUom.shortCode}`
    : `${recipe.yield}`;

  const handleClick = (e: React.MouseEvent) => {
    // Prevent navigation when clicking checkbox
    if ((e.target as HTMLElement).closest('.checkbox-area')) {
      return;
    }
    router.push(`/company/${companyId}/products/recipes/${recipe._id}`);
  };

  return (
    <Card 
      className="hover:shadow-md transition-shadow cursor-pointer p-4"
      onClick={handleClick}
    >
      <div className="flex items-center space-x-4">
        <div className="checkbox-area">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelect(recipe._id.toString())}
            className="mt-1"
          />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold truncate">{recipe.name}</h3>
              <p className="text-sm text-gray-500 truncate">{recipe.description}</p>
            </div>
            <div className="flex flex-col items-end space-y-1">
              <Badge variant={recipe.isSubRecipe ? "secondary" : "default"}>
                {recipe.isSubRecipe ? "Sub Recipe" : "Recipe"}
              </Badge>
              {recipe.Category && (
                <Badge variant="outline">{recipe.Category}</Badge>
              )}
            </div>
          </div>
          
          <div className="mt-2 flex justify-between items-center">
            <div className="text-sm text-gray-600">
              Yield: {displayYield}
            </div>
            <div className="text-sm text-gray-600">
              {recipe.recipeIngredients.length} ingredients
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}