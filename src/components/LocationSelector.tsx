import React from 'react';
import { useCompanyContext } from '../lib/contexts/CompanyContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './ui/select';
import { MapPin } from 'lucide-react';

export function LocationSelector() {
  const { companyData, locationId, setLocationId, loading } = useCompanyContext();

  if (loading || !companyData) {
    return null;
  }

  // If there's only one location, no need to show the selector
  if (companyData.locations.length === 1) {
    const location = companyData.locations[0];
    if (!locationId) {
      setLocationId(location.id);
    }
    return (
      <div className="flex items-center space-x-2 px-4 py-2 text-sm text-muted-foreground border-b">
        <MapPin className="h-4 w-4" />
        <span>{location.name}</span>
      </div>
    );
  }

  return (
    <div className="px-4 py-2 border-b">
      <Select
        value={locationId}
        onValueChange={setLocationId}
        defaultValue={companyData.locations[0]?.id}
      >
        <SelectTrigger className="w-full">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4" />
            <SelectValue placeholder="Select location" />
          </div>
        </SelectTrigger>
        <SelectContent>
          {companyData.locations.map((location) => (
            <SelectItem key={location.id} value={location.id}>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>{location.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
