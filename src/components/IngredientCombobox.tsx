import React from 'react';
import { Check, ChevronsUpDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useRequireCompanyUser } from '@/lib/auth';

interface IngredientOption {
  id: string;
  name: string;
  baseUomId: string;
  baseUomName: string;
  baseUomShortCode: string;
  type: 'ingredient' | 'recipe';
}

interface IngredientComboboxProps {
  value?: string;
  placeholder?: string;
  onChange: (value: { 
    ingredientName: string; 
    baseUomId: string; 
    isSubRecipe: boolean;
    itemId: string;
  }) => void;
  companyId: string;
}

export function IngredientCombobox({
  value,
  placeholder = 'Select ingredient...',
  onChange,
  companyId,
}: IngredientComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [options, setOptions] = React.useState<IngredientOption[]>([]);
  const [loading, setLoading] = React.useState(false);
  const { userData } = useRequireCompanyUser();

  React.useEffect(() => {
    const searchIngredients = async () => {
      if (!searchQuery || !userData?.companyId) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(
          `/api/company/${companyId}/search?${new URLSearchParams({
            query: searchQuery
          })}`,
          {
            headers: {
              'company-id': companyId
            }
          }
        );

        if (!response.ok) {
          console.error('Failed to search:', await response.text());
          setOptions([]);
          return;
        }

        const data = await response.json();
        console.log('Search results:', data);
        
        // Map the ingredients and recipes to a common format
        const combinedResults: IngredientOption[] = [
          ...data.ingredients.map((i: any) => ({
            id: i._id,
            name: i.name,
            baseUomId: i.baseUomId._id,
            baseUomName: i.baseUomId.name,
            baseUomShortCode: i.baseUomId.shortCode,
            type: 'ingredient' as const
          })),
          ...data.recipes.map((r: any) => ({
            id: r._id,
            name: r.name,
            baseUomId: r.baseYieldUomId._id,
            baseUomName: r.baseYieldUomId.name,
            baseUomShortCode: r.baseYieldUomId.shortCode,
            type: 'recipe' as const
          }))
        ];
        
        setOptions(combinedResults);
      } catch (error) {
        console.error('Error searching:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimeout = setTimeout(searchIngredients, 300);
    return () => clearTimeout(debounceTimeout);
  }, [searchQuery, companyId, userData?.companyId]);

  const handleSelect = (option: IngredientOption) => {
    onChange({
      ingredientName: option.name,
      baseUomId: option.baseUomId,
      isSubRecipe: option.type === 'recipe',
      itemId: option.id
    });
    setOpen(false);
    setSearchQuery('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {value || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <div className="flex items-center border-b px-3">
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          <Input
            placeholder="Search ingredients..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              if (!open) setOpen(true);
            }}
            className="h-9 border-0 focus-visible:ring-0"
          />
        </div>
        <ScrollArea className="max-h-[300px] overflow-auto">
          {loading ? (
            <div className="p-4 text-sm text-muted-foreground text-center">
              Loading...
            </div>
          ) : options.length === 0 ? (
            <div className="p-4 text-sm text-muted-foreground text-center">
              {searchQuery ? "No ingredients found." : "Type to search ingredients"}
            </div>
          ) : (
            <div className="p-1">
              {options.map((option) => (
                <div
                  key={option.id}
                  onClick={() => handleSelect(option)}
                  className={cn(
                    "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                    value === option.name && "bg-accent text-accent-foreground"
                  )}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.name ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span>{option.name}</span>
                  <span className="ml-2 text-xs text-muted-foreground">
                    {option.type === "recipe" ? "(Recipe)" : "(Ingredient)"} - {option.baseUomShortCode}
                  </span>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
