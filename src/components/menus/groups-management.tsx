"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { useAuth } from "@/lib/auth"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { Pencil, Plus, Trash2 } from "lucide-react"
import { Group } from "@/types/menu"

export function GroupsManagement() {
  const params = useParams()
  const { companyId } = params
  const [open, setOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const queryClient = useQueryClient()
  const { user } = useAuth()

  const { data: groups } = useQuery<Group[]>({
    queryKey: ["groups", companyId],
    queryFn: async () => {
      const response = await fetch(`/api/companies/${companyId}/groups`, {
        headers: {
          'company-id': companyId as string
        }
      })
      if (!response.ok) throw new Error("Failed to fetch groups")
      return response.json()
    },
    enabled: !!companyId && !!user
  })

  const createUpdateMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const data = {
        name: formData.get("name"),
        description: formData.get("description"),
        isGlobal: formData.get("isGlobal") === "on", // This field controls visibility across locations
      }

      const response = await fetch(
        `/api/companies/${companyId}/groups${
          editingGroup ? `/${editingGroup._id}` : ""
        }`,
        {
          method: editingGroup ? "PUT" : "POST",
          headers: {
            "Content-Type": "application/json",
            'company-id': companyId as string
          },
          body: JSON.stringify(editingGroup ? { ...data, _id: editingGroup._id } : data),
        }
      )

      if (!response.ok) throw new Error("Failed to save group")
      return response.json()
    },
    onSuccess: () => {
      toast.success(`Group ${editingGroup ? "updated" : "created"} successfully`)
      setOpen(false)
      setEditingGroup(null)
      queryClient.invalidateQueries({ queryKey: ["groups", companyId] })
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const deleteMutation = useMutation({
    mutationFn: async (groupId: string) => {
      const response = await fetch(
        `/api/companies/${companyId}/groups/${groupId}`,
        {
          method: "DELETE",
          headers: {
            'company-id': companyId as string
          }
        }
      )

      if (!response.ok) throw new Error("Failed to delete group")
      return response.json()
    },
    onSuccess: () => {
      toast.success("Group deleted successfully")
      queryClient.invalidateQueries({ queryKey: ["groups", companyId] })
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    createUpdateMutation.mutate(new FormData(e.currentTarget))
  }

  const handleDelete = async (group: Group) => {
    if (!confirm("Are you sure you want to delete this group?")) return
    deleteMutation.mutate(group._id)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingGroup(null)}>
              <Plus className="mr-2 h-4 w-4" /> Add Group
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>
                  {editingGroup ? "Edit Group" : "Create New Group"}
                </DialogTitle>
                <DialogDescription>
                  Add a new group to organize your menu items
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    defaultValue={editingGroup?.name}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    defaultValue={editingGroup?.description}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isGlobal"
                    name="isGlobal"
                    defaultChecked={editingGroup?.isGlobal}
                  />
                  <Label htmlFor="isGlobal">Available across all locations</Label>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">
                  {editingGroup ? "Update" : "Create"} Group
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {groups?.map((group) => (
          <div
            key={group._id}
            className="flex items-center justify-between p-4 border rounded-lg"
          >
            <div>
              <h3 className="font-medium">{group.name}</h3>
              <p className="text-sm text-muted-foreground">
                {group.description}
              </p>
              {group.isGlobal && (
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  All Locations
                </span>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setEditingGroup(group)
                  setOpen(true)
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDelete(group)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
