"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { useAuth } from "@/lib/auth"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { Pencil, Plus, Trash2 } from "lucide-react"
import { Category, Group } from "@/types/menu"

export function CategoriesManagement() {
  const params = useParams()
  const { companyId } = params
  const [open, setOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [selectedGroupId, setSelectedGroupId] = useState<string>("all")
  const queryClient = useQueryClient()
  const { user } = useAuth()

  const { data: groups } = useQuery<Group[]>({
    queryKey: ["groups", companyId],
    queryFn: async () => {
      const response = await fetch(`/api/companies/${companyId}/groups`, {
        headers: {
          'company-id': companyId as string
        }
      })
      if (!response.ok) throw new Error("Failed to fetch groups")
      return response.json()
    },
    enabled: !!companyId && !!user
  })

  const { data: categories } = useQuery<Category[]>({
    queryKey: ["categories", companyId, selectedGroupId],
    queryFn: async () => {
      const url = new URL(`/api/companies/${companyId}/categories`, window.location.origin)
      if (selectedGroupId && selectedGroupId !== 'all') {
        url.searchParams.set('groupId', selectedGroupId)
      }
      const response = await fetch(url, {
        headers: {
          'company-id': companyId as string
        }
      })
      if (!response.ok) throw new Error("Failed to fetch categories")
      return response.json()
    },
    enabled: !!companyId && !!user
  })

  const createUpdateMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const data = {
        name: formData.get("name"),
        description: formData.get("description"),
        groupId: formData.get("groupId"),
      }

      const response = await fetch(
        `/api/companies/${companyId}/categories${
          editingCategory ? `/${editingCategory._id}` : ""
        }`,
        {
          method: editingCategory ? "PUT" : "POST",
          headers: {
            "Content-Type": "application/json",
            'company-id': companyId as string
          },
          body: JSON.stringify(editingCategory ? { ...data, _id: editingCategory._id } : data),
        }
      )

      if (!response.ok) throw new Error("Failed to save category")
      return response.json()
    },
    onSuccess: () => {
      toast.success(`Category ${editingCategory ? "updated" : "created"} successfully`)
      setOpen(false)
      setEditingCategory(null)
      queryClient.invalidateQueries({ queryKey: ["categories", companyId] })
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const deleteMutation = useMutation({
    mutationFn: async (categoryId: string) => {
      const response = await fetch(
        `/api/companies/${companyId}/categories/${categoryId}`,
        {
          method: "DELETE",
          headers: {
            'company-id': companyId as string
          }
        }
      )

      if (!response.ok) throw new Error("Failed to delete category")
      return response.json()
    },
    onSuccess: () => {
      toast.success("Category deleted successfully")
      queryClient.invalidateQueries({ queryKey: ["categories", companyId] })
    },
    onError: (error: Error) => {
      toast.error(error.message)
    }
  })

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    createUpdateMutation.mutate(new FormData(e.currentTarget))
  }

  const handleDelete = async (category: Category) => {
    if (!confirm("Are you sure you want to delete this category?")) return
    deleteMutation.mutate(category._id)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Select value={selectedGroupId} onValueChange={setSelectedGroupId}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Filter by group" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Groups</SelectItem>
            {groups?.map((group) => (
              <SelectItem key={group._id} value={group._id}>
                {group.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingCategory(null)}>
              <Plus className="mr-2 h-4 w-4" /> Add Category
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? "Edit Category" : "Create New Category"}
                </DialogTitle>
                <DialogDescription>
                  Add a new category to organize your menu items within groups
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    defaultValue={editingCategory?.name}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    defaultValue={editingCategory?.description}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="groupId">Group</Label>
                  <Select
                    name="groupId"
                    defaultValue={editingCategory?.groupId || ""}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a group" />
                    </SelectTrigger>
                    <SelectContent>
                      {groups?.map((group) => (
                        <SelectItem key={group._id} value={group._id}>
                          {group.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={createUpdateMutation.isPending}>
                  {createUpdateMutation.isPending ? (
                    "Saving..."
                  ) : editingCategory ? (
                    "Save Changes"
                  ) : (
                    "Create Category"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories?.map((category) => (
          <div
            key={category._id}
            className="p-4 border rounded-lg space-y-2 bg-white"
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{category.name}</h3>
                <p className="text-sm text-gray-500">{category.description}</p>
                <p className="text-xs text-gray-400">
                  Group: {groups?.find(g => g._id === category.groupId)?.name}
                </p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setEditingCategory(category)
                    setOpen(true)
                  }}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDelete(category)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
