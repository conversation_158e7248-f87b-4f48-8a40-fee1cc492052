"use client"

import { useState, useEffect, useMemo } from "react"
import { useParams } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { Pencil, Plus, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, X, Search, ChevronDown } from "lucide-react"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { 
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"

// Enhanced types to match the system data models from memory

// MenuItem type based on the memory data structure
interface MenuItem {
  _id: string;  // MongoDB uses _id not id
  id?: string;
  name: string;
  description?: string;
  categoryId: string;
  type: 'DIRECT' | 'RECIPE';  // Correct enum values from the memory
  prices: {
    basePrice: number;
    vatRate: number;
    vatAmount: number;
    finalPrice: number;
  };
  status: 'active' | 'inactive';
  // For DIRECT items
  inventoryItem?: {
    itemId: string;
    unit: string;
  };
  // For RECIPE items
  recipeComponents?: RecipeComponent[];
  inventoryStatus?: 'pending' | 'linked';
}

// Define InventoryItem type based on the sellable-items API response
interface InventoryItem {
  _id?: string; // MongoDB ID (from old endpoint)
  id: string;   // Standardized ID from new endpoint
  companyId?: string;
  name: string;
  description?: string;
  type: 'ingredient' | 'recipe'; // Type from new sellable-items endpoint
  unit: string;
  unitName?: string;
  category?: string;
  // For reference only, not used in recipes directly
  currentStock?: number;
  locationId?: string;
  // For UOM options
  availableUnits?: Array<{
    id: string;
    name: string;
    shortCode: string;
    conversionFactor: number;
  }>;
}

// Define RecipeComponent type
interface RecipeComponent {
  itemId: string;
  quantity: number;
  unit: string;
  // For tracking selected UOM
  selectedUnitId?: string;
}

// Define MenuItemUpdate type
interface MenuItemUpdate {
  id: string;
  recipeComponents?: RecipeComponent[];
  inventoryStatus?: 'pending' | 'linked';
  type?: 'DIRECT' | 'RECIPE';  // Match the actual data model
}

export function MenuItemRecipesManagement() {
  const params = useParams()
  const companyId = params?.companyId as string
  
  // Location-independent recipe management
  const [editableMenuItems, setEditableMenuItems] = useState<Record<string, RecipeComponent[]>>({})
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')
  // Store currently visible menu item IDs to filter expanded state
  const [visibleMenuItemIds, setVisibleMenuItemIds] = useState<string[]>([])

  const queryClient = useQueryClient()

  // Fetch menu items with pagination
  const { data: menuItemsResponse, isLoading: menuItemsLoading, error: menuItemsError } = useQuery({
    queryKey: ["menuItems", companyId, page, pageSize],
    queryFn: async () => {
      try {
        // Include company-id header as required by your auth system
        const res = await fetch(`/api/company/${companyId}/menu/items?page=${page}&limit=${pageSize}`, {
          headers: {
            'company-id': companyId
          }
        })
        
        if (!res.ok) {
          let errorDetail = '';
          try {
            const errorText = await res.text();
            errorDetail = errorText;
            console.error("API error:", res.status, res.statusText, errorText);
          } catch (e) {
            console.error("Couldn't read error response", e);
          }
          throw new Error(`Failed to fetch menu items: ${res.status} ${errorDetail}`)
        }
        
        const data = await res.json()
        console.log("API response items:", data.items?.length || 0)
        return data
      } catch (error) {
        console.error("Error fetching menu items:", error)
        throw error
      }
    },
    enabled: !!companyId,
    retry: 0, // Don't retry while debugging
    refetchOnWindowFocus: false, // Disable refetching on window focus while debugging
  })
  
  // Validate if all recipe components exist in the branch inventory
  const validateRecipeComponents = (components: RecipeComponent[]) => {
    if (!components || components.length === 0) {
      return { valid: false, message: 'No components added' };
    }
    
    // Check if all itemIds exist in the inventory items
    const invalidComponents = components.filter(comp => {
      // Skip validation if inventory is still loading
      if (inventoryLoading || !inventoryItems) return false;
      
      // Check if the item exists in the inventory
      return !inventoryItems.some((item: any) => item.id === comp.itemId);
    });
    
    // Check if all quantities are valid
    const invalidQuantities = components.filter(comp => {
      return !comp.quantity || comp.quantity <= 0;
    });
    
    if (invalidComponents.length > 0) {
      return { 
        valid: false, 
        message: `${invalidComponents.length} component(s) not found in inventory` 
      };
    }
    
    if (invalidQuantities.length > 0) {
      return { 
        valid: false, 
        message: `${invalidQuantities.length} component(s) have invalid quantities` 
      };
    }
    
    return { valid: true, message: 'All components are valid' };
  };
  
  // Extract menu items and pagination info, debug the structure
  console.log("Menu items response:", menuItemsResponse)
  
  // Handle different possible API response structures
  const menuItems = menuItemsResponse?.items || menuItemsResponse?.data || []
  const pagination = menuItemsResponse?.pagination || menuItemsResponse?.meta || { total: 0, page: 1, pages: 1 }
  const totalPages = pagination.pages || Math.ceil((pagination.total || 0) / pageSize)

  // Fetch sellable inventory items using the new API endpoint


  const { data: inventoryItems = [], isLoading: inventoryLoading } = useQuery<InventoryItem[]>({
    queryKey: ["sellable-items", companyId],
    queryFn: async () => {
      try {
        console.log("Fetching sellable inventory items for companyId:", companyId);
        const res = await fetch(`/api/company/${companyId}/inventory/sellable-items?limit=100`, {
          headers: {
            'company-id': companyId
          }
        });
        
        if (!res.ok) {
          const errorText = await res.text();
          console.error(`Failed to fetch sellable items: ${res.status} ${errorText}`);
          throw new Error(`Failed to fetch sellable inventory items: ${res.status}`);
        }
        
        const data = await res.json();
        console.log(`Sellable inventory items: ${data.items?.length || 0} total items`);
        
        // Add mock available units for demonstration - in a real implementation
        // these would come from your API
        const enhancedData = (data.items || []).map((item: InventoryItem) => ({
          ...item,
          availableUnits: [
            { id: '1', name: item.unitName || 'Base Unit', shortCode: item.unit, conversionFactor: 1 },
            // Add alternative units based on the base unit
            ...(item.unit === 'kg' ? [{ id: '2', name: 'Grams', shortCode: 'g', conversionFactor: 1000 }] : []),
            ...(item.unit === 'g' ? [{ id: '3', name: 'Kilograms', shortCode: 'kg', conversionFactor: 0.001 }] : []),
            ...(item.unit === 'l' ? [{ id: '4', name: 'Milliliters', shortCode: 'ml', conversionFactor: 1000 }] : []),
            ...(item.unit === 'ml' ? [{ id: '5', name: 'Liters', shortCode: 'l', conversionFactor: 0.001 }] : [])
          ]
        }));
        
        return enhancedData;
      } catch (error) {
        console.error("Error fetching sellable items:", error);
        // Return an empty array instead of throwing to prevent query retries
        return [];
      }
    },
    // Always fetch inventory items when component mounts, not just when dialog opens
    enabled: !!companyId,
    // Use a stale time to prevent excessive refetching
    staleTime: 5 * 60 * 1000, // 5 minutes
    // Handle errors gracefully
    onError: (error) => {
      console.error("Inventory items query error:", error);
      toast.error("Failed to load inventory items. Please try again.");
    }
  })
  
  // Use useMemo to calculate filtered items only when dependencies change
  const memoizedFilteredItems = useMemo(() => {
    if (!inventoryItems) return [];
    
    if (!searchQuery.trim()) {
      return inventoryItems;
    } else {
      const query = searchQuery.toLowerCase().trim();
      return inventoryItems.filter((item: InventoryItem) => {
        return (
          item.name?.toLowerCase().includes(query) ||
          (item.description && item.description.toLowerCase().includes(query)) ||
          (item.category && item.category.toLowerCase().includes(query)) ||
          (item.type && item.type.toLowerCase().includes(query)) ||
          (item.unit && item.unit.toLowerCase().includes(query))
        );
      });
    }
  }, [inventoryItems, searchQuery]);
  
  // Directly use memoizedFilteredItems without the state update
  // This avoids the infinite update cycle

  // Update menu item recipe components
  const updateMenuItemMutation = useMutation({
    mutationFn: async (data: MenuItemUpdate) => {
      console.log(`Updating menu item ${data.id} with recipe components:`, data.recipeComponents)
      if (!data.id) {
        throw new Error("Menu item ID is required for update")
      }
      
      // Prepare update data according to the API expectations
      const updateData = {
        ...data,
        type: 'RECIPE', // Ensure consistent type for recipe items
        inventoryStatus: data.recipeComponents && data.recipeComponents.length > 0 
          ? 'linked' 
          : 'pending'
      }
      
      try {
        // Include required company-id header based on our auth system
        const res = await fetch(`/api/company/${companyId}/menu/items/${data.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'company-id': companyId // Required header per our auth system
          },
          body: JSON.stringify(updateData),
        })
        
        if (!res.ok) {
          let errorMessage = "Failed to update menu item";
          try {
            const errorData = await res.json();
            errorMessage = errorData.error || errorData.message || errorMessage;
          } catch (e) {
            console.error("Could not parse error response", e);
          }
          throw new Error(`${errorMessage} (${res.status})`);
        }
        
        return res.json();
      } catch (error) {
        console.error("Error updating menu item:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success("Recipe components updated successfully")
      // Remove references to undefined variables
      queryClient.invalidateQueries({ queryKey: ["menuItems"] })
    },
    onError: (error: Error) => {
      toast.error(`Failed to update recipe components: ${error.message}`)
    }
  })

  // Initialize editable components for all menu items
  useEffect(() => {
    if (!menuItems || menuItems.length === 0) return;
    
    // Build an object map of menu item ID -> recipe components
    const componentsMap: Record<string, RecipeComponent[]> = {};
    
    menuItems.forEach((item) => {
      const itemId = item._id || item.id as string;
      if (!itemId) return;
      
      // Only initialize if we haven't already saved edits for this item
      if (!editableMenuItems[itemId]) {
        componentsMap[itemId] = item.recipeComponents 
          ? item.recipeComponents.map(comp => ({
              itemId: comp.itemId,
              quantity: comp.quantity,
              unit: comp.unit,
              selectedUnitId: comp.selectedUnitId
            }))
          : [];
      }
    });
    
    // Set new items without overwriting existing edits
    setEditableMenuItems(prev => ({
      ...prev,
      ...componentsMap
    }));
    
    // Keep track of visible item IDs to filter expanded state
    setVisibleMenuItemIds(menuItems.map(item => (item._id || item.id) as string).filter(Boolean));
  }, [menuItems]);

  // Create a handler for toggling accordion expanded state
  const toggleAccordion = (itemId: string) => {
    if (!itemId) return; // Safety check
    
    // Close any search dropdown that might be open
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement && activeElement.blur) {
      activeElement.blur();
    }
    
    setExpandedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };

  // Define columns for menu items table
  const menuItemColumns: ColumnDef<MenuItem>[] = [
    {
      accessorKey: "name",
      header: "Menu Item",
      cell: ({ row }) => {
        const name = row.original.name
        return <span className="font-medium">{name}</span>
      }
    },
    {
      accessorKey: "type", 
      header: "Type",
      size: 100,
      cell: ({ row }) => {
        const type = row.original.type || ''
        return <span className="capitalize">{String(type).toLowerCase()}</span>
      }
    },
    {
      accessorKey: "prices.finalPrice",
      header: "Price",
      size: 100,
      cell: ({ row }) => {
        const price = row.original.prices?.finalPrice || 0
        return (
          <span>{new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(price)}</span>
        )
      }
    },
    {
      accessorKey: "inventoryStatus",
      header: "Status",
      size: 100,
      cell: ({ row }) => {
        const menuItem = row.original;
        const inventoryStatus = menuItem.inventoryStatus || 'pending';
        const componentsCount = menuItem.recipeComponents?.length || 0;
        
        return (
          <div className="flex items-center gap-2">
            <Badge variant={inventoryStatus === 'linked' ? "success" : "secondary"}>
              {inventoryStatus === 'linked' ? 'Linked' : 'Pending'}
            </Badge>
            {componentsCount > 0 && (
              <Badge variant="outline" className="ml-2">
                {componentsCount} {componentsCount === 1 ? 'component' : 'components'}
              </Badge>
            )}
          </div>
        )
      }
    },
    {
      id: "actions",
      size: 100,
      cell: ({ row }) => {
        const menuItem = row.original;
        const itemId = menuItem._id || menuItem.id as string;
        const isExpanded = expandedItems.includes(itemId);
        
        return (
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-2"
            onClick={() => toggleAccordion(itemId)}
          >
            {isExpanded ? "Close" : "Edit"}
            <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${isExpanded ? "rotate-180" : ""}`} />
          </Button>
        )
      }
    }
  ]

  // We're now using memoizedFilteredItems directly instead of this effect

  // Handle adding a new component to a specific menu item
  const handleAddComponent = (menuItemId: string) => {
    // Get first available inventory item or use empty string as fallback
    if (!inventoryItems || inventoryItems.length === 0) {
      // No items available, add an empty component
      setEditableMenuItems(prev => ({
        ...prev,
        [menuItemId]: [...(prev[menuItemId] || []), { itemId: '', quantity: 1, unit: '' }]
      }));
      return;
    }
    
    const filteredItems = memoizedFilteredItems || [];
    const firstItem = filteredItems.length > 0 ? filteredItems[0] : inventoryItems[0];
    
    if (firstItem) {
      const firstUnitId = firstItem.availableUnits && firstItem.availableUnits.length > 0 ? 
                          firstItem.availableUnits[0].id : undefined;
      
      setEditableMenuItems(prev => ({
        ...prev,
        [menuItemId]: [
          ...(prev[menuItemId] || []), 
          { 
            itemId: firstItem.id, 
            quantity: 1, 
            unit: firstItem.unit || '',
            selectedUnitId: firstUnitId
          }
        ]
      }));
    } else {
      // Fallback if no items available
      setEditableMenuItems(prev => ({
        ...prev,
        [menuItemId]: [...(prev[menuItemId] || []), { itemId: '', quantity: 1, unit: '' }]
      }));
    }
  }

  // Handle removing a component from a specific menu item
  const handleRemoveComponent = (menuItemId: string, index: number) => {
    setEditableMenuItems(prev => {
      const updatedComponents = [...(prev[menuItemId] || [])];
      updatedComponents.splice(index, 1);
      return {
        ...prev,
        [menuItemId]: updatedComponents
      };
    });
  }

  // Handle updating a component for a specific menu item
  const handleComponentChange = (menuItemId: string, index: number, field: keyof RecipeComponent, value: string | number) => {
    setEditableMenuItems(prev => {
      const updatedComponents = [...(prev[menuItemId] || [])];
      
      // Handle each field type separately with proper typing
      switch (field) {
        case 'itemId': {
          // For itemId, we also update the related unit information
          const stringValue = String(value);
          const selectedItem = inventoryItems.find((item: InventoryItem) => item && item.id === stringValue);
          
          if (selectedItem) {
            // Use the first available unit as default
            const defaultUnit = selectedItem.availableUnits && selectedItem.availableUnits.length > 0 
              ? selectedItem.availableUnits[0] 
              : { id: '', shortCode: selectedItem.unit || '' };
            
            updatedComponents[index] = {
              ...updatedComponents[index],
              itemId: stringValue,
              unit: defaultUnit.shortCode,
              selectedUnitId: defaultUnit.id
            };
            console.log(`Linked inventory item: ${selectedItem.name} with unit ${selectedItem.unit}`);
          } else {
            console.log(`Could not find inventory item with ID: ${stringValue}`);
            updatedComponents[index] = {
              ...updatedComponents[index],
              itemId: stringValue
            };
          }
          break;
        }
        
        case 'selectedUnitId': {
          // For selectedUnitId, update the unit shortcode and potentially convert quantities
          const stringValue = String(value);
          const selectedItem = inventoryItems.find((item: InventoryItem) => 
            item && item.id === updatedComponents[index]?.itemId
          );
          
          if (selectedItem && selectedItem.availableUnits) {
            const selectedUnit = selectedItem.availableUnits.find(unit => unit.id === stringValue);
            if (selectedUnit) {
              // Convert quantity based on conversion factor if needed
              const currentUnit = selectedItem.availableUnits.find(
                unit => unit.id === updatedComponents[index]?.selectedUnitId
              );
              
              let newQuantity = updatedComponents[index]?.quantity || 0;
              
              // Convert quantity if changing between units with different conversion factors
              if (currentUnit && selectedUnit && currentUnit.id !== selectedUnit.id) {
                // Convert to base value then to new unit
                newQuantity = (newQuantity / currentUnit.conversionFactor) * selectedUnit.conversionFactor;
                
                // Round to 3 decimal places for better display
                newQuantity = Math.round(newQuantity * 1000) / 1000;
              }
              
              updatedComponents[index] = {
                ...updatedComponents[index],
                selectedUnitId: stringValue,
                unit: selectedUnit.shortCode,
                quantity: newQuantity
              };
            }
          } else {
            updatedComponents[index] = {
              ...updatedComponents[index],
              selectedUnitId: stringValue
            };
          }
          break;
        }
        
        case 'quantity': {
          // For quantity, ensure we have a number value
          const numberValue = typeof value === 'string' ? parseFloat(value) : value;
          updatedComponents[index] = {
            ...updatedComponents[index],
            quantity: numberValue
          };
          break;
        }
        
        case 'unit': {
          // For unit, just update the string value
          const stringValue = String(value);
          updatedComponents[index] = {
            ...updatedComponents[index],
            unit: stringValue
          };
          break;
        }
      }
      
      return {
        ...prev,
        [menuItemId]: updatedComponents
      };
    });
  }

  // Validate recipe components
  const validateComponents = (components: RecipeComponent[]): { valid: boolean; message: string } => {
    if (!components || components.length === 0) {
      return { valid: false, message: 'No components added' };
    }
    
    // Check for invalid components
    const invalidComponents = components.filter(component => {
      return !component.itemId || !component.quantity || component.quantity <= 0;
    });
    
    if (invalidComponents.length > 0) {
      return { 
        valid: false, 
        message: `${invalidComponents.length} component(s) have invalid values` 
      };
    }
    
    return { valid: true, message: 'All components are valid' };
  };

  // Handle saving recipe components for a specific menu item
  const handleSaveComponents = (menuItemId: string) => {
    const components = editableMenuItems[menuItemId] || [];
    
    // Validate before saving
    const validation = validateComponents(components);
    if (!validation.valid) {
      toast.warning(validation.message);
      return;
    }
    
    // All validations passed, update the menu item
    updateMenuItemMutation.mutate({
      id: menuItemId,
      recipeComponents: components,
      inventoryStatus: 'linked', // Set to linked since validated
      type: 'RECIPE'
    });
  }

  // Render components editor for a menu item
  const renderComponentsEditor = (menuItem: MenuItem) => {
    const itemId = menuItem._id || menuItem.id as string;
    if (!itemId) return null;
    
    const components = editableMenuItems[itemId] || [];
    const isValid = validateComponents(components).valid;
    
    return (
      <div className="p-4 space-y-4 bg-gray-50 rounded-md border-t border-gray-200">
        {/* Status badge */}
        {components.length > 0 && (
          <div className="mb-4">
            <div className={`flex items-center gap-2 p-2 rounded ${!isValid ? 'bg-amber-50' : 'bg-green-50'}`}>
              <div className={`w-2 h-2 rounded-full ${!isValid ? 'bg-amber-500' : 'bg-green-500'}`}></div>
              <p className="text-sm font-medium">
                {!isValid 
                  ? 'Some components have invalid values' 
                  : 'Recipe is valid'
                }
              </p>
            </div>
          </div>
        )}
        
        {/* Components list */}
        <div className="space-y-3 max-h-[500px] overflow-y-auto">
          {components.length === 0 ? (
            <div className="text-center p-4 border rounded">
              <p className="text-muted-foreground">No components added yet</p>
            </div>
          ) : (
            components.map((component, index) => (
              <div key={index} className="flex flex-wrap items-end gap-3 p-3 border rounded">
                <div className="flex-1">
                  <Label className="text-xs mb-1">Inventory Item</Label>
                  <Select
                    value={component.itemId || "placeholder"}
                    onValueChange={(value) => handleComponentChange(itemId, index, 'itemId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select item" />
                    </SelectTrigger>
                    <SelectContent 
                      className="w-[400px]" 
                      position="popper" 
                      align="start" 
                      sideOffset={4}>
                      {/* Search input */}
                      <div className="flex items-center border-b px-3 py-2 sticky top-0 bg-white z-10">
                        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                        <Input
                          placeholder="Search items..."
                          value={searchQuery}
                          onChange={(e) => {
                            // Use a local variable to handle the change
                            const newValue = e.target.value;
                            // Wait until after current execution so typeahead doesn't try to focus
                            setTimeout(() => {
                              setSearchQuery(newValue);
                            }, 0);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          // Prevent ALL select's keyboard interactions from triggering
                          onKeyDown={(e) => {
                            e.stopPropagation();
                          }}
                          className="h-8 border-0 p-1 focus-visible:ring-0 focus-visible:ring-offset-0"
                        />
                      </div>
                      
                      <div onKeyDown={(e) => e.stopPropagation()}>
                        <ScrollArea className="h-[200px]">
                          {/* Always include placeholder item visible to prevent focus issues */}
                          <SelectItem value="placeholder">
                            Select an item...
                          </SelectItem>
                          
                          {inventoryLoading ? (
                            <SelectItem value="loading" disabled>
                              Loading items...
                            </SelectItem>
                          ) : !memoizedFilteredItems || memoizedFilteredItems.length === 0 ? (
                            <SelectItem value="none" disabled>
                              {searchQuery ? "No items match your search" : "No eligible inventory items"}
                            </SelectItem>
                          ) : (
                            memoizedFilteredItems.map((item: InventoryItem) => {
                              if (!item) return null;
                              // Ensure we always have a valid value and key - never falsy
                              const safeId = item.id || `item-${Math.random().toString(36).substring(2, 9)}`;
                              return (
                                <SelectItem key={safeId} value={safeId}>
                                  {item.name || 'Unnamed Item'} {item.type === 'ingredient' ? '(Ingredient)' : '(Recipe)'} {item.unit ? `(${item.unit})` : ''}
                                </SelectItem>
                              );
                            })
                          )}
                        </ScrollArea>
                      </div>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-28">
                  <Label className="text-xs mb-1">Quantity</Label>
                  <Input
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={component.quantity}
                    onChange={(e) => handleComponentChange(itemId, index, 'quantity', parseFloat(e.target.value))}
                  />
                </div>
                <div className="w-36">
                  <Label className="text-xs mb-1">Unit</Label>
                  {/* UOM Selection Dropdown */}
                  {component.itemId ? (
                    <Select
                      value={component.selectedUnitId || 'default'}
                      onValueChange={(value) => handleComponentChange(itemId, index, 'selectedUnitId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={component.unit || 'Select unit'} />
                      </SelectTrigger>
                      <SelectContent position="popper" align="start" sideOffset={4}>
                        <div onKeyDown={(e) => e.stopPropagation()}>
                          {/* Always include visible default option for a consistent focus target */}
                          <SelectItem value="default">
                            {component.unit || 'Default Unit'}
                          </SelectItem>
                          
                          {(() => {
                            try {
                              const item = inventoryItems?.find((item: InventoryItem) => item && item.id === component.itemId);
                              if (item?.availableUnits && item.availableUnits.length > 0) {
                                return item.availableUnits.map((unit: {id: string, name: string, shortCode: string}, unitIndex) => {
                                  // Ensure we always have a valid value and key
                                  const safeId = unit.id || `unit-${Math.random().toString(36).substring(2, 9)}`;
                                  return (
                                    <SelectItem key={safeId} value={safeId}>
                                      {unit.name || 'Unit'} ({unit.shortCode || ''})
                                    </SelectItem>
                                  );
                                });
                              } else {
                                return (
                                  <SelectItem value="default-unit">
                                    {component.unit || 'No additional units available'}
                                  </SelectItem>
                                );
                              }
                            } catch (error) {
                              console.error("Error rendering unit options:", error);
                              return (
                                <SelectItem value="error">
                                  Error loading units
                                </SelectItem>
                              );
                            }
                          })()
                          }
                        </div>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      type="text"
                      value={component.unit || ''}
                      readOnly
                      className="bg-muted/30"
                    />
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9"
                  onClick={() => handleRemoveComponent(itemId, index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))
          )}
          
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => handleAddComponent(itemId)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Ingredient
          </Button>
        </div>
        
        <div className="flex justify-end pt-2">
          <Button 
            onClick={() => handleSaveComponents(itemId)}
            disabled={updateMenuItemMutation.isPending || !isValid}
            className="ml-2"
          >
            {updateMenuItemMutation.isPending ? 'Saving...' : 'Save Recipe'}
          </Button>
        </div>
      </div>
    );
  };

  // Render menu item with accordion
  const renderMenuItem = (item: MenuItem) => {
    const itemId = item._id || item.id as string;
    if (!itemId) return null;
    
    const isExpanded = expandedItems.includes(itemId);
    const componentsCount = item.recipeComponents?.length || 0;
    const components = editableMenuItems[itemId] || [];
    
    return (
      <div key={itemId} className="border rounded-md mb-4 overflow-hidden">
        <div className="p-4 flex justify-between items-center bg-white">
          <div className="flex-1">
            <h3 className="font-medium">{item.name}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={item.type === 'RECIPE' ? "default" : "outline"}>
                {item.type === 'RECIPE' ? 'Recipe' : 'Direct'}
              </Badge>
              <Badge variant={item.inventoryStatus === 'linked' ? "success" : "secondary"}>
                {item.inventoryStatus === 'linked' ? 'Linked' : 'Pending'}
              </Badge>
              {componentsCount > 0 && (
                <Badge variant="outline">
                  {componentsCount} {componentsCount === 1 ? 'component' : 'components'}
                </Badge>
              )}
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-2"
            onClick={() => toggleAccordion(itemId)}
          >
            {isExpanded ? "Close" : "Edit Recipe"}
            <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${isExpanded ? "rotate-180" : ""}`} />
          </Button>
        </div>
        
        {isExpanded && renderComponentsEditor(item)}
      </div>
    );
  };

  // Render the menu items in an accordion list
  const renderMenuItems = () => {
    if (menuItemsLoading) {
      return <div className="py-4 text-center">Loading...</div>;
    }
    
    if (!menuItems || menuItems.length === 0) {
      return (
        <div className="p-4 text-center border-t">
          <p className="text-muted-foreground">No recipe items found.</p>
        </div>
      );
    }
    
    // Limit the displayed items based on pagination
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedItems = menuItems.slice(startIndex, endIndex);
    
    return (
      <div className="space-y-2">
        {paginatedItems.map(renderMenuItem)}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Recipe components apply across all locations - {pagination.total || menuItems?.length || 0} total items
        </p>
        
        {/* Search input for filtering menu items */}
        <div className="w-72 relative">
          <Input
            placeholder="Search menu items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        </div>
      </div>

      <Card>
        <CardHeader className="py-4">
          <CardTitle>Recipe Items</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Accordion List of Menu Items */}
          {renderMenuItems()}
          
          {/* Pagination Controls */}
          <div className="flex items-center justify-between mt-8">
            <div className="text-sm text-muted-foreground">
              {menuItems && menuItems.length > 0 ? (
                <>Showing {(page - 1) * pageSize + 1} to {Math.min(page * pageSize, pagination.total || menuItems.length)} of {pagination.total || menuItems.length}</>
              ) : (
                'No items to display'
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setPage(1);
                  setExpandedItems([]);
                }} 
                disabled={page === 1}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setPage(p => Math.max(1, p - 1));
                  setExpandedItems([]);
                }} 
                disabled={page === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setPage(p => Math.min(totalPages, p + 1));
                  setExpandedItems([]);
                }} 
                disabled={page === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setPage(totalPages);
                  setExpandedItems([]);
                }} 
                disabled={page === totalPages}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}