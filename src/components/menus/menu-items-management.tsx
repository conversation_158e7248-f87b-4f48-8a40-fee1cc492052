"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { Pencil, Plus, Trash2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"
import { MenuItem, MenuCategory, CreateMenuItemRequest } from "@/types/menu"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"

export function MenuItemsManagement() {
  const params = useParams()
  const { companyId } = params
  const [open, setOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [itemType, setItemType] = useState<'single' | 'recipe'>(editingItem?.type || 'single')
  
  // Add pagination state
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const queryClient = useQueryClient()

  // Fetch menu items with pagination
  const { data: menuItemsResponse, isLoading } = useQuery({
    queryKey: ["menuItems", companyId, selectedCategory, page, pageSize],
    queryFn: async () => {
      const categoryParam = selectedCategory !== "all" ? `&category=${selectedCategory}` : ""
      const paginationParams = `&page=${page}&limit=${pageSize}`
      const res = await fetch(`/api/company/${companyId}/menu/items?${categoryParam}${paginationParams}`)
      if (!res.ok) throw new Error("Failed to fetch menu items")
      return res.json()
    },
  })
  
  // Extract menu items and pagination info
  const menuItems = menuItemsResponse?.items || []
  const pagination = menuItemsResponse?.pagination || { total: 0, page: 1, pages: 1 }

  // Fetch categories for filter
  const { data: categories } = useQuery({
    queryKey: ["menuCategories", companyId],
    queryFn: async () => {
      const res = await fetch(`/api/company/${companyId}/menu/categories`)
      if (!res.ok) throw new Error("Failed to fetch categories")
      const data = await res.json()
      return data.categories as MenuCategory[]
    },
  })

  // Create menu item
  const createMutation = useMutation({
    mutationFn: async (data: CreateMenuItemRequest) => {
      const res = await fetch(`/api/company/${companyId}/menu/items`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })
      if (!res.ok) {
        const error = await res.json()
        throw new Error(error.message || "Failed to create menu item")
      }
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["menuItems"])
      setOpen(false)
      toast.success("Menu item created successfully")
    },
    onError: (error: Error) => {
      toast.error(error.message)
    },
  })

  // Update menu item
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateMenuItemRequest> }) => {
      console.log(`Making PATCH request to /api/company/${companyId}/menu/items/${id}`)
      console.log('Update payload:', data)
      
      if (!id) {
        throw new Error("Menu item ID is required for update")
      }
      
      const res = await fetch(`/api/company/${companyId}/menu/items/${id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        console.error("API error response:", errorData)
        throw new Error(errorData.message || errorData.error || "Failed to update menu item")
      }
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["menuItems"])
      setOpen(false)
      setEditingItem(null)
      toast.success("Menu item updated successfully")
    },
    onError: (error: Error) => {
      toast.error(error.message)
    },
  })

  const columns: ColumnDef<MenuItem>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "type",
      header: "Type",
    },
    {
      accessorKey: "prices.finalPrice",
      header: "Price",
      cell: ({ row }) => {
        const price = row.original.prices.finalPrice
        return new Intl.NumberFormat("rw-RW", {
          style: "currency",
          currency: "RWF",
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(price)
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <span className={row.original.status === "active" ? "text-green-600" : "text-red-600"}>
          {row.original.status}
        </span>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              console.log('Editing menu item:', row.original)
              if (!row.original.id) {
                console.error('Menu item missing ID:', row.original)
                toast.error('Cannot edit item: missing ID')
                return
              }
              setEditingItem(row.original)
              setItemType(row.original.type)
              setOpen(true)
            }}
          >
            <Pencil className="h-4 w-4" />
            <span className="sr-only">Edit {row.original.id}</span>
          </Button>
        </div>
      ),
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select
            value={selectedCategory}
            onValueChange={(value) => setSelectedCategory(value)}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories?.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Menu Item
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingItem ? "Edit Menu Item" : "Add Menu Item"}</DialogTitle>
              <DialogDescription>
                {editingItem
                  ? "Edit the details of the menu item below"
                  : "Add a new menu item to your menu"}
              </DialogDescription>
            </DialogHeader>
            <form
              onSubmit={async (e) => {
                e.preventDefault()
                const formData = new FormData(e.currentTarget)
                const data = {
                  name: formData.get("name") as string,
                  description: formData.get("description") as string,
                  categoryId: formData.get("categoryId") as string,
                  type: formData.get("type") as "single" | "recipe",
                  prices: {
                    basePrice: Number(formData.get("basePrice")),
                    vatRate: Number(formData.get("vatRate")),
                  },
                  ...(formData.get("type") === "single" && {
                    inventoryItemId: formData.get("inventoryItemId") as string,
                  }),
                }

                if (editingItem) {
                  console.log('Updating menu item with ID:', editingItem.id)
                  if (!editingItem.id) {
                    toast.error('Menu item ID is missing, cannot update!')
                    return
                  }
                  
                  try {
                    await updateMutation.mutateAsync({
                      id: editingItem.id,
                      data,
                    })
                  } catch (error) {
                    console.error('Error updating menu item:', error)
                    toast.error(`Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
                  }
                } else {
                  await createMutation.mutateAsync(data)
                }
              }}
              className="space-y-4"
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    required
                    defaultValue={editingItem?.name}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="categoryId">Category</Label>
                  <Select
                    name="categoryId"
                    defaultValue={editingItem?.categoryId}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories?.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  defaultValue={editingItem?.description}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select
                    name="type"
                    defaultValue={editingItem?.type || "single"}
                    required
                    onValueChange={(value: 'single' | 'recipe') => setItemType(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single">Single Item</SelectItem>
                      <SelectItem value="recipe">Recipe</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="basePrice">Base Price</Label>
                  <Input
                    id="basePrice"
                    name="basePrice"
                    type="number"
                    step="0.01"
                    required
                    defaultValue={editingItem?.prices.basePrice}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="vatRate">VAT Rate (%)</Label>
                <Input
                  id="vatRate"
                  name="vatRate"
                  type="number"
                  step="0.01"
                  required
                  defaultValue={editingItem?.prices.vatRate || 18}
                />
              </div>
              {itemType === "recipe" && (
                <div className="space-y-2">
                  <Label>Recipe Components</Label>
                  {/* Add recipe components management here */}
                  <div className="text-sm text-muted-foreground">
                    Recipe components will be managed in the inventory section
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setOpen(false)
                    setEditingItem(null)
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createMutation.isPending || updateMutation.isPending}>
                  {editingItem ? "Update" : "Create"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="space-y-4">
        <DataTable
          columns={columns}
          data={menuItems}
          withSorting
        />
        
        {/* Custom pagination controls */}
        <div className="flex items-center justify-between px-2">
          <div className="flex-1 text-sm text-muted-foreground">
            Showing {menuItems.length} of {pagination.total} items
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {pagination.page} of {pagination.pages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => setPage(1)}
                disabled={page <= 1}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page <= 1}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setPage(p => Math.min(pagination.pages, p + 1))}
                disabled={page >= pagination.pages}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => setPage(pagination.pages)}
                disabled={page >= pagination.pages}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
