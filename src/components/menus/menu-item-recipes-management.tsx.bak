"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "sonner"
import { Pencil, Plus, Trash2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, X } from "lucide-react"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"

// Enhanced types to match the system data models

// MenuItem type based on the memory data structure
interface MenuItem {
  id: string;
  name: string;
  description?: string;
  categoryId: string;
  type: 'single' | 'recipe';
  prices: {
    basePrice: number;
    vatRate: number;
    vatAmount: number;
    finalPrice: number;
  };
  status: 'active' | 'inactive';
  // For single items
  inventoryItem?: {
    itemId: string;
    unit: string;
  };
  // For recipe items
  recipeComponents?: RecipeComponent[];
  inventoryStatus?: 'pending' | 'linked';
}

// Define InventoryItem type based on the memory
interface InventoryItem {
  _id: string;
  companyId: string;
  name: string;
  unit: string;
  itemType?: 'RECIPE' | 'INGREDIENT';
  // For reference only, not used in recipes directly
  currentStock?: number;
  locationId?: string;
}

// Define RecipeComponent type
interface RecipeComponent {
  itemId: string;
  quantity: number;
  unit: string;
}

// Define MenuItemUpdate type
interface MenuItemUpdate {
  id: string;
  recipeComponents?: RecipeComponent[];
  inventoryStatus?: 'pending' | 'linked';
  type?: 'single' | 'recipe';
}

export function MenuItemRecipesManagement() {
  const params = useParams()
  const companyId = params?.companyId as string
  
  // Location-independent recipe management
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null)
  const [recipeComponents, setRecipeComponents] = useState<RecipeComponent[]>([])
  const [open, setOpen] = useState(false)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const queryClient = useQueryClient()

  // No longer need to fetch locations for recipe management

  // Fetch menu items with pagination
  const { data: menuItemsResponse, isLoading: menuItemsLoading } = useQuery({
    queryKey: ["menuItems", companyId, page, pageSize],
    queryFn: async () => {
      const res = await fetch(`/api/company/${companyId}/menu/items?page=${page}&limit=${pageSize}`)
      if (!res.ok) throw new Error("Failed to fetch menu items")
      return res.json()
    },
    enabled: !!companyId,
  })
  
  // Extract menu items and pagination info
  const menuItems = menuItemsResponse?.items || []
  const pagination = menuItemsResponse?.pagination || { total: 0, page: 1, pages: 1 }

  // Fetch all inventory items regardless of location
  const { data: inventoryItems, isLoading: inventoryLoading } = useQuery({
    queryKey: ["inventoryItems", companyId],
    queryFn: async () => {
      const res = await fetch(`/api/company/${companyId}/inventory/items`)
      if (!res.ok) throw new Error("Failed to fetch inventory items")
      const data = await res.json()
      return data.items || []
    },
    enabled: !!companyId
  })

  // Update menu item recipe components
  const updateMenuItemMutation = useMutation({
    mutationFn: async (data: MenuItemUpdate) => {
      console.log(`Updating menu item ${data.id} with recipe components:`, data.recipeComponents)
      if (!data.id) {
        throw new Error("Menu item ID is required for update")
      }
      
      const res = await fetch(`/api/company/${companyId}/menu/items/${data.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          recipeComponents: data.recipeComponents,
          inventoryStatus: data.inventoryStatus || 'linked',
          type: 'recipe' // Ensure it's marked as a recipe type
        }),
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        console.error("API error response:", errorData)
        throw new Error(errorData.error || "Failed to update menu item recipe")
      }
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["menuItems"] })
      setOpen(false)
      setSelectedMenuItem(null)
      setRecipeComponents([])
      toast.success("Menu item recipe components updated successfully")
    },
    onError: (error: Error) => {
      toast.error(error.message)
    },
  })

  // Initialize recipe components when a menu item is selected
  useEffect(() => {
    if (selectedMenuItem && selectedMenuItem.recipeComponents) {
      // Ensure we have the correct props for each component
      const formattedComponents = selectedMenuItem.recipeComponents.map(comp => ({
        itemId: comp.itemId,
        quantity: comp.quantity,
        unit: comp.unit
      }))
      setRecipeComponents(formattedComponents)
    } else {
      setRecipeComponents([])
    }
  }, [selectedMenuItem])

  // Column definition for menu items table
  const menuItemColumns: ColumnDef<MenuItem>[] = [
    {
      accessorKey: "name",
      header: "Menu Item",
    },
    {
      accessorKey: "type",
      header: "Type",
    },
    {
      accessorKey: "inventoryStatus",
      header: "Inventory Status",
      cell: ({ row }) => {
        const status = row.original.inventoryStatus || 'pending'
        return (
          <span className={`px-2 py-1 rounded text-xs ${
            status === 'linked' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
          }`}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        )
      }
    },
    {
      id: "components",
      header: "Components",
      cell: ({ row }) => {
        const components = row.original.recipeComponents || []
        return <span>{components.length} components</span>
      }
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setSelectedMenuItem(row.original)
                setOpen(true)
              }}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </Button>
          </div>
        )
      },
    },
  ]

  // Handle adding a new component
  const handleAddComponent = () => {
    setRecipeComponents([...recipeComponents, { itemId: "", quantity: 1, unit: "" }])
  }

  // Handle removing a component
  const handleRemoveComponent = (index: number) => {
    const updatedComponents = [...recipeComponents]
    updatedComponents.splice(index, 1)
    setRecipeComponents(updatedComponents)
  }

  // Handle updating a component
  const handleComponentChange = (index: number, field: string, value: string | number) => {
    const updatedComponents = [...recipeComponents]
    updatedComponents[index] = { 
      ...updatedComponents[index], 
      [field]: value 
    }
    
    // If itemId changes, also update the unit based on the selected inventory item
    if (field === 'itemId' && inventoryItems) {
      const selectedItem = inventoryItems.find((item: InventoryItem) => item._id === value)
      if (selectedItem) {
        updatedComponents[index].unit = selectedItem.unit
      }
    }
    
    setRecipeComponents(updatedComponents)
  }

  // Handle saving recipe components
  const handleSaveComponents = async () => {
    if (!selectedMenuItem) return
    
    // Validate components before saving
    const hasEmptyFields = recipeComponents.some(comp => !comp.itemId || comp.quantity <= 0)
    if (hasEmptyFields) {
      toast.error("All components must have an item selected and a quantity greater than zero")
      return
    }
    
    await updateMenuItemMutation.mutateAsync({
      id: selectedMenuItem.id,
      recipeComponents,
      inventoryStatus: 'linked'
    })
  }

  // Filter menu items to show only recipe types
  const recipeMenuItems = menuItems.filter((item: MenuItem) => item.type === 'recipe')

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="mb-4">
          <p className="text-sm text-muted-foreground">
            Recipe components are defined universally and will be applied across all locations. 
            The actual inventory deduction happens at the specific location when an item is sold.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Recipe Items</CardTitle>
            <CardDescription>
              Universal recipe components applied across all locations
            </CardDescription>
          </CardHeader>
          <CardContent>
                <DataTable
                  columns={menuItemColumns}
                  data={recipeMenuItems}
                  withSorting
                />
                
                {/* Custom pagination controls */}
                <div className="flex items-center justify-between px-2 mt-4">
                  <div className="flex-1 text-sm text-muted-foreground">
                    Showing {recipeMenuItems.length} of {pagination.total} items
                  </div>
                  <div className="flex items-center space-x-6 lg:space-x-8">
                    <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                      Page {pagination.page} of {pagination.pages}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        className="hidden h-8 w-8 p-0 lg:flex"
                        onClick={() => setPage(1)}
                        disabled={page <= 1}
                      >
                        <span className="sr-only">Go to first page</span>
                        <ChevronsLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="h-8 w-8 p-0"
                        onClick={() => setPage(p => Math.max(1, p - 1))}
                        disabled={page <= 1}
                      >
                        <span className="sr-only">Go to previous page</span>
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="h-8 w-8 p-0"
                        onClick={() => setPage(p => Math.min(pagination.pages, p + 1))}
                        disabled={page >= pagination.pages}
                      >
                        <span className="sr-only">Go to next page</span>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="hidden h-8 w-8 p-0 lg:flex"
                        onClick={() => setPage(pagination.pages)}
                        disabled={page >= pagination.pages}
                      >
                        <span className="sr-only">Go to last page</span>
                        <ChevronsRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recipe Components Dialog */}
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogContent className="max-w-3xl">
                <DialogHeader>
                  <DialogTitle>Edit Recipe Components</DialogTitle>
                  <DialogDescription>
                    {selectedMenuItem?.name} - Add or edit components that make up this recipe
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4 max-h-[500px] overflow-y-auto">
                  {recipeComponents.length === 0 && (
                    <div className="text-center p-4 border rounded-md">
                      <p className="text-muted-foreground">No components added yet</p>
                    </div>
                  )}
                  
                  {recipeComponents.map((component, index) => (
                    <div key={index} className="flex items-end gap-3 p-3 border rounded-md">
                      <div className="flex-1">
                        <Label className="mb-2 block">Inventory Item</Label>
                        <Select
                          value={component.itemId}
                          onValueChange={(value) => handleComponentChange(index, 'itemId', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select inventory item" />
                          </SelectTrigger>
                          <SelectContent>
                            {inventoryItems?.map((item: InventoryItem) => (
                              <SelectItem key={item._id} value={item._id}>
                                {item.name} ({item.unit})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="w-24">
                        <Label className="mb-2 block">Quantity</Label>
                        <Input
                          type="number"
                          min="0.01"
                          step="0.01"
                          value={component.quantity}
                          onChange={(e) => handleComponentChange(index, 'quantity', parseFloat(e.target.value))}
                        />
                      </div>
                      <div className="w-20">
                        <Label className="mb-2 block">Unit</Label>
                        <Input
                          type="text"
                          value={component.unit}
                          readOnly
                        />
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="mb-1"
                        onClick={() => handleRemoveComponent(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={handleAddComponent}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Component
                  </Button>
                </div>
                
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setOpen(false)
                      setSelectedMenuItem(null)
                      setRecipeComponents([])
                    }}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleSaveComponents}
                    disabled={updateMenuItemMutation.isPending}
                  >
                    Save Components
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  );
}
