'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useRequireCompanyUser } from '@/lib/auth';
import { useLocation } from '@/contexts/LocationContext';
import { Location } from '@/lib/types/location';

export function Sidebar() {
  const { userData } = useRequireCompanyUser();
  const pathname = usePathname();
  const { setSelectedLocation } = useLocation();
  const [locations, setLocations] = useState<Location[]>([]);
  const [isLocationsOpen, setIsLocationsOpen] = useState(false);

  useEffect(() => {
    async function fetchLocations() {
      try {
        const response = await fetch('/api/locations', {
          headers: {
            'company-id': userData?.companyId || '',
          },
        });
        if (response.ok) {
          const data = await response.json();
          setLocations(data);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
      }
    }

    if (userData?.companyId) {
      fetchLocations();
    }
  }, [userData?.companyId]);

  const handleLocationClick = (location: Location) => {
    setSelectedLocation(location);
    if (location.locationType === 'RETAIL_SHOP') {
      window.location.href = `/retail/${location._id}/dashboard`;
    }
  };

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'CENTRAL_KITCHEN':
        return 'Central Kitchen';
      case 'RETAIL_SHOP':
        return 'Retail Shop';
      case 'SINGLE_LOCATION':
        return 'Single Location';
      default:
        return type;
    }
  };

  const menuItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: (
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
    },
    {
      name: 'Locations',
      type: 'dropdown',
      icon: (
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
    },
    // Add other menu items here
  ];

  return (
    <div className="h-full bg-white shadow-lg">
      <div className="px-4 py-6">
        <h2 className="text-lg font-semibold text-gray-900">
          {userData?.companyName || 'Company Name'}
        </h2>
      </div>
      <nav className="space-y-1 px-2">
        {menuItems.map((item) => {
          if (item.type === 'dropdown' && item.name === 'Locations') {
            return (
              <div key={item.name}>
                <button
                  onClick={() => setIsLocationsOpen(!isLocationsOpen)}
                  className="w-full group flex items-center justify-between px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                >
                  <div className="flex items-center">
                    <div className="mr-3 flex-shrink-0 text-gray-400 group-hover:text-gray-500">
                      {item.icon}
                    </div>
                    <span>{item.name}</span>
                  </div>
                  <svg
                    className={`h-5 w-5 transform transition-transform ${
                      isLocationsOpen ? 'rotate-180' : ''
                    }`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                
                {isLocationsOpen && (
                  <div className="mt-1 ml-8 space-y-1">
                    {locations.map((location) => (
                      <button
                        key={location._id}
                        onClick={() => handleLocationClick(location)}
                        className="w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      >
                        <div className="mr-3 flex-shrink-0 text-gray-400 group-hover:text-gray-500">
                          {location.locationType === 'RETAIL_SHOP' ? (
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                          )}
                        </div>
                        <div className="flex flex-col items-start">
                          <span>{location.name}</span>
                          <span className="text-xs text-gray-500">{getLocationTypeLabel(location.locationType)}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            );
          }

          return (
            <Link
              key={item.name}
              href={item.href}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                pathname === item.href
                  ? 'bg-gray-100 text-gray-900'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div
                className={`mr-3 flex-shrink-0 ${
                  pathname === item.href ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500'
                }`}
              >
                {item.icon}
              </div>
              {item.name}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}