// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { corsMiddleware, handleCorsOptions } from './middleware/cors-middleware';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Handle OPTIONS preflight requests for all API routes
  if (request.method === 'OPTIONS' && pathname.startsWith('/api')) {
    return handleCorsOptions(request);
  }
  
  // Apply CORS middleware for API routes
  if (pathname.startsWith('/api')) {
    // Create a basic response that will be modified by the CORS middleware
    const response = NextResponse.next();
    return corsMiddleware(request, response);
  }
  
  // Public paths that don't require authentication
  if (
    pathname.startsWith('/_next') ||
    pathname === '/login' ||
    pathname === '/signup'
  ) {
    return NextResponse.next();
  }

  // Get the session token from the cookies
  const session = request.cookies.get('auth-token'); // Changed from 'session' to 'auth-token' for consistency

  // If no session is present and trying to access protected routes, redirect to login
  if (!session && !pathname.startsWith('/public')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

// Specify which routes to run middleware on
export const config = {
  matcher: [
    // Include all API routes
    '/api/:path*',
    // Include all other routes except static assets
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
