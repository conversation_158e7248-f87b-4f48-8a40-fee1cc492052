import dbConnect from '../lib/db';
import { MenuItem } from '../models/MenuItem';
import * as fs from 'fs';
import * as path from 'path';

interface ExtractedRecipe {
  name: string;
  posCode: string;
  prep: string;
  components: {
    name: string;
    quantity: number;
    unit: string;
  }[];
}

interface MenuItemMatch {
  menuItem: {
    _id: string;
    name: string;
    categoryId: string;
  };
  recipe: ExtractedRecipe;
  nameMatchScore: number;
}

function calculateNameSimilarity(name1: string, name2: string): number {
  // Convert both names to lowercase and remove special characters
  const clean = (s: string) => s.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
  const s1 = clean(name1);
  const s2 = clean(name2);

  // Debug log for very different strings
  if (s1 !== s2) {
    console.log(`Comparing: "${s1}" with "${s2}"`);
  }

  // Exact match after cleaning
  if (s1 === s2) return 1;

  // One is contained in the other
  if (s1.includes(s2) || s2.includes(s1)) return 0.9;

  // Split into words and remove common words like "and", "with", etc.
  const removeCommonWords = (s: string) => {
    const commonWords = new Set(['and', 'with', 'the', 'a', 'an', '&']);
    return s.split(/\s+/).filter(word => !commonWords.has(word));
  };
  
  const words1 = new Set(removeCommonWords(s1));
  const words2 = new Set(removeCommonWords(s2));
  
  // Calculate word overlap
  const intersection = new Set([...words1].filter(x => words2.has(x)));
  const union = new Set([...words1, ...words2]);
  
  const score = intersection.size / union.size;
  
  // If score is low but we have some matching words, log it
  if (score < 0.5 && intersection.size > 0) {
    console.log(`Low score match: "${name1}" vs "${name2}" (${score.toFixed(2)})`);
    console.log(`Matching words: ${[...intersection].join(', ')}`);
  }
  
  return score;
}

async function matchRecipes(companyId: string) {
  try {
    console.log('Starting recipe matching process...');
    
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await dbConnect();
    console.log('Connected to MongoDB');

    // Read the extracted recipes
    console.log('Reading extracted recipes file...');
    const recipesPath = '/Users/<USER>/Downloads/extracted_recipes.json';
    console.log('Recipes file path:', recipesPath);
    
    if (!fs.existsSync(recipesPath)) {
      console.error('Error: extracted_recipes.json not found at:', recipesPath);
      return;
    }
    
    const recipes: ExtractedRecipe[] = JSON.parse(fs.readFileSync(recipesPath, 'utf-8'));
    console.log(`Found ${recipes.length} recipes to match`);
    console.log('\nSample recipes from file:');
    recipes.slice(0, 3).forEach(recipe => {
      console.log(`- "${recipe.name}" with ${recipe.components.length} components`);
    });

    // Get all menu items for this company
    console.log('\nFetching menu items from MongoDB...');
    const menuItems = await MenuItem.find({ companyId }).select('_id name categoryId');
    console.log(`Found ${menuItems.length} existing menu items`);
    console.log('\nSample menu items from MongoDB:');
    menuItems.slice(0, 3).forEach(item => {
      console.log(`- "${item.name}" (${item._id})`);
    });

    // Match recipes to menu items
    console.log('\nStarting matching process...');
    const matches: MenuItemMatch[] = [];
    const unmatched: ExtractedRecipe[] = [];
    let processed = 0;

    for (const recipe of recipes) {
      // Find best matching menu item
      let bestMatch: MenuItemMatch | null = null;
      
      for (const menuItem of menuItems) {
        const score = calculateNameSimilarity(recipe.name, menuItem.name);
        
        if (score > 0.5 && (!bestMatch || score > bestMatch.nameMatchScore)) {
          bestMatch = {
            menuItem: {
              _id: menuItem._id.toString(),
              name: menuItem.name,
              categoryId: menuItem.categoryId.toString()
            },
            recipe,
            nameMatchScore: score
          };
        }
      }

      if (bestMatch) {
        matches.push(bestMatch);
      } else {
        unmatched.push(recipe);
      }
      
      processed++;
      if (processed % 10 === 0) {
        console.log(`Processed ${processed}/${recipes.length} recipes...`);
      }
    }

    console.log('Sorting matches...');
    // Sort matches by score
    matches.sort((a, b) => b.nameMatchScore - a.nameMatchScore);

    console.log('Generating report...');
    // Generate preview report
    const report = {
      summary: {
        totalRecipes: recipes.length,
        matchedRecipes: matches.length,
        unmatchedRecipes: unmatched.length
      },
      matches: matches.map(match => ({
        menuItemId: match.menuItem._id,
        menuItemName: match.menuItem.name,
        recipeName: match.recipe.name,
        matchScore: match.nameMatchScore,
        components: match.recipe.components
      })),
      unmatched: unmatched.map(recipe => ({
        name: recipe.name,
        components: recipe.components
      }))
    };

    // Save report
    console.log('Saving report...');
    const reportPath = '/Users/<USER>/Downloads/recipe_matches.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\nReport saved to ${reportPath}`);
    
    // Print summary
    console.log('\nSummary:');
    console.log(`Total recipes: ${report.summary.totalRecipes}`);
    console.log(`Matched recipes: ${report.summary.matchedRecipes}`);
    console.log(`Unmatched recipes: ${report.summary.unmatchedRecipes}`);
    
    // Print sample of best and worst matches
    console.log('\nTop 5 matches:');
    matches.slice(0, 5).forEach(match => {
      console.log(`${match.recipe.name} -> ${match.menuItem.name} (score: ${match.nameMatchScore.toFixed(2)})`);
    });
    
    if (unmatched.length > 0) {
      console.log('\nSample of unmatched recipes:');
      unmatched.slice(0, 5).forEach(recipe => {
        console.log(`- ${recipe.name}`);
      });
    }

    console.log('\nDone!');
  } catch (error) {
    console.error('Matching failed:', error);
  }
}

// Get command line arguments
const companyId = process.argv[2];

if (!companyId) {
  console.error('Usage: ts-node match-recipes.ts <companyId>');
  process.exit(1);
}

console.log('Starting script with companyId:', companyId);
matchRecipes(companyId);
