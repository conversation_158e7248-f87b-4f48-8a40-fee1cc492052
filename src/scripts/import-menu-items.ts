import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { parse } from 'csv-parse';
import fs from 'fs';
import path from 'path';
import { MenuItem } from '../models/MenuItem';
import { MenuCategory } from '../models/MenuCategory';
import dbConnect from '../lib/db';

dotenv.config();

interface MenuItemRow {
  Name: string;
  'Internal Reference': string;
  'Sales Price Including VAT': string;
  'Point of Sale Category': string;
  'Unit of Measure': string;
}

interface CategoryDocument {
  _id: mongoose.Types.ObjectId;
  name: string;
  companyId: mongoose.Types.ObjectId;
  description: string;
  order: number;
  status: string;
  __v: number;
}

const VAT_RATE = 18; // Rwanda VAT rate, adjust if needed

async function importMenuItems(companyId: string, filePath: string) {
  try {
    await dbConnect();
    console.log('Connected to MongoDB.');

    // First, get all existing categories to avoid duplicates
    const existingCategories = await MenuCategory.find({ companyId }).lean();
    const categoryMap = new Map<string, CategoryDocument>();
    existingCategories.forEach(cat => categoryMap.set(cat.name.toLowerCase(), cat as CategoryDocument));
    console.log(`Found ${existingCategories.length} existing categories.`);

    // Read and parse the CSV file
    const fileContent = fs.readFileSync(filePath, 'utf-8').replace(/^\uFEFF/, ''); // Remove BOM if present
    const records: MenuItemRow[] = await new Promise((resolve, reject) => {
      parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
        delimiter: ',',
        relax_quotes: true,
        skip_records_with_empty_values: true
      }, (err, records) => {
        if (err) reject(err);
        else resolve(records);
      });
    });

    console.log(`Found ${records.length} records to import.`);

    // Process each record
    for (const record of records) {
      try {
        if (!record.Name || !record['Point of Sale Category']) {
          console.warn('Skipping record: Missing name or category');
          continue;
        }

        // Clean up the price
        const priceStr = record['Sales Price Including VAT'].replace(/[^0-9.,]/g, '');
        const priceWithVAT = Number(priceStr.replace(/,/g, ''));
        
        if (isNaN(priceWithVAT)) {
          console.warn(`Skipping ${record.Name}: Invalid price ${record['Sales Price Including VAT']}`);
          continue;
        }

        const basePrice = Number((priceWithVAT / (1 + VAT_RATE / 100)).toFixed(2));
        const vatAmount = Number((priceWithVAT - basePrice).toFixed(2));

        // Find or create category
        const categoryName = record['Point of Sale Category'];
        let category = categoryMap.get(categoryName.toLowerCase());

        if (!category) {
          const newCategory = await MenuCategory.create({
            companyId,
            name: categoryName,
            description: '',
            order: 0,
            status: 'active',
          });
          console.log(`Created new category: ${newCategory.name}`);
          category = newCategory.toObject() as CategoryDocument;
          categoryMap.set(categoryName.toLowerCase(), category);
        }

        // At this point category is guaranteed to exist
        if (!category) {
          console.error(`Failed to create category: ${categoryName}`);
          continue;
        }

        // Check if menu item already exists
        const existingItem = await MenuItem.findOne({
          companyId,
          name: record.Name,
          categoryId: category._id
        });

        if (existingItem) {
          console.log(`Skipping duplicate item: ${record.Name}`);
          continue;
        }

        // Create menu item
        const menuItem = await MenuItem.create({
          companyId,
          name: record.Name,
          description: '',
          categoryId: category._id,
          type: 'single',
          prices: {
            basePrice,
            vatRate: VAT_RATE,
            vatAmount,
            finalPrice: priceWithVAT,
          },
          status: 'active',
          inventoryStatus: 'pending'
        });

        console.log(`Imported menu item: ${menuItem.name} (Price: ${priceWithVAT} RWF)`);
      } catch (error) {
        console.error(`Error importing item ${record.Name}:`, error);
      }
    }

    console.log('Import completed.');
  } catch (error) {
    console.error('Import failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Check if running directly (not imported)
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length !== 2) {
    console.log('Usage: ts-node import-menu-items.ts <companyId> <filePath>');
    process.exit(1);
  }

  const [companyId, filePath] = args;
  importMenuItems(companyId, filePath)
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}
