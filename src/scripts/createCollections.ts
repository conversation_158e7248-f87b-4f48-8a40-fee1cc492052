import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

async function createCollections() {
  await dbConnect();
  const db = mongoose.connection;

  try {
    // Create time series collection for InventoryTransaction
    await db.createCollection('inventorytransactions', {
      timeseries: {
        timeField: 'timestamp',
        metaField: 'locationId',
        granularity: 'hours'
      }
    });
    console.log('Successfully created inventorytransactions time series collection');

    // Note: Other collections (branchinventories, stockcounts) will be 
    // automatically created when first document is inserted
    
  } catch (error: any) {
    // Handle case where collection might already exist
    if (error.code === 48) {
      console.log('Collection already exists');
    } else {
      console.error('Error creating collections:', error);
      throw error;
    }
  } finally {
    await mongoose.disconnect();
  }
}

// Only run if called directly
if (require.main === module) {
  createCollections()
    .then(() => console.log('Done'))
    .catch(console.error);
}

export default createCollections;
