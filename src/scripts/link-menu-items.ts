import mongoose from 'mongoose';
import dbConnect from '../lib/db';

// Types
interface MenuItem {
  _id: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  name: string;
  description: string;
  categoryId: mongoose.Types.ObjectId;
  type: 'single' | 'recipe';
  prices: {
    basePrice: number;
    vatIncluded: boolean;
    vatPercentage: number;
  };
  status: 'active' | 'inactive';
  inventoryStatus: 'pending' | 'linked';
  inventoryItemId?: mongoose.Types.ObjectId;
  recipeComponents?: {
    inventoryItemId: mongoose.Types.ObjectId;
    quantity: number;
    unit: string;
  }[];
}

interface BranchInventory {
  _id: mongoose.Types.ObjectId;
  itemId: mongoose.Types.ObjectId;
  itemType: 'RECIPE' | 'INVENTORY';
  companyId: mongoose.Types.ObjectId;
  locationId: mongoose.Types.ObjectId;
  isActive: boolean;
}

interface InventoryItem {
  _id: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  unit: string;
  status: 'active' | 'inactive';
}

interface Recipe {
  _id: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  components: {
    inventoryItemId: mongoose.Types.ObjectId;
    quantity: number;
    unit: string;
  }[];
}

// MongoDB Models
const MenuItemSchema = new mongoose.Schema<MenuItem>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  categoryId: { type: mongoose.Schema.Types.ObjectId, required: true },
  type: { type: String, enum: ['single', 'recipe'], required: true },
  prices: {
    basePrice: { type: Number, required: true },
    vatIncluded: { type: Boolean, required: true },
    vatPercentage: { type: Number, required: true }
  },
  status: { type: String, enum: ['active', 'inactive'], required: true },
  inventoryStatus: { type: String, enum: ['pending', 'linked'], required: true },
  inventoryItemId: { type: mongoose.Schema.Types.ObjectId },
  recipeComponents: [{
    inventoryItemId: { type: mongoose.Schema.Types.ObjectId, required: true },
    quantity: { type: Number, required: true },
    unit: { type: String, required: true }
  }]
});

const BranchInventorySchema = new mongoose.Schema<BranchInventory>({
  itemId: { type: mongoose.Schema.Types.ObjectId, required: true },
  itemType: { type: String, enum: ['RECIPE', 'INVENTORY'], required: true },
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  locationId: { type: mongoose.Schema.Types.ObjectId, required: true },
  isActive: { type: Boolean, required: true }
});

const InventoryItemSchema = new mongoose.Schema<InventoryItem>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  unit: { type: String, required: true },
  status: { type: String, enum: ['active', 'inactive'], required: true }
});

const RecipeSchema = new mongoose.Schema<Recipe>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  status: { type: String, enum: ['active', 'inactive'], required: true },
  components: [{
    inventoryItemId: { type: mongoose.Schema.Types.ObjectId, required: true },
    quantity: { type: Number, required: true },
    unit: { type: String, required: true }
  }]
});

const MenuItem = mongoose.models.MenuItem || mongoose.model<MenuItem>('MenuItem', MenuItemSchema);
const BranchInventory = mongoose.models.BranchInventory || mongoose.model<BranchInventory>('BranchInventory', BranchInventorySchema, 'branchinventories');
const InventoryItem = mongoose.models.InventoryItem || mongoose.model<InventoryItem>('InventoryItem', InventoryItemSchema, 'inventoryitems');
const Recipe = mongoose.models.Recipe || mongoose.model<Recipe>('Recipe', RecipeSchema, 'recipes');

async function linkMenuItems() {
  try {
    await dbConnect();
    console.log('Connected to MongoDB');

    // Get menu items that need linking
    const menuItems = await MenuItem.find({
      inventoryStatus: 'pending'
    }).lean();
    console.log(`Found ${menuItems.length} menu items to process`);

    // Get branch inventory items
    const branchInventories = await BranchInventory.find({
      isActive: true
    }).lean();
    console.log(`Found ${branchInventories.length} branch inventory items`);

    // Get all inventory items and recipes referenced by branch inventories
    const inventoryItemIds = branchInventories
      .filter(bi => bi.itemType === 'INVENTORY')
      .map(bi => bi.itemId);
    
    const recipeIds = branchInventories
      .filter(bi => bi.itemType === 'RECIPE')
      .map(bi => bi.itemId);

    const [rawInventoryItems, rawRecipes] = await Promise.all([
      InventoryItem.find({
        _id: { $in: inventoryItemIds },
        status: 'active'
      }).lean(),
      Recipe.find({
        _id: { $in: recipeIds },
        status: 'active'
      }).lean()
    ]);

    const inventoryItems = rawInventoryItems as unknown as InventoryItem[];
    const recipes = rawRecipes as unknown as Recipe[];

    console.log(`Found ${inventoryItems.length} inventory items and ${recipes.length} recipes`);

    // Track results
    const results = {
      success: 0,
      noMatch: 0,
      noRecipe: 0,
      noInventory: 0
    };

    // Process each menu item
    for (const menuItem of menuItems) {
      console.log(`\nProcessing menu item: ${menuItem.name}`);

      if (menuItem.type === 'single') {
        // Try to find matching inventory item
        const inventoryItem = inventoryItems.find(i => 
          i.name.toLowerCase().includes(menuItem.name.toLowerCase()) ||
          menuItem.name.toLowerCase().includes(i.name.toLowerCase())
        );

        if (inventoryItem) {
          // Verify it's in branch inventory
          const branchInventory = branchInventories.find(bi => 
            bi.itemType === 'INVENTORY' && 
            bi.itemId.toString() === (inventoryItem._id as mongoose.Types.ObjectId).toString()
          );

          if (branchInventory) {
            await MenuItem.updateOne(
              { _id: menuItem._id },
              {
                $set: {
                  inventoryItemId: inventoryItem._id,
                  inventoryStatus: 'linked'
                }
              }
            );
            console.log(`Linked single item ${menuItem.name} to inventory item ${inventoryItem.name}`);
            results.success++;
            continue;
          }
        }

        // Try to find matching recipe
        const recipe = recipes.find(r => 
          r.name.toLowerCase().includes(menuItem.name.toLowerCase()) ||
          menuItem.name.toLowerCase().includes(r.name.toLowerCase())
        );

        if (recipe) {
          // Verify it's in branch inventory
          const branchInventory = branchInventories.find(bi => 
            bi.itemType === 'RECIPE' && 
            bi.itemId.toString() === (recipe._id as mongoose.Types.ObjectId).toString()
          );

          if (branchInventory) {
            await MenuItem.updateOne(
              { _id: menuItem._id },
              {
                $set: {
                  type: 'recipe',
                  recipeComponents: recipe.components,
                  inventoryStatus: 'linked'
                }
              }
            );
            console.log(`Linked single item ${menuItem.name} to recipe ${recipe.name}`);
            results.success++;
            continue;
          }
        }

        console.log(`No inventory or recipe match found for: ${menuItem.name}`);
        results.noInventory++;

      } else {
        // For recipe items, try to find matching recipe first
        const recipe = recipes.find(r => 
          r.name.toLowerCase().includes(menuItem.name.toLowerCase()) ||
          menuItem.name.toLowerCase().includes(r.name.toLowerCase())
        );

        if (recipe) {
          // Verify it's in branch inventory
          const branchInventory = branchInventories.find(bi => 
            bi.itemType === 'RECIPE' && 
            bi.itemId.toString() === (recipe._id as mongoose.Types.ObjectId).toString()
          );

          if (branchInventory) {
            await MenuItem.updateOne(
              { _id: menuItem._id },
              {
                $set: {
                  recipeComponents: recipe.components,
                  inventoryStatus: 'linked'
                }
              }
            );
            console.log(`Linked recipe ${menuItem.name} to branch recipe ${recipe.name}`);
            results.success++;
            continue;
          }
        }

        console.log(`No recipe match found for: ${menuItem.name}`);
        results.noRecipe++;
      }
    }

    console.log('\nResults:');
    console.log(`Success: ${results.success}`);
    console.log(`No matching recipe: ${results.noRecipe}`);
    console.log(`No matching inventory: ${results.noInventory}`);
    console.log(`No match: ${results.noMatch}`);

  } catch (error) {
    console.error('Failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

linkMenuItems();
