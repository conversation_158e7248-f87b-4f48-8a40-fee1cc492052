import { MongoClient, ObjectId } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || "mongodb+srv://jkayobotsi:<EMAIL>/?retryWrites=true&w=majority&appName=restaurant-inventory-dev";

async function createMissingBranchInventory() {
  let client = null;
  
  try {
    // Connect directly to MongoDB
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('Connected to database');
    
    const db = client.db();
    const ingredientsCollection = db.collection('ingredients');
    const locationsCollection = db.collection('locations');
    const branchInventoryCollection = db.collection('branchinventories');
    
    // Find the specific ingredient
    const ingredientId = '676aca53d253bdfa34d20544'; // Betteraves (kg)
    const ingredient = await ingredientsCollection.findOne({ _id: new ObjectId(ingredientId) });
    
    if (!ingredient || !ingredient.sellingDetails || ingredient.sellingDetails.length === 0) {
      console.error('Ingredient not found or has no selling details');
      return;
    }
    
    const sellingDetail = ingredient.sellingDetails[0];
    console.log('Using selling detail:', sellingDetail._id);
    
    // Get all locations for this company
    const locations = await locationsCollection.find({ companyId: new ObjectId(ingredient.companyId) }).toArray();
    console.log(`Found ${locations.length} locations`);
    
    let createdCount = 0;
    let existingCount = 0;
    
    // Create branch inventory records for all locations
    for (const location of locations) {
      console.log(`Processing location: ${location.name}`);
      
      // Check if the branch inventory record already exists
      const existingInventory = await branchInventoryCollection.findOne({
        companyId: new ObjectId(ingredient.companyId),
        locationId: new ObjectId(location._id),
        itemId: new ObjectId(ingredient._id),
        itemType: 'INGREDIENT',
        sellingOptionId: new ObjectId(sellingDetail._id)
      });
      
      if (existingInventory) {
        console.log('Branch inventory record already exists');
        existingCount++;
      } else {
        // Create new inventory with correct baseUomId
        await branchInventoryCollection.insertOne({
          companyId: new ObjectId(ingredient.companyId),
          locationId: new ObjectId(location._id),
          itemId: new ObjectId(ingredient._id),
          itemType: 'INGREDIENT',
          sellingOptionId: new ObjectId(sellingDetail._id),
          baseUomId: new ObjectId(ingredient.baseUomId),
          currentStock: 0,
          pendingStock: 0,
          parLevel: 0,
          reorderPoint: 0,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        console.log('Created new branch inventory record');
        createdCount++;
      }
    }
    
    console.log(`Summary: Created ${createdCount} new records, ${existingCount} already existed`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (client) {
      await client.close();
    }
    console.log('Script completed');
    process.exit(0);
  }
}

createMissingBranchInventory();
