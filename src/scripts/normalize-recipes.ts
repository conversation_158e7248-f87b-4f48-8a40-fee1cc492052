import dbConnect from '../lib/db';
import mongoose, { Schema } from 'mongoose';
import * as fs from 'fs';

// Define UOM schema based on actual DB structure
const uomSchema = new Schema({
  name: String,
  shortCode: String,
  system: String,
  baseType: String,
  factorToCanonical: Number,
  synonyms: [String],
  description: String,
  companyId: Schema.Types.ObjectId
});

// Use the existing model if it exists, otherwise create it
const UOM = mongoose.models.UOM || mongoose.model('UOM', uomSchema);

interface RecipeComponent {
  name: string;
  quantity: number;
  unit: string;
}

interface Recipe {
  name: string;
  posCode: string;
  prep: string;
  components: RecipeComponent[];
}

interface MongoUOM {
  name?: string;
  shortCode?: string;
  system?: string;
  baseType?: string;
  synonyms?: string[];
  factorToCanonical?: number;
  companyId?: any;
}

// Unit fixes mapping
const unitFixes: { [key: string]: string } = {
  // Volume units
  'l': 'l',
  'lt': 'l',
  'ltr': 'l',
  'litre': 'l',
  'litres': 'l',
  'liter': 'l',
  'liters': 'l',
  'ml': 'ml',
  'mi': 'ml',  // Fix for 'mi' -> milliliters
  'millilitre': 'ml',
  'milliliter': 'ml',
  
  // Weight units
  'g': 'g',
  'gr': 'g',
  'gar': 'g',  // Fix for 'gar' -> grams
  'ar': 'g',   // Fix for 'ar' -> grams
  'j': 'g',    // Fix for 'j' -> grams
  'reg': 'g',  // Fix for 'reg' -> grams
  'gram': 'g',
  'grams': 'g',
  'kg': 'kg',
  'kgs': 'kg',
  'kilo': 'kg',
  'kilos': 'kg',
  'kilogram': 'kg',
  'kilograms': 'kg',
  'oz': 'oz',
  'ounce': 'oz',
  'ounces': 'oz',
  'lb': 'lb',
  'lbs': 'lb',
  'pound': 'lb',
  'pounds': 'lb',
  
  // Count units
  'ea': 'unit',
  'each': 'unit',
  'piece': 'unit',
  'pieces': 'unit',
  'unit': 'unit',
  'units': 'unit',
  'doz': 'doz',
  'dozen': 'doz',
  'dozens': 'doz',
  
  // Special cases and fixes
  'rn': 'unit',    // Fix for 'RN' -> units
  'si': 'unit',    // Fix for 'Si' -> units
  'ap': 'unit',    // Fix for 'Ap' -> units
  'pe': 'unit',    // Fix for 'Pe' -> units
  'dn': 'unit',    // Fix for 'DN' -> units
  'seconds': 'unit', // Fix for 'seconds' -> units
  'c.a.c': 'g',    // Fix for 'c.a.c' -> grams
  '25': 'g',       // Fix for numbered units -> grams
  'a': 'g',        // Fix for letter units -> grams
  'x': 'g',
  '74': 'g',
  '.': 'g',
  '4': 'g',
  'so': 'unit',
  '-': 'unit'
};

function cleanIngredientName(name: string): string {
  // Remove unit-like suffixes from ingredient names
  const suffixesToRemove = [
    /\s*\([^)]*\)\s*$/,  // Remove anything in parentheses at the end
    /\s+\d+\s*(ml|g|kg|oz|lb|EA|unit|piece|pieces)\s*$/i,  // Remove unit-like endings
    /\s+\d+\s*$/,  // Remove trailing numbers
    /\s+[A-Z]+\s*$/,  // Remove trailing uppercase letters
    /\s+vd\s*$/i,  // Remove 'vd' suffix
    /\s+-\s*$/,  // Remove trailing dash
    /\s+\.*\s*$/,  // Remove trailing dots
    /\s+$/,  // Remove trailing spaces
    /\s+UHT\s+\d+L$/i,  // Remove UHT volume suffix
    /-Pack\d+EA$/i,  // Remove pack suffix
    /-Pack\d+gr$/i,   // Remove pack gram suffix
    /\s+Mix\s+for.*$/i,  // Remove mixing instructions
    /\s+add\s+.*$/i,  // Remove additional instructions
    /\s+and\s+.*$/i   // Remove conjunctions and following text
  ];
  
  let cleaned = name;
  suffixesToRemove.forEach(regex => {
    cleaned = cleaned.replace(regex, '');
  });
  
  // Remove any text after "PROCEDURE" or "INGREDIENTS"
  cleaned = cleaned.split(/PROCEDURE|INGREDIENTS/)[0];
  
  // Handle special cases
  if (cleaned === 'T') return 'Tomatoes';
  if (cleaned === 'ee') return '';  // Empty string for invalid ingredients
  if (cleaned === '-') return '';   // Empty string for dashes
  if (cleaned === '.') return '';   // Empty string for dots
  if (cleaned.trim() === '') return ''; // Empty string for whitespace
  
  // Remove any remaining non-ingredient text
  cleaned = cleaned.replace(/^(add|mix|pour|stir|blend)\s+.*$/i, '');
  
  return cleaned.trim();
}

async function normalizeRecipes() {
  try {
    await dbConnect();
    console.log('Connected to MongoDB');

    // Get valid UOMs
    const validUOMs = await UOM.find({}).lean() as MongoUOM[];
    console.log('Raw UOMs from DB:', validUOMs);
    
    // Create a set of valid units from both shortCodes and synonyms
    const validUnitSet = new Set<string>();
    const unitToCanonical = new Map<string, string>();
    const unitToFactor = new Map<string, number>();
    
    // Custom conversion factors
    const customFactors: { [key: string]: number } = {
      'ml': 0.001,  // 1 ml = 0.001 L
      'g': 0.001,   // 1 g = 0.001 kg
      'l': 1,       // 1 L = 1 L (base unit)
      'kg': 1,      // 1 kg = 1 kg (base unit)
      'unit': 1,    // 1 unit = 1 unit (base unit)
      'doz': 12     // 1 dozen = 12 units
    };
    
    validUOMs.forEach(uom => {
      if (uom.shortCode) {
        const shortCode = uom.shortCode.toLowerCase();
        validUnitSet.add(shortCode);
        unitToCanonical.set(shortCode, shortCode);
        // Use custom factors for specific units
        unitToFactor.set(shortCode, customFactors[shortCode] || uom.factorToCanonical || 1);
      }
      if (uom.synonyms) {
        uom.synonyms.forEach(syn => {
          const synLower = syn.toLowerCase();
          validUnitSet.add(synLower);
          if (uom.shortCode) {
            unitToCanonical.set(synLower, uom.shortCode.toLowerCase());
          }
          // Use custom factors for specific units
          const canonical = uom.shortCode?.toLowerCase() || '';
          unitToFactor.set(synLower, customFactors[canonical] || uom.factorToCanonical || 1);
        });
      }
    });
    
    console.log('\nValid UOMs found:', 
      validUOMs
        .filter(u => u.name && u.shortCode)
        .map(u => `${u.name} (${u.shortCode})`)
        .join(', ')
    );

    // Read recipes
    const recipes: Recipe[] = JSON.parse(
      fs.readFileSync('/Users/<USER>/Downloads/extracted_recipes.json', 'utf-8')
    );
    console.log(`\nProcessing ${recipes.length} recipes`);

    // Track issues
    const issues: { recipe: string; component: string; unit: string }[] = [];
    
    // Normalize units
    const normalized = recipes.map(recipe => ({
      ...recipe,
      components: recipe.components
        .map(comp => {
          // Clean up the ingredient name first
          const cleanedName = cleanIngredientName(comp.name);
          
          // Skip empty or invalid ingredients
          if (!cleanedName) return null;
          
          const unit = comp.unit.toLowerCase().trim();
          const fixed = unitFixes[unit] || unit;
          const canonical = unitToCanonical.get(fixed) || fixed;
          
          if (!validUnitSet.has(canonical)) {
            issues.push({ 
              recipe: recipe.name, 
              component: cleanedName, 
              unit: comp.unit 
            });
            return { ...comp, name: cleanedName };
          }
          
          // Get the conversion factor
          const factor = unitToFactor.get(canonical) || 1;
          let quantity = comp.quantity;
          
          // Special conversion for common units
          if (canonical === 'ml') {
            quantity = quantity * 1000; // Convert L to ml
          } else if (canonical === 'g') {
            quantity = quantity * 1000; // Convert kg to g
          }
          
          // Apply the conversion factor
          quantity = quantity * factor;
          
          return { 
            ...comp, 
            name: cleanedName,
            unit: canonical,
            quantity
          };
        })
        .filter(comp => comp !== null) as RecipeComponent[] // Remove null components
    }));

    // Save results
    fs.writeFileSync(
      '/Users/<USER>/Downloads/normalized_recipes.json', 
      JSON.stringify(normalized, null, 2)
    );

    if (issues.length > 0) {
      console.log('\nUnit normalization issues:');
      issues.forEach(({ recipe, component, unit }) => 
        console.log(`- Recipe "${recipe}" - "${component}" has unknown unit "${unit}"`)
      );
      
      fs.writeFileSync(
        '/Users/<USER>/Downloads/unit_issues.json',
        JSON.stringify(issues, null, 2)
      );
    }

    // Show samples
    console.log('\nSample normalized recipes:');
    normalized.slice(0, 5).forEach(recipe => {
      console.log(`\n${recipe.name}:`);
      recipe.components.forEach(c => 
        console.log(`- ${c.quantity} ${c.unit} ${c.name}`)
      );
    });

  } catch (error) {
    console.error('Failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

normalizeRecipes();
