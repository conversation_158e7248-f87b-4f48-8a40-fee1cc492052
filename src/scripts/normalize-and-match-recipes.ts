import dbConnect from '../lib/db';
import mongoose from 'mongoose';
import * as fs from 'fs';

// Define UOM schema
const uomSchema = new mongoose.Schema({
  name: String,
  abbreviation: String,
  type: String
});

const UOM = mongoose.model('UOM', uomSchema);

interface ExtractedRecipe {
  name: string;
  posCode: string;
  prep: string;
  components: {
    name: string;
    quantity: number;
    unit: string;
  }[];
}

interface ValidUOM {
  _id: string;
  name: string;
  abbreviation: string;
  type: string;
}

function normalizeUnit(unit: string, validUOMs: ValidUOM[]): string | null {
  // Convert to lowercase for comparison
  const normalizedUnit = unit.toLowerCase().trim();
  
  // Common unit fixes
  const unitFixes: { [key: string]: string } = {
    'ar': 'gr',
    'gar': 'gr',
    'ea': 'unit',
    'piece': 'unit',
    'pieces': 'unit',
    'ml': 'ml',
    'milliliter': 'ml',
    'millilitre': 'ml',
    'g': 'gr',
    'gram': 'gr',
    'grams': 'gr'
  };

  // First try direct match with valid UOMs
  const directMatch = validUOMs.find(uom => 
    uom.abbreviation.toLowerCase() === normalizedUnit ||
    uom.name.toLowerCase() === normalizedUnit
  );
  
  if (directMatch) {
    return directMatch.abbreviation;
  }

  // Try unit fixes
  if (unitFixes[normalizedUnit]) {
    const fixedUnit = unitFixes[normalizedUnit];
    const fixedMatch = validUOMs.find(uom => 
      uom.abbreviation.toLowerCase() === fixedUnit ||
      uom.name.toLowerCase() === fixedUnit
    );
    if (fixedMatch) {
      return fixedMatch.abbreviation;
    }
  }

  return null;
}

async function normalizeRecipes() {
  try {
    // Connect to MongoDB
    await dbConnect();
    console.log('Connected to MongoDB');

    // Get valid UOMs from MongoDB
    const validUOMs = await UOM.find({});
    console.log(`Found ${validUOMs.length} valid UOMs`);
    console.log('Sample UOMs:', validUOMs.slice(0, 5).map(uom => `${uom.name} (${uom.abbreviation})`));

    // Read the extracted recipes
    const recipesPath = '/Users/<USER>/Downloads/extracted_recipes.json';
    const recipes: ExtractedRecipe[] = JSON.parse(fs.readFileSync(recipesPath, 'utf-8'));
    console.log(`\nProcessing ${recipes.length} recipes`);

    // Track unit normalization issues
    const issues: { recipe: string; component: string; originalUnit: string }[] = [];
    
    // Normalize units in recipes
    const normalizedRecipes = recipes.map(recipe => {
      const normalizedComponents = recipe.components.map(component => {
        const normalizedUnit = normalizeUnit(component.unit, validUOMs);
        
        if (!normalizedUnit) {
          issues.push({
            recipe: recipe.name,
            component: component.name,
            originalUnit: component.unit
          });
          return component;
        }
        
        return {
          ...component,
          unit: normalizedUnit
        };
      });

      return {
        ...recipe,
        components: normalizedComponents
      };
    });

    // Save normalized recipes
    const normalizedPath = '/Users/<USER>/Downloads/normalized_recipes.json';
    fs.writeFileSync(normalizedPath, JSON.stringify(normalizedRecipes, null, 2));
    console.log(`\nNormalized recipes saved to: ${normalizedPath}`);

    // Report issues
    if (issues.length > 0) {
      console.log('\nUnit normalization issues found:');
      issues.forEach(issue => {
        console.log(`Recipe "${issue.recipe}" - Component "${issue.component}" has unknown unit "${issue.originalUnit}"`);
      });
      
      const issuesPath = '/Users/<USER>/Downloads/unit_normalization_issues.json';
      fs.writeFileSync(issuesPath, JSON.stringify(issues, null, 2));
      console.log(`\nDetailed issues saved to: ${issuesPath}`);
    } else {
      console.log('\nNo unit normalization issues found!');
    }

    // Print sample of normalized recipes
    console.log('\nSample of normalized recipes:');
    normalizedRecipes.slice(0, 3).forEach(recipe => {
      console.log(`\nRecipe: ${recipe.name}`);
      recipe.components.forEach(comp => {
        console.log(`- ${comp.quantity} ${comp.unit} ${comp.name}`);
      });
    });

  } catch (error) {
    console.error('Normalization failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Run the script
normalizeRecipes();
