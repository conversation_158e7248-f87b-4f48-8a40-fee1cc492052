import { MongoClient, ObjectId } from 'mongodb';

async function fixBetteravesDocument() {
  // Use the same MongoDB connection string as in the project
  const MONGODB_URI = process.env.MONGODB_URI || "mongodb+srv://jkayobotsi:<EMAIL>/?retryWrites=true&w=majority&appName=restaurant-inventory-dev";
  
  let client = null;
  
  try {
    // Connect directly to MongoDB
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('Connected to database');
    
    // Get the database and collection
    const db = client.db();
    const ingredientsCollection = db.collection('ingredients');
    
    // Find the specific document
    const ingredientId = '676aca53d253bdfa34d20544'; // Betteraves (kg)
    const ingredient = await ingredientsCollection.findOne({ _id: new ObjectId(ingredientId) });
    
    if (!ingredient) {
      console.error('Ingredient not found');
      return;
    }
    
    console.log('Found ingredient:', ingredient.name);
    
    // Check and fix the selling details
    if (ingredient.sellingDetails && ingredient.sellingDetails.length > 0) {
      let updated = false;
      
      // Add _id to each selling detail that's missing it
      for (let i = 0; i < ingredient.sellingDetails.length; i++) {
        if (!ingredient.sellingDetails[i]._id) {
          // Create a new ObjectId for this selling detail
          const newId = new ObjectId();
          console.log('Adding _id to selling detail:', newId.toString());
          
          // Update the document directly in MongoDB
          await ingredientsCollection.updateOne(
            { _id: new ObjectId(ingredientId) },
            { $set: { [`sellingDetails.${i}._id`]: newId } }
          );
          
          updated = true;
        }
      }
      
      if (updated) {
        console.log('Successfully updated the ingredient document');
      } else {
        console.log('No updates were needed');
      }
    } else {
      console.log('No selling details found on the document');
    }
    
  } catch (error) {
    console.error('Error fixing document:', error);
  } finally {
    if (client) {
      await client.close();
    }
    console.log('Script completed');
    process.exit(0);
  }
}

// Run the fix
fixBetteravesDocument();
