import { connect } from '@/lib/db';
import { MenuItem } from '@/models/MenuItem';
import * as fs from 'fs';
import * as path from 'path';
import { Types } from 'mongoose';

interface ExtractedRecipe {
  name: string;
  posCode: string;
  prep: string;
  components: {
    name: string;
    quantity: number;
    unit: string;
  }[];
}

interface Price {
  basePrice: number;
  vatRate: number;
  vatAmount: number;
  totalPrice: number;
}

async function importRecipes(companyId: string, categoryId: string) {
  try {
    // Connect to MongoDB
    await connect();

    // Read the extracted recipes
    const recipesPath = path.join(process.cwd(), '..', 'Downloads', 'extracted_recipes.json');
    const recipes: ExtractedRecipe[] = JSON.parse(fs.readFileSync(recipesPath, 'utf-8'));

    console.log(`Found ${recipes.length} recipes to import`);

    // Default price structure (you can modify this)
    const defaultPrice: Price = {
      basePrice: 0,
      vatRate: 0.16, // 16% VAT
      vatAmount: 0,
      totalPrice: 0
    };

    // Import each recipe as a menu item
    for (const recipe of recipes) {
      try {
        // Check if menu item already exists
        const existing = await MenuItem.findOne({
          companyId,
          name: recipe.name
        });

        if (existing) {
          console.log(`Skipping ${recipe.name} - already exists`);
          continue;
        }

        // Create the menu item
        const menuItem = new MenuItem({
          companyId: new Types.ObjectId(companyId),
          categoryId: new Types.ObjectId(categoryId),
          name: recipe.name,
          description: recipe.prep || '',
          type: 'recipe',
          status: 'active',
          inventoryStatus: 'pending',
          prices: defaultPrice,
          recipeComponents: recipe.components.map(component => ({
            name: component.name,
            quantity: component.quantity,
            unit: component.unit,
            inventoryItemId: null // This will need to be linked later
          }))
        });

        await menuItem.save();
        console.log(`Imported ${recipe.name}`);
      } catch (error) {
        console.error(`Error importing ${recipe.name}:`, error);
      }
    }

    console.log('Import completed');
  } catch (error) {
    console.error('Import failed:', error);
  }
}

// Get command line arguments
const companyId = process.argv[2];
const categoryId = process.argv[3];

if (!companyId || !categoryId) {
  console.error('Usage: ts-node import-recipes.ts <companyId> <categoryId>');
  process.exit(1);
}

importRecipes(companyId, categoryId);
