import * as fs from 'fs';
import * as readline from 'readline';

interface RecipeComponent {
  name: string;
  quantity: number;
  unit: string;
}

interface MatchedRecipe {
  menuItemId: string;
  menuItemName: string;
  recipeName: string;
  matchScore: number;
  components: RecipeComponent[];
}

interface UnmatchedRecipe {
  name: string;
  components: RecipeComponent[];
}

interface MatchReport {
  summary: {
    totalRecipes: number;
    matchedRecipes: number;
    unmatchedRecipes: number;
  };
  matches: MatchedRecipe[];
  unmatched: UnmatchedRecipe[];
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function question(query: string): Promise<string> {
  return new Promise((resolve) => rl.question(query, resolve));
}

async function reviewMatches() {
  try {
    // Read the matches report
    const reportPath = '/Users/<USER>/Downloads/recipe_matches.json';
    const report: MatchReport = JSON.parse(fs.readFileSync(reportPath, 'utf-8'));

    console.log('\nReviewing matches...\n');
    
    const approvedMatches: MatchedRecipe[] = [];
    const mongoUpdates: string[] = [];

    // Review each match
    for (const match of report.matches) {
      console.log(`\nMenu Item: ${match.menuItemName}`);
      console.log(`Matched Recipe: ${match.recipeName} (score: ${match.matchScore.toFixed(2)})`);
      console.log('\nComponents to be added:');
      match.components.forEach(comp => {
        console.log(`- ${comp.quantity} ${comp.unit} ${comp.name}`);
      });

      const answer = await question('\nApprove this match? (y/n/q to quit): ');
      
      if (answer.toLowerCase() === 'q') {
        break;
      }
      
      if (answer.toLowerCase() === 'y') {
        approvedMatches.push(match);
        
        // Generate MongoDB update command
        const updateCommand = `db.menuitems.updateOne(
  { _id: ObjectId("${match.menuItemId}") },
  { 
    $set: { 
      type: "recipe",
      recipeComponents: ${JSON.stringify(match.components, null, 2)}
    }
  }
);`;
        mongoUpdates.push(updateCommand);
      }
    }

    // Save approved matches and MongoDB commands
    if (approvedMatches.length > 0) {
      const updatesPath = '/Users/<USER>/Downloads/approved_recipe_updates.js';
      fs.writeFileSync(updatesPath, mongoUpdates.join('\n\n'));
      console.log(`\nMongoDB update commands saved to: ${updatesPath}`);
      
      const approvedPath = '/Users/<USER>/Downloads/approved_matches.json';
      fs.writeFileSync(approvedPath, JSON.stringify({
        approved: approvedMatches,
        unmatched: report.unmatched
      }, null, 2));
      console.log(`Approved matches saved to: ${approvedPath}`);
    }

    console.log(`\nSummary:`);
    console.log(`Total matches reviewed: ${report.matches.length}`);
    console.log(`Matches approved: ${approvedMatches.length}`);
    console.log(`Matches rejected: ${report.matches.length - approvedMatches.length}`);
    console.log(`Unmatched recipes: ${report.unmatched.length}`);

  } catch (error) {
    console.error('Review failed:', error);
  } finally {
    rl.close();
  }
}

reviewMatches();
