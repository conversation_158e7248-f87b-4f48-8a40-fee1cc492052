import mongoose from 'mongoose';
import dbConnect from '../lib/db';
import fs from 'fs';

// Types
interface NormalizedRecipe {
  name: string;
  components: {
    name: string;
    quantity: number;
    unit: string;
  }[];
}

interface InventoryItem {
  _id: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  locationId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  unit: string;
  status: 'active' | 'inactive';
}

// MongoDB Models
const InventoryItemSchema = new mongoose.Schema<InventoryItem>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  locationId: { type: mongoose.Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  unit: { type: String, required: true },
  status: { type: String, enum: ['active', 'inactive'], required: true }
});

const InventoryItem = mongoose.models.InventoryItem || mongoose.model<InventoryItem>('InventoryItem', InventoryItemSchema);

async function createInventoryItems() {
  try {
    await dbConnect();
    console.log('Connected to MongoDB');

    // Read normalized recipes
    const recipes: NormalizedRecipe[] = JSON.parse(
      fs.readFileSync('/Users/<USER>/Downloads/normalized_recipes.json', 'utf-8')
    );
    console.log(`Loaded ${recipes.length} normalized recipes`);

    // Get unique ingredients and their units
    const uniqueIngredients = new Map<string, string>();
    recipes.forEach(recipe => {
      recipe.components.forEach(component => {
        uniqueIngredients.set(component.name, component.unit);
      });
    });

    // Add single items that don't have components
    recipes.forEach(recipe => {
      if (recipe.components.length === 0) {
        uniqueIngredients.set(recipe.name, 'unit');
      }
    });

    console.log(`Found ${uniqueIngredients.size} unique ingredients`);

    // Create inventory items for each location
    // Using a hardcoded company and location for now - you'll need to update these
    const companyId = new mongoose.Types.ObjectId('67682466d436c5f697693330');
    const locationId = new mongoose.Types.ObjectId('676824a1d436c5f697693332');

    const inventoryItems = [];
    for (const [name, unit] of uniqueIngredients.entries()) {
      inventoryItems.push({
        companyId,
        locationId,
        name,
        unit,
        status: 'active',
        description: `Ingredient for recipes`
      });
    }

    // Insert inventory items
    const result = await InventoryItem.insertMany(inventoryItems);
    console.log(`Created ${result.length} inventory items`);

    // Output the created items for reference
    console.log('\nCreated inventory items:');
    result.forEach(item => {
      console.log(`${item.name} (${item.unit})`);
    });

  } catch (error) {
    console.error('Failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

createInventoryItems();
