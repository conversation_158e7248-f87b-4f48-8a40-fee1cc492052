# Scripts Directory

## extract-recipes.ts.disabled

This script was disabled to prevent deployment issues with the `canvas` package on Vercel.

The script was used for PDF recipe extraction using OCR (Tesseract.js) and PDF-to-image conversion.

### To re-enable (if needed):

1. Rename `extract-recipes.ts.disabled` back to `extract-recipes.ts`
2. Install the required packages:
   ```bash
   npm install pdf-img-convert@^2.0.0 pdf2pic@^3.1.3
   ```
3. Note: This will require native system libraries that may not be available in serverless environments like Vercel.

### Alternative approaches:

- Use a separate microservice for PDF processing
- Process PDFs client-side using browser-based libraries
- Use cloud services like AWS Textract or Google Vision API