import { Types } from 'mongoose';
import path from 'path';
import dotenv from 'dotenv';
import dbConnect from '../lib/db';
import Recipe from '../models/Recipe';
import { Ingredient } from '../models/Ingredient';
import BranchInventory from '../models/BranchInventory';
import Location from '../models/Location';

// Set MongoDB URI directly
process.env.MONGODB_URI = "mongodb+srv://jkayobotsi:<EMAIL>/?retryWrites=true&w=majority&appName=restaurant-inventory-dev";

// Load environment variables from root .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// interface SellingOption {
//   _id: Types.ObjectId;
//   unitOfSelling: Types.ObjectId;
//   visibility: {
//     type: 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';
//     locations?: Types.ObjectId[];
//   };
// }

// interface LocationResult {
//   _id: Types.ObjectId;
//   companyId: Types.ObjectId;
// }

async function populateBranchInventory() {
  try {
    console.log('Connecting to database...');
    await dbConnect();

    // Get all companies with their locations
    const locations = await Location.find({}).select('_id companyId').lean();
    const locationsByCompany = locations.reduce((acc: { [key: string]: string[] }, loc: any) => {
      const companyId = loc.companyId.toString();
      if (!acc[companyId]) {
        acc[companyId] = [];
      }
      acc[companyId].push(loc._id.toString());
      return acc;
    }, {});

    console.log(`Found ${locations.length} locations across ${Object.keys(locationsByCompany).length} companies`);

    // Process recipes
    console.log('Processing recipes...');
    const recipes = await Recipe.find({ 'sellingDetails.0': { $exists: true } }).lean();
    console.log(`Found ${recipes.length} recipes with selling options`);

    // Process ingredients
    console.log('Processing ingredients...');
    const ingredients = await Ingredient.find({ 'sellingDetails.0': { $exists: true } }).lean();
    console.log(`Found ${ingredients.length} ingredients with selling options`);

    // Prepare batch operations
    const operations: any[] = [];

    // Process recipes
    for (const recipe of recipes) {
      const companyId = (recipe as any).companyId.toString();
      const companyLocations = locationsByCompany[companyId] || [];

      for (const option of ((recipe as any).sellingDetails || [])) {
        // Determine which locations should have this selling option
        let targetLocations: string[] = [];
        if (option.visibility.type === 'ALL_LOCATIONS') {
          targetLocations = companyLocations;
        } else if (option.visibility.type === 'SPECIFIC_LOCATIONS' && option.visibility.locations) {
          targetLocations = option.visibility.locations.map((locId: any) => locId.toString())
            .filter((locId: string) => companyLocations.includes(locId));
        }
        // Skip if EXTERNAL_ONLY or no valid locations

        // Create inventory entries for each location
        for (const locationId of targetLocations) {
          operations.push({
            updateOne: {
              filter: {
                companyId: new Types.ObjectId(companyId),
                locationId: new Types.ObjectId(locationId),
                itemId: (recipe as any)._id,
                itemType: 'RECIPE',
                sellingOptionId: option._id.toString(),
              },
              update: {
                $setOnInsert: {
                  currentStock: 0,
                  parLevel: 0,
                  reorderPoint: 0,
                  baseUomId: option.unitOfSelling,
                  lastUpdated: new Date(),
                  isActive: true,
                }
              },
              upsert: true
            }
          });
        }
      }
    }

    // Process ingredients
    for (const ingredient of ingredients) {
      const companyId = (ingredient as any).companyId.toString();
      const companyLocations = locationsByCompany[companyId] || [];

      for (const option of ((ingredient as any).sellingDetails || [])) {
        // Determine which locations should have this selling option
        let targetLocations: string[] = [];
        if (option.visibility.type === 'ALL_LOCATIONS') {
          targetLocations = companyLocations;
        } else if (option.visibility.type === 'SPECIFIC_LOCATIONS' && option.visibility.locations) {
          targetLocations = option.visibility.locations.map((locId: any) => locId.toString())
            .filter((locId: string) => companyLocations.includes(locId));
        }
        // Skip if EXTERNAL_ONLY or no valid locations

        // Create inventory entries for each location
        for (const locationId of targetLocations) {
          operations.push({
            updateOne: {
              filter: {
                companyId: new Types.ObjectId(companyId),
                locationId: new Types.ObjectId(locationId),
                itemId: (ingredient as any)._id,
                itemType: 'INGREDIENT',
                sellingOptionId: option._id.toString(),
              },
              update: {
                $setOnInsert: {
                  currentStock: 0,
                  parLevel: 0,
                  reorderPoint: 0,
                  baseUomId: option.unitOfSelling,
                  lastUpdated: new Date(),
                  isActive: true,
                }
              },
              upsert: true
            }
          });
        }
      }
    }

    // Execute batch operations in chunks of 500
    console.log(`Executing ${operations.length} operations in batches of 500...`);
    const batchSize = 500;
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      if (batch.length > 0) {
        await BranchInventory.bulkWrite(batch);
      }
    }

    console.log('Branch inventory population completed successfully!');
  } catch (error) {
    console.error('Error populating branch inventory:', error);
    throw error;
  }
}

// Run the script
populateBranchInventory();
