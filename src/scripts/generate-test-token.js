// src/scripts/generate-test-token.js
import jwt from 'jsonwebtoken';

// This should match your JWT_SECRET environment variable
const secret = process.env.JWT_SECRET || 'your-jwt-secret-for-testing';

// Create a token with the expected payload structure
const payload = {
  id: '67682464d436c5f69769332d',  // User ID
  email: '<EMAIL>',
  userType: 'company_user',
  companyId: '67682466d436c5f697693330',
  role: 'owner'
};

// Generate the token
const token = jwt.sign(payload, secret, { expiresIn: '24h' });

console.log('Generated auth token:');
console.log(token);
console.log('\nPayload:');
console.log(payload);
