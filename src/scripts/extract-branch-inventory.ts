import mongoose from 'mongoose';
import dbConnect from '../lib/db';
import fs from 'fs';

interface BranchInventory {
  _id: mongoose.Types.ObjectId;
  itemId: mongoose.Types.ObjectId;
  itemType: 'RECIPE' | 'INGREDIENT';
  companyId: mongoose.Types.ObjectId;
  locationId: mongoose.Types.ObjectId;
  isActive: boolean;
}

interface InventoryItem {
  _id: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  unit: string;
  status: 'active' | 'inactive';
}

interface Recipe {
  _id: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  components: {
    inventoryItemId: mongoose.Types.ObjectId;
    quantity: number;
    unit: string;
  }[];
}

// MongoDB Models
const BranchInventorySchema = new mongoose.Schema<BranchInventory>({
  itemId: { type: mongoose.Schema.Types.ObjectId, required: true },
  itemType: { type: String, enum: ['RECIPE', 'INGREDIENT'], required: true },
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  locationId: { type: mongoose.Schema.Types.ObjectId, required: true },
  isActive: { type: Boolean, required: true }
});

const InventoryItemSchema = new mongoose.Schema<InventoryItem>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  unit: { type: String, required: true },
  status: { type: String, enum: ['active', 'inactive'], required: true }
});

const RecipeSchema = new mongoose.Schema<Recipe>({
  companyId: { type: mongoose.Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String },
  status: { type: String, enum: ['active', 'inactive'], required: true },
  components: [{
    inventoryItemId: { type: mongoose.Schema.Types.ObjectId, required: true },
    quantity: { type: Number, required: true },
    unit: { type: String, required: true }
  }]
});

const BranchInventory = mongoose.models.BranchInventory || mongoose.model<BranchInventory>('BranchInventory', BranchInventorySchema, 'branchinventories');
const InventoryItem = mongoose.models.InventoryItem || mongoose.model<InventoryItem>('InventoryItem', InventoryItemSchema, 'ingredients');
const Recipe = mongoose.models.Recipe || mongoose.model<Recipe>('Recipe', RecipeSchema, 'recipes');

interface RawDocument {
  _id: mongoose.Types.ObjectId;
  [key: string]: any;
}

async function extractBranchInventory() {
  try {
    await dbConnect();
    console.log('Connected to MongoDB');

    // Get branch inventory items
    const branchInventories = await BranchInventory.find({
      isActive: true
    }).lean() as RawDocument[];
    console.log(`Found ${branchInventories.length} branch inventory items`);

    // Get all inventory items and recipes referenced by branch inventories
    const inventoryItemIds = branchInventories
      .filter(bi => bi.itemType === 'INGREDIENT')
      .map(bi => bi.itemId);
    
    const recipeIds = branchInventories
      .filter(bi => bi.itemType === 'RECIPE')
      .map(bi => bi.itemId);

    const [rawInventoryItems, rawRecipes] = await Promise.all([
      InventoryItem.find({
        _id: { $in: inventoryItemIds }
      }).lean() as Promise<RawDocument[]>,
      Recipe.find({
        _id: { $in: recipeIds }
      }).lean() as Promise<RawDocument[]>
    ]);

    console.log(`Found ${rawInventoryItems.length} ingredients and ${rawRecipes.length} recipes`);

    // Create a mapping of IDs to names
    const inventoryMap = new Map(
      rawInventoryItems.map(item => [item._id.toString(), item])
    );
    const recipeMap = new Map(
      rawRecipes.map(recipe => [recipe._id.toString(), recipe])
    );

    // Create output data
    const output = branchInventories.map(bi => {
      const itemId = bi.itemId.toString();
      if (bi.itemType === 'INGREDIENT') {
        const item = inventoryMap.get(itemId);
        return {
          branchInventoryId: bi._id,
          itemId: bi.itemId,
          itemType: bi.itemType,
          name: item?.name || 'Unknown',
          unit: item?.unit || 'Unknown',
          status: item?.status || 'Unknown'
        };
      } else {
        const recipe = recipeMap.get(itemId);
        return {
          branchInventoryId: bi._id,
          itemId: bi.itemId,
          itemType: bi.itemType,
          name: recipe?.name || 'Unknown',
          components: recipe?.components?.map((comp: { inventoryItemId: mongoose.Types.ObjectId; quantity: number; unit: string }) => {
            const item = inventoryMap.get(comp.inventoryItemId.toString());
            return {
              ...comp,
              name: item?.name || 'Unknown'
            };
          }) || []
        };
      }
    });

    // Write to file
    fs.writeFileSync('branch-inventory.json', JSON.stringify(output, null, 2));
    console.log('Output written to branch-inventory.json');

  } catch (error) {
    console.error('Failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

extractBranchInventory();
