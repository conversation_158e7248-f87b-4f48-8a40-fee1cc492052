import mongoose from 'mongoose';
import dbConnect from '../lib/db';
import BranchInventory from '../models/BranchInventory';
import Recipe from '../models/Recipe';
import { Ingredient } from '../models/Ingredient';

interface BaseDoc {
    _id: any;
    category?: string;
    Category?: string;
}

async function migrateBranchInventoryCategories() {
    try {
        await dbConnect();
        console.log('Connected to database');

        // Get all branch inventories
        const inventories = await BranchInventory.find({}).lean();
        console.log(`Found ${inventories.length} branch inventories to process`);

        // Get all ingredients and recipes for lookup
        const ingredients = await Ingredient.find({}).lean() as BaseDoc[];
        const recipes = await Recipe.find({}).lean() as BaseDoc[];

        console.log(`Found ${ingredients.length} ingredients and ${recipes.length} recipes for reference`);

        // Create lookup maps
        const ingredientMap = new Map(ingredients.map(ing => [ing._id.toString(), ing.category]));
        const recipeMap = new Map(recipes.map(recipe => [recipe._id.toString(), recipe.Category || recipe.category]));

        // Update each inventory
        let updated = 0;
        let skipped = 0;
        let errors = 0;

        for (const inv of inventories) {
            try {
                const itemId = inv.itemId?.toString();
                let category;

                if (inv.itemType === 'INGREDIENT') {
                    category = ingredientMap.get(itemId);
                } else if (inv.itemType === 'RECIPE') {
                    category = recipeMap.get(itemId);
                }

                if (category) {
                    await BranchInventory.findByIdAndUpdate(inv._id, { category });
                    updated++;
                    console.log(`Updated inventory ${inv._id} with category: ${category}`);
                } else {
                    skipped++;
                    console.log(`Skipped inventory ${inv._id}: No category found for ${inv.itemType} with ID ${itemId}`);
                }
            } catch (err) {
                errors++;
                console.error(`Error updating inventory ${inv._id}:`, err);
            }
        }

        console.log('\nMigration Summary:');
        console.log(`Total processed: ${inventories.length}`);
        console.log(`Successfully updated: ${updated}`);
        console.log(`Skipped (no category found): ${skipped}`);
        console.log(`Errors: ${errors}`);

    } catch (err) {
        console.error('Migration failed:', err);
    } finally {
        await mongoose.disconnect();
        console.log('\nDisconnected from database');
    }
}

// Run migration
migrateBranchInventoryCategories();
