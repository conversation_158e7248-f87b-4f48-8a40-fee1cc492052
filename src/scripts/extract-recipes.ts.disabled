import { createWorker } from 'tesseract.js';
import * as fs from 'fs';
import * as path from 'path';
import { fromPath } from 'pdf2pic';

interface RecipeComponent {
  name: string;
  quantity: number;
  unit: string;
}

interface Recipe {
  name: string;
  posCode: string;
  prep: string;
  components: RecipeComponent[];
}

async function convertPDFToImages(pdfPath: string): Promise<string[]> {
  try {
    const tempDir = path.join(path.dirname(pdfPath), "temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }

    const options = {
      density: 300,
      saveFilename: "page",
      savePath: tempDir,
      format: "png",
      width: 2000,
      height: 2000
    };

    const convert = fromPath(pdfPath, options);
    
    // Get the number of pages
    const pageCount = (await convert.bulk(-1)).length;
    console.log(`PDF has ${pageCount} pages`);
    
    const imagePaths: string[] = [];
    
    // Convert each page
    for (let i = 1; i <= pageCount; i++) {
      const result = await convert(i, { responseType: "image" });
      if (result && result.path) {
        imagePaths.push(result.path);
        console.log(`Converted page ${i} to ${result.path}`);
      }
    }

    return imagePaths;
  } catch (error) {
    console.error('Error converting PDF:', error);
    throw error;
  }
}

async function extractRecipeFromImage(imagePath: string): Promise<Recipe | null> {
  const worker = await createWorker('eng');
  
  try {
    // Configure Tesseract
    await worker.setParameters({
      tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-()., ',
      preserve_interword_spaces: '1',
    });

    // Read the image
    const { data: { text } } = await worker.recognize(imagePath);

    // Split into lines
    const lines = text.split('\n').filter(line => line.trim());

    // Check if this page contains a recipe
    if (!lines.some(line => line.includes('INGREDIENTS'))) {
      return null;
    }

    // Extract recipe info
    const name = lines.find(line => /^[A-Za-z\s]+$/.test(line))?.trim() || '';
    const prep = lines.find(line => line.toLowerCase().includes('prep:'))?.split(':')[1]?.trim() || '';
    const posCode = lines.find(line => line.includes('POS code'))?.split('POS code')[1]?.trim() || '';

    // Extract components
    const components: RecipeComponent[] = [];
    let inIngredients = false;

    for (const line of lines) {
      if (line.includes('INGREDIENTS')) {
        inIngredients = true;
        continue;
      }

      if (inIngredients && line.trim() && !line.includes('QUANTITY') && !line.includes('UNIT')) {
        // Split by multiple spaces to handle various formats
        const parts = line.split(/\s+/);
        
        // Last two parts should be quantity and unit
        if (parts.length >= 3) {
          const unit = parts[parts.length - 1];
          const quantity = parseFloat(parts[parts.length - 2]);
          const name = parts.slice(0, -2).join(' ');

          if (name && !isNaN(quantity) && unit) {
            components.push({ name, quantity, unit });
          }
        }
      }
    }

    // Only return if we have valid data
    if (name && components.length > 0) {
      return {
        name,
        posCode,
        prep,
        components
      };
    }

    return null;
  } finally {
    await worker.terminate();
  }
}

async function cleanup(tempDir: string) {
  if (fs.existsSync(tempDir)) {
    fs.rmSync(tempDir, { recursive: true });
  }
}

async function main() {
  const pdfPath = process.argv[2] || '/Users/<USER>/Downloads/Marketman POS Recipes.pdf';
  const tempDir = path.join(path.dirname(pdfPath), "temp");
  
  try {
    console.log('Converting PDF to images...');
    const imagePaths = await convertPDFToImages(pdfPath);
    console.log(`Created ${imagePaths.length} image files`);

    const recipes: Recipe[] = [];
    
    for (let i = 0; i < imagePaths.length; i++) {
      console.log(`Processing page ${i + 1}/${imagePaths.length}...`);
      const recipe = await extractRecipeFromImage(imagePaths[i]);
      if (recipe) {
        recipes.push(recipe);
        console.log(`Extracted recipe: ${recipe.name}`);
      }
    }

    // Save all recipes to a JSON file
    const outputPath = path.join(
      path.dirname(pdfPath),
      'extracted_recipes.json'
    );
    
    fs.writeFileSync(outputPath, JSON.stringify(recipes, null, 2));
    console.log(`\nExtracted ${recipes.length} recipes and saved to ${outputPath}`);
  } catch (error) {
    console.error('Error processing recipes:', error);
  } finally {
    // Clean up temporary files
    await cleanup(tempDir);
  }
}

if (require.main === module) {
  main();
}
