import fs from 'fs';

interface NormalizedRecipe {
  name: string;
  posCode: string;
  prep: string;
  components: {
    name: string;
    quantity: number;
    unit: string;
  }[];
}

interface BranchInventoryItem {
  branchInventoryId: string;
  itemId: string;
  itemType: 'RECIPE' | 'INGREDIENT';
  name: string;
  unit?: string;
  status?: string;
  components?: {
    inventoryItemId: string;
    quantity: number;
    unit: string;
    name: string;
  }[];
}

function normalizeString(str: string): string {
  return str.toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove all non-alphanumeric characters
    .trim();
}

function findBestMatch(searchName: string, items: BranchInventoryItem[]): BranchInventoryItem | null {
  const normalizedSearch = normalizeString(searchName);
  
  // First try exact match
  const exactMatch = items.find(item => normalizeString(item.name) === normalizedSearch);
  if (exactMatch) return exactMatch;

  // Then try contains match
  const containsMatch = items.find(item => normalizeString(item.name).includes(normalizedSearch) || 
                                         normalizedSearch.includes(normalizeString(item.name)));
  if (containsMatch) return containsMatch;

  return null;
}

async function matchRecipesToInventory() {
  try {
    // Load normalized recipes
    const normalizedRecipes: NormalizedRecipe[] = JSON.parse(
      fs.readFileSync('/Users/<USER>/Downloads/normalized_recipes.json', 'utf-8')
    );
    console.log(`Loaded ${normalizedRecipes.length} normalized recipes`);

    // Load branch inventory
    const branchInventory: BranchInventoryItem[] = JSON.parse(
      fs.readFileSync('/Users/<USER>/Downloads/branch_inventory.json', 'utf-8')
    );
    console.log(`Loaded ${branchInventory.length} branch inventory items`);

    // Split branch inventory into recipes and ingredients
    const recipes = branchInventory.filter(item => item.itemType === 'RECIPE');
    const ingredients = branchInventory.filter(item => item.itemType === 'INGREDIENT');
    
    console.log(`Found ${recipes.length} recipes and ${ingredients.length} ingredients in branch inventory`);

    // Try to match each normalized recipe
    const matches = normalizedRecipes.map(recipe => {
      // Try to find matching recipe in branch inventory
      const matchedRecipe = findBestMatch(recipe.name, recipes);
      
      // Try to match each component
      const matchedComponents = (recipe.components || []).map(component => {
        const matchedIngredient = findBestMatch(component.name, ingredients);
        return {
          originalName: component.name,
          originalQuantity: component.quantity,
          originalUnit: component.unit,
          matched: matchedIngredient ? {
            branchInventoryId: matchedIngredient.branchInventoryId,
            itemId: matchedIngredient.itemId,
            name: matchedIngredient.name,
            unit: matchedIngredient.unit
          } : null
        };
      });

      return {
        recipeName: recipe.name,
        posCode: recipe.posCode,
        matchedRecipe: matchedRecipe ? {
          branchInventoryId: matchedRecipe.branchInventoryId,
          itemId: matchedRecipe.itemId,
          name: matchedRecipe.name,
          components: matchedRecipe.components
        } : null,
        components: matchedComponents,
        matchStatus: {
          recipeMatched: !!matchedRecipe,
          totalComponents: (recipe.components || []).length,
          matchedComponents: matchedComponents.filter(i => i.matched).length,
          matchRate: `${((matchedComponents.filter(i => i.matched).length / (recipe.components || []).length) * 100).toFixed(1)}%`
        }
      };
    });

    // Generate summary
    const summary = {
      totalNormalizedRecipes: normalizedRecipes.length,
      matchedRecipes: matches.filter(m => m.matchedRecipe).length,
      recipeMatchRate: `${((matches.filter(m => m.matchedRecipe).length / normalizedRecipes.length) * 100).toFixed(1)}%`,
      totalComponents: matches.reduce((sum, m) => sum + m.matchStatus.totalComponents, 0),
      matchedComponents: matches.reduce((sum, m) => sum + m.matchStatus.matchedComponents, 0),
      componentMatchRate: `${((matches.reduce((sum, m) => sum + m.matchStatus.matchedComponents, 0) / 
                             matches.reduce((sum, m) => sum + m.matchStatus.totalComponents, 0)) * 100).toFixed(1)}%`
    };

    // Write results
    fs.writeFileSync('/Users/<USER>/Downloads/recipe-matches.json', JSON.stringify(matches, null, 2));
    fs.writeFileSync('/Users/<USER>/Downloads/recipe-matches-summary.json', JSON.stringify(summary, null, 2));

    console.log('Match Summary:', summary);
    
  } catch (error) {
    console.error('Failed:', error);
  }
}

matchRecipesToInventory();
