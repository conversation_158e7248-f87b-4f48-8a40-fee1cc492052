import mongoose from 'mongoose';
import dbConnect from '../lib/db';
import BranchInventory, { IBranchInventory } from '../models/BranchInventory';
import { Ingredient } from '../models/Ingredient';

async function fixBranchInventoryBaseUoms() {
  try {
    await dbConnect();
    console.log('Connected to database');

    // Find all branch inventory records for ingredients
    const branchInventories = await BranchInventory.find({
      itemType: 'INGREDIENT'
    }).lean();

    console.log(`Found ${branchInventories.length} branch inventory records to check`);

    let fixedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Process each inventory record
    for (const inventory of branchInventories) {
      try {
        // Find the corresponding ingredient
        const ingredient = await Ingredient.findById(inventory.itemId);
        
        if (!ingredient) {
          errors.push(`Ingredient not found for inventory ${inventory._id}`);
          errorCount++;
          continue;
        }

        // Check if baseUomId needs updating
        if (inventory.baseUomId.toString() !== ingredient.baseUomId.toString()) {
          console.log(`Fixing baseUomId for inventory ${inventory._id}`);
          console.log(`  Old baseUomId: ${inventory.baseUomId}`);
          console.log(`  New baseUomId: ${ingredient.baseUomId}`);

          // Update the baseUomId
          await BranchInventory.findByIdAndUpdate(inventory._id, {
            baseUomId: ingredient.baseUomId
          });
          fixedCount++;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push(`Error processing inventory ${inventory._id}: ${errorMessage}`);
        errorCount++;
      }
    }

    // Print summary
    console.log('\nMigration Summary:');
    console.log(`Total records checked: ${branchInventories.length}`);
    console.log(`Records fixed: ${fixedCount}`);
    console.log(`Errors encountered: ${errorCount}`);

    if (errors.length > 0) {
      console.log('\nErrors:');
      errors.forEach(error => console.log(`- ${error}`));
    }

  } catch (error) {
    console.error('Migration failed:', error instanceof Error ? error.message : String(error));
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

// Run the migration
fixBranchInventoryBaseUoms();
