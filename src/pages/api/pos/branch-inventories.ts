import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/db';
import { validate<PERSON>pi<PERSON>ey } from '../../../middleware/apiKeyAuth';
import BranchInventory from '../../../models/BranchInventory';
import UOM from '../../../models/UOM';
import Recipe from '../../../models/Recipe';
import { Ingredient } from '../../../models/Ingredient';
import mongoose from 'mongoose';

// CORS configuration
const setCorsHeaders = (res: NextApiResponse) => {
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:8100');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-API-Key, X-Company-Id, X-Location-Id');
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    setCorsHeaders(res);
    return res.status(200).end();
  }

  // Only allow GET method
  if (req.method !== 'GET') {
    setCorsHeaders(res);
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only GET method is allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Validate API key - pass both req and res
    const authResult = validateApiKey(req, res);
    if (authResult) return authResult; // If validateApiKey returns a response, return it
    
    await dbConnect();

    // Extract companyId and locationId from headers
    const rawCompanyId = req.headers['x-company-id'] as string;
    const rawLocationId = req.headers['x-location-id'] as string;

    // Mapping: if locationId is a friendly ID (e.g., 'location1'), map it to the actual ObjectId
    let mappedLocationId = rawLocationId;
    if (rawLocationId === 'location1') {
      mappedLocationId = '676976a081f62ee1ec7c4bef';
    }

    // Cast locationId to ObjectId if valid, otherwise leave as string
    const locationId = mongoose.Types.ObjectId.isValid(mappedLocationId) ? new mongoose.Types.ObjectId(mappedLocationId) : mappedLocationId;
    const companyId = mongoose.Types.ObjectId.isValid(rawCompanyId) ? new mongoose.Types.ObjectId(rawCompanyId) : rawCompanyId;

    // Get all UOMs
    const uoms = await UOM.find().lean();
    console.log('Debug: Found UOMs:', uoms.length);

    // Create UOM map for efficient lookup
    const uomMap = new Map(uoms.map(uom => [uom._id.toString(), { shortCode: uom.shortCode }]));
    
    // Get all ingredients and recipes for this company, including selling details
    const ingredients = await Ingredient.find(
      { companyId: companyId },
      'name category _id sellingDetails baseUomId'
    ).lean();
    
    const recipes = await Recipe.find(
      { companyId: companyId },
      'name Category _id sellingDetails baseUomId'
    ).lean();

    console.log('Debug: Found items:', {
      ingredientsCount: ingredients.length,
      recipesCount: recipes.length
    });

    // Query BranchInventories for the given company and location
    const inventories = await BranchInventory.find({
      companyId,
      locationId,
      isActive: true
    }).lean();

    console.log('Debug: Found inventories:', inventories.length);

    // Group inventories by itemId
    const groupedMap = new Map();
    for (const inv of inventories) {
      const key = inv.itemId.toString();
      if (!groupedMap.has(key)) {
        groupedMap.set(key, []);
      }
      groupedMap.get(key).push(inv);
    }

    // Build the grouped response
    const groupedInventories = Array.from(groupedMap.entries()).map(([itemId, invGroup]) => {
      // Get item details from ingredients or recipes based on the first inventory entry
      const sampleInv = invGroup[0];
      const itemDetails = sampleInv.itemType === 'INGREDIENT'
        ? ingredients.find(i => i._id.toString() === itemId)
        : recipes.find(r => r._id.toString() === itemId);

      console.log('Debug: Processing item:', {
        itemId,
        itemType: sampleInv.itemType,
        name: itemDetails?.name,
        inventoryCount: invGroup.length,
        sellingDetailsCount: itemDetails?.sellingDetails?.length
      });

      // For each inventory record in this group, find the matching selling option
      const alternatives = invGroup.map(inv => {
        let conversionFactor = 1;
        let sellingUomId;
        let sellingOption;

        if (itemDetails?.sellingDetails) {
          sellingOption = itemDetails.sellingDetails.find(
            sd => sd._id.toString() === inv.sellingOptionId
          );
          if (sellingOption) {
            conversionFactor = sellingOption.conversionFactor || 1;
            sellingUomId = sellingOption.unitOfSelling;
          }
        }

        const uomShortCode = sellingUomId ? 
          (uomMap.get(sellingUomId.toString())?.shortCode || '') : '';

        console.log('Debug: Processing alternative:', {
          sellingOptionId: inv.sellingOptionId,
          foundSellingOption: !!sellingOption,
          conversionFactor,
          uomShortCode
        });

        return {
          sellingOptionId: inv.sellingOptionId,
          uom: uomShortCode,
          conversionFactor,
          currentStock: inv.currentStock || 0,
          parLevel: inv.parLevel || 0,
          reorderPoint: inv.reorderPoint || 0,
          lastUpdated: inv.lastUpdated,
          isLocked: inv.isLocked,
          // Add price fields from the sellingOption
          priceWithoutTax: sellingOption?.priceWithoutTax || 0,
          priceWithTax: sellingOption?.priceWithTax || 0,
          taxRate: sellingOption?.taxRate || 0
        };
      });

      // Compute total stock in base UOM
      const totalStockInBase = alternatives.reduce(
        (sum, alt) => sum + (alt.currentStock * alt.conversionFactor), 
        0
      );

      // Get the base UOM shortcode
      const baseUomId = itemDetails?.baseUomId?.toString();
      const baseUomShortCode = baseUomId ? 
        (uomMap.get(baseUomId)?.shortCode || '') : '';

      return {
        _id: sampleInv._id,
        itemId: sampleInv.itemId,
        companyId: sampleInv.companyId,
        locationId: sampleInv.locationId,
        itemType: sampleInv.itemType,
        name: itemDetails?.name || '',
        category: itemDetails?.category || itemDetails?.Category || '',
        baseUom: baseUomShortCode,
        totalStockInBase,
        alternatives,
        updatedAt: sampleInv.updatedAt
      };
    });

    console.log('Debug: Grouped response:', {
      totalItems: groupedInventories.length,
      sample: groupedInventories[0]
    });

    setCorsHeaders(res);
    return res.status(200).json({
      success: true,
      data: groupedInventories,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in branch inventories endpoint:', error);
    setCorsHeaders(res);
    return res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Internal server error'
      },
      timestamp: new Date().toISOString()
    });
  }
}
