// src/pages/api/pos/orders/sync.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import Order from '@/models/Order';
// import { v4 as uuidv4 } from 'uuid'; // Currently unused
import { logSyncEvent } from '@/lib/sync-logger';

// Helper function to set CORS headers
const setCorsHeaders = (res: NextApiResponse) => {
  // Allow requests from Ionic app's development server
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:8100');
  // Allow credentials (cookies, authorization headers)
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  // Allow specific methods
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  // Allow specific headers
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id');
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle OPTIONS preflight requests
  if (req.method === 'OPTIONS') {
    setCorsHeaders(res);
    return res.status(204).end();
  }

  // Set CORS headers for all responses
  setCorsHeaders(res);

  // Connect to database
  await dbConnect();

  try {
    // Only allow POST requests for order sync
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Extract and validate payload
    const { operation, order } = req.body;

    if (!operation || !order) {
      return res.status(400).json({ error: 'Invalid payload format' });
    }

    // Handle based on operation type
    switch (operation) {
      case 'CREATE':
      case 'UPDATE':
        // Process order data
        const {
          orderNumber,
          status,
          buyer,
          seller,
          sellerLocationId,
          buyerLocationId,
          items,
          companyId,
          branchId,
          ...otherFields
        } = order;

        // Validate required fields
        if (!orderNumber || !status || !buyer || !companyId) {
          return res.status(400).json({ 
            error: 'Missing required fields', 
            details: 'Order must include orderNumber, status, buyer, and companyId' 
          });
        }

        // Look for existing order first (by orderNumber or branchId)
        const existingOrder = await Order.findOne({
          $or: [
            { orderNumber },
            { branchId }
          ]
        });

        let result;

        if (existingOrder) {
          // Update existing order
          existingOrder.status = status;
          existingOrder.items = items;
          existingOrder.syncStatus = 'SYNCED';
          existingOrder.lastSyncTimestamp = new Date();
          
          // Set other fields
          for (const [key, value] of Object.entries(otherFields)) {
            if (key !== '_id' && key !== 'id') { // Don't overwrite IDs
              // @ts-expect-error - Dynamic property assignment
              existingOrder[key] = value;
            }
          }

          result = await existingOrder.save();
          
          // Log sync event
          await logSyncEvent(
            companyId,
            buyerLocationId || sellerLocationId,
            orderNumber,
            'ORDER_UPDATED',
            { orderId: result._id.toString() }
          );
          
          return res.status(200).json({
            success: true,
            message: 'Order updated successfully',
            orderId: result._id.toString(),
            syncStatus: 'SYNCED'
          });
        } else {
          // Create new order with generated central system ID
          const newOrder = new Order({
            orderNumber,
            status,
            buyer,
            seller,
            sellerLocationId,
            buyerLocationId,
            items: items || [],
            companyId: new Types.ObjectId(companyId),
            branchId,
            syncStatus: 'SYNCED',
            lastSyncTimestamp: new Date(),
            ...otherFields
          });

          result = await newOrder.save();
          
          // Log sync event
          await logSyncEvent(
            companyId,
            buyerLocationId || sellerLocationId,
            orderNumber,
            'ORDER_CREATED',
            { orderId: result._id.toString() }
          );
          
          return res.status(201).json({
            success: true,
            message: 'Order created successfully',
            orderId: result._id.toString(),
            syncStatus: 'SYNCED'
          });
        }
      
      case 'DELETE':
        // Validate order data for deletion
        if (!order.orderNumber && !order.branchId && !order.id) {
          return res.status(400).json({ 
            error: 'Missing identifier for deletion', 
            details: 'Must provide orderNumber, branchId, or id to delete an order' 
          });
        }

        // Build query to find the order
        const deleteQuery: any = {};
        if (order.orderNumber) deleteQuery.orderNumber = order.orderNumber;
        if (order.branchId) deleteQuery.branchId = order.branchId;
        if (order.id) deleteQuery._id = new Types.ObjectId(order.id);

        // Find and delete the order
        const deletedOrder = await Order.findOneAndUpdate(
          deleteQuery,
          { 
            status: 'DELETED',
            syncStatus: 'SYNCED',
            lastSyncTimestamp: new Date()
          },
          { new: true }
        );

        if (!deletedOrder) {
          return res.status(404).json({ 
            error: 'Order not found', 
            details: 'The order to delete could not be found' 
          });
        }

        // Log sync event
        await logSyncEvent(
          order.companyId,
          order.buyerLocationId || order.sellerLocationId,
          order.orderNumber || 'unknown',
          'ORDER_DELETED',
          { orderId: deletedOrder._id.toString() }
        );

        return res.status(200).json({
          success: true,
          message: 'Order marked as deleted',
          orderId: deletedOrder._id.toString(),
          syncStatus: 'SYNCED'
        });

      default:
        return res.status(400).json({ 
          error: 'Invalid operation', 
          details: `Operation ${operation} is not supported` 
        });
    }
  } catch (error) {
    console.error('Error in order sync:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
