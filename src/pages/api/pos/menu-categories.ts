import { NextApiRequest, NextApiResponse } from 'next';
import { validate<PERSON>pi<PERSON><PERSON> } from '../../../middleware/apiKeyAuth';
import { MenuCategory } from '../../../models/MenuCategory';
import dbConnect from '../../../lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:8100');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-API-Key, X-Company-Id, X-Location-Id, Content-Type'
  );

  // Handle preflight request
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Validate API key
  const authError = validateApiKey(req, res);
  if (authError) return authError;

  try {
    // Connect to database
    await dbConnect();

    // Support pagination
    const page = parseInt(req.query.page as string || '1');
    const limit = parseInt(req.query.limit as string || '100');
    const skip = (page - 1) * limit;

    // Build query
    const query = { 
      companyId: req.companyId,
      status: 'active' // Only return active categories
    };

    // Get total count for pagination
    const total = await MenuCategory.countDocuments(query);

    // Get categories
    const categories = await MenuCategory.find(query)
      .sort({ order: 1 }) // Sort by order field
      .skip(skip)
      .limit(limit)
      .lean();

    // Transform the data for POS use
    const transformedCategories = categories.map(category => ({
      _id: category._id.toString(),
      companyId: category.companyId.toString(),
      name: category.name,
      description: category.description || '',
      order: category.order,
      status: category.status,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt
    }));

    // Return paginated response
    return res.status(200).json({
      categories: transformedCategories,
      total,
      page,
      limit
    });

  } catch (error) {
    console.error('Error in menu categories API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
