// src/pages/api/pos/companies/[companyId]/locations.ts
import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from "@/lib/db";
import Location from "@/models/Location";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Check API key
    const apiKey = req.headers['x-api-key'] || req.headers['X-API-Key'];
    if (apiKey !== 'test_api_key_1') {
      return res.status(401).json({
        success: false,
        message: 'Invalid API key',
        timestamp: new Date().toISOString()
      });
    }

    const { companyId } = req.query;

    // Validate companyId parameter
    if (!companyId || typeof companyId !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Company ID is required',
        timestamp: new Date().toISOString()
      });
    }

    // Connect to database
    await dbConnect();

    // Find all locations for the specified company
    const locations = await Location.find(
      { 
        companyId: companyId
      },
      {
        // Return necessary fields
        _id: 1,
        name: 1,
        address: 1,
        companyId: 1,
      }
    ).lean();

    // Transform the data for the mobile app
    const locationsData = locations.map((location) => ({
      id: location._id.toString(),
      name: location.name,
      address: location.address || '',
      companyId: location.companyId.toString(),
    }));

    return res.status(200).json({
      success: true,
      data: locationsData,
      timestamp: new Date().toISOString()
    });
  } catch (error: unknown) {
    console.error("Error fetching locations:", error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
}