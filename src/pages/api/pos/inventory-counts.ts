import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/db';
import { validateApiKey } from '../../../middleware/apiKeyAuth';
import StockCount from '../../../models/StockCount';

// CORS configuration
const setCorsHeaders = (res: NextApiResponse) => {
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:8100');
  res.setHeader('Access-Control-Allow-Methods', 'POST,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'X-API-Key, X-Company-Id, X-Location-Id, Content-Type');
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  setCorsHeaders(res);

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed'
      },
      timestamp: new Date().toISOString()
    });
  }

  // Validate authentication using API key
  const authError = validateApiKey(req, res);
  if (authError) return;

  try {
    await dbConnect();

    // Validate headers for company and location
    const companyId = req.headers['x-company-id'];
    const locationIdHeader = req.headers['x-location-id'];
    if (!companyId || !locationIdHeader) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_HEADERS',
          message: 'Missing X-Company-Id or X-Location-Id header'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Parse and validate request body
    const { locationId, counts, countDate, performedBy } = req.body;

    // Basic validations
    if (!locationId || !counts || !countDate || !performedBy) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Missing required fields in request body'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Ensure that the locationId in the body matches the header
    if (locationId !== locationIdHeader) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'LOCATION_MISMATCH',
          message: 'The locationId in the body does not match the header'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Additional validations can be applied here (e.g., non-negative counts, valid UOM, etc.)
    // For brevity, we'll assume the request body is valid if these fields are present

    // Create a new StockCount record
    const newCount = await StockCount.create({
      companyId,
      locationId,
      counts,
      countDate,
      performedBy,
      // Optionally, you can set a default status (e.g., 'PENDING_APPROVAL')
      status: 'PENDING_APPROVAL'
    });

    return res.status(200).json({
      success: true,
      data: {
        status: 'success',
        message: 'Inventory count submitted successfully'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error submitting inventory count:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Internal server error',
        details: error
      },
      timestamp: new Date().toISOString()
    });
  }
}
