import { NextApiRequest, NextApiResponse } from 'next';
import { validate<PERSON>pi<PERSON><PERSON> } from '../../../middleware/apiKeyAuth';
import Location from '../../../models/Location';
import dbConnect from '../../../lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Validate API key
  const authError = validateApiKey(req, res);
  if (authError) return authError;

  try {
    // Connect to database
    await dbConnect();

    // Get locations for company
    const locations = await Location.find({
      companyId: req.companyId,
      // Only get retail locations that can sell
      locationType: { $in: ['RETAIL_SHOP', 'SINGLE_LOCATION'] },
      canSellToExternal: true
    });

    // Transform the data for POS use
    const transformedLocations = locations.map(location => ({
      id: location._id,
      name: location.name,
      type: location.locationType,
      address: location.address,
      contactInfo: location.contactInfo
    }));

    return res.status(200).json({
      success: true,
      data: transformedLocations,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching locations:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
