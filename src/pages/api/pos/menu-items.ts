import { NextApiRequest, NextApiResponse } from 'next';
import { validate<PERSON>pi<PERSON><PERSON> } from '../../../middleware/apiKeyAuth';
import { MenuItem } from '../../../models/MenuItem';
import { MenuCategory } from '../../../models/MenuCategory';
import dbConnect from '../../../lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:8100');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-API-Key, X-Company-Id, X-Location-Id, Content-Type'
  );

  // Handle preflight request
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Validate API key
  const authError = validateApiKey(req, res);
  if (authError) return authError;

  try {
    // Connect to database
    await dbConnect();

    // Get menu items for company
    const menuItems = await MenuItem.find({
      companyId: req.companyId,
      status: 'active'  // Only get active menu items
    }).lean();  // Use lean() for better performance

    // Transform the data for POS use
    const transformedItems = menuItems.map(item => ({
      id: item._id,
      name: item.name,
      description: item.description,
      category: item.categoryId,  // Just return the ID for now
      type: item.type,
      price: {
        base: item.prices.basePrice,
        vat: item.prices.vatAmount,
        final: item.prices.finalPrice
      },
      image: item.image,
      inventoryStatus: item.inventoryStatus,
      inventoryItemId: item.inventoryItemId,
      recipeComponents: item.recipeComponents
    }));

    return res.status(200).json({
      success: true,
      data: transformedItems,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ error: 'Failed to fetch menu items' });
  }
}
