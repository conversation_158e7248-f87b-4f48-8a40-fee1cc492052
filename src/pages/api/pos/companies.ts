// src/pages/api/pos/companies.ts
import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from "@/lib/db";
import Company from "@/models/Company";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    // Check API key
    const apiKey = req.headers['x-api-key'] || req.headers['X-API-Key'];
    if (apiKey !== 'test_api_key_1') {
      return res.status(401).json({
        success: false,
        message: 'Invalid API key',
        timestamp: new Date().toISOString()
      });
    }

    // Connect to database
    await dbConnect();

    // Find all companies that have POS access configured
    const companies = await Company.find(
      { 
        // Only return companies that have POS password set up
        posPassword: { $exists: true, $ne: null }
      },
      {
        // Only return necessary fields for security
        _id: 1,
        name: 1,
        companyCode: 1,
      }
    ).lean();

    // Transform the data for the mobile app
    const companiesData = companies.map((company) => ({
      id: company._id.toString(),
      name: company.name,
      code: company.companyCode,
    }));

    return res.status(200).json({
      success: true,
      data: companiesData,
      timestamp: new Date().toISOString()
    });
  } catch (error: unknown) {
    console.error("Error fetching companies:", error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
}