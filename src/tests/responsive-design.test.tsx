/** @jest-environment jsdom */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { renderWithAuth } from './test-utils';
import { CompanyNav } from '../components/CompanyNav';
import { AdminDashboard } from '../components/dashboards/AdminDashboard';
import { ManagerDashboard } from '../components/dashboards/ManagerDashboard';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: query === '(max-width: 768px)', // Match mobile query
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn((event, handler) => {
      if (event === 'change') handler({ matches: query === '(max-width: 768px)' });
    }),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('Responsive Design', () => {
  const mockCompanyData = {
    id: 'company-1',
    name: 'Test Company',
    subdomain: 'test',
    locations: [
      { id: 'loc-1', name: 'Location 1', address: '123 Main St' },
    ],
  };

  describe('Navigation Collapse', () => {
    const adminUserData = {
      role: 'admin' as const,
      companyId: 'company-1',
      userType: 'company_user' as const,
      permissions: ['all'],
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'Test Admin',
      lastLogin: new Date(),
      createdAt: new Date(),
    };

    beforeEach(() => {
      // Reset to desktop view
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn((event, handler) => {
          if (event === 'change') handler({ matches: false });
        }),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));
    });

    it('should show expanded navigation on desktop', () => {
      renderWithAuth(
        <CompanyNav />,
        { userData: adminUserData, companyData: mockCompanyData }
      );

      expect(screen.getByTestId('nav-expanded')).toBeVisible();
      expect(screen.queryByTestId('nav-collapsed')).not.toBeInTheDocument();
    });

    it('should collapse navigation on mobile', () => {
      // Mock mobile view
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 768px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn((event, handler) => {
          if (event === 'change') handler({ matches: true });
        }),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      renderWithAuth(
        <CompanyNav />,
        { userData: adminUserData, companyData: mockCompanyData }
      );

      expect(screen.getByTestId('nav-collapsed')).toBeVisible();
      expect(screen.queryByTestId('nav-expanded')).not.toBeInTheDocument();
    });

    it('should toggle navigation on mobile menu click', () => {
      // Mock mobile view
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 768px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn((event, handler) => {
          if (event === 'change') handler({ matches: true });
        }),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      renderWithAuth(
        <CompanyNav />,
        { userData: adminUserData, companyData: mockCompanyData }
      );

      const menuButton = screen.getByTestId('mobile-menu-button');
      fireEvent.click(menuButton);

      expect(screen.getByTestId('nav-expanded')).toBeVisible();
    });
  });

  describe('Dashboard Layout Responsiveness', () => {
    it('should adjust admin dashboard layout for mobile', () => {
      // Mock mobile view
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 768px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn((event, handler) => {
          if (event === 'change') handler({ matches: true });
        }),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      renderWithAuth(
        <AdminDashboard />,
        {
          userData: {
            role: 'admin' as const,
            companyId: 'company-1',
            userType: 'company_user' as const,
            permissions: ['all'],
            uid: 'test-uid',
            email: '<EMAIL>',
            displayName: 'Test Admin',
            lastLogin: new Date(),
            createdAt: new Date(),
          },
          companyData: mockCompanyData
        }
      );

      expect(screen.getByTestId('dashboard-mobile')).toBeVisible();
      expect(screen.queryByTestId('dashboard-desktop')).not.toBeInTheDocument();
    });

    it('should adjust manager dashboard layout for mobile', () => {
      // Mock mobile view
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 768px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn((event, handler) => {
          if (event === 'change') handler({ matches: true });
        }),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      renderWithAuth(
        <ManagerDashboard />,
        {
          userData: {
            role: 'manager' as const,
            companyId: 'company-1',
            locationId: 'loc-1',
            userType: 'company_user' as const,
            permissions: ['location_management'],
            uid: 'test-manager',
            email: '<EMAIL>',
            displayName: 'Test Manager',
            lastLogin: new Date(),
            createdAt: new Date(),
          },
          companyData: mockCompanyData
        }
      );

      expect(screen.getByTestId('dashboard-mobile')).toBeVisible();
      expect(screen.queryByTestId('dashboard-desktop')).not.toBeInTheDocument();
    });
  });
});
