import { describe, it, expect, beforeEach } from '@jest/globals';
import { renderWithAuth } from './test-utils';
import React from 'react';
import { CompanyData } from '../lib/types/company';
import { CompanyUserData } from '../lib/auth';

// Import page components
import AdminDashboardPage from '../app/company/[companyId]/admin/dashboard/page';
import ManagerDashboardPage from '../app/company/[companyId]/manager/dashboard/page';
import StorekeeperInventoryCountPage from '../app/company/[companyId]/storekeeper/inventory-count/page';

// Mock the auth module
const mockUseRequireCompanyUser = jest.fn();
jest.mock('../lib/auth', () => ({
  useRequireCompanyUser: (...args: unknown[]) => mockUseRequireCompanyUser(...args),
}));

// Mock the company service
const mockGetCompanyData = jest.fn();
jest.mock('../lib/services/companyService', () => ({
  getCompanyData: (...args: unknown[]) => mockGetCompanyData(...args),
}));

describe('Role-based Access Control', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Admin Routes Access', () => {
    it('should allow admin to access admin routes', () => {
      const userData: CompanyUserData = {
        role: 'admin',
        companyId: 'test-company',
        uid: 'admin-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(AdminDashboardPage), { userData });
      }).not.toThrow();
    });

    it('should redirect manager from admin routes', () => {
      const userData: CompanyUserData = {
        role: 'manager',
        companyId: 'test-company',
        uid: 'manager-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(AdminDashboardPage), { userData });
      }).toThrow('Redirected to /');
    });

    it('should redirect storekeeper from admin routes', () => {
      const userData: CompanyUserData = {
        role: 'storekeeper',
        companyId: 'test-company',
        uid: 'storekeeper-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(AdminDashboardPage), { userData });
      }).toThrow('Redirected to /');
    });
  });

  describe('Manager Routes Access', () => {
    it('should allow manager to access manager routes', () => {
      const userData: CompanyUserData = {
        role: 'manager',
        companyId: 'test-company',
        uid: 'manager-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(ManagerDashboardPage), { userData });
      }).not.toThrow();
    });

    it('should redirect storekeeper from manager routes', () => {
      const userData: CompanyUserData = {
        role: 'storekeeper',
        companyId: 'test-company',
        uid: 'storekeeper-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(ManagerDashboardPage), { userData });
      }).toThrow('Redirected to /');
    });
  });

  describe('Storekeeper Routes Access', () => {
    it('should allow storekeeper to access storekeeper routes', () => {
      const userData: CompanyUserData = {
        role: 'storekeeper',
        companyId: 'test-company',
        uid: 'storekeeper-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(StorekeeperInventoryCountPage), { userData });
      }).not.toThrow();
    });

    it('should allow manager to access storekeeper routes', () => {
      const userData: CompanyUserData = {
        role: 'manager',
        companyId: 'test-company',
        uid: 'manager-user-123',
        email: '<EMAIL>',
        userType: 'company_user',
      };

      mockUseRequireCompanyUser.mockReturnValue({
        userData,
        loading: false,
      });

      expect(() => {
        renderWithAuth(React.createElement(StorekeeperInventoryCountPage), { userData });
      }).not.toThrow();
    });
  });
});

describe('Company Data Isolation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not allow access to company A data from company B context', () => {
    const companyAData: CompanyData = {
      id: 'company-a',
      name: 'Company A',
      locations: [],
      ownerId: 'owner-user-id',
      roles: { 'admin-user-123': 'admin' },
      permissions: { 'admin-user-123': ['all'] },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const companyBUserData: CompanyUserData = {
      role: 'admin',
      companyId: 'company-b',
      uid: 'admin-user-123',
      email: '<EMAIL>',
      userType: 'company_user',
    };

    mockUseRequireCompanyUser.mockReturnValue({
      userData: companyBUserData,
      loading: false,
    });

    mockGetCompanyData.mockResolvedValue(companyAData);

    expect(() => {
      renderWithAuth(React.createElement(AdminDashboardPage), {
        userData: companyBUserData,
        companyData: companyAData,
      });
    }).toThrow('Unauthorized');
  });

  it('should allow access to correct company data', () => {
    const companyAData: CompanyData = {
      id: 'company-a',
      name: 'Company A',
      locations: [],
      ownerId: 'owner-user-id',
      roles: { 'admin-user-123': 'admin' },
      permissions: { 'admin-user-123': ['all'] },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const companyAUserData: CompanyUserData = {
      role: 'admin',
      companyId: 'company-a',
      uid: 'admin-user-123',
      email: '<EMAIL>',
      userType: 'company_user',
    };

    mockUseRequireCompanyUser.mockReturnValue({
      userData: companyAUserData,
      loading: false,
    });

    mockGetCompanyData.mockResolvedValue(companyAData);

    expect(() => {
      renderWithAuth(React.createElement(AdminDashboardPage), {
        userData: companyAUserData,
        companyData: companyAData,
      });
    }).not.toThrow();
  });

  it('should prevent cross-company data access in API calls', async () => {
    const companyBUserData: CompanyUserData = {
      role: 'admin',
      companyId: 'company-b',
      uid: 'admin-user-123',
      email: '<EMAIL>',
      userType: 'company_user',
    };

    mockUseRequireCompanyUser.mockReturnValue({
      userData: companyBUserData,
      loading: false,
    });

    mockGetCompanyData.mockImplementation((id) => {
      if (id === 'company-a') {
        return Promise.reject(new Error('Unauthorized'));
      }
      return Promise.resolve(null);
    });
    await expect(mockGetCompanyData('company-a')).rejects.toThrow('Unauthorized');
  });
});

describe('Redirect Behavior', () => {
  it('should attempt redirect if manager tries to access admin route', () => {
    const userData: CompanyUserData = {
      role: 'manager',
      companyId: 'test-company',
      uid: 'manager-user-123',
      email: '<EMAIL>',
      userType: 'company_user',
    };

    mockUseRequireCompanyUser.mockImplementation(() => {
      return { userData, loading: false };
    });

    renderWithAuth(React.createElement(AdminDashboardPage), { userData });
  });

  it('should attempt redirect if storekeeper tries to access manager route', () => {
    const userData: CompanyUserData = {
      role: 'storekeeper',
      companyId: 'test-company',
      uid: 'storekeeper-user-123',
      email: '<EMAIL>',
      userType: 'company_user',
    };

    mockUseRequireCompanyUser.mockImplementation(() => {
      return { userData, loading: false };
    });

    renderWithAuth(React.createElement(ManagerDashboardPage), { userData });
  });
});
