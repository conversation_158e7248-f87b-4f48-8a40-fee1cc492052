import { createContext, useContext } from 'react';

export interface CompanyUserData {
  role: 'admin' | 'manager' | 'storekeeper';
  companyId?: string;
}

export interface CompanyContextType {
  userData: CompanyUserData | null;
  loading: boolean;
  useRequireCompanyUser: (requiredRole?: string) => {
    userData: CompanyUserData | null;
    loading: boolean;
  };
}

export const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const useCompanyContext = () => {
  const context = useContext(CompanyContext);
  if (!context) {
    throw new Error('useCompanyContext must be used within a CompanyContextProvider');
  }
  return context;
};
