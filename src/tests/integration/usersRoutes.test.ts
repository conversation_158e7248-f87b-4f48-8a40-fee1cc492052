import mongoose from 'mongoose';
import { NextRequest } from 'next/server';
import { GET as listUsers, POST as createUser } from '@/app/api/company/[companyId]/users/route';
import { GET as getUser, PUT as updateUser, DELETE as deleteUser } from '@/app/api/company/[companyId]/users/[userId]/route';

// Bypass auth: always supply a superuser/owner
jest.mock('@/lib/auth-helpers', () => ({
  withAuth: (handler: any) => {
    return async (req: any, context: any) => {
      const params = await context.params;
      const dummyUser = { userType: 'superuser', companyId: params.companyId, role: 'owner' };
      return handler(req, dummyUser, context);
    };
  },
}));

describe('Users API - CRUD', () => {
  const companyId = new mongoose.Types.ObjectId().toString();
  let createdUserId: string;

  it('creates a user (POST)', async () => {
    const url = `http://localhost/api/company/${companyId}/users`;
    const req = new NextRequest(url, {
      method: 'POST',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        displayName: 'Test User',
        role: 'user',
        password: 'password123',
      }),
    });
    const res = await createUser(req, { params: Promise.resolve({ companyId }) });
    const json = await res.json();
    expect(json.message).toBe('User created successfully');
    expect(json.user).toHaveProperty('_id');
    expect(json.user.email).toBe('<EMAIL>');
    createdUserId = json.user._id;
  });

  it('lists users (GET list)', async () => {
    const url = `http://localhost/api/company/${companyId}/users`;
    const req = new NextRequest(url, { headers: { 'company-id': companyId } });
    const res = await listUsers(req, { params: Promise.resolve({ companyId }) });
    const json = await res.json();
    expect(Array.isArray(json.users)).toBe(true);
    expect(json.users.length).toBe(1);
  });

  it('fetches single user (GET by ID)', async () => {
    const url = `http://localhost/api/company/${companyId}/users/${createdUserId}`;
    const req = new NextRequest(url, { headers: { 'company-id': companyId } });
    const res = await getUser(req, { params: Promise.resolve({ companyId, userId: createdUserId }) });
    const json = await res.json();
    expect(json.user._id).toBe(createdUserId);
    expect(json.user.email).toBe('<EMAIL>');
  });

  it('updates a user (PUT)', async () => {
    const url = `http://localhost/api/company/${companyId}/users/${createdUserId}`;
    const req = new NextRequest(url, {
      method: 'PUT',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ displayName: 'Updated User' }),
    });
    const res = await updateUser(req, { params: Promise.resolve({ companyId, userId: createdUserId }) });
    const json = await res.json();
    expect(json.message).toBe('User updated successfully');
    expect(json.user.displayName).toBe('Updated User');
  });

  it('soft-deletes a user (DELETE)', async () => {
    const url = `http://localhost/api/company/${companyId}/users/${createdUserId}`;
    const req = new NextRequest(url, { method: 'DELETE', headers: { 'company-id': companyId } });
    const res = await deleteUser(req, { params: Promise.resolve({ companyId, userId: createdUserId }) });
    const json = await res.json();
    expect(json.message).toBe('User deleted successfully');

    // Verify user no longer appears in list
    const listReq = new NextRequest(`http://localhost/api/company/${companyId}/users`, { headers: { 'company-id': companyId } });
    const listRes = await listUsers(listReq, { params: Promise.resolve({ companyId }) });
    const listJson = await listRes.json();
    expect(listJson.users.find((u: any) => u._id === createdUserId)).toBeUndefined();
  });
});
