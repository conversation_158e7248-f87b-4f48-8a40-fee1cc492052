import mongoose from 'mongoose';
import { NextRequest } from 'next/server';
import Role from '@/models/Role';
import { GET as listRoles, POST as createRole } from '@/app/api/company/[companyId]/roles/route';
import { GET as getRole, PUT as updateRole, DELETE as deleteRole } from '@/app/api/company/[companyId]/roles/[roleId]/route';

// Bypass auth: always supply a superuser/owner
jest.mock('@/lib/auth-helpers', () => ({
  withAuth: (handler: any) => {
    return async (req: any, context: any) => {
      const params = await context.params;
      const dummyUser = { userType: 'superuser', companyId: params.companyId, role: 'owner' };
      return handler(req, dummyUser, context);
    };
  },
}));

describe('Roles API - CRUD', () => {
  const companyId = new mongoose.Types.ObjectId().toString();

  beforeEach(async () => {
    await Role.deleteMany({});
  });

  it('creates a role (POST)', async () => {
    const url = `http://localhost/api/company/${companyId}/roles`;
    const req = new NextRequest(url, {
      method: 'POST',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ name: 'Test Role', description: 'A test', permissions: ['perm1', 'perm2'] }),
    });
    const res = await createRole(req, { params: Promise.resolve({ companyId }) });
    const json = await res.json();
    expect(json.message).toBe('Role created successfully');
    expect(json.role).toHaveProperty('_id');
    expect(json.role.name).toBe('Test Role');
    expect(json.role.description).toBe('A test');
    expect(json.role.permissions).toEqual(['perm1', 'perm2']);
  });

  it('lists roles (GET list)', async () => {
    // seed one role
    const seedReq = new NextRequest(`http://localhost/api/company/${companyId}/roles`, {
      method: 'POST',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ name: 'List Role', description: 'desc', permissions: [] }),
    });
    await createRole(seedReq, { params: Promise.resolve({ companyId }) });

    const url = `http://localhost/api/company/${companyId}/roles`;
    const req = new NextRequest(url, { headers: { 'company-id': companyId } });
    const res = await listRoles(req, { params: Promise.resolve({ companyId }) });
    const json = await res.json();
    expect(Array.isArray(json.roles)).toBe(true);
    expect(json.roles.length).toBe(1);
    expect(json.roles[0].name).toBe('List Role');
  });

  it('fetches single role (GET by ID)', async () => {
    // seed one role
    const seedReq = new NextRequest(`http://localhost/api/company/${companyId}/roles`, {
      method: 'POST',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ name: 'Single Role', description: 'fetch me', permissions: [] }),
    });
    const seedRes = await createRole(seedReq, { params: Promise.resolve({ companyId }) });
    const createJson = await seedRes.json();
    const roleId = createJson.role._id;

    const url = `http://localhost/api/company/${companyId}/roles/${roleId}`;
    const req = new NextRequest(url, { headers: { 'company-id': companyId } });
    const res = await getRole(req, { params: Promise.resolve({ companyId, roleId }) });
    const json = await res.json();
    expect(json.role._id).toBe(roleId);
    expect(json.role.name).toBe('Single Role');
  });

  it('updates a role (PUT)', async () => {
    // seed one role
    const seedReq = new NextRequest(`http://localhost/api/company/${companyId}/roles`, {
      method: 'POST',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ name: 'Updatable Role', description: 'old', permissions: [] }),
    });
    const seedRes = await createRole(seedReq, { params: Promise.resolve({ companyId }) });
    const { role } = await seedRes.json();
    const roleId = role._id;

    const url = `http://localhost/api/company/${companyId}/roles/${roleId}`;
    const req = new NextRequest(url, {
      method: 'PUT',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ name: 'Updated Role', description: 'new' }),
    });
    const res = await updateRole(req, { params: Promise.resolve({ companyId, roleId }) });
    const json = await res.json();
    expect(json.message).toBe('Role updated successfully');
    expect(json.role.name).toBe('Updated Role');
    expect(json.role.description).toBe('new');
  });

  it('deletes a role (DELETE)', async () => {
    // seed one role
    const seedReq = new NextRequest(`http://localhost/api/company/${companyId}/roles`, {
      method: 'POST',
      headers: { 'company-id': companyId, 'content-type': 'application/json' },
      body: JSON.stringify({ name: 'Delete Role', description: '', permissions: [] }),
    });
    const seedRes = await createRole(seedReq, { params: Promise.resolve({ companyId }) });
    const { role } = await seedRes.json();
    const roleId = role._id;

    const urlDelete = `http://localhost/api/company/${companyId}/roles/${roleId}`;
    const reqDelete = new NextRequest(urlDelete, { method: 'DELETE', headers: { 'company-id': companyId } });
    const resDelete = await deleteRole(reqDelete, { params: Promise.resolve({ companyId, roleId }) });
    const jsonDelete = await resDelete.json();
    expect(jsonDelete.message).toBe('Role deleted successfully');

    // verify removal
    const listReq = new NextRequest(`http://localhost/api/company/${companyId}/roles`, { headers: { 'company-id': companyId } });
    const listRes = await listRoles(listReq, { params: Promise.resolve({ companyId }) });
    const listJson = await listRes.json();
    expect(listJson.roles.find((r: any) => r._id === roleId)).toBeUndefined();
  });
});
