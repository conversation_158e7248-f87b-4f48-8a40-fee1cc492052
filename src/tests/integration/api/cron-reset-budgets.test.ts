import { NextRequest } from 'next/server';
import { Types } from 'mongoose';
import Company from '@/models/Company';
import BranchInventory from '@/models/BranchInventory';

// --- Core mock functions for Company chain ---
const mockCompanyLean = jest.fn();
const mockCompanySelect = jest.fn(() => ({ lean: mockCompanyLean }));
const mockCompanyFind = jest.fn(() => ({ select: mockCompanySelect }));

// --- Core mock functions for BranchInventory chain ---
const mockInventoryLean = jest.fn();
const mockInventorySelect = jest.fn(() => ({ lean: mockInventoryLean }));
const mockInventoryFind = jest.fn(() => ({ select: mockInventorySelect }));
const mockInventoryUpdateMany = jest.fn();

// Mock database connection
jest.mock('@/lib/db', () => ({ __esModule: true, default: jest.fn(() => Promise.resolve()) }));

// We'll use spies on actual model static methods for mocking

// Dynamic import of route after mocks
let GET: typeof import('@/app/api/cron/reset-budgets/route').GET;

describe('GET /api/cron/reset-budgets', () => {
  beforeEach(async () => {
    jest.resetModules();
    const routeModule = await import('@/app/api/cron/reset-budgets/route');
    GET = routeModule.GET;
    // Spy on model static methods
    jest.spyOn(Company, 'find').mockImplementation(mockCompanyFind as any);
    jest.spyOn(BranchInventory, 'find').mockImplementation(mockInventoryFind as any);
    jest.spyOn(BranchInventory, 'updateMany').mockImplementation(mockInventoryUpdateMany as any);
    jest.useFakeTimers();
    mockCompanyFind.mockClear();
    mockCompanySelect.mockClear();
    mockCompanyLean.mockClear();
    mockInventoryFind.mockClear();
    mockInventorySelect.mockClear();
    mockInventoryLean.mockClear();
    mockInventoryUpdateMany.mockClear();
    mockCompanyLean.mockResolvedValue([]);
    mockInventoryLean.mockResolvedValue([]);
    mockInventoryUpdateMany.mockResolvedValue({ acknowledged: true, matchedCount: 0, modifiedCount: 0 });
  });
  afterEach(() => { jest.useRealTimers(); });

  // --- Test Cases ---

  it('should require CRON_SECRET', async () => {
    const originalSecret = process.env.CRON_SECRET;
    delete process.env.CRON_SECRET;
    const request = new NextRequest('http://localhost/api/cron/reset-budgets');
    const response = await GET(request);
    const responseBody = await response.json();

    expect(response.status).toBe(503);
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('CRON_SECRET not set');

    process.env.CRON_SECRET = originalSecret; // Restore secret
  });

  it('should require correct CRON_SECRET', async () => {
    const request = new NextRequest('http://localhost/api/cron/reset-budgets', {
      headers: { Authorization: 'Bearer WRONG_SECRET' },
    });
    const response = await GET(request);
    const responseBody = await response.json();

    expect(response.status).toBe(401);
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toBe('Unauthorized');
  });

  it('should run successfully even if no companies or inventory need updates', async () => {
    // Arrange
    process.env.CRON_SECRET = 'TEST_SECRET';
    // Default mocks (empty arrays) are already set in beforeEach

    const request = new NextRequest('http://localhost/api/cron/reset-budgets', {
      headers: { Authorization: 'Bearer TEST_SECRET' },
    });

    // Act
    const response = await GET(request);
    const responseBody = await response.json();

    // Assert
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.message).toContain('completed');
    expect(responseBody.details).toEqual([]); // No companies found or processed
    expect(mockCompanyLean).toHaveBeenCalledTimes(1);
    expect(mockInventoryLean).toHaveBeenCalledTimes(0); // Not called if no companies
    expect(mockInventoryUpdateMany).toHaveBeenCalledTimes(0);
  });

  it('should reset DAILY budgets correctly', async () => {
    // Arrange
    process.env.CRON_SECRET = 'TEST_SECRET';
    const companyId = new Types.ObjectId();
    jest.useFakeTimers().setSystemTime(new Date('2024-01-02T08:00:00.000Z')); // Tuesday

    // Override mocks for this specific test
    const mockCompanyData = [{ _id: companyId, name: 'Test Co', weekStartDay: 1 }];
    mockCompanyFind.mockResolvedValueOnce(mockCompanyData);

    const mockInventoryData = [
      { _id: new Types.ObjectId(), companyId: companyId, branchId: new Types.ObjectId(), name: 'Daily Item 1', budgetPeriod: 'daily', budgetAmount: 10, budgetUsed: 5 },
      { _id: new Types.ObjectId(), companyId: companyId, branchId: new Types.ObjectId(), name: 'Daily Item 2', budgetPeriod: 'daily', budgetAmount: 20, budgetUsed: 15 },
      { _id: new Types.ObjectId(), companyId: companyId, branchId: new Types.ObjectId(), name: 'Weekly Item', budgetPeriod: 'weekly', budgetAmount: 50, budgetUsed: 30 }, // Should not be reset daily
    ];
    mockInventoryLean.mockResolvedValueOnce(mockInventoryData);

    const mockUpdateResult = { acknowledged: true, matchedCount: 2, modifiedCount: 2 };
    mockInventoryUpdateMany.mockResolvedValueOnce(mockUpdateResult); // Expecting daily reset

    const request = new NextRequest('http://localhost/api/cron/reset-budgets', {
      headers: { Authorization: 'Bearer TEST_SECRET' },
    });

    // Act
    const response = await GET(request);
    const responseBody = await response.json();

    // --- Assert ---
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.details).toHaveLength(1); // One company processed
    expect(responseBody.details[0].companyId).toBe(companyId.toString());
    expect(responseBody.details[0].daily?.updatedCount).toBe(2); // 2 daily items reset
    expect(responseBody.details[0].weekly?.updatedCount).toBe(0);
    expect(responseBody.details[0].monthly?.updatedCount).toBe(0);

    // Verify mock calls
    expect(mockCompanyLean).toHaveBeenCalledTimes(1);
    expect(mockInventoryFind).toHaveBeenCalledTimes(1); // Called once for the company
    expect(mockInventoryUpdateMany).toHaveBeenCalledTimes(1); // Only daily update called
    expect(mockInventoryUpdateMany).toHaveBeenCalledWith(
      { companyId: companyId, budgetPeriod: 'daily' }, // Filter for daily items of this company
      { $set: { budgetUsed: 0 } } // Action to reset budgetUsed
    );
  });

  it('should reset WEEKLY budgets correctly based on weekStartDay', async () => {
    // Arrange
    process.env.CRON_SECRET = 'TEST_SECRET';
    const companyIdMondayStart = new Types.ObjectId();
    const companyIdSundayStart = new Types.ObjectId();
    // Set time to a Monday
    jest.useFakeTimers().setSystemTime(new Date('2024-01-08T08:00:00.000Z')); // Monday

    const mockCompaniesData = [
      { _id: companyIdMondayStart, name: 'Test Co Mon', weekStartDay: 1 }, // Week starts Monday
      { _id: companyIdSundayStart, name: 'Test Co Sun', weekStartDay: 0 }  // Week starts Sunday
    ];
    mockCompanyFind.mockResolvedValueOnce(mockCompaniesData);

    const mockInventoryData = [
      // Items for Monday-start company (daily + weekly should be reset)
      { _id: new Types.ObjectId(), companyId: companyIdMondayStart, branchId: new Types.ObjectId(), name: 'Mon Daily', budgetPeriod: 'daily', budgetAmount: 10, budgetUsed: 5 },
      { _id: new Types.ObjectId(), companyId: companyIdMondayStart, branchId: new Types.ObjectId(), name: 'Mon Weekly', budgetPeriod: 'weekly', budgetAmount: 100, budgetUsed: 50 },
      // Items for Sunday-start company (only daily should be reset)
      { _id: new Types.ObjectId(), companyId: companyIdSundayStart, branchId: new Types.ObjectId(), name: 'Sun Daily', budgetPeriod: 'daily', budgetAmount: 20, budgetUsed: 10 },
      { _id: new Types.ObjectId(), companyId: companyIdSundayStart, branchId: new Types.ObjectId(), name: 'Sun Weekly', budgetPeriod: 'weekly', budgetAmount: 200, budgetUsed: 80 },
    ];
    // This will be called PER company inside the loop
    mockInventoryLean
      .mockResolvedValueOnce([mockInventoryData[0], mockInventoryData[1]]) // First call for MonStart company
      .mockResolvedValueOnce([mockInventoryData[2], mockInventoryData[3]]); // Second call for SunStart company

    // Mock updateMany results - called PER company PER period if needed
    const mockDailyUpdateResultMon = { acknowledged: true, matchedCount: 1, modifiedCount: 1 };
    const mockWeeklyUpdateResultMon = { acknowledged: true, matchedCount: 1, modifiedCount: 1 };
    const mockDailyUpdateResultSun = { acknowledged: true, matchedCount: 1, modifiedCount: 1 };

    mockInventoryUpdateMany
      .mockResolvedValueOnce(mockDailyUpdateResultMon)   // Daily for MonStart
      .mockResolvedValueOnce(mockWeeklyUpdateResultMon)  // Weekly for MonStart
      .mockResolvedValueOnce(mockDailyUpdateResultSun);   // Daily for SunStart
      // Weekly for SunStart should NOT be called

    const request = new NextRequest('http://localhost/api/cron/reset-budgets', {
      headers: { Authorization: 'Bearer TEST_SECRET' },
    });

    // Act
    const response = await GET(request);
    const responseBody = await response.json();

    // --- Assert ---
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.details).toHaveLength(2); // Two companies processed

    // Find details for each company (order might vary)
    const monDetails = responseBody.details.find((d: any) => d.companyId === companyIdMondayStart.toString());
    const sunDetails = responseBody.details.find((d: any) => d.companyId === companyIdSundayStart.toString());

    expect(monDetails).toBeDefined();
    expect(monDetails.daily.updatedCount).toBe(1);
    expect(monDetails.weekly.updatedCount).toBe(1); // Reset weekly because it's Monday
    expect(monDetails.monthly.updatedCount).toBe(0);

    expect(sunDetails).toBeDefined();
    expect(sunDetails.daily.updatedCount).toBe(1);
    expect(sunDetails.weekly.updatedCount).toBe(0); // NOT reset weekly because week starts Sunday
    expect(sunDetails.monthly.updatedCount).toBe(0);

    // Verify mock calls
    expect(mockCompanyLean).toHaveBeenCalledTimes(1);
    expect(mockInventoryFind).toHaveBeenCalledTimes(2); // Called once per company
    expect(mockInventoryUpdateMany).toHaveBeenCalledTimes(3); // Daily(Mon), Weekly(Mon), Daily(Sun)
    // Check specific calls
    expect(mockInventoryUpdateMany).toHaveBeenCalledWith({ companyId: companyIdMondayStart, budgetPeriod: 'daily' }, { $set: { budgetUsed: 0 } });
    expect(mockInventoryUpdateMany).toHaveBeenCalledWith({ companyId: companyIdMondayStart, budgetPeriod: 'weekly' }, { $set: { budgetUsed: 0 } });
    expect(mockInventoryUpdateMany).toHaveBeenCalledWith({ companyId: companyIdSundayStart, budgetPeriod: 'daily' }, { $set: { budgetUsed: 0 } });
    expect(mockInventoryUpdateMany).not.toHaveBeenCalledWith({ companyId: companyIdSundayStart, budgetPeriod: 'weekly' }, { $set: { budgetUsed: 0 } });

  });

  it('should reset MONTHLY budgets correctly', async () => {
     // Arrange
    process.env.CRON_SECRET = 'TEST_SECRET';
    const companyId = new Types.ObjectId();
    // Set time to the first day of a month
    jest.useFakeTimers().setSystemTime(new Date('2024-02-01T08:00:00.000Z')); // Feb 1st

    const mockCompanyData = [
      { _id: companyId, name: 'Test Co Monthly', weekStartDay: 1 },
    ];
    mockCompanyFind.mockResolvedValueOnce(mockCompanyData);

    const mockInventoryData = [
      { _id: new Types.ObjectId(), companyId: companyId, branchId: new Types.ObjectId(), name: 'Monthly Item', budgetPeriod: 'monthly', budgetAmount: 1000, budgetUsed: 500 },
      { _id: new Types.ObjectId(), companyId: companyId, branchId: new Types.ObjectId(), name: 'Daily Item', budgetPeriod: 'daily', budgetAmount: 10, budgetUsed: 5 },
      { _id: new Types.ObjectId(), companyId: companyId, branchId: new Types.ObjectId(), name: 'Weekly Item', budgetPeriod: 'weekly', budgetAmount: 50, budgetUsed: 30 }, // Should not be reset monthly
    ];
    mockInventoryLean.mockResolvedValueOnce(mockInventoryData);

    // Mock updateMany results - expect daily and monthly calls
    const mockDailyUpdateResult = { acknowledged: true, matchedCount: 1, modifiedCount: 1 };
    const mockMonthlyUpdateResult = { acknowledged: true, matchedCount: 1, modifiedCount: 1 };

    mockInventoryUpdateMany
      .mockResolvedValueOnce(mockDailyUpdateResult)   // Daily call
      .mockResolvedValueOnce(mockMonthlyUpdateResult); // Monthly call
      // Weekly should not be called

    const request = new NextRequest('http://localhost/api/cron/reset-budgets', {
      headers: { Authorization: 'Bearer TEST_SECRET' },
    });

    // Act
    const response = await GET(request);
    const responseBody = await response.json();

    // --- Assert ---
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.details).toHaveLength(1); // One company processed
    expect(responseBody.details[0].companyId).toBe(companyId.toString());
    expect(responseBody.details[0].daily?.updatedCount).toBe(1);
    expect(responseBody.details[0].weekly?.updatedCount).toBe(0);
    expect(responseBody.details[0].monthly?.updatedCount).toBe(1);

    // Verify mock calls
    expect(mockCompanyLean).toHaveBeenCalledTimes(1);
    expect(mockInventoryFind).toHaveBeenCalledTimes(1); // Called once for the company
    expect(mockInventoryUpdateMany).toHaveBeenCalledTimes(2); // Daily and Monthly updates
    expect(mockInventoryUpdateMany).toHaveBeenCalledWith({ companyId: companyId, budgetPeriod: 'daily' }, { $set: { budgetUsed: 0 } });
    expect(mockInventoryUpdateMany).toHaveBeenCalledWith({ companyId: companyId, budgetPeriod: 'monthly' }, { $set: { budgetUsed: 0 } });
    expect(mockInventoryUpdateMany).not.toHaveBeenCalledWith({ companyId: companyId, budgetPeriod: 'weekly' }, { $set: { budgetUsed: 0 } });
  });

});
