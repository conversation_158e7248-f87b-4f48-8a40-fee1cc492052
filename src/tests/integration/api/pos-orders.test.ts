import { POST } from '@/app/api/pos/[companyId]/locations/[locationId]/orders/route';
import dbConnect from '@/lib/db';
import { validateIonicAuth } from '@/lib/ionic-auth';
import { validateLocationAccess } from '@/lib/location-validation';
import BranchInventory from '@/models/BranchInventory';
import { MenuItem } from '@/models/MenuItem';
import { NextRequest } from 'next/server';
import { Types } from 'mongoose';

// --- Mocks --- 

// Mock database connection
jest.mock('@/lib/db');

// Mock models
jest.mock('@/models/MenuItem');
jest.mock('@/models/BranchInventory');

// Mock authentication helpers
jest.mock('@/lib/ionic-auth');
jest.mock('@/lib/location-validation');

// Typed mocks
const mockedValidateIonicAuth = validateIonicAuth as jest.Mock;
const mockedValidateLocationAccess = validateLocationAccess as jest.Mock;
const mockedMenuItemFindById = MenuItem.findById as jest.Mock;
const mockedBranchInventoryBulkWrite = BranchInventory.bulkWrite as jest.Mock;

// --- Test Suite --- 

describe('POST /api/pos/[companyId]/locations/[locationId]/orders', () => {

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Default mock implementations (can be overridden in tests)
    mockedValidateIonicAuth.mockResolvedValue({ isAuthenticated: true });
    mockedValidateLocationAccess.mockResolvedValue(undefined); // Resolves successfully
    mockedMenuItemFindById.mockResolvedValue(null); // Default to not found
    mockedBranchInventoryBulkWrite.mockResolvedValue({ ok: 1 }); // Default successful bulk write
  });

  it('should process a valid order with a single item and update budget', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const inventoryItemId = new Types.ObjectId();
    const menuItemId = new Types.ObjectId();
    const orderQuantity = 2;

    // Mock MenuItem found
    const mockMenuItemData = {
      _id: menuItemId,
      companyId: companyId,
      name: 'Test Coffee',
      type: 'single',
      inventoryItem: {
        itemId: inventoryItemId,
        unit: 'unit'
      },
      // other necessary fields...
    };
    // Mock the chainable call: findById(...).lean()
    const leanMock = jest.fn().mockResolvedValue(mockMenuItemData);
    // Use mockImplementation to return the object with the lean method
    mockedMenuItemFindById.mockImplementation(() => ({
      lean: leanMock
    }));

    // Mock request payload
    const mockPayload = {
      items: [
        { menuItemId: menuItemId.toString(), quantity: orderQuantity }
      ]
    };

    // Create mock NextRequest
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: {
        'company-id': companyId.toString()
      },
      body: JSON.stringify(mockPayload)
    });

    // Mock context
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler --- 
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions --- 
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.message).toContain('Order processed successfully');

    // Verify mocks were called
    expect(mockedValidateIonicAuth).toHaveBeenCalledTimes(1);
    expect(mockedValidateLocationAccess).toHaveBeenCalledWith(companyId.toString(), locationId.toString(), companyId.toString());
    // The route handler receives the ID as a string from the payload
    expect(mockedMenuItemFindById).toHaveBeenCalledWith(menuItemId.toString());

    // Verify BranchInventory.bulkWrite call
    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalledTimes(1);
    const expectedBulkOp = [
      {
        updateOne: {
          filter: { _id: inventoryItemId, companyId: companyId, locationId: locationId },
          update: { $inc: { orderBudgetUsed: orderQuantity } }
        }
      }
    ];
    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalledWith(expectedBulkOp);
  });

  it('should process a valid order with a recipe item and update budgets for components', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const inventoryItemId1 = new Types.ObjectId();
    const inventoryItemId2 = new Types.ObjectId();
    const recipeMenuItemId = new Types.ObjectId();
    const orderQuantity = 3;

    // Mock Recipe MenuItem found
    const mockRecipeMenuItemData = {
      _id: recipeMenuItemId,
      companyId: companyId,
      name: 'Test Latte',
      type: 'recipe',
      // Use recipeComponents directly as expected by the route handler
      recipeComponents: [
        { itemId: inventoryItemId1, quantity: 1 }, // e.g., 1 unit of Espresso
        { itemId: inventoryItemId2, quantity: 150 } // e.g., 150 ml of Milk
      ],
      // No direct inventoryItem link for recipe type
      inventoryItem: null,
    };

    // Update mock implementation for this test
    const leanMock = jest.fn().mockResolvedValue(mockRecipeMenuItemData);
    mockedMenuItemFindById.mockImplementation(() => ({ lean: leanMock }));

    // Mock request payload
    const mockPayload = {
      items: [
        { menuItemId: recipeMenuItemId.toString(), quantity: orderQuantity }
      ]
    };

    // Create mock NextRequest
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: {
        'company-id': companyId.toString()
      },
      body: JSON.stringify(mockPayload)
    });

    // Mock context
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler --- 
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions --- 
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.message).toContain('Order processed successfully');

    // Verify mocks were called
    expect(mockedValidateIonicAuth).toHaveBeenCalledTimes(1);
    expect(mockedValidateLocationAccess).toHaveBeenCalledWith(companyId.toString(), locationId.toString(), companyId.toString());
    expect(mockedMenuItemFindById).toHaveBeenCalledWith(recipeMenuItemId.toString());

    // Verify BranchInventory.bulkWrite call for components
    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalledTimes(1);
    const expectedBulkOps = [
      {
        updateOne: {
          filter: { _id: inventoryItemId1, companyId: companyId, locationId: locationId },
          update: { $inc: { orderBudgetUsed: 1 * orderQuantity } } // component.quantity * order.quantity
        }
      },
      {
        updateOne: {
          filter: { _id: inventoryItemId2, companyId: companyId, locationId: locationId },
          update: { $inc: { orderBudgetUsed: 150 * orderQuantity } } // component.quantity * order.quantity
        }
      }
    ];
    // Use expect.arrayContaining because the order of operations might not be guaranteed
    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalledWith(expect.arrayContaining(expectedBulkOps));
    // Ensure the correct number of operations were sent
    const actualArgs = mockedBranchInventoryBulkWrite.mock.calls[0][0];
    expect(actualArgs).toHaveLength(expectedBulkOps.length);

  });

  it('should process an order with mixed single and recipe items', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();

    // --- Single Item Setup ---
    const singleInventoryId = new Types.ObjectId();
    const singleMenuItemId = new Types.ObjectId();
    const singleOrderQuantity = 2;
    const singleMenuItemData = {
      _id: singleMenuItemId,
      companyId: companyId,
      name: 'Test Drip Coffee',
      type: 'single',
      inventoryItem: { itemId: singleInventoryId, unit: 'unit' },
    };

    // --- Recipe Item Setup ---
    const recipeInvId1 = new Types.ObjectId(); // Espresso
    const recipeInvId2 = new Types.ObjectId(); // Milk
    const recipeMenuItemId = new Types.ObjectId();
    const recipeOrderQuantity = 1;
    const recipeMenuItemData = {
      _id: recipeMenuItemId,
      companyId: companyId,
      name: 'Test Flat White',
      type: 'recipe',
      recipeComponents: [
        { itemId: recipeInvId1, quantity: 2 }, // Double shot espresso
        { itemId: recipeInvId2, quantity: 120 } // 120ml Milk
      ],
      inventoryItem: null,
    };

    // --- Mock findById to handle both items ---
    mockedMenuItemFindById.mockImplementation((id: string | Types.ObjectId) => {
      const idStr = id.toString();
      if (idStr === singleMenuItemId.toString()) {
        return { lean: jest.fn().mockResolvedValue(singleMenuItemData) };
      }
      if (idStr === recipeMenuItemId.toString()) {
        return { lean: jest.fn().mockResolvedValue(recipeMenuItemData) };
      }
      return { lean: jest.fn().mockResolvedValue(null) }; // Not found
    });

    // --- Mock request payload ---
    const mockPayload = {
      items: [
        { menuItemId: singleMenuItemId.toString(), quantity: singleOrderQuantity },
        { menuItemId: recipeMenuItemId.toString(), quantity: recipeOrderQuantity }
      ]
    };

    // --- Mock Request & Context ---
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload)
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(200);
    expect(responseBody.success).toBe(true);
    expect(responseBody.message).toContain('Order processed successfully');

    // Verify findById called for both items
    expect(mockedMenuItemFindById).toHaveBeenCalledWith(singleMenuItemId.toString());
    expect(mockedMenuItemFindById).toHaveBeenCalledWith(recipeMenuItemId.toString());

    // Verify bulkWrite includes updates for all inventory items
    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalledTimes(1);
    const expectedCombinedOps = [
      { // From single item
        updateOne: {
          filter: { _id: singleInventoryId, companyId: companyId, locationId: locationId },
          update: { $inc: { orderBudgetUsed: singleOrderQuantity } }
        }
      },
      { // From recipe item component 1
        updateOne: {
          filter: { _id: recipeInvId1, companyId: companyId, locationId: locationId },
          update: { $inc: { orderBudgetUsed: 2 * recipeOrderQuantity } }
        }
      },
      { // From recipe item component 2
        updateOne: {
          filter: { _id: recipeInvId2, companyId: companyId, locationId: locationId },
          update: { $inc: { orderBudgetUsed: 120 * recipeOrderQuantity } }
        }
      }
    ];

    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalledWith(
      expect.arrayContaining(expectedCombinedOps)
    );
    const actualArgs = mockedBranchInventoryBulkWrite.mock.calls[0][0];
    expect(actualArgs).toHaveLength(expectedCombinedOps.length);
  });

  it('should return an error if a single item is not linked to inventory', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const unlinkedMenuItemId = new Types.ObjectId();
    const orderQuantity = 1;

    // Mock MenuItem found, but not linked to inventory
    const mockUnlinkedMenuItemData = {
      _id: unlinkedMenuItemId,
      companyId: companyId,
      name: 'Test Unlinked Item',
      type: 'single',
      inventoryItem: null, // Explicitly not linked
    };

    // Mock findById
    mockedMenuItemFindById.mockImplementation((id: string | Types.ObjectId) => {
      if (id.toString() === unlinkedMenuItemId.toString()) {
        return { lean: jest.fn().mockResolvedValue(mockUnlinkedMenuItemData) };
      }
      return { lean: jest.fn().mockResolvedValue(null) };
    });

    // Mock request payload
    const mockPayload = {
      items: [
        { menuItemId: unlinkedMenuItemId.toString(), quantity: orderQuantity }
      ]
    };

    // Mock Request & Context
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload)
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(400); // Expect Bad Request
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Order processed with errors');
    expect(responseBody.errors).toBeDefined();
    expect(responseBody.errors[0]).toContain('is type \'single\' but not linked to inventory');

    // Verify bulkWrite was NOT called
    expect(mockedBranchInventoryBulkWrite).not.toHaveBeenCalled();
  });

  it('should return an error if a recipe item has no components defined', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const recipeNoComponentsId = new Types.ObjectId();
    const orderQuantity = 1;

    // Mock Recipe MenuItem found, but with empty components
    const mockRecipeNoComponentsData = {
      _id: recipeNoComponentsId,
      companyId: companyId,
      name: 'Test Empty Recipe',
      type: 'recipe',
      recipeComponents: [], // Explicitly empty
      inventoryItem: null,
    };

    // Mock findById
    mockedMenuItemFindById.mockImplementation((id: string | Types.ObjectId) => {
      if (id.toString() === recipeNoComponentsId.toString()) {
        return { lean: jest.fn().mockResolvedValue(mockRecipeNoComponentsData) };
      }
      return { lean: jest.fn().mockResolvedValue(null) };
    });

    // Mock request payload
    const mockPayload = {
      items: [
        { menuItemId: recipeNoComponentsId.toString(), quantity: orderQuantity }
      ]
    };

    // Mock Request & Context
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload)
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(400); // Expect Bad Request
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Order processed with errors');
    expect(responseBody.errors).toBeDefined();
    expect(responseBody.errors[0]).toContain('is type \'recipe\' but has no recipe components defined');

    // Verify bulkWrite was NOT called
    expect(mockedBranchInventoryBulkWrite).not.toHaveBeenCalled();
  });

  it('should return an error if a menu item is not found', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const nonExistentMenuItemId = new Types.ObjectId();
    const orderQuantity = 1;

    // Mock findById to return null (not found)
    mockedMenuItemFindById.mockImplementation((id: string | Types.ObjectId) => {
      // Always return not found for this test
      return { lean: jest.fn().mockResolvedValue(null) };
    });

    // Mock request payload
    const mockPayload = {
      items: [
        { menuItemId: nonExistentMenuItemId.toString(), quantity: orderQuantity }
      ]
    };

    // Mock Request & Context
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload)
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(400); // Expect Bad Request
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Order processed with errors');
    expect(responseBody.errors).toBeDefined();
    expect(responseBody.errors[0]).toContain('Menu item not found');
    expect(responseBody.errors[0]).toContain(nonExistentMenuItemId.toString());

    // Verify bulkWrite was NOT called
    expect(mockedBranchInventoryBulkWrite).not.toHaveBeenCalled();
  });

  it('should return an error for invalid quantity (e.g., zero)', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const menuItemId = new Types.ObjectId();
    const invalidQuantity = 0;

    // Mock findById just enough to pass the 'not found' check, 
    // the validation should happen before checking item details
    const mockMenuItemData = { _id: menuItemId, companyId: companyId, type: 'single', inventoryItem: {itemId: new Types.ObjectId()} }; 
    mockedMenuItemFindById.mockImplementation((id: string | Types.ObjectId) => {
      if (id.toString() === menuItemId.toString()) {
          return { lean: jest.fn().mockResolvedValue(mockMenuItemData) };
      }
      return { lean: jest.fn().mockResolvedValue(null) };
    });

    // Mock request payload with invalid quantity
    const mockPayload = {
      items: [
        { menuItemId: menuItemId.toString(), quantity: invalidQuantity }
      ]
    };

    // Mock Request & Context
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload)
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(400); // Expect Bad Request
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Order processed with errors');
    expect(responseBody.errors).toBeDefined();
    expect(responseBody.errors[0]).toContain('Invalid data for menu item');
    expect(responseBody.errors[0]).toContain(`quantity: ${invalidQuantity}`);

    // Verify bulkWrite was NOT called
    expect(mockedBranchInventoryBulkWrite).not.toHaveBeenCalled();

  });

  it('should return 401 Unauthorized if company-id header does not match URL parameter', async () => {
    const urlCompanyId = new Types.ObjectId(); // ID in the URL
    const headerCompanyId = new Types.ObjectId(); // Different ID in the header
    const locationId = new Types.ObjectId();
    const menuItemId = new Types.ObjectId();

    // Mock request payload (can be minimal, auth fails first)
    const mockPayload = {
      items: [
        { menuItemId: menuItemId.toString(), quantity: 1 }
      ]
    };

    // Mock Request & Context with mismatching IDs
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': headerCompanyId.toString() }, // Header uses different ID
      body: JSON.stringify(mockPayload) // Send the invalid payload
    });
    const mockContext = {
      params: Promise.resolve({ companyId: urlCompanyId.toString(), locationId: locationId.toString() }) // Context uses URL ID
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(401); // Expect Unauthorized
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Unauthorized');

    // Verify mocks related to order processing were NOT called
    expect(mockedMenuItemFindById).not.toHaveBeenCalled();
    expect(mockedBranchInventoryBulkWrite).not.toHaveBeenCalled();
  });

  it('should return 500 if database bulkWrite fails', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();
    const singleItemId = new Types.ObjectId(); // Use a valid single item for simplicity
    const inventoryId = new Types.ObjectId();
    const orderQuantity = 1;

    // Mock MenuItem findById (valid single item)
    const mockMenuItemData = {
      _id: singleItemId,
      companyId: companyId,
      name: 'Test Single Item',
      type: 'single',
      inventoryItem: { itemId: inventoryId, quantity: 1 }
    };
    mockedMenuItemFindById.mockImplementation((id: string | Types.ObjectId) => {
      if (id.toString() === singleItemId.toString()) {
        return { lean: jest.fn().mockResolvedValue(mockMenuItemData) };
      }
      return { lean: jest.fn().mockResolvedValue(null) };
    });

    // Mock BranchInventory bulkWrite to throw an error
    const dbError = new Error('Simulated DB Error during bulkWrite');
    mockedBranchInventoryBulkWrite.mockRejectedValue(dbError);

    // Mock request payload
    const mockPayload = {
      items: [
        { menuItemId: singleItemId.toString(), quantity: orderQuantity }
      ]
    };

    // Mock Request & Context
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload)
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(500); // Expect Internal Server Error
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Simulated DB Error during bulkWrite');

    // Verify bulkWrite was called (even though it failed)
    expect(mockedBranchInventoryBulkWrite).toHaveBeenCalled();

  });

  it('should return 400 Bad Request if payload is missing items array', async () => {
    const companyId = new Types.ObjectId();
    const locationId = new Types.ObjectId();

    // Mock request payload (missing items)
    const mockPayload = {}; // Invalid payload

    // Mock Request & Context
    const mockReq = new NextRequest('http://localhost/api/pos/test/locations/test/orders', {
      method: 'POST',
      headers: { 'company-id': companyId.toString() },
      body: JSON.stringify(mockPayload) // Send the invalid payload
    });
    const mockContext = {
      params: Promise.resolve({ companyId: companyId.toString(), locationId: locationId.toString() })
    };

    // --- Execute Handler ---
    const response = await POST(mockReq, mockContext);
    const responseBody = await response.json();

    // --- Assertions ---
    expect(response.status).toBe(400); // Expect Bad Request
    expect(responseBody.success).toBe(false);
    expect(responseBody.message).toContain('Invalid payload. Expected { items: [...] } with at least one item.'); // Update expected message

    // Ensure DB wasn't touched
    expect(mockedMenuItemFindById).not.toHaveBeenCalled();
    expect(mockedBranchInventoryBulkWrite).not.toHaveBeenCalled();
  });

  // TODO: Add more test cases:
  // - Invalid payload (empty items array?)
});
