import mongoose from 'mongoose';
import { NextRequest } from 'next/server';
import User from '@/models/User';
import { GET } from '@/app/api/company/[companyId]/users/sync/route';

// Mock Ionic POS auth to always succeed for tests
jest.mock('@/lib/ionic-auth', () => ({
  validateIonicAuth: jest.fn().mockResolvedValue({
    isAuthenticated: true,
    userId: 'test-user',
    companyId: 'test-company',
    permissions: ['users.sync']
  })
}));

describe('User Sync API - GET', () => {
  const companyId = new mongoose.Types.ObjectId().toString();
  const sinceDate = new Date(0).toISOString();

  beforeAll(async () => {
    // Connect via mongodb-memory-server (setup.ts handles connection)
  });

  beforeEach(async () => {
    // Seed some users
    await User.create([
      {
        _id: new mongoose.Types.ObjectId('aaaaaaaaaaaaaaaaaaaaaaaa'),
        companyId,
        email: '<EMAIL>',
        passwordHash: 'hash',
        userType: 'company_user',
        role: 'user',
        name: '<PERSON>',
        lastModified: new Date(),
        isDeleted: false,
      },
      {
        _id: new mongoose.Types.ObjectId('bbbbbbbbbbbbbbbbbbbbbbbb'),
        companyId,
        email: '<EMAIL>',
        passwordHash: 'hash',
        userType: 'company_user',
        role: 'user',
        name: 'Bob',
        lastModified: new Date(),
        isDeleted: false,
      },
    ]);
  });

  afterEach(async () => {
    await mongoose.connection.collections['users'].deleteMany({});
  });

  it('returns users list and pagination', async () => {
    const url = `http://localhost/api/company/${companyId}/users/sync?since=${sinceDate}&limit=10&page=1`;
    const req = new NextRequest(url, {
      headers: { 'company-id': companyId }
    });
    const res = await GET(req, { params: Promise.resolve({ companyId }) });
    const json = await res.json();
    expect(json.users).toHaveLength(2);
    expect(json.deletedUserIds).toEqual([]);
    expect(json.pagination).toMatchObject({ totalCount: 2, page: 1, totalPages: 1, hasMore: false });
  });
});
