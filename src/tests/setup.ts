import '@testing-library/jest-dom';
import mongoose from 'mongoose';
import connectToDatabase from '@/lib/mongoDb';
import { MongoMemoryServer } from 'mongodb-memory-server';

// Extend default Jest timeout for database setup
jest.setTimeout(30000);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
let mongoServer: any;

beforeAll(async () => {
  process.env.MONGOMS_START_TIMEOUT = '60000';
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  process.env.MONGODB_URI = uri;
  await connectToDatabase();
});

afterAll(async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});
