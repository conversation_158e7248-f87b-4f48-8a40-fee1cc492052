import React from 'react';
import { screen } from '@testing-library/react';
import { renderWithAuth } from './test-utils';
import { CompanyData } from '../lib/types/company';
import type { InventoryItem } from '../lib/types/inventory';

// Mock components
const MockManagerDashboard = () => (
  <div data-testid="manager-dashboard">
    Manager Dashboard
    <div data-testid="inventory-items">
      <div>Item 1</div>
      <div>Item 2</div>
    </div>
  </div>
);

const MockAdminDashboard = () => (
  <div data-testid="admin-dashboard">
    Admin Dashboard
    <div data-testid="locations">
      <div>Location 1</div>
      <div>Location 2</div>
      <div>Location 3</div>
    </div>
  </div>
);

const MockLocationSelector = () => (
  <div data-testid="location-selector">
    Location Selector
    <select data-testid="location-select">
      <option value="loc-1">Location 1</option>
      <option value="loc-2">Location 2</option>
      <option value="loc-3">Location 3</option>
    </select>
  </div>
);

jest.mock('../components/dashboards/ManagerDashboard', () => ({
  ManagerDashboard: MockManagerDashboard
}));

jest.mock('../components/dashboards/AdminDashboard', () => ({
  AdminDashboard: MockAdminDashboard
}));

jest.mock('../components/LocationSelector', () => ({
  LocationSelector: MockLocationSelector
}));

// Mock company data with multiple locations
const mockCompanyData: CompanyData = {
  id: 'company-1',
  name: 'Test Company',
  locations: [
    { id: 'loc-1', name: 'Location 1', address: '123 Main St' },
    { id: 'loc-2', name: 'Location 2', address: '456 Oak Ave' },
    { id: 'loc-3', name: 'Location 3', address: '789 Pine Rd' },
  ],
  ownerId: 'owner-user-id',
  roles: { 'admin-456': 'admin', 'manager-123': 'manager' },
  permissions: { 
    'admin-456': ['all'], 
    'manager-123': ['manage_inventory'] 
  }, 
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Mock inventory data with proper typing
interface MockInventoryData {
  [locationId: string]: InventoryItem[];
}

const mockInventoryData: MockInventoryData = {
  'loc-1': [
    { id: 'item-1', name: 'Item 1', quantity: 10 },
    { id: 'item-2', name: 'Item 2', quantity: 20 },
  ],
  'loc-2': [
    { id: 'item-3', name: 'Item 3', quantity: 30 },
    { id: 'item-4', name: 'Item 4', quantity: 40 },
  ],
};

// Mock the getLocationInventory function
const mockGetLocationInventory = jest.fn();

jest.mock('../lib/services/inventoryService', () => ({
  getLocationInventory: () => mockGetLocationInventory(),
}));

describe('Location-based Access Control', () => {
  describe('Store Manager Access', () => {
    const managerUserData = {
      role: 'manager' as const,
      companyId: 'company-1',
      uid: 'manager-123',
      email: '<EMAIL>',
      userType: 'company_user' as const,
    };

    it('should only show inventory for manager\'s location', () => {
      renderWithAuth(
        <MockManagerDashboard />,
        { userData: managerUserData, companyData: mockCompanyData }
      );

      // Manager should see their location's inventory
      expect(screen.getByTestId('manager-dashboard')).toBeInTheDocument();
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
      expect(screen.queryByText('Item 3')).not.toBeInTheDocument();
      expect(screen.queryByText('Item 4')).not.toBeInTheDocument();
    });

    it('should not allow access to other location\'s data via API', async () => {
      mockGetLocationInventory.mockImplementation((locationId: string) => {
        const managerAssignedLocation = 'loc-1';
        if (locationId !== managerAssignedLocation) {
          return Promise.reject(new Error('Unauthorized'));
        }
        return Promise.resolve(mockInventoryData[locationId]);
      });

      await expect(mockGetLocationInventory('loc-2')).rejects.toThrow('Unauthorized');
    });
  });

  describe('Multi-location Company', () => {
    const adminUserData = {
      role: 'admin' as const,
      companyId: 'company-1',
      uid: 'admin-456',
      email: '<EMAIL>',
      userType: 'company_user' as const,
    };

    it('should allow admin to see all locations', () => {
      renderWithAuth(
        <MockAdminDashboard />,
        { userData: adminUserData, companyData: mockCompanyData }
      );

      expect(screen.getByTestId('admin-dashboard')).toBeInTheDocument();
      mockCompanyData.locations!.forEach(location => {
        expect(screen.getByText(location.name)).toBeInTheDocument();
      });
    });

    it('should allow admin to switch between locations', () => {
      renderWithAuth(
        <MockLocationSelector />,
        { userData: adminUserData, companyData: mockCompanyData }
      );

      expect(screen.getByTestId('location-selector')).toBeInTheDocument();
      expect(screen.getByTestId('location-select')).toBeInTheDocument();
      mockCompanyData.locations!.forEach(location => {
        expect(screen.getByText(location.name)).toBeInTheDocument();
      });
    });
  });
});
