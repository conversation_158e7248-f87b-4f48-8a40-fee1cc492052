import React from 'react';
import { render } from '@testing-library/react';
import { CompanyContext, CompanyContextType, CompanyData } from '../lib/contexts/CompanyContext';
import { AuthContext, AuthContextType, CompanyUserData } from '../lib/auth';
import { User } from 'firebase/auth';

// Create a wrapper component that provides mocked contexts
const MockProvider: React.FC<{
  children: React.ReactNode;
  userData?: CompanyUserData | null;
  companyData?: CompanyData | null;
}> = ({ children, userData = null, companyData = null }) => {
  const validateCompanyAccess = (requestedCompanyId: string): boolean => {
    if (!userData?.companyId || !requestedCompanyId) return false;
    return userData.companyId === requestedCompanyId;
  };

  const validateLocationAccess = (requestedLocationId: string, userRole: string): boolean => {
    if (!userData?.locationId || !requestedLocationId) {
      // <PERSON><PERSON> can access any location
      return userRole === 'admin';
    }
    // Managers and storekeepers can only access their assigned location
    return userData.locationId === requestedLocationId && (userRole === 'manager' || userRole === 'storekeeper');
  };

  // Mock auth context value
  const mockAuthContextValue: AuthContextType = {
    user: userData ? {
      uid: 'test-uid',
      email: '<EMAIL>',
      emailVerified: true,
      isAnonymous: false,
      metadata: {
        creationTime: '2024-01-01',
        lastSignInTime: '2024-01-01'
      },
      providerData: [],
      refreshToken: 'test-refresh-token',
      tenantId: null,
      delete: jest.fn(),
      getIdToken: jest.fn(),
      getIdTokenResult: jest.fn(),
      reload: jest.fn(),
      toJSON: jest.fn(),
      displayName: null,
      phoneNumber: null,
      photoURL: null,
      providerId: 'firebase',
      emailPasswordSignIn: jest.fn(),
    } as User : null,
    loading: false,
    userData: userData,
    isSuperUser: false,
    signIn: jest.fn(),
    signUp: jest.fn(),
    signInWithGoogle: jest.fn(),
    logout: jest.fn(),
  };

  // Mock company context value
  const mockCompanyContextValue: CompanyContextType = {
    companyId: userData?.companyId ?? '',
    locationId: userData?.locationId,
    setLocationId: jest.fn(),
    companyData: companyData || (userData ? {
      id: userData.companyId ?? 'test-company',
      name: 'Test Company',
      subdomain: 'test',
      locations: [],
      branding: {
        logo: 'test-logo.png',
        primaryColor: '#000000',
      },
    } : null),
    setCompanyData: jest.fn(),
    loading: false,
    validateCompanyAccess,
    validateLocationAccess,
  };

  return (
    <AuthContext.Provider value={mockAuthContextValue}>
      <CompanyContext.Provider value={mockCompanyContextValue}>
        {children}
      </CompanyContext.Provider>
    </AuthContext.Provider>
  );
};

// Create a custom render function that includes providers
export function renderWithAuth(
  ui: React.ReactElement,
  {
    userData = null,
    companyData = null,
    ...options
  }: {
    userData?: CompanyUserData | null;
    companyData?: CompanyData | null;
  } & Record<string, unknown> = {}
) {
  return render(ui, {
    wrapper: (props) => (
      <MockProvider {...props} userData={userData} companyData={companyData} />
    ),
    ...options,
  });
}

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      pathname: '',
    };
  },
  usePathname() {
    return '/company/test-company/dashboard';
  },
  redirect(path: string) {
    throw new Error(`Redirected to ${path}`);
  },
}));

// Mock Firebase Auth
jest.mock('../lib/firebaseClient', () => ({
  auth: {
    onAuthStateChanged: jest.fn(),
  },
  db: {},
  googleProvider: {},
}));
