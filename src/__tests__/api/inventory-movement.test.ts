// src/__tests__/api/inventory-movement.test.ts
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose, { Types } from 'mongoose';
import { Ingredient } from '../../models/Ingredient';
import Recipe from '../../models/Recipe';
import BranchInventory from '../../models/BranchInventory';
import InventoryTransaction from '../../models/InventoryTransaction';
import UOM from '../../models/UOM';
import Location from '../../models/Location';
import { POST as inventoryMovementHandler } from '../../app/api/company/[companyId]/inventory/movement/v2/route';
import { NextRequest } from 'next/server';

let mongoServer: MongoMemoryServer;
const companyId = new Types.ObjectId().toString();
const userId = new Types.ObjectId().toString();

// Mock data
let centralKitchenId: string;
let branchLocationId: string;
let secondBranchId: string;
let ingredientId: string;
let recipeId: string;
let unitId: string;

// Mock Next.js request
const createMockRequest = (body: any): NextRequest => {
  const headers = new Headers();
  headers.set('company-id', companyId);
  headers.set('user-id', userId);
  
  return {
    json: () => Promise.resolve(body),
    headers,
    nextUrl: {
      searchParams: new URLSearchParams()
    }
  } as unknown as NextRequest;
};

// Mock context with params
const createMockContext = () => ({
  params: Promise.resolve({ companyId })
});

describe('Inventory Movement API', () => {
  beforeAll(async () => {
    // Start in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri);

    // Create test data
    await setupTestData();
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Helper function to set up test data
  async function setupTestData() {
    // Create unit of measure
    const uom = await UOM.create({
      name: 'Kilogram',
      shortCode: 'kg',
      system: 'METRIC',
      baseType: 'MASS',
      factorToCanonical: 1,
      synonyms: ['kilo', 'kilos'],
      description: 'Metric unit of mass'
    });
    unitId = (uom._id as mongoose.Types.ObjectId).toString();

    // Create locations
    const centralKitchen = await Location.create({
      name: 'Central Kitchen',
      companyId,
      address: '123 Main St',
      type: 'KITCHEN',
      isActive: true
    });
    centralKitchenId = centralKitchen._id.toString();

    const branchLocation = await Location.create({
      name: 'Branch Location',
      companyId,
      address: '456 Branch St',
      type: 'BRANCH',
      isActive: true
    });
    branchLocationId = branchLocation._id.toString();

    const secondBranch = await Location.create({
      name: 'Second Branch',
      companyId,
      address: '789 Second St',
      type: 'BRANCH',
      isActive: true
    });
    secondBranchId = secondBranch._id.toString();

    // Create ingredient
    const ingredient = await Ingredient.create({
      name: 'Test Ingredient',
      baseUomId: uom._id,
      companyId,
      canBeSold: true,
      currentStock: 50,
      pendingStock: 0,
      sellingDetails: [{
        unitOfSelling: uom._id,
        priceWithoutTax: 10,
        priceWithTax: 11,
        taxRate: 10,
        taxCategory: 'STANDARD',
        conversionFactor: 1,
        visibility: {
          type: 'ALL_LOCATIONS',
          externalAccess: true
        }
      }]
    });
    ingredientId = (ingredient._id as mongoose.Types.ObjectId).toString();

    // Create recipe
    const recipe = await Recipe.create({
      name: 'Test Recipe',
      companyId,
      description: 'A test recipe',
      yield: 4,
      baseYieldUOM: uom._id,
      category: 'Test',
      stockable: true,
      canBeSold: true,
      currentStock: 20,
      pendingStock: 0,
      recipeIngredients: [{
        ingredientName: 'Test Ingredient',
        quantity: 2,
        unitOfMeasure: uom._id,
        ingredientId: ingredient._id,
        isSubRecipe: false
      }],
      sellingDetails: [{
        unitOfSelling: uom._id,
        priceWithoutTax: 15,
        priceWithTax: 16.5,
        taxRate: 10,
        taxCategory: 'STANDARD',
        conversionFactor: 1,
        visibility: {
          type: 'ALL_LOCATIONS',
          externalAccess: true
        }
      }]
    });
    recipeId = recipe._id.toString();

    // Create initial branch inventory
    await BranchInventory.create({
      companyId,
      locationId: branchLocationId,
      itemId: ingredientId,
      itemType: 'INGREDIENT',
      currentStock: 10,
      pendingStock: 0,
      costBasis: 10,
      originalCost: 10
    });

    await BranchInventory.create({
      companyId,
      locationId: branchLocationId,
      itemId: recipeId,
      itemType: 'RECIPE',
      currentStock: 5,
      pendingStock: 0,
      costBasis: 15,
      originalCost: 15
    });
  }

  test('Should process TRANSFER_OUT with percentage markup', async () => {
    const requestBody = {
      transactionType: 'TRANSFER_OUT',
      locationId: centralKitchenId,
      destinationLocationId: branchLocationId,
      items: [
        {
          itemId: ingredientId,
          itemType: 'INGREDIENT',
          quantity: 5,
          unitId,
          cost: 10.00
        }
      ],
      priceMarkup: {
        type: 'PERCENTAGE',
        value: 15,
        applyTax: false
      },
      notes: 'Transfer with markup test'
    };

    const request = createMockRequest(requestBody);
    const context = createMockContext();

    const response = await inventoryMovementHandler(request, context);
    const responseData = await response.json();

    expect(response.status).toBe(200);
    expect(responseData.success).toBe(true);
    expect(responseData.transactionType).toBe('TRANSFER_OUT');
    
    // Verify ingredient stock was reduced at central kitchen
    const updatedIngredient = await Ingredient.findById(ingredientId);
    expect(updatedIngredient?.currentStock).toBe(45); // 50 - 5
    
    // Verify pending stock was increased at branch with markup
    const branchInventory = await BranchInventory.findOne({
      companyId,
      locationId: branchLocationId,
      itemId: ingredientId,
      itemType: 'INGREDIENT'
    });
    
    // BranchInventory with proper type casting to account for missing properties in model
    expect(branchInventory?.pendingStock).toBe(5);
    expect(branchInventory?.costBasis).toBe(11.5); // 10 + 15%
    expect(branchInventory?.originalCost).toBe(10);
    expect(branchInventory?.markup?.percentage).toBe(15);
    
    // Verify transaction record was created with markup info
    const transaction = await InventoryTransaction.findOne({
      'metadata.transactionType': 'TRANSFER_OUT',
      'metadata.itemId': new Types.ObjectId(ingredientId)
    });
    
    expect(transaction).toBeTruthy();
    expect(transaction?.transferMarkup?.originalCost).toBe(10);
    expect(transaction?.transferMarkup?.markedUpCost).toBe(11.5);
    expect(transaction?.transferMarkup?.markupType).toBe('PERCENTAGE');
    expect(transaction?.transferMarkup?.markupValue).toBe(15);
  });

  test('Should process TRANSFER_IN correctly', async () => {
    const requestBody = {
      transactionType: 'TRANSFER_IN',
      locationId: branchLocationId,
      sourceLocationId: centralKitchenId,
      items: [
        {
          itemId: ingredientId,
          itemType: 'INGREDIENT',
          quantity: 5,
          unitId
        }
      ],
      notes: 'Receiving transfer'
    };

    const request = createMockRequest(requestBody);
    const context = createMockContext();

    const response = await inventoryMovementHandler(request, context);
    const responseData = await response.json();

    expect(response.status).toBe(200);
    expect(responseData.success).toBe(true);
    expect(responseData.transactionType).toBe('TRANSFER_IN');
    
    // Verify pending stock was decreased and current stock increased at branch
    const branchInventory = await BranchInventory.findOne({
      companyId,
      locationId: branchLocationId,
      itemId: ingredientId,
      itemType: 'INGREDIENT'
    });
    
    // Direct access now that model has correct type definitions
    expect(branchInventory?.pendingStock).toBe(0);
    expect(branchInventory?.currentStock).toBe(15); // 10 + 5
    
    // Markup should be preserved
    expect(branchInventory?.costBasis).toBe(11.5);
    expect(branchInventory?.originalCost).toBe(10);
  });

  test('Should process branch to branch TRANSFER_OUT without markup', async () => {
    const requestBody = {
      transactionType: 'TRANSFER_OUT',
      locationId: branchLocationId, // First branch is source
      destinationLocationId: secondBranchId, // Second branch is destination
      items: [
        {
          itemId: ingredientId,
          itemType: 'INGREDIENT',
          quantity: 2,
          unitId,
          cost: 11.50 // Using marked-up cost as this is now the branch's cost basis
        }
      ],
      // No markup specified - should maintain current cost
      notes: 'Branch to branch transfer'
    };

    const request = createMockRequest(requestBody);
    const context = createMockContext();

    const response = await inventoryMovementHandler(request, context);
    const responseData = await response.json();

    expect(response.status).toBe(200);
    expect(responseData.success).toBe(true);
    
    // Verify stock was reduced at source branch
    const sourceBranchInventory = await BranchInventory.findOne({
      companyId,
      locationId: branchLocationId,
      itemId: ingredientId,
      itemType: 'INGREDIENT'
    });
    
    expect((sourceBranchInventory as any)?.currentStock).toBe(13); // 15 - 2
    
    // Verify pending stock was increased at destination branch with preserved cost
    const destBranchInventory = await BranchInventory.findOne({
      companyId,
      locationId: secondBranchId,
      itemId: ingredientId,
      itemType: 'INGREDIENT'
    });
    
    expect((destBranchInventory as any)?.pendingStock).toBe(2);
    expect((destBranchInventory as any)?.costBasis).toBe(11.5); // Same as source branch
    expect((destBranchInventory as any)?.originalCost).toBe(10); // Original cost should be preserved
  });

  test('Should process SALE transaction correctly', async () => {
    const requestBody = {
      transactionType: 'SALE',
      locationId: branchLocationId,
      items: [
        {
          itemId: ingredientId,
          itemType: 'INGREDIENT',
          quantity: 3,
          unitId,
          cost: 11.50 // Using marked-up cost
        }
      ],
      reference: {
        id: new Types.ObjectId().toString(),
        type: 'ORDER'
      },
      notes: 'Sale test'
    };

    const request = createMockRequest(requestBody);
    const context = createMockContext();

    const response = await inventoryMovementHandler(request, context);
    const responseData = await response.json();

    expect(response.status).toBe(200);
    expect(responseData.success).toBe(true);
    expect(responseData.transactionType).toBe('SALE');
    
    // Verify stock was reduced at branch
    const branchInventory = await BranchInventory.findOne({
      companyId,
      locationId: branchLocationId,
      itemId: ingredientId,
      itemType: 'INGREDIENT'
    });
    
    expect((branchInventory as any)?.currentStock).toBe(10); // 13 - 3
    
    // Verify transaction record was created
    const transaction = await InventoryTransaction.findOne({
      'metadata.transactionType': 'SALE',
      'metadata.itemId': new Types.ObjectId(ingredientId),
      'metadata.locationId': new Types.ObjectId(branchLocationId)
    });
    
    expect(transaction).toBeTruthy();
    expect(transaction?.difference).toBe(-3);
  });
});
