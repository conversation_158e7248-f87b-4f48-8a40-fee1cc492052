import { Types } from 'mongoose';

export type UOMSystem = 'metric' | 'imperial' | 'custom';
export type UOMBaseType = 'mass' | 'volume' | 'count' | string;

export interface UOM {
  _id: string | Types.ObjectId;
  companyId?: string | Types.ObjectId;
  name: string;
  shortCode: string;
  system: UOMSystem;
  baseType: UOMBaseType;
  factorToCanonical: number;
  synonyms: string[];
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUOMInput {
  companyId?: string;
  name: string;
  shortCode: string;
  system: UOMSystem;
  baseType: UOMBaseType;
  factorToCanonical: number;
  synonyms?: string[];
  description?: string;
}

export interface UpdateUOMInput extends Partial<CreateUOMInput> {
  _id: string;
}
