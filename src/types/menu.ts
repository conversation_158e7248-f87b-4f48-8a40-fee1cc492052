export interface Group {
  _id: string
  name: string
  companyId: string
  description: string
  isGlobal: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Category {
  _id: string
  name: string
  description: string
  groupId: string
  companyId: string
  isGlobal: boolean
  isCustomizable: boolean
  createdAt: Date
  updatedAt: Date
}

export interface MenuPrice {
  basePrice: number;
  vatRate: number;
  vatAmount: number;
  finalPrice: number;
}

export interface MenuRecipeComponent {
  inventoryItemId: string;
  quantity: number;
  unit: string;
}

export interface MenuItem {
  id: string;
  companyId: string;
  name: string;
  description: string;
  categoryId: string;
  type: 'single' | 'recipe';
  prices: MenuPrice;
  status: 'active' | 'inactive';
  image?: string;
  inventoryItemId?: string;
  recipeComponents?: MenuRecipeComponent[];
  createdAt: Date;
  updatedAt: Date;
}

export interface MenuCategory {
  id: string;
  companyId: string;
  name: string;
  description: string;
  order: number;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

// Request/Response Types
export interface CreateMenuItemRequest {
  name: string;
  description?: string;
  categoryId: string;
  type: 'single' | 'recipe';
  prices: Omit<MenuPrice, 'vatAmount' | 'finalPrice'>;
  inventoryItemId?: string;
  recipeComponents?: Omit<MenuRecipeComponent, 'id'>[];
}

export interface UpdateMenuItemRequest extends Partial<CreateMenuItemRequest> {
  status?: 'active' | 'inactive';
}

export interface CreateMenuCategoryRequest {
  name: string;
  description?: string;
  order?: number;
}

export interface UpdateMenuCategoryRequest extends Partial<CreateMenuCategoryRequest> {
  status?: 'active' | 'inactive';
}
