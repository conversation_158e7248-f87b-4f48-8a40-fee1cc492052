import { Types } from 'mongoose';

export interface UOM {
  _id: Types.ObjectId;
  name: string;
  shortCode: string;
}

export interface SupplierUnit {
  unitOfMeasure: Types.ObjectId | UOM;
  quantityInBaseUom: number;
  price: number;
  pricePerBaseUom: number;
}

export interface Supplier {
  _id: Types.ObjectId;
  name: string;
  taxNumber?: string;
  contactInfo?: {
    address?: string;
    phone?: string;
    email?: string;
  };
  paymentTerms?: string;
  companyId: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface SupplierDetail {
  supplierId: Types.ObjectId | Supplier;
  unitsOfOrdering: SupplierUnit[];
}

export interface IIngredient {
  _id: Types.ObjectId;
  name: string;
  description: string | null;
  reorderPoint: number | null;
  baseUomId: Types.ObjectId | UOM;
  category: string;
  SKU: string;
  companyId: Types.ObjectId;
  defaultSupplierId?: Types.ObjectId | Supplier;
  supplierDetails: SupplierDetail[];
  canBeSold: boolean;
  sellingDetails: SellingOption[];
  createdAt: Date;
  updatedAt: Date;
}
