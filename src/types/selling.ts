export type VisibilityType = 'ALL_LOCATIONS' | 'SPECIFIC_LOCATIONS' | 'EXTERNAL_ONLY';

export interface SellingOptionVisibility {
  type: VisibilityType;
  locations?: string[]; // Location IDs
  externalAccess: boolean;
}

export interface SellingOption {
  id: string;
  unitOfSelling: string;
  priceWithoutTax: number;
  priceWithTax: number;
  taxRate: number;
  taxCategory: 'STANDARD' | 'REDUCED' | 'ZERO' | 'EXEMPT';
  conversionFactor: number;
  visibility: SellingOptionVisibility;
}
