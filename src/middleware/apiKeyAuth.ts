import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';

// Schema for API key validation
const apiKeySchema = z.object({
  companyId: z.string().min(1, 'Company ID is required'),
  locationId: z.string().min(1, 'Location ID is required'),
  key: z.string().min(1, 'API key is required'),
});

// Get API keys from environment
const API_KEYS = process.env.POS_API_KEYS ? 
  JSON.parse(process.env.POS_API_KEYS) : {};

export function validateApiKey(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check if POS API is enabled
    if (process.env.POS_API_ENABLED !== 'true') {
      return res.status(403).json({
        error: 'POS API is not enabled'
      });
    }

    console.log('Debug: Validating API Key...');
    console.log('Headers received:', {
      apiKey: req.headers['x-api-key'],
      companyId: req.headers['x-company-id'],
      locationId: req.headers['x-location-id']
    });

    // Extract API key from headers
    const apiKey = req.headers['x-api-key'];
    const companyId = req.headers['x-company-id'];
    const locationId = req.headers['x-location-id'];

    // Validate required headers
    const result = apiKeySchema.safeParse({
      companyId,
      locationId,
      key: apiKey,
    });

    if (!result.success) {
      console.log('Debug: Missing or invalid authentication headers');
      return res.status(401).json({
        error: 'Missing or invalid authentication headers',
        details: result.error.issues,
      });
    }

    // Validate API key matches company and location
    const expectedKey = API_KEYS[`${companyId}_${locationId}`];
    console.log('Debug: Expected API Key:', expectedKey);
    console.log('Debug: Received API Key:', apiKey);

    if (!expectedKey || expectedKey !== apiKey) {
      console.log('Debug: Invalid API key');
      return res.status(401).json({
        error: 'Invalid API key',
      });
    }

    console.log('Debug: API Key validation successful');
    // Add validated data to request
    req.companyId = companyId as string;
    req.locationId = locationId as string;

    return null;
  } catch (error) {
    console.error('API key validation error:', error);
    return res.status(500).json({
      error: 'Internal server error during authentication',
    });
  }
}
