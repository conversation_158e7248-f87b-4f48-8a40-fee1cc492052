import { NextRequest } from 'next/server';
import { Types } from 'mongoose';
import Test<PERSON><PERSON><PERSON>ey from '@/models/TestApiKey';
import dbConnect from '@/lib/db';

export async function validateTestApi<PERSON>ey(request: NextRequest, companyId: string) {
  // Only allow in non-production environments
  if (process.env.NODE_ENV === 'production') {
    return { isValid: false, userId: null };
  }

  const apiKey = request.headers.get('x-test-api-key');
  if (!apiKey) {
    return { isValid: false, userId: null };
  }

  try {
    await dbConnect();
    
    // Find API key in database
    const apiKeyDoc = await TestApiKey.findOne({ 
      key: apiKey,
      companyId: new Types.ObjectId(companyId),
      expiresAt: { $gt: new Date() }
    });
    
    if (!apiKeyDoc) {
      return { isValid: false, userId: null };
    }
    
    // Return userId associated with this key
    return { 
      isValid: true,
      userId: apiKeyDoc.userId.toString(),
      role: apiKeyDoc.role
    };
  } catch (error) {
    console.error('API key validation error:', error);
    return { isValid: false, userId: null };
  }
}
