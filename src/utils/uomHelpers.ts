import { UOM } from '@/models/UOM';

// Helper function to get canonical unit and one unit below for display
export function getDisplayUnits(uoms: UOM[], baseUom: UOM) {
  // Filter UOMs to match base type and system
  const compatibleUoms = uoms.filter(u => 
    u.baseType === baseUom.baseType && 
    u.system === baseUom.system &&
    !u.shortCode.toLowerCase().includes('pack')
  );
  
  // Sort by factor (largest to smallest)
  const sortedByFactor = compatibleUoms.sort((a, b) => b.factorToCanonical - a.factorToCanonical);
  
  // Find the canonical unit (factorToCanonical = 1)
  const canonicalUnit = sortedByFactor.find(u => u.factorToCanonical === 1);
  if (!canonicalUnit) return { small: baseUom, large: baseUom };

  // Find the unit one step below canonical
  const canonicalIndex = sortedByFactor.findIndex(u => u.factorToCanonical === 1);
  const smallerUnit = sortedByFactor[canonicalIndex + 1];

  return {
    small: smallerUnit || canonicalUnit,
    large: canonicalUnit
  };
}
