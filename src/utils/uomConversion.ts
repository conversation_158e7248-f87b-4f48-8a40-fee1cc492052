import { Types } from 'mongoose';

export interface UOM {
  _id: Types.ObjectId;
  name: string;
  shortCode: string;
  system: 'metric' | 'imperial';
  baseType: 'mass' | 'volume' | 'unit';
  factorToCanonical: number;
  synonyms: string[];
  description: string;
}

export interface DisplayUOMValue {
  baseValue: number;
  displayValue: number;
  baseUom: UOM;
  displayUom: UOM;
}

/**
 * Convert a value from one UOM to another
 */
export function convertUOMValue(
  value: number,
  fromUom: UOM,
  toUom: UOM
): number {
  if (fromUom.baseType !== toUom.baseType) {
    throw new Error(`Cannot convert between different base types: ${fromUom.baseType} and ${toUom.baseType}`);
  }

  // Convert to canonical unit then to target unit
  // Multiply by source factor to get to canonical (e.g. g * 0.001 = kg)
  const canonicalValue = value * fromUom.factorToCanonical;
  // Divide by target factor to get to target unit (e.g. kg / 0.001 = g)
  return canonicalValue / toUom.factorToCanonical;
}

/**
 * Get the appropriate display UOM for a base UOM
 */
export const getDisplayUOM = (baseUom: UOM, uoms: UOM[]): UOM => {
  // Find a UOM with the same baseType and system but with a larger factorToCanonical
  const displayUom = uoms.find(uom => 
    uom.baseType === baseUom.baseType &&
    uom.system === baseUom.system &&
    uom.factorToCanonical > baseUom.factorToCanonical
  );

  return displayUom || baseUom;
};

/**
 * Format a value with its UOM for display
 */
export const formatUOMValue = (value: number, uom: UOM): string => {
  return `${value.toFixed(2)} ${uom.shortCode}`;
};

/**
 * Create a display value object with both base and converted values
 */
export const createDisplayValue = (
  baseValue: number,
  baseUom: UOM,
  displayUom: UOM
): DisplayUOMValue => {
  return {
    baseValue,
    displayValue: convertUOMValue(baseValue, baseUom, displayUom),
    baseUom,
    displayUom
  };
};
