// src/lib/ionic-auth.ts
import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import { decodeToken, getUserPermissionsFromToken } from './authUtils';
import User, { IUser } from '@/models/User';

// Permissions required for various inventory operations
// These are used for user-initiated actions in the POS app
const INVENTORY_PERMISSIONS = {
  // Sync operations don't require specific permissions (system-level operation)
  SYNC: [], // Empty array means no specific permissions required
  
  // User-initiated actions that should be permission-controlled
  ADJUSTMENT: ['inventory:write', 'inventory:adjust'],
  WASTAGE: ['inventory:write', 'inventory:wastage'],
  COUNT: ['inventory:count']
};

/**
 * Validates authentication from Ionic app using JWT token
 * Supports both Bearer token authentication and session cookie authentication
 */
export async function validateIonicAuth(req: NextRequest) {
  // Try to authenticate with Bearer token first
  const authHeader = req.headers.get('Authorization');
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return validateBearerToken(authHeader.substring(7));
  }
  
  // Fallback to session cookie authentication (for web admin users)
  return validateSessionCookie(req);
}

/**
 * Validates a Bearer token from the Ionic app
 */
async function validateBearerToken(token: string) {
  const secret = process.env.JWT_SECRET;
  
  if (!secret) {
    console.error('[Ionic Auth] JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }
  
  try {
    const decoded = verify(token, secret) as {
      id?: string;
      userId?: string; // Support both formats
      userType: string;
      role?: string;
      companyId?: string;
      locationId?: string;
      permissions?: string[];
    };
    console.log('[Ionic Auth] Bearer token decoded:', decoded);
    
    // Get user from database to ensure we have the latest permissions
    // Support both id and userId formats in the token
    const userId = decoded.id || decoded.userId;
    if (!userId) {
      console.error('[Ionic Auth] No user ID in token');
      return { isAuthenticated: false, error: 'Invalid token format' };
    }
    
    console.log('[Ionic Auth] Looking for user with ID:', userId);
    const user = await User.findById(userId).lean() as IUser | null;
    if (!user) {
      console.log('[Ionic Auth] User not found in database for ID:', userId);
      return { isAuthenticated: false, error: 'User not found' };
    }
    console.log('[Ionic Auth] User found:', user.email);
    
    // For inventory sync, we don't require specific permissions - any authenticated user can sync
    // The user-specific permissions are handled at the UI level in the Ionic app
    // We'll still return the permissions so the app can use them for UI decisions
    const permissions = getUserPermissionsFromToken(token);
    
    // Successfully authenticated
    return { 
      isAuthenticated: true, 
      userId: userId, 
      companyId: decoded.companyId || user.companyId?.toString(),
      locationId: decoded.locationId,
      role: decoded.role || user.role,
      permissions: decoded.permissions || user.permissions || []
    };
  } catch (error) {
    console.error('Token validation error:', error);
    return { isAuthenticated: false, error: 'Invalid token' };
  }
}

/**
 * Validates a session cookie (fallback for web admin users)
 */
async function validateSessionCookie(req: NextRequest) {
  // Get cookies from request
  const sessionCookie = req.cookies.get('session');
  
  if (!sessionCookie) {
    console.log('[Ionic Auth] No session cookie found');
    return { isAuthenticated: false, error: 'No session cookie' };
  }
  console.log('[Ionic Auth] Session cookie found');
  
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    console.error('[Ionic Auth] JWT secret not configured');
    return { isAuthenticated: false, error: 'Server configuration error' };
  }
  
  try {
    const decoded = verify(sessionCookie.value, secret) as {
      id?: string;
      userId?: string; // Support both formats as in verify/route.ts
      userType: string;
      role?: string;
      companyId?: string;
    };
    console.log('[Ionic Auth] Decoded JWT:', decoded);
    
    // Get user from database to ensure we have the latest data
    // Support both id and userId formats in the token as in verify/route.ts
    const userId = decoded.id || decoded.userId;
    if (!userId) {
      console.error('[Ionic Auth] No user ID in token');
      return { isAuthenticated: false, error: 'Invalid token format' };
    }
    
    console.log('[Ionic Auth] Looking for user with ID:', userId);
    const user = await User.findById(userId).lean() as IUser | null;
    if (!user) {
      console.log('[Ionic Auth] User not found in database for ID:', userId);
      return { isAuthenticated: false, error: 'User not found' };
    }
    console.log('[Ionic Auth] User found:', user.email);
    
    // Successfully authenticated
    return { 
      isAuthenticated: true, 
      userId: decoded.id, 
      companyId: decoded.companyId || user.companyId?.toString(),
      role: decoded.role || user.role,
      permissions: user.permissions || []
    };
  } catch (error) {
    return { isAuthenticated: false, error: 'Invalid session' };
  }
}

/**
 * Validates company and location access
 */
export async function validateLocationAccess(
  companyId: string,
  locationId: string,
  headerCompanyId: string | null
) {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
  
  // Additional location validation could be added here if needed
  return true;
}
