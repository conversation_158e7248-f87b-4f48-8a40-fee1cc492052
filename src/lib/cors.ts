// src/lib/cors.ts
import { NextRequest, NextResponse } from 'next/server';

/**
 * CORS middleware for Next.js API routes
 * Allows requests from the Ionic POS app and handles preflight requests
 */
export function cors(req: NextRequest, res: NextResponse) {
  // Define allowed origins
  const allowedOrigins = [
    'http://localhost:8100', // Ionic app local development
    'http://localhost:3000', // Next.js app local development
    'capacitor://localhost', // Ionic Capacitor
    'ionic://localhost' // Ionic framework
  ];

  // Get the origin from the request
  const origin = req.headers.get('origin') || '';
  
  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  // Set CORS headers
  const headers = new Headers(res.headers);
  
  if (isAllowedOrigin) {
    headers.set('Access-Control-Allow-Origin', origin);
  } else {
    // For security, we only set the specific allowed origins
    // If origin is not in our list, we don't set the header
    console.warn(`Request from non-allowed origin: ${origin}`);
  }
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id');
  
  // Create a new response with the updated headers
  const response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });
  
  // Copy all headers to the new response
  headers.forEach((value, key) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Helper function to apply CORS headers to a Response object
 */
export function applyCorsHeaders(response: Response, req: NextRequest): Response {
  const origin = req.headers.get('origin') || '';
  const allowedOrigins = [
    'http://localhost:8100', // Ionic app local development
    'http://localhost:3000', // Next.js app local development
    'capacitor://localhost', // Ionic Capacitor
    'ionic://localhost', // Ionic framework
    'http://localhost:5173', // Vite default dev port
    '*' // Temporarily allow all origins for debugging
  ];
  
  // Always allow the request during development (REMOVE IN PRODUCTION)
  // This allows us to debug without CORS issues
  const headers = new Headers(response.headers);
  
  // In development, we're always returning the origin
  headers.set('Access-Control-Allow-Origin', origin);
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, x-company-id');
  
  // Log CORS headers for debugging
  console.log('CORS headers being sent:', {
    'Access-Control-Allow-Origin': headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Credentials': headers.get('Access-Control-Allow-Credentials'),
    'Access-Control-Allow-Methods': headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': headers.get('Access-Control-Allow-Headers')
  });
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function handleCorsOptions(req: NextRequest) {
  const origin = req.headers.get('origin') || '';
  const allowedOrigins = [
    'http://localhost:8100', // Ionic app local development
    'http://localhost:3000', // Next.js app local development
    'capacitor://localhost', // Ionic Capacitor
    'ionic://localhost', // Ionic framework
    'http://localhost:5173', // Vite default dev port
    '*' // Temporarily allow all origins for debugging
  ];
  
  // Always allow the origin in development mode
  const headers = new Headers();
  
  // In development, we're always returning the origin
  headers.set('Access-Control-Allow-Origin', origin);
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, x-company-id');
  
  // Log for debugging
  console.log('OPTIONS request received from:', origin);
  console.log('Responding with CORS headers:', {
    'Access-Control-Allow-Origin': headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Credentials': headers.get('Access-Control-Allow-Credentials'),
    'Access-Control-Allow-Methods': headers.get('Access-Control-Allow-Methods')
  });
  
  return new Response(null, {
    status: 204,
    headers
  });
}
