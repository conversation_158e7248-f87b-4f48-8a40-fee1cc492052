// src/lib/auth-helpers.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import mongoose from 'mongoose';
import User, { IUser } from '@/models/User'; // Assuming you have a User model
import bcrypt from 'bcrypt';
import { getUserPermissions } from '@/lib/syncUtils';
import dbConnect from '@/lib/db'; // Added import
import Api<PERSON>ey from '@/models/ApiKey'; // Added import - adjust path if needed

// Define the structure of the authenticated user object
export interface AuthUser {
  id: string;
  email: string;
  role: string;
  tenantId: string;
  metadata?: Record<string, any>;
}

// Define expected JWT payload structure based on recommended memory
interface DecodedToken extends JwtPayload {
  sub: string;         // User ID
  tenant_id: string;   // Tenant/Company ID
  role: string;
  email: string;
}

/**
 * Extracts and verifies JWT from request cookie or API key header.
 * Returns the decoded payload if valid, otherwise null.
 * @param req - NextRequest object
 * @returns {Promise<DecodedToken | null>} Decoded token payload or null
 */
export async function getAuthFromRequest(req: NextRequest): Promise<DecodedToken | null> {
  // Use req.cookies instead of cookies() from next/headers for consistency
  const token = req.cookies.get('auth-token')?.value;

  if (!token) {
    // Check API key header
    const apiKeyHeader = req.headers.get('x-test-api-key');
    if (apiKeyHeader) {
      try {
        await dbConnect();
        // Find active API key and populate necessary user details (like email)
        const apiKeyData = await ApiKey.findOne({ key: apiKeyHeader, isActive: true })
                                    .populate<{ userId: Pick<IUser, '_id' | 'email'> }>('userId', '_id email') // Populate _id and email
                                    .lean(); // Use lean for plain JS object

        if (apiKeyData && apiKeyData.userId) {
          console.log(`API Key auth successful for user: ${apiKeyData.userId._id}`);

          // Construct an object mimicking DecodedToken from API Key data
          const decodedFromApiKey: DecodedToken = {
            sub: apiKeyData.userId._id.toString(),
            tenant_id: apiKeyData.companyId.toString(), // Ensure string format
            role: apiKeyData.role, // Role from ApiKey model
            email: apiKeyData.userId.email, // Email from populated User
            // Add dummy iat/exp if required downstream, though not inherent to API keys
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + (60 * 60), // Dummy 1-hour expiry
          };

          // Map to legacy fields for compatibility
          const mapped = {
            ...decodedFromApiKey,
            id: decodedFromApiKey.sub,
            companyId: decodedFromApiKey.tenant_id,
          } as DecodedToken & { id: string; companyId: string };

          return mapped;

        } else {
          console.warn(`Invalid or inactive API Key provided.`);
          return null; // Invalid/inactive key
        }
      } catch (error) {
        console.error('Error during API Key authentication:', error);
        return null; // DB or other error during API key lookup
      }
    }
    // No token and no API key header provided
    return null;
  }

  try {
    // Verify the token using the secret and best-practice options
    const decoded = jwt.verify(token, process.env.JWT_SECRET!, {
      issuer: process.env.JWT_ISSUER,
      audience: process.env.JWT_AUDIENCE,
    }) as DecodedToken;

    // Validate essential claims
    if (!decoded.sub || !decoded.tenant_id || !decoded.role) {
      console.error('Token missing essential claims (sub, tenant_id, role)');
      return null;
    }

    // Map to legacy id and companyId for backward compatibility
    const mapped = {
      ...decoded,
      id: decoded.sub,
      companyId: decoded.tenant_id,
    } as DecodedToken & { id: string; companyId: string };
    return mapped;
  } catch (error) {
    // Handle specific JWT errors if needed (e.g., TokenExpiredError)
    console.error('Token verification failed:', error instanceof Error ? error.message : error);
    return null;
  }
}

// Overloads for direct and curried invocation
export function requireAuth(req: NextRequest): Promise<AuthUser>;
export function requireAuth(allowedRoles?: string[]): (req: NextRequest) => Promise<AuthUser>;

// Middleware to require authentication (supports direct and curried usage)
export function requireAuth(arg?: string[] | NextRequest) {
  const roles = Array.isArray(arg) ? arg : [];
  const directReq = !Array.isArray(arg) ? arg as NextRequest : undefined;

  const authFn = async (req: NextRequest): Promise<AuthUser> => { 
    const decoded = await getAuthFromRequest(req);
    if (!decoded) {
      throw new Error('Invalid session');
    }
    // Check allowed roles
    if (roles.length > 0 && !roles.includes(decoded.role)) {
      throw new Error('Insufficient permissions');
    }
    // Map to AuthUser
    const authUser: AuthUser = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      tenantId: decoded.companyId,
      metadata: {},
    };
    return authUser;
  };

  if (directReq) {
    return authFn(directReq);
  }
  return authFn;
}

/**
 * Validates device token authentication for IonicPOS
 * @param req - NextRequest object containing headers
 */
export async function validateDeviceAuth(req: NextRequest) {
  const deviceToken = req.headers.get('X-Device-Token');
  if (!deviceToken) return null;
  
  try {
    // Validate the device token JWT
    const decoded = await decodeToken(deviceToken); // Await the async call
    if (!decoded || !decoded.id) {
      return null;
    }
    
    // Find the associated user
    const user = await User.findById(decoded.id).lean(); // Use lean for plain object
    if (!user) {
      return null;
    }
    
    // Return a structure similar to AuthUser or DecodedToken if needed
    // For now, returning the user object fetched from DB
    return user; // Consider returning a standardized AuthUser/DecodedToken object
  } catch (error) {
    console.error('Device authentication error:', error);
    return null;
  }
}

/**
 * Validates PIN-based authentication for POS users
 * @param userId - The user ID
 * @param pin - The PIN code to verify
 */
export async function validateUserPin(userId: string, pin: string) {
  try {
    const user = await User.findById(userId);
    if (!user || !user.pin) {
      return false;
    }
    
    // Compare the provided PIN with the stored hash
    return await bcrypt.compare(pin, user.pin);
  } catch (error) {
    console.error('PIN validation error:', error);
    return false;
  }
}

/**
 * Hash a PIN for secure storage
 * @param pin - The PIN to hash
 */
export async function hashPin(pin: string): Promise<string> {
  return await bcrypt.hash(pin, 10);
}

/**
 * Extended authentication wrapper that supports standard web auth, device auth, and company token auth
 * 
 * @param handler - The route handler function to be executed after authentication
 * @param options - Configuration options
 *                 allowedRoles: Array of roles allowed to access this endpoint
 *                 allowCompanyToken: Whether to allow company token authentication
 *                 requiredPermissions: Array of permissions required to access this endpoint
 * 
 * @returns A middleware function that:
 * 1. Attempts standard JWT authentication from session cookie
 * 2. Falls back to device authentication for IonicPOS
 * 3. Optionally checks for company token (for POS company authentication)
 * 4. Validates user roles against allowed roles
 * 5. Validates user permissions against required permissions
 * 6. Passes authenticated user to the handler
 * 
 * @throws 401 Unauthorized if no valid authentication is found
 * @throws 403 Forbidden if user's role or permissions are not sufficient
 */
export function withAuth(
  handler: (...args: any[]) => any,
  options: {
    allowedRoles?: string[];
    allowCompanyToken?: boolean;
    requiredPermissions?: string[];
  } | string[] = {}
) {
  // Handle backward compatibility with old function signature
  let allowedRoles: string[] = [];
  let allowCompanyToken = false;
  let requiredPermissions: string[] = [];

  if (Array.isArray(options)) {
    // Old format: withAuth(handler, ['role1', 'role2'])
    allowedRoles = options;
  } else {
    // New format: withAuth(handler, { allowedRoles, allowCompanyToken, requiredPermissions })
    allowedRoles = options.allowedRoles || [];
    allowCompanyToken = options.allowCompanyToken || false;
    requiredPermissions = options.requiredPermissions || [];
  }
  
  return async (req: NextRequest, context?: any) => {
    // Try standard authentication
    try {
      const user = await requireAuth(allowedRoles)(req);
      // Enforce fine-grained permissions
      if (requiredPermissions.length > 0) {
        const companyId = req.headers.get('company-id');
        if (!companyId) {
          return NextResponse.json({ error: 'Company ID is required for permission check' }, { status: 400 });
        }
        const permObj = await getUserPermissions(user.id, companyId);
        const userPerms = permObj.permissions;
        if (!requiredPermissions.some(p => userPerms.includes(p))) {
          return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
        }
      }
      return handler(req, user, context);
    } catch (error) {
      // If standard auth fails, try device auth for IonicPOS
      const deviceUserRaw = await validateDeviceAuth(req);
      if (deviceUserRaw) {
        // Explicitly type the user and use _id
        // Use 'as any' for now as .lean() object doesn't fully match IUser
        const deviceUser = deviceUserRaw as any;

        // Check if device user's role is allowed
        if (allowedRoles.length && deviceUser.role && !allowedRoles.includes(deviceUser.role)) {
          return NextResponse.json({ error: 'Unauthorized role' }, { status: 403 });
        }
        // Enforce fine-grained permissions
        if (requiredPermissions.length > 0 && deviceUser._id) {
          const companyId = req.headers.get('company-id');
          if (!companyId) {
            return NextResponse.json({ error: 'Company ID is required for permission check' }, { status: 400 });
          }
          const permObj = await getUserPermissions(deviceUser._id.toString(), companyId);
          const userPerms = permObj.permissions;
          if (!requiredPermissions.some(p => userPerms.includes(p))) {
            return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
          }
        }
        return handler(req, deviceUser, context);
      }
      
      // If company token authentication is allowed, try that
      if (allowCompanyToken) {
        const companyToken = req.headers.get('X-Company-Token');
        if (companyToken) {
          try {
            const decodedCompanyToken = await verifyCompanyToken(companyToken);
            if (decodedCompanyToken && decodedCompanyToken.companyId) {
              // Company token is valid
              // Create a simple user object with company information
              const companyUser = {
                role: 'pos_company', // Or derive from token if available
                companyId: decodedCompanyToken.companyId, // Ensure this exists in the company token payload
                token_type: 'company_access', // Custom property to indicate auth type
                // Add other relevant details if needed
              };
              // Role/permission check for company token might be needed here
              return handler(req, companyUser, context);
            }
          } catch (tokenError) {
            console.error('Company token validation error:', tokenError);
          }
        }
      }
      
      // All auth methods failed
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
  };
}

/**
 * Decodes a JWT token
 * @param token - The JWT token to decode
 * @returns {Promise<JwtPayload | null>} The decoded token payload or null if decoding fails.
 */
export async function decodeToken(token: string): Promise<JwtPayload | null> {
  try {
    const decoded = jwt.decode(token) as JwtPayload;
    return decoded;
  } catch (error) {
    console.error('Token decoding error:', error);
    return null;
  }
}

/**
 * Validates a company token
 * @param token - The company token to validate
 * @returns {Promise<JwtPayload | null>} The decoded token payload or null if validation fails.
 */
export async function verifyCompanyToken(token: string): Promise<JwtPayload | null> {
  try {
    const decoded = jwt.verify(token, process.env.COMPANY_TOKEN_SECRET!) as JwtPayload;
    return decoded;
  } catch (error) {
    console.error('Company token validation error:', error);
    return null;
  }
}