// src/lib/userTracking.ts
import User from '@/models/User';
import mongoose from 'mongoose';

/**
 * Tracks changes to user data and updates sync-related fields
 * @param userId - The ID of the user being updated
 * @param updatedFields - Object containing the fields that were updated
 */
export async function trackUserChanges(userId: string, updatedFields: any) {
  try {
    // Skip update if no fields are provided
    if (!updatedFields || Object.keys(updatedFields).length === 0) {
      return;
    }

    // Convert string ID to ObjectId if needed
    const userObjectId = typeof userId === 'string' 
      ? new mongoose.Types.ObjectId(userId) 
      : userId;

    // Update the user document with the modified fields and sync metadata
    await User.findByIdAndUpdate(
      userObjectId,
      {
        $set: {
          ...updatedFields,
          lastModified: new Date(),
          syncStatus: 'pending',
          modifiedFields: Object.keys(updatedFields)
        }
      },
      { new: true }
    );
  } catch (error) {
    console.error('Error tracking user changes:', error);
    throw error;
  }
}

/**
 * Marks a user as deleted for sync purposes
 * This performs a soft delete by setting isDeleted flag
 * 
 * @param userId - The ID of the user to delete
 * @param companyId - The company ID for verification
 */
export async function markUserDeleted(userId: string, companyId: string) {
  try {
    const userObjectId = typeof userId === 'string' 
      ? new mongoose.Types.ObjectId(userId) 
      : userId;
    
    const companyObjectId = typeof companyId === 'string'
      ? new mongoose.Types.ObjectId(companyId)
      : companyId;

    // Update user as deleted and mark for sync
    const result = await User.findOneAndUpdate(
      { 
        _id: userObjectId,
        companyId: companyObjectId
      },
      {
        $set: {
          isDeleted: true,
          lastModified: new Date(),
          syncStatus: 'pending',
          modifiedFields: ['isDeleted']
        }
      },
      { new: true }
    );

    if (!result) {
      throw new Error(`User not found or doesn't belong to the specified company`);
    }

    return result;
  } catch (error) {
    console.error('Error marking user as deleted:', error);
    throw error;
  }
}

/**
 * Updates a user's PIN securely
 * 
 * @param userId - The ID of the user to update
 * @param pin - The new PIN (should be pre-hashed by the caller)
 */
export async function updateUserPin(userId: string, pin: string) {
  try {
    const userObjectId = typeof userId === 'string' 
      ? new mongoose.Types.ObjectId(userId) 
      : userId;

    // Update the user with the new PIN
    await User.findByIdAndUpdate(
      userObjectId,
      {
        $set: {
          pin,
          lastModified: new Date(),
          syncStatus: 'pending',
          modifiedFields: ['pin']
        }
      },
      { new: true }
    );
  } catch (error) {
    console.error('Error updating user PIN:', error);
    throw error;
  }
}