import { Types } from 'mongoose';
import Order from '@/models/Order';
import DeliveryNote from '@/models/DeliveryNote';
import axios from 'axios';

export async function updateOrderDeliveryStatus(orderId: string) {
  const order = await Order.findById(orderId);
  if (!order || order.status !== 'CONFIRMED') {
    return;
  }

  // Get all completed delivery notes for this order
  const deliveryNotes = await DeliveryNote.find({
    orderId: new Types.ObjectId(orderId),
    status: { $in: ['COMPLETED'] }
  });

  // If no delivery notes exist, set to NOT_DELIVERED
  if (deliveryNotes.length === 0) {
    order.status = 'NOT_DELIVERED';
    await order.save();
    return;
  }

  // Calculate total delivered quantities for each item
  const deliveredQuantities: { [key: string]: number } = {};
  
  // Initialize with 0
  order.items.forEach(item => {
    deliveredQuantities[item.itemId.toString()] = 0;
  });

  // Sum up delivered quantities from delivery notes
  deliveryNotes.forEach(note => {
    note.handoverFlow.forEach(step => {
      if (step.status === 'SIGNED') {
        step.confirmedItems.forEach(confirmedItem => {
          const itemId = confirmedItem.itemId.toString();
          deliveredQuantities[itemId] = (deliveredQuantities[itemId] || 0) + confirmedItem.confirmedQty;
        });
      }
    });
  });

  // Update delivered quantities in order
  let allItemsDelivered = true;
  order.items.forEach(item => {
    const itemId = item.itemId.toString();
    item.deliveredQuantity = deliveredQuantities[itemId] || 0;
    if (item.deliveredQuantity < item.quantity) {
      allItemsDelivered = false;
    }
  });

  // Update order status
  if (allItemsDelivered) {
    order.status = 'DELIVERED';
  } else {
    order.status = 'PARTIALLY_DELIVERED';
  }

  await order.save();
}

export async function closeOrder(orderId: string) {
  try {
    const response = await axios.patch(`/api/orders/${orderId}`, {
      status: 'DELIVERED'
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || error.message || 'Failed to close order');
  }
}
