'use server';

import mongoose from 'mongoose';
import dbConnect from '../db';
import Company from '@/models/Company'; 
import User from '@/models/User'; 

export type CompanyData = {
  id: string;
  name: string;
  subdomain: string;
  ownerId: string;
  roles?: {
    [key: string]: 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper';
  };
  createdAt: Date;
  updatedAt: Date;
};

export async function createCompany(data: {
  name: string;
  subdomain: string;
  ownerId: string;
}): Promise<CompanyData> {
  await dbConnect();
  
  const company = await Company.create({
    name: data.name,
    subdomain: data.subdomain,
    ownerId: data.ownerId,
  });

  return {
    id: company._id.toString(),
    name: company.name,
    subdomain: company.subdomain,
    ownerId: company.ownerId.toString(),
    createdAt: company.createdAt,
    updatedAt: company.updatedAt,
  };
}

export async function getUserCompany(userId: string) {
  await dbConnect();
  
  const user = await User.findById(userId);
  if (!user || user.userType !== 'company_user' || !user.companyId) {
    return null;
  }

  const company = await Company.findById(user.companyId);
  if (!company) {
    return null;
  }

  return {
    companyId: company._id.toString(),
    userType: 'company_user' as const,
    roles: user.permissions || {},
  };
}

export async function getCompanyById(companyId: string): Promise<CompanyData | null> {
  await dbConnect();
  
  const company = await Company.findById(companyId);
  if (!company) return null;

  return {
    id: company._id.toString(),
    name: company.name,
    subdomain: company.subdomain,
    ownerId: company.ownerId.toString(),
    createdAt: company.createdAt,
    updatedAt: company.updatedAt,
  };
}

export async function getCompanyBySubdomain(subdomain: string): Promise<CompanyData | null> {
  await dbConnect();
  
  const company = await Company.findOne({ subdomain });
  if (!company) return null;

  return {
    id: company._id.toString(),
    name: company.name,
    subdomain: company.subdomain,
    ownerId: company.ownerId.toString(),
    createdAt: company.createdAt,
    updatedAt: company.updatedAt,
  };
}
