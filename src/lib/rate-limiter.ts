// src/lib/rate-limiter.ts
import { Redis } from '@upstash/redis';

// Initialize Redis client if Upstash Redis URL is provided
// Otherwise use a simple in-memory store for development
let redis: Redis | null = null;

if (process.env.UPSTASH_REDIS_URL && process.env.UPSTASH_REDIS_TOKEN) {
  // Dynamic import to avoid build issues
  import('@upstash/redis').then(({ Redis }) => {
    redis = new Redis({
      url: process.env.UPSTASH_REDIS_URL as string,
      token: process.env.UPSTASH_REDIS_TOKEN as string,
    });
  }).catch(err => {
    console.error('Failed to initialize Redis client:', err);
  });
}

// In-memory store as fallback (not suitable for production with multiple instances)
const inMemoryStore: Record<string, { count: number; expires: number }> = {};

// Clean up expired entries every 5 minutes
if (typeof window === 'undefined') { // Only run on server
  setInterval(() => {
    const now = Date.now();
    Object.keys(inMemoryStore).forEach(key => {
      if (inMemoryStore[key].expires < now) {
        delete inMemoryStore[key];
      }
    });
  }, 5 * 60 * 1000);
}

/**
 * Rate limiter function to limit requests based on IP and action
 * 
 * @param ip Client IP address
 * @param action Action identifier (e.g., 'login', 'pos-company-auth')
 * @param limit Maximum number of requests allowed in the time window
 * @param windowSecs Time window in seconds
 * @returns Object indicating if the request should be allowed
 */
export async function rateLimiter(
  ip: string,
  action: string,
  limit: number = 10,
  windowSecs: number = 60
): Promise<{ success: boolean; current: number; reset: number }> {
  const key = `ratelimit:${ip}:${action}`;
  const now = Date.now();
  const windowMs = windowSecs * 1000;
  const expires = now + windowMs;

  try {
    // Use Redis if available
    if (redis) {
      // Initialize if not exists
      const exists = await redis.exists(key);
      if (!exists) {
        await redis.set(key, 1, { ex: windowSecs });
        return { success: true, current: 1, reset: expires };
      }

      // Increment counter
      const current = await redis.incr(key);
      const ttl = await redis.ttl(key);
      
      return {
        success: current <= limit,
        current,
        reset: now + (ttl * 1000)
      };
    } 
    // Fallback to in-memory store
    else {
      // Initialize if not exists
      if (!inMemoryStore[key]) {
        inMemoryStore[key] = { count: 1, expires };
        return { success: true, current: 1, reset: expires };
      }

      // Check if expired
      if (inMemoryStore[key].expires < now) {
        inMemoryStore[key] = { count: 1, expires };
        return { success: true, current: 1, reset: expires };
      }

      // Increment counter
      inMemoryStore[key].count += 1;
      
      return {
        success: inMemoryStore[key].count <= limit,
        current: inMemoryStore[key].count,
        reset: inMemoryStore[key].expires
      };
    }
  } catch (error) {
    console.error('Rate limiter error:', error);
    // Default to allowing the request in case of errors
    return { success: true, current: 0, reset: expires };
  }
}
