import { User } from '@/models/User';
import { Types } from 'mongoose';

export type Role = 'admin' | 'owner' | 'user';

/**
 * Check if a user has permission to perform actions for a specific company
 */
export async function hasPermission(
  userId: string,
  companyId: string | Types.ObjectId,
  allowedRoles: Role[]
): Promise<boolean> {
  try {
    const user = await User.findById(userId).select('role companyId');
    
    if (!user) {
      return false;
    }

    // Convert companyId to string for comparison
    const userCompanyId = user.companyId?.toString();
    const targetCompanyId = companyId.toString();

    // Check if user belongs to the company and has the required role
    return userCompanyId === targetCompanyId && allowedRoles.includes(user.role as Role);
  } catch (error) {
    console.error('Error checking permissions:', error);
    return false;
  }
}
