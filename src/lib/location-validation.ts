// src/lib/location-validation.ts
import mongoose from 'mongoose';

/**
 * Validates company and location access
 */
export async function validateLocationAccess(
  companyId: string,
  locationId: string,
  headerCompanyId: string | null
) {
  if (!companyId || !headerCompanyId) {
    throw new Error('Company ID is required');
  }
  
  if (companyId !== headerCompanyId) {
    throw new Error('Company ID mismatch');
  }
  
  // In development mode, we'll accept any location ID
  if (process.env.NODE_ENV !== 'production') {
    return true;
  }

  try {
    // In production, we would check if the location belongs to the company
    // For now, we'll implement a simplified version
    const Location = mongoose.models.Location;
    
    if (!Location) {
      // If Location model isn't loaded yet, assume it's okay (for testing)
      console.warn('Location model not found, skipping validation');
      return true;
    }
    
    const location = await Location.findOne({
      _id: new mongoose.Types.ObjectId(locationId),
      companyId: new mongoose.Types.ObjectId(companyId)
    });
    
    if (!location) {
      throw new Error('Location not found or doesn\'t belong to the specified company');
    }
    
    return true;
  } catch (error) {
    console.error('Error validating location access:', error);
    throw new Error('Location validation failed');
  }
}