// src/lib/auth.tsx
'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

export type UserType = 'superuser' | 'company_user';

export interface CompanyUserData {
  uid: string;
  email: string;
  userType: 'company_user';
  companyId: string;
  role: 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper';
  permissions?: string[];
}

export interface SuperUserData {
  uid: string;
  email: string;
  userType: 'superuser';
}

interface AuthContextType {
  userData: CompanyUserData | SuperUserData | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType>({
  userData: null,
  loading: true,
  signIn: async () => {},
  signOut: async () => {},
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [userData, setUserData] = useState<CompanyUserData | SuperUserData | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const verifySession = async () => {
      try {
        const response = await fetch('/api/auth/verify', {
          credentials: 'include' // Important for cookies
        });
        
        if (!response.ok) {
          setUserData(null);
          return;
        }

        const data = await response.json();
        setUserData(data);
      } catch (error) {
        console.error('Session verification error:', error);
        setUserData(null);
      } finally {
        setLoading(false);
      }
    };

    verifySession();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include' // Important for cookies
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to sign in');
      }

      const data = await response.json();
      setUserData(data.user);

      // Redirect based on user type
      if (data.user.userType === 'company_user') {
        router.push(`/company/${data.user.companyId}/dashboard`);
      } else {
        router.push('/superadmin/dashboard');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await fetch('/api/auth/signout', {
        method: 'POST',
        credentials: 'include'
      });
      setUserData(null);
      router.push('/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ userData, loading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Add the useRequireCompanyUser Hook
export const useRequireCompanyUser = (requiredRole?: 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper') => {
  const { userData, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only check authentication after loading is complete
    if (!loading) {
      // Redirect to login if user is not authenticated
      if (!userData || userData.userType !== 'company_user') {
        console.log('[Auth] Redirecting to login - No user data or wrong user type');
        router.replace('/login');
        return;
      }

      // Special handling for admin access - allow both admin and owner roles
      if (requiredRole === 'admin') {
        if (userData.role !== 'admin' && userData.role !== 'owner') {
          console.log('[Auth] Redirecting to unauthorized - Not admin or owner');
          router.replace('/unauthorized');
          return;
        }
      }
      // For other roles, require exact match
      else if (requiredRole && userData.role !== requiredRole) {
        console.log('[Auth] Redirecting to unauthorized - Role mismatch');
        router.replace('/unauthorized');
        return;
      }
    }
  }, [userData, loading, router, requiredRole]);

  return { userData, loading };
};