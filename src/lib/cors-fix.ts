// src/lib/cors-fix.ts
// This is an enhanced version of the cors.ts file with complete header support
// Replace the contents of cors.ts with this file to fix CORS issues

import { NextRequest, NextResponse } from 'next/server';

/**
 * CORS middleware for Next.js API routes
 * Allows requests from the Ionic POS app and handles preflight requests
 */
export function cors(req: NextRequest, res: NextResponse) {
  // Define allowed origins
  const allowedOrigins = [
    'http://localhost:8100', // Ionic app local development
    'http://localhost:3000', // Next.js app local development
    'capacitor://localhost', // Ionic Capacitor
    'ionic://localhost', // Ionic framework
    'http://localhost:5173', // Vite default port
    '*' // For development only - REMOVE IN PRODUCTION
  ];

  // Get the origin from the request
  const origin = req.headers.get('origin') || '';
  
  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  // Set CORS headers
  const headers = new Headers(res.headers);
  
  // In development mode, always allow the origin
  headers.set('Access-Control-Allow-Origin', origin);
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  
  // Include ALL headers that could be sent by the Ionic app
  headers.set('Access-Control-Allow-Headers', 
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, ' +
    'Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, ' +
    'x-company-id, X-Company-Id, X-Location-Id, location-id, X-API-Key, ' + 
    'Cache-Control, Pragma');
  
  // Log for debugging
  console.log('CORS headers applied:', {
    'Access-Control-Allow-Origin': headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Credentials': headers.get('Access-Control-Allow-Credentials'),
    'Access-Control-Allow-Methods': headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': headers.get('Access-Control-Allow-Headers')
  });
  
  // Create a new response with the updated headers
  const response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });
  
  // Copy all headers to the new response
  headers.forEach((value, key) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Helper function to apply CORS headers to a Response object
 */
export function applyCorsHeaders(response: Response, req: NextRequest): Response {
  const origin = req.headers.get('origin') || '';
  const allowedOrigins = [
    'http://localhost:8100', // Ionic app local development
    'http://localhost:3000', // Next.js app local development
    'capacitor://localhost', // Ionic Capacitor
    'ionic://localhost', // Ionic framework
    'http://localhost:5173', // Vite default dev port
    '*' // Temporarily allow all origins for debugging
  ];
  
  // Always allow the request during development (REMOVE IN PRODUCTION)
  const headers = new Headers(response.headers);
  
  // In development, we're always returning the origin
  headers.set('Access-Control-Allow-Origin', origin);
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  
  // Include ALL headers that could be sent by the Ionic app
  headers.set('Access-Control-Allow-Headers', 
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, ' +
    'Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, ' +
    'x-company-id, X-Company-Id, X-Location-Id, location-id, X-API-Key, ' + 
    'Cache-Control, Pragma');
  
  // Log CORS headers for debugging
  console.log('CORS headers being sent:', {
    'Access-Control-Allow-Origin': headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Credentials': headers.get('Access-Control-Allow-Credentials'),
    'Access-Control-Allow-Methods': headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': headers.get('Access-Control-Allow-Headers')
  });
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function handleCorsOptions(req: NextRequest) {
  const origin = req.headers.get('origin') || '';
  
  // Always allow the origin in development mode
  const headers = new Headers();
  
  // In development, we're always returning the origin
  headers.set('Access-Control-Allow-Origin', origin);
  
  headers.set('Access-Control-Allow-Credentials', 'true');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  
  // Include ALL headers that could be sent by the Ionic app
  headers.set('Access-Control-Allow-Headers', 
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, ' +
    'Content-MD5, Content-Type, Date, X-Api-Version, Authorization, company-id, ' +
    'x-company-id, X-Company-Id, X-Location-Id, location-id, X-API-Key, ' + 
    'Cache-Control, Pragma');
  
  // Log for debugging
  console.log('OPTIONS request received from:', origin);
  console.log('Responding with CORS headers:', {
    'Access-Control-Allow-Origin': headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Credentials': headers.get('Access-Control-Allow-Credentials'),
    'Access-Control-Allow-Methods': headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': headers.get('Access-Control-Allow-Headers')
  });
  
  return new Response(null, {
    status: 204,
    headers
  });
}