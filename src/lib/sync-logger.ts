// src/lib/sync-logger.ts
import mongoose, { Schema, Types, Document } from 'mongoose';

export interface ISyncLog extends Document {
  timestamp: Date;
  companyId: Types.ObjectId;
  locationId: Types.ObjectId;
  syncId: string;
  eventType: 'SYNC_START' | 'SYNC_COMPLETE' | 'SYNC_ERROR' | 'ITEM_ERROR' | 'ORDER_CREATED' | 'ORDER_UPDATED' | 'ORDER_DELETED';
  details: any;
}

const syncLogSchema = new Schema<ISyncLog>({
  timestamp: { type: Date, default: Date.now },
  companyId: { type: Schema.Types.ObjectId, required: true },
  locationId: { type: Schema.Types.ObjectId, required: true },
  syncId: { type: String, required: true },
  eventType: { 
    type: String, 
    required: true,
    enum: ['SYNC_START', 'SYNC_COMPLETE', 'SYNC_ERROR', 'ITEM_ERROR', 'ORDER_CREATED', 'ORDER_UPDATED', 'ORDER_DELETED']
  },
  details: { type: Schema.Types.Mixed }
});

const SyncLog = mongoose.models.SyncLog || 
  mongoose.model<ISyncLog>('SyncLog', syncLogSchema);

export async function logSyncEvent(
  companyId: string,
  locationId: string,
  syncId: string,
  eventType: 'SYNC_START' | 'SYNC_COMPLETE' | 'SYNC_ERROR' | 'ITEM_ERROR' | 'ORDER_CREATED' | 'ORDER_UPDATED' | 'ORDER_DELETED',
  details: any
) {
  try {
    const log = new SyncLog({
      companyId: new Types.ObjectId(companyId),
      locationId: new Types.ObjectId(locationId),
      syncId,
      eventType,
      details
    });
    
    await log.save();
  } catch (error) {
    console.error('Error logging sync event:', error);
  }
}