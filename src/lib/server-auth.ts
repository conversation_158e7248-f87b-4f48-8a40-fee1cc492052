// src/lib/server-auth.ts
import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { Types } from 'mongoose';

interface JwtPayload {
  id: string;
  email: string;
  userType: 'superuser' | 'company_user';
  companyId?: string;
  role?: 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper';
}

/**
 * Validates if the user has access to the specified company
 */
export async function isValidCompanyUser(request: NextRequest, companyId: string) {
  try {
    // Get the token from cookies or Authorization header
    let token = request.cookies.get('auth-token')?.value;
    
    // If no cookie token found, check Authorization header
    if (!token) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }
    
    if (!token) {
      return { isValid: false, message: 'No authentication token found' };
    }
    
    // Verify the token
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET is not defined');
    }
    
    const payload = verify(token, secret) as JwtPayload;
    
    // Handle compatibility: if userId exists but id doesn't, use userId as id
    if ((payload as any).userId && !payload.id) {
      payload.id = (payload as any).userId;
    }
    
    // Check if the user exists
    await dbConnect();
    const user = await User.findById(payload.id);
    
    if (!user) {
      return { isValid: false, message: 'User not found' };
    }
    
    // For company users, validate company access
    if (payload.userType === 'company_user') {
      if (payload.companyId !== companyId) {
        return { isValid: false, message: 'User does not have access to this company' };
      }
      
      return { 
        isValid: true, 
        userId: payload.id,
        userRole: payload.role,
        userType: payload.userType
      };
    }
    
    // Superusers have access to all companies
    if (payload.userType === 'superuser') {
      return { 
        isValid: true, 
        userId: payload.id,
        userType: payload.userType
      };
    }
    
    return { isValid: false, message: 'Invalid user type' };
  } catch (error: any) {
    console.error('Auth validation error:', error.message);
    return { isValid: false, message: error.message };
  }
}

/**
 * Validates if the user has the required role for a company
 */
export async function hasRequiredCompanyRole(
  request: NextRequest, 
  companyId: string,
  requiredRole?: 'owner' | 'admin' | 'manager' | 'user' | 'storekeeper'
) {
  console.log(`[hasRequiredCompanyRole] Checking for role: ${requiredRole} for company: ${companyId}`);
  
  // Get cookie and headers for debugging
  console.log(`[hasRequiredCompanyRole] Auth cookie: ${request.cookies.get('auth-token')?.value ? 'Present' : 'Missing'}`);
  console.log(`[hasRequiredCompanyRole] Auth header: ${request.headers.get('Authorization') ? 'Present' : 'Missing'}`);
  console.log(`[hasRequiredCompanyRole] Company header: ${request.headers.get('company-id')}`);
  
  const authResult = await isValidCompanyUser(request, companyId);
  console.log(`[hasRequiredCompanyRole] Auth result:`, JSON.stringify(authResult));
  
  if (!authResult.isValid) {
    console.log(`[hasRequiredCompanyRole] Invalid auth: ${authResult.message}`);
    return authResult;
  }
  
  // Superusers bypass role checks
  if (authResult.userType === 'superuser') {
    console.log(`[hasRequiredCompanyRole] Superuser detected, bypassing role check`);
    return authResult;
  }
  
  // If no specific role is required, just being a valid company user is enough
  if (!requiredRole) {
    console.log(`[hasRequiredCompanyRole] No role required, passing`);
    return authResult;
  }
  
  // Role hierarchy (higher roles can do everything lower roles can)
  const roleHierarchy = {
    'owner': 4,
    'admin': 3,
    'manager': 2,
    'storekeeper': 1,
    'user': 0
  };
  
  const userRoleValue = roleHierarchy[authResult.userRole || 'user'];
  const requiredRoleValue = roleHierarchy[requiredRole];
  
  console.log(`[hasRequiredCompanyRole] User role: ${authResult.userRole} (${userRoleValue}), Required: ${requiredRole} (${requiredRoleValue})`);
  
  if (userRoleValue >= requiredRoleValue) {
    console.log(`[hasRequiredCompanyRole] Role check passed`);
    return authResult;
  }
  
  console.log(`[hasRequiredCompanyRole] Role check failed`);
  return { 
    isValid: false, 
    message: `User does not have the required role. Required: ${requiredRole}, User has: ${authResult.userRole}` 
  };
}
