import mongoose from 'mongoose';

// Production fallback URI (used only if MONGODB_URI env var is not set)
const FALLBACK_MONGODB_URI = "mongodb+srv://jkayobotsi:<EMAIL>/?retryWrites=true&w=majority&appName=restaurant-inventory-dev";

interface CachedConnection {
  conn: mongoose.Connection | null;
  promise: Promise<mongoose.Connection> | null;
}

declare global {
  let mongoose: CachedConnection | undefined;
}

const cached: CachedConnection = global.mongoose || { conn: null, promise: null };

if (!global.mongoose) {
  global.mongoose = cached;
}

async function dbConnect() {
  // Determine URI at runtime, prefer env var (test or prod), fallback if not set
  const uri = process.env.MONGODB_URI || FALLBACK_MONGODB_URI;
  if (!uri) {
    throw new Error('MongoDB URI not found in environment variables.');
  }
  if (cached.conn) return cached.conn;
  if (!cached.promise) {
    const opts = { bufferCommands: false };
    cached.promise = mongoose.connect(uri, opts).then((mongoose) => mongoose.connection);
  }

  try {
    cached.conn = await cached.promise;
    return cached.conn;
  } catch (e) {
    cached.promise = null;
    throw e;
  }
}

export default dbConnect;
