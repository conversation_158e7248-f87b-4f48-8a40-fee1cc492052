import { CompanyData } from '../contexts/CompanyContext';

// Update CompanyData interface with RBAC fields
export interface CompanyData {
  id?: string;
  name: string;
  ownerId: string;
  roles: { [userId: string]: string }; // Map of userId to role
  permissions: { [userId: string]: string[] }; // Map of userId to permissions
  createdAt: Date;
  updatedAt: Date;
  locations?: Location[];
}

// Location type for company locations
export interface Location {
  id: string;
  name: string;
  address: string;
  type?: 'store' | 'warehouse' | 'kitchen';
  managerId?: string;
  storekeeperId?: string;
}

// Extended company data with additional fields for internal use
export interface CompanyDetails extends CompanyData {
  status: 'active' | 'inactive' | 'suspended';
  subscription?: {
    plan: string;
    startDate: Date;
    expiresAt: Date;
  };
}
