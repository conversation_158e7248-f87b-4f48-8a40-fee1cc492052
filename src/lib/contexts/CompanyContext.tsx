"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { CompanyData } from '../types/company';

interface CompanyContextType {
  companyId: string;
  locationId?: string;
  setLocationId: (id: string) => void;
  companyData: CompanyData | null;
  setCompanyData: (data: CompanyData) => void;
  loading: boolean;
  validateCompanyAccess: (requestedCompanyId: string) => boolean;
  validateLocationAccess: (requestedLocationId: string, userRole: string) => boolean;
}

export const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

interface CompanyProviderProps {
  children: ReactNode;
  initialCompanyData?: CompanyData;
  userData?: {
    companyId?: string;
    locationId?: string;
    role?: string;
  } | null;
}

export function CompanyProvider({ children, initialCompanyData, userData }: CompanyProviderProps) {
  const [companyData, setCompanyData] = useState<CompanyData | null>(initialCompanyData || null);
  const [locationId, setLocationId] = useState<string>(userData?.locationId || '');
  const [loading, setLoading] = useState(!initialCompanyData);

  // The company ID comes from the URL parameter in the layout
  const companyId = companyData?.id ?? '';

  // Validate that the user has access to the requested company
  const validateCompanyAccess = (requestedCompanyId: string): boolean => {
    if (!userData?.companyId || !requestedCompanyId) return false;
    return userData.companyId === requestedCompanyId;
  };

  // Validate that the user has access to the requested location
  const validateLocationAccess = (requestedLocationId: string, userRole: string): boolean => {
    if (!companyData || !requestedLocationId) return false;

    // Admins can access all locations
    if (userRole === 'admin') return true;

    // Managers and storekeepers can only access their assigned location
    if ((userRole === 'manager' || userRole === 'storekeeper') && userData?.locationId) {
      return userData.locationId === requestedLocationId;
    }

    return false;
  };

  const value = {
    companyId,
    locationId,
    setLocationId,
    companyData,
    setCompanyData,
    loading,
    validateCompanyAccess,
    validateLocationAccess,
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
}

export function useCompanyContext() {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompanyContext must be used within a CompanyProvider');
  }
  return context;
}

export type { CompanyContextType };
