// authUtils.ts
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import User, { IUser } from '../models/User';
import Role from '../models/Role';

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

export interface JwtPayload {
  id?: string;
  userId?: string;
  userType?: string;
  email?: string;
  role?: string;
  companyId?: string;
  tenantId?: string;
  permissions?: string[];
  locationAccess?: string[];
  iat?: number;
  exp?: number;
  token_type?: string;
}

export async function signUp(email: string, password: string, userType: string, companyId?: string, role?: string): Promise<IUser> {
  const passwordHash = await bcrypt.hash(password, 10);
  const user = new User({ email, passwordHash, userType, companyId, role });
  await user.save();
  return user;
}

export async function signIn(email: string, password: string): Promise<string> {
  const user = await User.findOne({ email });
  if (!user) throw new Error('User not found');
  
  const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
  if (!isPasswordValid) throw new Error('Invalid credentials');
  
  // Get user role permissions if applicable
  let allPermissions = [...(user.permissions || [])];
  
  if (user.userType === 'company_user' && user.role && user.companyId) {
    try {
      const roleDoc = await Role.findOne({ 
        companyId: user.companyId, 
        name: user.role 
      });
      
      if (roleDoc && roleDoc.permissions) {
        // Combine role permissions with user permissions (without duplicates)
        allPermissions = [...new Set([...allPermissions, ...roleDoc.permissions])];
      }
    } catch (error) {
      console.error('Error fetching role permissions:', error);
    }
  }
  
  // Create JWT payload with additional information
  const payload: JwtPayload = {
    id: user._id.toString(),
    userType: user.userType,
    permissions: allPermissions
  };
  
  // Add role if it exists
  if (user.role) {
    payload.role = user.role;
  }
  
  // Add companyId if it exists
  if (user.companyId) {
    payload.companyId = user.companyId.toString();
    payload.tenantId = user.companyId.toString();
  }
  
  // Add location access if needed in the future
  // payload.locationAccess = [...]
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '8h' });
}

export async function verifyToken(token: string): Promise<JwtPayload | null> {
  try {
    console.log('Verifying token:', token); // Debug log
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    console.log('Decoded token:', decoded); // Debug log

    // Convert userId to id for compatibility
    if (decoded.userId && !decoded.id) {
      decoded.id = decoded.userId;
    }

    // Additional verification steps if needed
    if (!decoded.id) {
      console.log('Token missing user ID'); // Debug log
      return null;
    }

    // Optional: Check if user still exists
    const userId = decoded.id;
    console.log('Looking up user with ID:', userId);
    const user = await User.findById(userId);
    if (!user) {
      console.log('User not found for token ID:', userId); // Debug log
      return null;
    }
    
    // Add user data to the decoded token
    decoded.userType = user.userType;
    decoded.role = user.role;
    decoded.companyId = user.companyId?.toString();
    decoded.tenantId = user.companyId?.toString();
    console.log('Enhanced token payload:', decoded);

    return decoded;
  } catch (error) {
    console.error('Token verification error:', error); // Detailed error logging
    return null;
  }
}

export function decodeToken(token: string): JwtPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    console.error('Token decode error:', error);
    return null;
  }
}

export function getUserPermissionsFromToken(token: string): string[] {
  const decoded = decodeToken(token);
  return decoded?.permissions || [];
}

// Check if a user has the required permission
export function hasPermission(token: string, requiredPermission: string): boolean {
  const permissions = getUserPermissionsFromToken(token);
  return permissions.includes(requiredPermission);
}

// Check if a user has any of the required permissions
export function hasAnyPermission(token: string, requiredPermissions: string[]): boolean {
  const permissions = getUserPermissionsFromToken(token);
  return requiredPermissions.some(perm => permissions.includes(perm));
}

// Check if a user has all of the required permissions
export function hasAllPermissions(token: string, requiredPermissions: string[]): boolean {
  const permissions = getUserPermissionsFromToken(token);
  return requiredPermissions.every(perm => permissions.includes(perm));
}

/**
 * Generate a company token for IonicPOS app
 * This token has limited scope and short lifespan
 * @param companyId - ID of the company
 * @param companyName - Name of the company
 * @returns JWT token string
 */
export function generateCompanyToken(companyId: string, companyName: string): string {
  const payload: JwtPayload = {
    role: 'pos_company',
    companyId: companyId,
    tenantId: companyId,
    token_type: 'company_access',
    // No user ID or permissions - limited scope
  };
  
  // Short lifespan - 15 minutes
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '15m' });
}

/**
 * Verify a company token
 * @param token - The company token to verify
 * @returns JwtPayload | null - Decoded payload or null if invalid
 */
export async function verifyCompanyToken(token: string): Promise<JwtPayload | null> {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
    
    // Ensure this is a company token
    if (decoded.token_type !== 'company_access' || decoded.role !== 'pos_company') {
      console.log('Invalid company token type or role');
      return null;
    }
    
    // Ensure company ID exists
    if (!decoded.companyId || !decoded.tenantId) {
      console.log('Company token missing company ID');
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Company token verification error:', error);
    return null;
  }
}
