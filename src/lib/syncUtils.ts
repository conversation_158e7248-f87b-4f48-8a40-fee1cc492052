// src/lib/syncUtils.ts
import mongoose from 'mongoose';
import Role, { IRole } from '../models/Role';
import Permission, { IPermission } from '../models/Permission';
import User, { IUser } from '../models/User';

/**
 * Serialize roles and permissions for Ionic app sync
 */
export async function serializeRolesAndPermissions(companyId: string) {
  if (!companyId) {
    throw new Error('Company ID is required');
  }

  try {
    // Get all roles for the company
    const roles = await Role.find({ companyId }).lean();
    
    // Get all permissions (both system and company-specific)
    const permissions = await Permission.find({
      $or: [
        { isSystemLevel: true },
        { companyId }
      ]
    }).lean();

    // Format for Ionic app consumption
    return {
      roles: roles.map(role => ({
        id: role._id.toString(),
        name: role.name,
        permissions: role.permissions || [],
        createdAt: role.createdAt,
        updatedAt: role.updatedAt
      })),
      permissions: permissions.map(permission => ({
        id: permission._id.toString(),
        name: permission.name,
        description: permission.description,
        category: permission.category,
        isSystemLevel: permission.isSystemLevel || false
      }))
    };
  } catch (error) {
    console.error('Error serializing roles and permissions:', error);
    throw error;
  }
}

/**
 * Get user permissions including role-based permissions
 */
export async function getUserPermissions(userId: string, companyId: string) {
  if (!userId || !companyId) {
    throw new Error('User ID and Company ID are required');
  }

  try {
    // Find the user
    const user = await User.findOne({ 
      _id: userId,
      companyId
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get direct user permissions
    let allPermissions = [...(user.permissions || [])];

    // If user has a role, get role permissions
    if (user.role) {
      const role = await Role.findOne({ 
        companyId, 
        name: user.role 
      });

      if (role && role.permissions) {
        // Combine role permissions with user permissions (without duplicates)
        allPermissions = [...new Set([...allPermissions, ...role.permissions])];
      }
    }

    return {
      userId: user._id.toString(),
      role: user.role,
      permissions: allPermissions
    };
  } catch (error) {
    console.error('Error getting user permissions:', error);
    throw error;
  }
}

/**
 * Get all users with their roles and permissions for a company
 */
export async function getCompanyUsersWithPermissions(companyId: string) {
  if (!companyId) {
    throw new Error('Company ID is required');
  }

  try {
    // Get all company users
    const users = await User.find({ 
      companyId,
      userType: 'company_user'
    }).select('-passwordHash').lean();

    // Get all roles
    const roles = await Role.find({ companyId }).lean();

    // Create a map of role names to permissions for quick lookup
    const rolePermissionsMap = new Map<string, string[]>();
    roles.forEach(role => {
      rolePermissionsMap.set(role.name, role.permissions || []);
    });

    // Enrich users with full permissions (direct + role-based)
    return users.map(user => {
      const directPermissions = user.permissions || [];
      const rolePermissions = user.role ? rolePermissionsMap.get(user.role) || [] : [];
      
      // Combine permissions without duplicates
      const allPermissions = [...new Set([...directPermissions, ...rolePermissions])];

      return {
        id: user._id.toString(),
        email: user.email,
        displayName: user.displayName,
        role: user.role,
        permissions: allPermissions
      };
    });
  } catch (error) {
    console.error('Error getting company users with permissions:', error);
    throw error;
  }
}
