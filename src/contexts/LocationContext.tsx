'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Location } from '@/lib/types/location';

interface LocationContextType {
  selectedLocation: Location | null;
  setSelectedLocation: (location: Location | null) => void;
  isRetailView: boolean;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export function LocationProvider({ children }: { children: ReactNode }) {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);

  const isRetailView = selectedLocation?.locationType === 'RETAIL_SHOP';

  return (
    <LocationContext.Provider value={{ selectedLocation, setSelectedLocation, isRetailView }}>
      {children}
    </LocationContext.Provider>
  );
}

export function useLocation() {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
}
